<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1771px" height="477px" viewBox="-0.5 -0.5 1771 477" style="background-color: rgb(255, 255, 255);"><defs><style type="text/css">@font-face {&#xa;font-family: "思源黑体";&#xa;src: url("https://pocdn.processon.com/wps/fonts/sy-heiti.woff2");&#xa;}&#xa;</style></defs><rect fill="#ffffff" width="100%" height="100%" x="0" y="0"/><g><g data-cell-id="0"><g data-cell-id="1"><g data-cell-id="fRV1jjPQcb5wottTv5Cd-5Polly"><g><path d="M 121 115 L 192.76 115" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 198.76 115 L 190.76 119 L 192.76 115 L 190.76 111 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-1Polly"><g><rect x="1" y="85" width="120" height="60" fill="#fff2cc" stroke="#d6b656" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 2px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">kubelet</div></div></div></foreignObject><text x="61" y="120" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">kubelet</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-2Polly"><g><rect x="1" y="185" width="120" height="60" fill="#fff2cc" stroke="#d6b656" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 2px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">kubelet</div></div></div></foreignObject><text x="61" y="220" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">kubelet</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-3Polly"><g><rect x="1" y="285" width="120" height="60" fill="#fff2cc" stroke="#d6b656" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 2px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">kubelet</div></div></div></foreignObject><text x="61" y="320" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">kubelet</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-4Polly"><g><rect x="1" y="385" width="120" height="60" fill="#fff2cc" stroke="#d6b656" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 415px; margin-left: 2px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">kubelet</div></div></div></foreignObject><text x="61" y="420" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">kubelet</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-6Polly"><g><path d="M 121 214.5 L 192.76 214.5" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 198.76 214.5 L 190.76 218.5 L 192.76 214.5 L 190.76 210.5 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-7Polly"><g><path d="M 121 314.5 L 192.76 314.5" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 198.76 314.5 L 190.76 318.5 L 192.76 314.5 L 190.76 310.5 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-8Polly"><g><path d="M 121 414.5 L 192.76 414.5" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 198.76 414.5 L 190.76 418.5 L 192.76 414.5 L 190.76 410.5 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-13Polly"><g><path d="M 321 115 L 392.76 115" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 398.76 115 L 390.76 119 L 392.76 115 L 390.76 111 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-9Polly"><g><rect x="201" y="85" width="120" height="60" fill="#2f9e44" stroke="#2f9e44" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 202px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Dockershim</div></div></div></foreignObject><text x="261" y="120" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Dockershim</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-18Polly"><g><path d="M 321 215 L 392.76 215" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 398.76 215 L 390.76 219 L 392.76 215 L 390.76 211 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-10Polly"><g><rect x="201" y="185" width="120" height="60" fill="#2f9e44" stroke="#2f9e44" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 202px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">CRI-Containerd</div></div></div></foreignObject><text x="261" y="220" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">CRI-Containerd</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-25Polly"><g><path d="M 321 315 L 392.76 315" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 398.76 315 L 390.76 319 L 392.76 315 L 390.76 311 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-11Polly"><g><rect x="201" y="285" width="120" height="60" fill="#1971c2" stroke="#1971c2" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 202px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Containerd</div></div></div></foreignObject><text x="261" y="320" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Containerd</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-31Polly"><g><path d="M 321 415 L 992.76 415" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 998.76 415 L 990.76 419 L 992.76 415 L 990.76 411 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-12Polly"><g><rect x="201" y="385" width="120" height="60" fill="#e03131" stroke="#e03131" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 415px; margin-left: 202px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">CRI-O</div></div></div></foreignObject><text x="261" y="420" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">CRI-O</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-17Polly"><g><path d="M 521 115 L 592.76 115" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 598.76 115 L 590.76 119 L 592.76 115 L 590.76 111 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-14Polly"><g><rect x="401" y="85" width="120" height="60" fill="#e1d5e7" stroke="#9673a6" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 402px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Docker Engine</div></div></div></foreignObject><text x="461" y="120" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Docker Engine</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-23Polly"><g><path d="M 521 215 L 592.76 215" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 598.76 215 L 590.76 219 L 592.76 215 L 590.76 211 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-15Polly"><g><rect x="401" y="185" width="120" height="60" fill="#1971c2" stroke="#1971c2" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 402px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Containerd</div></div></div></foreignObject><text x="461" y="220" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Containerd</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-20Polly"><g><path d="M 721 115 L 792.76 115" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 798.76 115 L 790.76 119 L 792.76 115 L 790.76 111 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-16Polly"><g><rect x="601" y="85" width="120" height="60" fill="#1971c2" stroke="#1971c2" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 602px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Containerd</div></div></div></foreignObject><text x="661" y="120" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Containerd</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-34Polly"><g><path d="M 921 115 L 992.76 115" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 998.76 115 L 990.76 119 L 992.76 115 L 990.76 111 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-19Polly"><g><rect x="801" y="85" width="120" height="60" fill="#f08c00" stroke="#f08c00" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 802px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Containerd-shim</div></div></div></foreignObject><text x="861" y="120" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Containerd-shim</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-33Polly"><g><path d="M 721 215 L 992.76 215" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 998.76 215 L 990.76 219 L 992.76 215 L 990.76 211 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-21Polly"><g><rect x="601" y="185" width="120" height="60" fill="#f08c00" stroke="#f08c00" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 602px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Containerd-shim</div></div></div></foreignObject><text x="661" y="220" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Containerd-shim</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-32Polly"><g><path d="M 521 315 L 992.76 315" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 998.76 315 L 990.76 319 L 992.76 315 L 990.76 311 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-22Polly"><g><rect x="401" y="285" width="120" height="60" fill="#f08c00" stroke="#f08c00" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 402px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Containerd-shim</div></div></div></foreignObject><text x="461" y="320" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">Containerd-shim</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-36Polly"><g><path d="M 1121 115 L 1192.76 115" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1198.76 115 L 1190.76 119 L 1192.76 115 L 1190.76 111 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-26Polly"><g><rect x="1001" y="85" width="120" height="60" fill="#f8cecc" stroke="#b85450" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 1002px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OCI runtime</div></div></div></foreignObject><text x="1061" y="120" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">OCI runtime</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-40Polly"><g><path d="M 1121 215 L 1192.76 215" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1198.76 215 L 1190.76 219 L 1192.76 215 L 1190.76 211 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-27Polly"><g><rect x="1001" y="185" width="120" height="60" fill="#f8cecc" stroke="#b85450" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 1002px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OCI runtime</div></div></div></foreignObject><text x="1061" y="220" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">OCI runtime</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-41Polly"><g><path d="M 1121 315 L 1192.76 315" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1198.76 315 L 1190.76 319 L 1192.76 315 L 1190.76 311 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-29Polly"><g><rect x="1001" y="285" width="120" height="60" fill="#f8cecc" stroke="#b85450" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 1002px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OCI runtime</div></div></div></foreignObject><text x="1061" y="320" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">OCI runtime</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-42Polly"><g><path d="M 1121 415 L 1192.76 415" fill="none" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1198.76 415 L 1190.76 419 L 1192.76 415 L 1190.76 411 Z" fill="#666666" stroke="#666666" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-30Polly"><g><rect x="1001" y="385" width="120" height="60" fill="#f8cecc" stroke="#b85450" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 415px; margin-left: 1002px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OCI runtime</div></div></div></foreignObject><text x="1061" y="420" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">OCI runtime</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-35Polly"><g><rect x="1201" y="85" width="120" height="60" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 115px; margin-left: 1202px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">container</div></div></div></foreignObject><text x="1261" y="120" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">container</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-37Polly"><g><rect x="1201" y="185" width="120" height="60" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 215px; margin-left: 1202px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">container</div></div></div></foreignObject><text x="1261" y="220" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">container</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-38Polly"><g><rect x="1201" y="285" width="120" height="60" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 315px; margin-left: 1202px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">container</div></div></div></foreignObject><text x="1261" y="320" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">container</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-39Polly"><g><rect x="1201" y="385" width="120" height="60" fill="#d5e8d4" stroke="#82b366" stroke-width="2" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 415px; margin-left: 1202px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">container</div></div></div></foreignObject><text x="1261" y="420" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">container</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-46Polly"><g><path d="M 148.5 475 L 148.5 55 M 153.5 55 L 153.5 475 M 153.5 55" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-47Polly"><g><path d="M 948.5 475 L 948.5 55 M 953.5 55 L 953.5 475 M 953.5 55" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-linejoin="round" stroke-miterlimit="10" pointer-events="all"/></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-48Polly"><g><rect x="121" y="0" width="60" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 20px; margin-left: 122px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">CRI</div></div></div></foreignObject><text x="151" y="25" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">CRI</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-49Polly"><g><rect x="921" y="5" width="50" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 20px; margin-left: 946px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: nowrap;">OCI</div></div></div></foreignObject><text x="946" y="25" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">OCI</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-50Polly"><g><rect x="201" y="335" width="60" height="20" fill="#f08c00" stroke="#f08c00" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 345px; margin-left: 202px;"><div data-drawio-colors="color: #fff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">CRI-Plugin</font></div></div></div></foreignObject><text x="231" y="350" fill="#fff" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">CRI-Plu...</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-56Polly"><g><rect x="1351" y="310" width="420" height="10" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 418px; height: 1px; padding-top: 315px; margin-left: 1352px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;">【当前主流方案】</div><span style="color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; text-align: left; background-color: rgb(252, 252, 252);">Containerd 内置 CRI 插件，Kubelet 直接通过 CRI 调用它</span></div></div></div></foreignObject><text x="1561" y="320" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px" text-anchor="middle">【当前主流方案】Containerd 内置 CRI 插件，Kubelet 直接通过 CRI 调用它</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-58Polly"><g><rect x="1351" y="205" width="290" height="40" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 225px; margin-left: 1353px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="text-wrap-mode: nowrap;">【已废弃】k8s v1.24移除了Dockershim</span><div style="text-wrap-mode: nowrap;">直接通过<code class="hyc-common-markdown__code__inline" style="margin: 0px; padding: 0px; border: 0px; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-variant-position: inherit; font-variant-emoji: inherit; font-stretch: inherit; font-size: 14px; line-height: inherit; font-optical-sizing: inherit; font-size-adjust: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; vertical-align: baseline; white-space: break-spaces; word-break: break-word; color: rgba(0, 0, 0, 0.9); background-color: rgb(252, 252, 252); font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace !important;">CRI-Containerd</code><span style="text-wrap-mode: wrap; color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif; background-color: rgb(252, 252, 252);"> 适配 CRI</span></div></div></div></div></foreignObject><text x="1353" y="230" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px">【已废弃】k8s v1.24移除了Dockershim...</text></switch></g></g></g><g data-cell-id="fRV1jjPQcb5wottTv5Cd-63Polly"><g><rect x="1351" y="395" width="390" height="30" fill="none" stroke="none" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 388px; height: 1px; padding-top: 410px; margin-left: 1353px;"><div data-drawio-colors="color: #333; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: 思源黑体; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">【轻量替代】<div>CRI-O转为k8s设计，<span style="background-color: rgb(252, 252, 252); color: rgba(0, 0, 0, 0.9); font-family: &quot;PingFang SC&quot;, -apple-system, &quot;system-ui&quot;, &quot;Segoe UI&quot;, Roboto, Ubuntu, &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, &quot;Source Han Sans CN&quot;, sans-serif;">直接调用 OCI 运行时（如 runc）</span></div></div></div></div></foreignObject><text x="1353" y="415" fill="#333" font-family="&quot;思源黑体&quot;" font-size="16px">【轻量替代】&#xa;CRI-O转为k8s设计，直接调用 OCI 运行时（如 runc）</text></switch></g></g></g></g></g></g></svg>