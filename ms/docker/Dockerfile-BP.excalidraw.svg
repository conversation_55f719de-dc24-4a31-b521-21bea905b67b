<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 931.4688618977865 1112.4650268554688" width="931.4688618977865" height="1112.4650268554688">
  <!-- svg-source:excalidraw -->
  <!-- payload-type:application/vnd.excalidraw+json --><!-- payload-version:2 --><!-- payload-start -->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<!-- payload-end -->
  <defs>
    <style class="style-fonts">
      @font-face {
        font-family: "Virgil";
        src: url("https://unpkg.com/revedraw@1.0.0-alpha.10/dist/excalidraw-assets/Virgil.woff2");
      }
      @font-face {
        font-family: "Cascadia";
        src: url("https://unpkg.com/revedraw@1.0.0-alpha.10/dist/excalidraw-assets/Cascadia.woff2");
      }
    </style>

  </defs>
  <rect x="0" y="0" width="931.4688618977865" height="1112.4650268554688" fill="#fffce8"></rect><g transform="translate(43.5268147786459 290.537585005055) rotate(0 26.93598175048828 17.500000000000014)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="28px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">build</text></g><g transform="translate(61.1182454427084 671.2503680012148) rotate(0 16.491989135742188 17.5)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="28px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">sec</text></g><g transform="translate(244.36147054036465 136.26528930664062) rotate(0 47.309974670410156 12.5)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="20px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">image 体积</text></g><g transform="translate(245.45363362630215 312.4984893798828) rotate(0 47.309974670410156 12.5)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="20px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">image 层数</text></g><g transform="translate(250.14235432942715 435.83067321777344) rotate(0 39.83997344970703 12.5)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="20px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">加速build</text></g><g stroke-linecap="round"><g transform="translate(293.5914656999614 310.11744689941406) rotate(0 -8.245056825275526 -64.13604000483949)"><path d="M0.04 0.42 C-2.67 -21.02, -13.99 -106.95, -16.53 -128.69" stroke="#1e1e1e" stroke-width="1.5" fill="none" stroke-dasharray="1.5 7"></path></g><g transform="translate(293.5914656999614 310.11744689941406) rotate(0 -8.245056825275526 -64.13604000483949)"><path d="M-2.15 -102.9 C-6.35 -108.16, -12.39 -116.42, -16.07 -130.35" stroke="#1e1e1e" stroke-width="1.5" fill="none" stroke-dasharray="1.5 5"></path></g><g transform="translate(293.5914656999614 310.11744689941406) rotate(0 -8.245056825275526 -64.13604000483949)"><path d="M-22.51 -100.35 C-21.01 -106.33, -21.34 -115.31, -16.07 -130.35" stroke="#1e1e1e" stroke-width="1.5" fill="none" stroke-dasharray="1.5 5"></path></g></g><mask></mask><g stroke-opacity="0.5" fill-opacity="0.5" transform="translate(289.7584228515626 223.1284942626953) rotate(0 103.27959442138672 12.651819499808255)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="10.121455599846605px" fill="#e03131" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">注意二者之间的关系，类似</text><text x="0" y="12.651819499808257" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="10.121455599846605px" fill="#e03131" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">git的 .git 体积和commit次数的关系（大文件）</text></g><g transform="translate(604.2136433919271 74.16694641113281) rotate(0 48.39971160888672 18.771630148073214)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="15.01730411845857px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">基础image选择</text><text x="0" y="18.771630148073214" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="15.01730411845857px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text></g><g transform="translate(583.9746297200521 273.45790100097656) rotate(0 163.0399169921875 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">单行RUN（而非多行RUN） ----&gt; 合并指令&amp;&amp; </text></g><g transform="translate(592.3840535481771 401.51600646972656) rotate(0 120.8159408569336 20)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">Copy &gt; Add (优先使用Copy而非Add)</text><text x="0" y="20" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text></g><g stroke-linecap="round"><g transform="translate(341.43868001302087 147.3522139500859) rotate(0 124.37013825788165 -33.343472732873536)"><path d="M1 0.46 C41.99 -10.73, 205.52 -55.98, 246.47 -67.15 M0.07 -0.34 C41.38 -11.33, 207.15 -54.25, 248.67 -65.63" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(341.43868001302087 147.3522139500859) rotate(0 124.37013825788165 -33.343472732873536)"><path d="M224.82 -50.1 C232.51 -55.86, 242.02 -59, 247.68 -65.75 M224.06 -48.56 C230.32 -54.59, 238.43 -59.4, 249.52 -64.72" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(341.43868001302087 147.3522139500859) rotate(0 124.37013825788165 -33.343472732873536)"><path d="M219.56 -69.94 C229.06 -68.89, 240.4 -65.17, 247.68 -65.75 M218.8 -68.39 C226.65 -68.75, 236.27 -67.83, 249.52 -64.72" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(347.7498982747396 330.9807784154691) rotate(0 114.27231774124317 -25.123931949033874)"><path d="M0.04 0.35 C37.9 -7.65, 189.85 -40.71, 227.86 -49.08 M-1.41 -0.5 C36.74 -8.73, 191.3 -42.02, 229.95 -50.6" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(347.7498982747396 330.9807784154691) rotate(0 114.27231774124317 -25.123931949033874)"><path d="M205.19 -36.55 C215.71 -41.91, 223.48 -47.79, 231.51 -51.64 M205.27 -34.77 C211.02 -39.36, 219.62 -42.8, 230.34 -50.31" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(347.7498982747396 330.9807784154691) rotate(0 114.27231774124317 -25.123931949033874)"><path d="M200.81 -56.6 C212.73 -54.79, 222.07 -53.48, 231.51 -51.64 M200.9 -54.82 C207.93 -53.79, 217.75 -51.66, 230.34 -50.31" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(340.9640096028646 456.8228358858853) rotate(0 118.95092124315909 -24.895899965636204)"><path d="M0.99 0.29 C40.19 -7.87, 196.23 -40.26, 235.45 -48.26 M0.04 -0.6 C39.64 -9.19, 198.59 -41.95, 237.86 -50.09" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(340.9640096028646 456.8228358858853) rotate(0 118.95092124315909 -24.895899965636204)"><path d="M212.82 -35.31 C221.25 -39.11, 230.34 -43.19, 239.34 -50.67 M212.46 -35.24 C219.84 -39.45, 226.29 -44.23, 237.7 -49.65" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(340.9640096028646 456.8228358858853) rotate(0 118.95092124315909 -24.895899965636204)"><path d="M208.66 -55.4 C218.54 -52.87, 228.94 -50.58, 239.34 -50.67 M208.31 -55.34 C216.97 -53.92, 224.59 -53.04, 237.7 -49.65" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g transform="translate(578.1364746093751 571.0109608968099) rotate(0 42.607200622558594 19.2441147985665)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="15.395291838853195px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">限制IP和Port</text><text x="0" y="19.244114798566493" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="15.395291838853195px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text></g><g stroke-linecap="round"><g transform="translate(99.08618672688809 684.9571461422174) rotate(0 224.63403954480887 82.30154878973713)"><path d="M0.13 0.92 C75.3 28.26, 375.8 135.84, 450.53 163.22 M-1.26 0.35 C73.86 27.88, 374.7 137.09, 450.05 164.25" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(99.08618672688809 684.9571461422174) rotate(0 224.63403954480887 82.30154878973713)"><path d="M421.58 165.79 C426.3 162.42, 437.39 163.75, 451.73 163.85 M420.07 164.08 C426.51 164.88, 432.68 164.29, 450.1 164.96" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(99.08618672688809 684.9571461422174) rotate(0 224.63403954480887 82.30154878973713)"><path d="M428.56 146.5 C431.33 148.17, 440.59 154.54, 451.73 163.85 M427.05 144.78 C432 149.7, 436.68 153.22, 450.1 164.96" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(98.83630879720059 696.1319574679691) rotate(0 227.95682643961942 -59.3514363484951)"><path d="M0.23 -0.86 C76.46 -20.69, 381.1 -98.6, 457.03 -118.11 M-1.11 1.3 C75.06 -18.97, 380.46 -99.79, 456.44 -120" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(98.83630879720059 696.1319574679691) rotate(0 227.95682643961942 -59.3514363484951)"><path d="M430.38 -101.71 C439 -108.36, 445.86 -113.15, 454.69 -120.37 M432.49 -103.07 C438.49 -106.56, 445.44 -112.31, 456.9 -119.39" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(98.83630879720059 696.1319574679691) rotate(0 227.95682643961942 -59.3514363484951)"><path d="M425.12 -121.54 C435.28 -122.92, 443.54 -122.42, 454.69 -120.37 M427.23 -122.91 C434.51 -120.93, 442.9 -121.23, 456.9 -119.39" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(349.52986653645837 147.44752814941194) rotate(0 115.03342615719083 17.623332772032505)"><path d="M1.08 -0.19 C39.56 5.84, 191.76 30.59, 229.88 36.58 M0.18 -1.34 C38.68 4.3, 191.22 28.35, 229.44 34.72" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(349.52986653645837 147.44752814941194) rotate(0 115.03342615719083 17.623332772032505)"><path d="M199.66 41.97 C207.16 40.39, 212.15 37.07, 230.99 33.44 M199.51 40.37 C206.98 39.62, 213.34 37.8, 229.25 35.57" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(349.52986653645837 147.44752814941194) rotate(0 115.03342615719083 17.623332772032505)"><path d="M202.93 21.71 C209.79 24.54, 214.07 25.6, 230.99 33.44 M202.78 20.11 C209.5 23.88, 215.13 26.54, 229.25 35.57" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(343.04173946334697 337.9952051579297) rotate(0 121.5871262491836 0.35877412368958517)"><path d="M-0.46 0.13 C40.2 0.04, 203 0.28, 243.64 0.34 M1.49 -0.85 C42.07 -0.86, 202.71 1.16, 242.91 1.56" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(343.04173946334697 337.9952051579297) rotate(0 121.5871262491836 0.35877412368958517)"><path d="M214.82 12.19 C221.15 9.2, 229.04 7.1, 243.85 0.62 M214.72 11.79 C224.22 7.57, 234.5 3.84, 242.57 1.73" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(343.04173946334697 337.9952051579297) rotate(0 121.5871262491836 0.35877412368958517)"><path d="M215.05 -8.33 C221.47 -5.8, 229.29 -2.39, 243.85 0.62 M214.95 -8.73 C224.45 -5.85, 234.65 -2.48, 242.57 1.73" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g transform="translate(44.1831868489584 946.2855377197266) rotate(0 28.839996337890625 17.5)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="28px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">其他</text></g><g stroke-linecap="round"><g transform="translate(100.9977620442709 297.07561186458753) rotate(0 66.81133194661126 -65.88873302665019)"><path d="M-0.28 0.07 C21.91 -21.87, 111.84 -109.76, 133.9 -131.85 M1.78 -0.93 C23.82 -22.67, 111.31 -108.65, 133.43 -130.42" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(100.9977620442709 297.07561186458753) rotate(0 66.81133194661126 -65.88873302665019)"><path d="M120.64 -101.83 C124.93 -114.69, 128.62 -124.52, 132.34 -129.35 M120.86 -102.86 C124.48 -111.09, 127.65 -116.58, 133.63 -129.64" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(100.9977620442709 297.07561186458753) rotate(0 66.81133194661126 -65.88873302665019)"><path d="M106.25 -116.46 C115.75 -123.96, 124.67 -128.47, 132.34 -129.35 M106.47 -117.49 C113.65 -122.19, 120.42 -124.03, 133.63 -129.64" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(105.84816487630215 299.3171987738916) rotate(0 69.72676020726252 81.32620022697331)"><path d="M-0.35 0.01 C22.82 27.05, 116.51 135.23, 139.8 162.46 M1.67 -1.04 C24.63 26.15, 116.15 136.04, 138.96 163.69" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(105.84816487630215 299.3171987738916) rotate(0 69.72676020726252 81.32620022697331)"><path d="M113.07 148.88 C121.57 153.84, 132.56 159.41, 136.96 164.4 M113.59 148.33 C123.6 155.16, 132.46 159.78, 139.17 162.89" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(105.84816487630215 299.3171987738916) rotate(0 69.72676020726252 81.32620022697331)"><path d="M128.87 135.78 C131.61 145.48, 136.83 155.84, 136.96 164.4 M129.38 135.23 C133.43 147.09, 136.32 156.66, 139.17 162.89" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(98.86987304687503 298.71546936035145) rotate(0 71.28559536859393 10.537251508980987)"><path d="M-0.86 -0.79 C23.02 2.66, 119.61 18.21, 143.43 21.86 M0.89 1.41 C24.64 5.04, 118.82 16.64, 142.75 20.15" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(98.86987304687503 298.71546936035145) rotate(0 71.28559536859393 10.537251508980987)"><path d="M112.14 27.44 C123.92 23.31, 131.91 23.77, 140.82 19.27 M113.69 27.12 C124.16 23.33, 136.75 20.67, 142.87 19.67" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(98.86987304687503 298.71546936035145) rotate(0 71.28559536859393 10.537251508980987)"><path d="M114.86 7.1 C125.98 9.35, 133.12 16.22, 140.82 19.27 M116.41 6.78 C125.86 10.87, 137.4 16.1, 142.87 19.67" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g transform="translate(584.4131062825521 913.4657135009766) rotate(0 168.5278778076172 20)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">直接expose端口（不在Dockerfile中做端口映射）</text><text x="0" y="20" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text></g><g stroke-linecap="round"><g transform="translate(105.47768147786465 962.5677398654759) rotate(0 230.43966442421078 -21.81158731948875)"><path d="M-0.61 -0.77 C76.32 -8.46, 384.47 -37.76, 461.49 -45.07 M1.26 1.44 C78.05 -5.6, 383.99 -36.47, 460.58 -43.95" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(105.47768147786465 962.5677398654759) rotate(0 230.43966442421078 -21.81158731948875)"><path d="M432.26 -30.84 C439.99 -34.41, 448.67 -38.57, 459.96 -42.01 M433.71 -30.91 C443.47 -35.22, 451.86 -39.05, 459.62 -44.19" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(105.47768147786465 962.5677398654759) rotate(0 230.43966442421078 -21.81158731948875)"><path d="M430.23 -51.26 C438.74 -48.86, 448.01 -47.04, 459.96 -42.01 M431.68 -51.33 C441.95 -48.8, 451.02 -45.79, 459.62 -44.19" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(107.00905354817715 964.8306190890671) rotate(0 224.40856531692233 40.652134604793616)"><path d="M0.98 0.32 C75.59 14.05, 374.1 68.55, 448.78 81.86 M0.03 -0.56 C74.45 12.88, 373.44 66.58, 448.24 80.19" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(107.00905354817715 964.8306190890671) rotate(0 224.40856531692233 40.652134604793616)"><path d="M419.21 86.56 C431.78 84.79, 440.64 81.76, 446.6 79.53 M419.04 86.25 C426.02 83.38, 434.4 82.88, 448.16 80.56" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(107.00905354817715 964.8306190890671) rotate(0 224.40856531692233 40.652134604793616)"><path d="M422.86 66.37 C433.9 72.58, 441.33 77.51, 446.6 79.53 M422.69 66.05 C428.74 68.76, 436.12 73.81, 448.16 80.56" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g transform="translate(42.16640218098965 10) rotate(0 88.7999267578125 50)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">Title: Dockerfile 最佳实践</text><text x="0" y="20" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text><text x="0" y="40" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">Date: 2025-04-15</text><text x="0" y="60" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text><text x="0" y="80" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge"></text></g><g transform="translate(605.8742879231771 122.31796264648438) rotate(0 40.11198425292969 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#f08c00" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">多阶段build</text></g><g transform="translate(591.2249959309896 165.064208984375) rotate(0 90.63997650146484 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">尽量删除不必要的软件包</text></g><g stroke-linecap="round"><g transform="translate(346.9548543294271 147.34710693359375) rotate(0 123.15666449001526 -5.2913477316498785)"><path d="M-0.87 -0.32 C40.09 -1.94, 203.81 -8.96, 245.1 -10.26 M0.87 -1.54 C42.24 -2.99, 206.26 -7.93, 247.19 -9.32" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(346.9548543294271 147.34710693359375) rotate(0 123.15666449001526 -5.2913477316498785)"><path d="M218.8 -0.15 C224.26 0.3, 234.62 -3.36, 248.67 -9.39 M220.1 2.4 C229.66 -1.91, 240.44 -7.16, 246.38 -8.36" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(346.9548543294271 147.34710693359375) rotate(0 123.15666449001526 -5.2913477316498785)"><path d="M218.15 -20.66 C223.66 -15.23, 234.18 -13.9, 248.67 -9.39 M219.45 -18.11 C229.35 -14.32, 240.39 -11.46, 246.38 -8.36" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g transform="translate(594.8994344075521 320.27001953125) rotate(0 137.31991577148438 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">尽量在Dockerfile之前处理chmod等操作 </text></g><g transform="translate(596.2011922200521 479.43878173828125) rotate(0 18.14397430419922 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#f08c00" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">Cache</text></g><g transform="translate(595.8094685872396 440.1666259765625) rotate(0 38.007972717285156 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#f08c00" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">Image 预热</text></g><g stroke-linecap="round"><g transform="translate(336.0857137044271 462.6568603515625) rotate(0 126.1698705749447 18.730801961920207)"><path d="M0.24 -0.06 C42.37 6.37, 211.16 32.33, 253.43 38.59 M-1.09 -1.13 C40.81 4.9, 210.03 29.99, 252.43 36.57" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(336.0857137044271 462.6568603515625) rotate(0 126.1698705749447 18.730801961920207)"><path d="M222.93 42.33 C233.8 40.91, 242.43 36.53, 254.2 36.49 M222.51 42.34 C230.66 41.78, 238.17 38.89, 252.29 36.27" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(336.0857137044271 462.6568603515625) rotate(0 126.1698705749447 18.730801961920207)"><path d="M226 22.04 C235.78 27.84, 243.33 30.67, 254.2 36.49 M225.58 22.05 C233.01 26.37, 239.78 28.38, 252.29 36.27" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(339.2724812825521 460.814453125) rotate(0 120.33763726877515 -2.291103063523792)"><path d="M1 0.09 C41.06 -0.5, 200.84 -4.1, 240.62 -4.67 M0.06 -0.92 C39.95 -1.28, 199.39 -2.85, 239.74 -3.61" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(339.2724812825521 460.814453125) rotate(0 120.33763726877515 -2.291103063523792)"><path d="M211.83 7.26 C219.26 2.03, 230.45 -1.58, 239.69 -2.73 M212.58 7.64 C217.94 4.71, 223.23 2.31, 239.58 -3.49" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g transform="translate(339.2724812825521 460.814453125) rotate(0 120.33763726877515 -2.291103063523792)"><path d="M211.55 -13.26 C218.88 -11.91, 230.16 -8.95, 239.69 -2.73 M212.3 -12.88 C217.68 -11.3, 223.04 -9.18, 239.58 -3.49" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></g><mask></mask><g transform="translate(583.7102050781251 616.9580281575521) rotate(0 81.71996307373047 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">指定UID（非root用户）</text></g><g transform="translate(580.7210693359376 656.9848836263021) rotate(0 85.45594024658203 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#f08c00" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">指定tag（不要用latest）</text></g><g transform="translate(580.6158040364584 697.9662679036459) rotate(0 98.87997436523438 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">单一职责：容器内单个进程</text></g><g transform="translate(585.18603515625 743.300537109375) rotate(0 36.55194854736328 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#f08c00" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">healthcheck</text></g><g transform="translate(578.7993977864584 823.8600870768229) rotate(0 77.93595123291016 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">定期scan漏洞（trivy）</text></g><g transform="translate(585.7528686523439 779.258280436198) rotate(0 98.87997436523438 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">默认容器是暂时和一次性的</text></g><g transform="translate(585.4898681640624 950.3373819986978) rotate(0 89.38394927978516 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">CMD和Entrypoint结合使用</text></g><g transform="translate(584.0523478190104 982.5858154296875) rotate(0 37.439971923828125 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">ARG和ENV</text></g><g transform="translate(582.7533365885416 1019.205810546875) rotate(0 99.18394470214844 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">不要打印各种logging ---&gt; pip</text></g><g transform="translate(748.7738240559896 1053.5968627929688) rotate(0 40.31993865966797 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">---&gt; apt-get</text></g><g transform="translate(756.1423543294271 1082.4650268554688) rotate(0 48.87994384765625 10)"><text x="0" y="0" font-family="HanziPen SC, HanziPen Remote, Virgil, KaiTi, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="text-before-edge">---&gt; curl/wget</text></g><g stroke-linecap="round" transform="translate(231.12579345703128 128.46673583984395) rotate(0 58.77182006835932 23.124643961588532)"><path d="M38.9 2.12 C48.3 0.56, 63 -0.67, 73.28 0.07 C83.56 0.8, 93.36 3.37, 100.58 6.53 C107.8 9.69, 114.53 14.75, 116.61 19.02 C118.7 23.29, 116.94 28.25, 113.09 32.15 C109.25 36.05, 102.17 39.92, 93.55 42.42 C84.93 44.92, 71.94 47.03, 61.36 47.16 C50.78 47.28, 39.2 45.57, 30.08 43.17 C20.96 40.77, 11.57 36.44, 6.63 32.77 C1.68 29.1, -0.72 25.11, 0.43 21.15 C1.57 17.18, 4.99 12.54, 13.51 8.96 C22.02 5.39, 43.33 1.01, 51.52 -0.3 C59.71 -1.6, 62.68 0.78, 62.65 1.12 M62.27 0.91 C72.18 0.43, 83.45 1.15, 92.11 3.24 C100.76 5.33, 110.03 9.36, 114.2 13.46 C118.38 17.57, 119.57 23.73, 117.16 27.88 C114.74 32.02, 107 35.29, 99.71 38.34 C92.43 41.39, 83.45 44.91, 73.45 46.15 C63.45 47.39, 49.76 47.12, 39.71 45.76 C29.66 44.41, 19.79 41.54, 13.13 38.02 C6.46 34.49, 0.97 28.63, -0.27 24.62 C-1.51 20.6, 0.72 17.64, 5.67 13.92 C10.62 10.2, 20.06 4.56, 29.43 2.31 C38.81 0.06, 56.34 0.48, 61.92 0.42 C67.51 0.36, 62.85 1.54, 62.93 1.95" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g stroke-linecap="round" transform="translate(241.71732584635416 303.91239420572936) rotate(0 52.65543619791663 25.75673421223958)"><path d="M55.71 -0.47 C64.84 -0.43, 76.83 2.76, 84.4 5.66 C91.97 8.55, 97.79 12.55, 101.13 16.9 C104.47 21.26, 106.48 27.36, 104.45 31.8 C102.43 36.24, 95.86 40.53, 89 43.56 C82.13 46.59, 72.46 49.06, 63.27 49.96 C54.07 50.86, 42.66 50.32, 33.84 48.96 C25.02 47.6, 15.97 45.42, 10.37 41.81 C4.76 38.2, 0.99 32.05, 0.21 27.29 C-0.57 22.53, 1.29 17.21, 5.69 13.25 C10.1 9.29, 16.79 5.48, 26.64 3.55 C36.48 1.61, 57.08 1.88, 64.76 1.65 C72.44 1.43, 72.78 1.89, 72.69 2.17 M49.9 1.4 C58.95 1.05, 72.58 3.06, 80.91 5.01 C89.25 6.96, 95.7 9.12, 99.9 13.09 C104.11 17.06, 107.22 24.2, 106.16 28.85 C105.11 33.49, 99.65 37.19, 93.58 40.95 C87.52 44.7, 78.63 50, 69.77 51.39 C60.91 52.79, 49.8 50.51, 40.41 49.32 C31.01 48.13, 20.23 47.54, 13.4 44.25 C6.56 40.96, 0.99 34.08, -0.61 29.58 C-2.2 25.08, -0.12 21.2, 3.84 17.26 C7.8 13.32, 15.18 8.82, 23.16 5.93 C31.13 3.04, 46.81 0.67, 51.69 -0.1 C56.57 -0.87, 52.36 0.62, 52.45 1.32" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g stroke-linecap="round" transform="translate(244.15527343750003 423.52370198567723) rotate(0 44.74578857421875 28.241170247395814)"><path d="M66.27 3.26 C74.02 5.33, 82.05 11.28, 85.93 16.43 C89.8 21.59, 91.23 28.67, 89.5 34.19 C87.78 39.7, 82.08 45.7, 75.58 49.51 C69.08 53.32, 59.29 56.46, 50.52 57.05 C41.75 57.63, 30.62 55.87, 22.96 53.01 C15.29 50.14, 8.3 44.77, 4.53 39.87 C0.75 34.96, -1.34 28.99, 0.29 23.56 C1.92 18.13, 7.77 11.11, 14.29 7.28 C20.81 3.44, 30.25 1.1, 39.39 0.56 C48.52 0.01, 63.49 3.12, 69.08 4.01 C74.67 4.89, 73.31 5.27, 72.9 5.87 M24.51 1.91 C32.09 -0.8, 45.13 -0.37, 53.94 0.95 C62.75 2.27, 71.43 5.79, 77.38 9.84 C83.33 13.88, 88.58 19.8, 89.64 25.23 C90.7 30.65, 87.99 37.42, 83.74 42.4 C79.49 47.37, 72.02 52.89, 64.16 55.08 C56.3 57.28, 45.04 56.58, 36.57 55.56 C28.11 54.54, 19.45 52.69, 13.39 48.97 C7.32 45.25, 1.31 38.66, 0.18 33.25 C-0.95 27.83, 2.29 21.49, 6.59 16.49 C10.88 11.49, 22.63 5.24, 25.94 3.24 C29.25 1.23, 26.34 3.94, 26.44 4.47" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g stroke-linecap="round" transform="translate(10 283.2387695312502) rotate(0 51.532592773437514 30.005747477213518)"><path d="M15 0 M15 0 C41.25 1.52, 67.59 -1.13, 88.06 0 M15 0 C41.39 1.05, 67.39 0.71, 88.06 0 M88.06 0 C98.54 1.31, 104.52 4.36, 103.07 15 M88.06 0 C99.64 1.21, 104.74 5.03, 103.07 15 M103.07 15 C102.58 26.15, 102.94 33.48, 103.07 45.01 M103.07 15 C102.26 23.14, 103.22 31.84, 103.07 45.01 M103.07 45.01 C103.25 53.74, 97.83 58.85, 88.06 60.01 M103.07 45.01 C103.84 52.72, 99.91 59.14, 88.06 60.01 M88.06 60.01 C71.5 58.07, 51.99 60.03, 15 60.01 M88.06 60.01 C69.67 60.67, 51.68 59.3, 15 60.01 M15 60.01 C4.94 60.06, -0.43 53.54, 0 45.01 M15 60.01 C6.23 61.1, -0.71 56.09, 0 45.01 M0 45.01 C0.93 35.74, -1.7 29.47, 0 15 M0 45.01 C-0.18 38.88, -0.55 30.52, 0 15 M0 15 C0.42 4.32, 5.62 1.32, 15 0 M0 15 C-0.43 4.22, 4.31 -1.3, 15 0" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g stroke-linecap="round" transform="translate(42.269185384114564 678.6035563151044) rotate(0 31.16444905598955 24.23004150390625)"><path d="M12.12 0 M12.12 0 C22.94 -0.37, 29.57 0.86, 50.21 0 M12.12 0 C23.46 0.4, 33.74 0.63, 50.21 0 M50.21 0 C57.38 -0.19, 64.31 4.47, 62.33 12.12 M50.21 0 C57.4 -0.07, 62.38 3.55, 62.33 12.12 M62.33 12.12 C61.84 18.55, 61.9 21.91, 62.33 36.35 M62.33 12.12 C61.48 20.86, 61.74 28.28, 62.33 36.35 M62.33 36.35 C64.07 43.74, 59.99 47.03, 50.21 48.46 M62.33 36.35 C61.11 44.9, 57.51 49.17, 50.21 48.46 M50.21 48.46 C35.67 47.2, 22.44 48.22, 12.12 48.46 M50.21 48.46 C37.9 47.92, 23.68 48.38, 12.12 48.46 M12.12 48.46 C3.37 50.45, -1.84 43.52, 0 36.35 M12.12 48.46 C2.24 48.11, -0.15 45.29, 0 36.35 M0 36.35 C1.83 32.62, -0.27 27.08, 0 12.12 M0 36.35 C0.41 30.74, 0.78 22.51, 0 12.12 M0 12.12 C0.73 5.17, 2.38 -0.62, 12.12 0 M0 12.12 C0.57 5.67, 1.79 0.24, 12.12 0" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g><g stroke-linecap="round" transform="translate(28.34564208984375 940.1149902343751) rotate(0 46.504211425781264 37.0513916015625)"><path d="M18.53 0 M18.53 0 C37.4 -1.46, 58.3 0.49, 74.48 0 M18.53 0 C31.54 -0.77, 46.77 -0.56, 74.48 0 M74.48 0 C88.53 -1.43, 91.95 6.6, 93.01 18.53 M74.48 0 C86.05 0.71, 94.52 5.75, 93.01 18.53 M93.01 18.53 C92.99 28.28, 93.78 38.22, 93.01 55.58 M93.01 18.53 C92.98 30.6, 92.97 44.06, 93.01 55.58 M93.01 55.58 C91.17 67.03, 85.27 73.8, 74.48 74.1 M93.01 55.58 C92.86 68.8, 85.2 74.39, 74.48 74.1 M74.48 74.1 C62.08 76.46, 49.5 75.22, 18.53 74.1 M74.48 74.1 C53.1 73.66, 30.37 75.15, 18.53 74.1 M18.53 74.1 C4.52 73.49, 0.5 69.34, 0 55.58 M18.53 74.1 C3.93 74.34, -2.3 70.18, 0 55.58 M0 55.58 C2.01 47.54, 1.39 39.04, 0 18.53 M0 55.58 C-0.75 46.02, 1.16 34.01, 0 18.53 M0 18.53 C-0.53 8.04, 7.6 -0.91, 18.53 0 M0 18.53 C-1.17 8.21, 5.33 -1.02, 18.53 0" stroke="#1e1e1e" stroke-width="1" fill="none"></path></g></svg>
