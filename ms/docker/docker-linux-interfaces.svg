<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1"
  xmlns="http://www.w3.org/2000/svg"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xml:space="preserve"
  x="0px"
  y="0px"
  width="720px"
	height="664px"
  viewBox="-5 -5 360 332">
<defs>
  <style type="text/css">
    text {
      font-family:<PERSON><PERSON><PERSON><PERSON>, Liberation Sans, Arial, sans-serif;
      fill:#333333;
      font-size: 11px;
      text-anchor:middle;
    }
    .mono {
      font-family:Liberation Mono, DejaVu Sans Mono, consolas, monospace;
    }
    .red {
      fill:#C1272D;
      font-size:14px;
    }
    .title {
      font-size:24px;
    }
    .bubbles {
      fill:#F2F2F2;
      stroke:#333333;
    }
    .arrows {
      fill:none;
      stroke:#4D4D4D;
    }
    .arrow_heads {
      fill:#4D4D4D;
      stroke:none;
    }
  </style>
</defs>
  <g>
  <g>
  	<path class="bubbles" d="M345,51.3c0,2.1-2.4,3.7-5.4,3.7H10.4c-3,0-5.4-1.7-5.4-3.7V8.8C5,6.7,7.4,5,10.4,5h329.2
  		c3,0,5.4,1.7,5.4,3.8V51.3z"/>
  	<path class="bubbles" d="M345,311.6c0,2.9-2.7,5.3-6,5.3H11c-3.3,0-6-2.4-6-5.3V182.1c0-2.9,2.7-5.3,6-5.3h328
  		c3.3,0,6,2.4,6,5.3V311.6z"/>
  	<path class="bubbles" d="M193.4,129c0,3.3-2.3,6-5.3,6h-59.5c-2.9,0-5.3-2.7-5.3-6v-28c0-3.3,2.4-6,5.3-6h59.5
  		c2.9,0,5.3,2.7,5.3,6V129z"/>
  	<path class="bubbles" d="M269.2,129c0,3.3-2.4,6-5.3,6h-59.5c-2.9,0-5.3-2.7-5.3-6v-28c0-3.3,2.4-6,5.3-6h59.5
  		c2.9,0,5.3,2.7,5.3,6V129z"/>
  	<path class="bubbles" d="M345,129c0,3.3-2.4,6-5.3,6h-59.5c-2.9,0-5.3-2.7-5.3-6v-28c0-3.3,2.3-6,5.3-6h59.5
  		c2.9,0,5.3,2.7,5.3,6V129z"/>
  	<path class="bubbles" d="M113.2,65.6c0,2.1-3.4,3.8-7.5,3.8h-85c-4.1,0-7.5-1.7-7.5-3.8V47.7
  		c0-2.1,3.4-3.8,7.5-3.8h85c4.1,0,7.5,1.7,7.5,3.8V65.6z"/>
  	<path class="bubbles" d="M293.7,44.2"/>
  	<path class="bubbles" d="M298.7,414.5"/>
  </g>
  <g>
  	<g>
  		<line class="arrows" x1="64.5" y1="69.4" x2="64.5" y2="169.8"/>
  		<polygon class="arrows_heads" points="60.8,167 64.5,168.6 68.2,167 64.5,175.8"/>
  	</g>
  	<g>
  		<line class="arrows" x1="157.5" y1="55" x2="157.5" y2="88"/>
  		<polygon class="arrows_heads" points="153.8,85.2 157.5,86.8 161.2,85.2 157.5,94"/>
  	</g>
  	<g>
  		<line class="arrows" x1="157.8" y1="135" x2="157.8" y2="169.8"/>
  		<polygon class="arrows_heads" points="154.1,167 157.8,168.6 161.5,167 157.8,175.8"/>
  	</g>
  	<g>
  		<line class="arrows" x1="233.5" y1="55" x2="233.5" y2="88"/>
  		<polygon class="arrows_heads" points="229.8,85.2 233.5,86.8 237.2,85.2 233.5,94"/>
  	</g>
  	<g>
  		<line class="arrows" x1="233.5" y1="135" x2="233.5" y2="169.8"/>
  		<polygon class="arrows_heads" points="229.8,167 233.5,168.6 237.2,167 233.5,175.8"/>
  	</g>
  	<g>
  		<line class="arrows" x1="310.4" y1="55" x2="310.4" y2="88"/>
  		<polygon class="arrows_heads" points="306.7,85.2 310.4,86.8 314.1,85.2 310.4,94"/>
  	</g>
  	<g>
  		<line class="arrows" x1="310" y1="135" x2="310" y2="169.8"/>
  		<polygon class="arrows_heads" points="306.3,167 310,168.6 313.7,167 310,175.8"/>
  	</g>
  </g>
  <g>
  	<g id="mono">
  		<text transform="matrix(1 0 0 1 65 60)" class="mono">libcontainer</text>
  		<text transform="matrix(1 0 0 1 160 118)" class="mono">libvirt</text>
  		<text transform="matrix(1 0 0 1 312 112)" class="mono">systemd-</text>
  		<text transform="matrix(1 0 0 1 312 123)" class="mono">nspawn</text>
  	</g>
    <text transform="matrix(1 0 0 1 235 118)">LXC</text>
  	<g id="red">
  		<text transform="matrix(1 0 0 1 60 235)" class="red">cgroups</text>
  		<text transform="matrix(1 0 0 1 160 240)" class="red">namespaces</text>
  		<text transform="matrix(1 0 0 1 150 265)" class="red">SELinux</text>
  		<text transform="matrix(1 0 0 1 220 295)" class="red">AppArmor</text>
  		<text transform="matrix(1 0 0 1 250 270)" class="red">Netfilter</text>
  		<text transform="matrix(1 0 0 1 280 235)" class="red">Netlink</text>
  		<text transform="matrix(1 0 0 1 90 290)" class="red">capabilities</text>
  	</g>
  	<g id="title">
  		<text transform="matrix(1 0 0 1 170 35)" class="title">Docker</text>
  		<text transform="matrix(1 0 0 1 170 208)" class="title">Linux kernel</text>
  	</g>
  </g>
</g>
</svg>
