{"$schema": "https://raw.githubusercontent.com/streetsidesoftware/cspell/main/cspell.schema.json", "version": "0.2", "language": "en,en-US", "allowCompoundWords": true, "words": ["acmr", "antd", "antdv", "astro", "brotli", "clsx", "defu", "demi", "echarts", "ependencies", "esno", "etag", "execa", "iconify", "iconoir", "intlify", "lockb", "lucide", "minh", "minw", "mkdist", "<PERSON><PERSON>", "nocheck", "noopener", "<PERSON><PERSON><PERSON><PERSON>", "nprogress", "nuxt", "pinia", "prefixs", "publint", "qrcode", "shadcn", "sonner", "sortablejs", "styl", "taze", "ui-kit", "uicons", "unplugin", "unref", "vben", "vbenjs", "vite", "vite<PERSON><PERSON>", "vitepress", "vnode", "vueuse", "yxxx"], "ignorePaths": ["**/node_modules/**", "**/dist/**", "**/*-dist/**", "**/icons/**", "pnpm-lock.yaml", "**/*.log", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**"]}