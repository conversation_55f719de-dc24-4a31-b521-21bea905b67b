<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 802.5390625 614" style="max-width: 802.5390625px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-27"><style>#mermaid-svg-27{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#mermaid-svg-27 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#mermaid-svg-27 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#mermaid-svg-27 .error-icon{fill:#552222;}#mermaid-svg-27 .error-text{fill:#552222;stroke:#552222;}#mermaid-svg-27 .edge-thickness-normal{stroke-width:1px;}#mermaid-svg-27 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-27 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-27 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-svg-27 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-27 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-27 .marker{fill:#333333;stroke:#333333;}#mermaid-svg-27 .marker.cross{stroke:#333333;}#mermaid-svg-27 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-27 p{margin:0;}#mermaid-svg-27 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-svg-27 .cluster-label text{fill:#333;}#mermaid-svg-27 .cluster-label span{color:#333;}#mermaid-svg-27 .cluster-label span p{background-color:transparent;}#mermaid-svg-27 .label text,#mermaid-svg-27 span{fill:#333;color:#333;}#mermaid-svg-27 .node rect,#mermaid-svg-27 .node circle,#mermaid-svg-27 .node ellipse,#mermaid-svg-27 .node polygon,#mermaid-svg-27 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-svg-27 .rough-node .label text,#mermaid-svg-27 .node .label text,#mermaid-svg-27 .image-shape .label,#mermaid-svg-27 .icon-shape .label{text-anchor:middle;}#mermaid-svg-27 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-svg-27 .rough-node .label,#mermaid-svg-27 .node .label,#mermaid-svg-27 .image-shape .label,#mermaid-svg-27 .icon-shape .label{text-align:center;}#mermaid-svg-27 .node.clickable{cursor:pointer;}#mermaid-svg-27 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-svg-27 .arrowheadPath{fill:#333333;}#mermaid-svg-27 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-svg-27 .flowchart-link{stroke:#333333;fill:none;}#mermaid-svg-27 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-27 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-svg-27 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-27 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-svg-27 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-svg-27 .cluster text{fill:#333;}#mermaid-svg-27 .cluster span{color:#333;}#mermaid-svg-27 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-27 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-svg-27 rect.text{fill:none;stroke-width:0;}#mermaid-svg-27 .icon-shape,#mermaid-svg-27 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-27 .icon-shape p,#mermaid-svg-27 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-svg-27 .icon-shape rect,#mermaid-svg-27 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-27 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-27_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-27_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-27_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-27_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-27_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-27_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M524.93,55.469L546.906,62.724C568.882,69.979,612.835,84.49,634.811,103.911C656.787,123.333,656.787,147.667,656.787,159.833L656.787,172"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_C_0" d="M400.93,58.138L382.681,64.948C364.432,71.759,327.934,85.379,309.759,97.773C291.584,110.167,291.733,121.334,291.808,126.917L291.882,132.5"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M258.865,237.43L247.4,249.025C235.934,260.62,213.002,283.81,201.611,300.988C190.219,318.167,190.368,329.334,190.443,334.917L190.517,340.5"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E_0" d="M325.006,237.43L336.305,249.025C347.604,260.62,370.202,283.81,381.502,307.572C392.801,331.333,392.801,355.667,392.801,367.833L392.801,380"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_0" d="M158.974,446.904L148.687,458.253C138.401,469.602,117.827,492.301,107.541,509.151C97.254,526,97.254,537,97.254,542.5L97.254,548"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_G_0" d="M222.167,446.904L232.287,458.253C242.407,469.602,262.647,492.301,272.767,509.151C282.887,526,282.887,537,282.887,542.5L282.887,548"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_H_0" d="M636.277,230L626.528,242.833C616.78,255.667,597.282,281.333,587.534,306.333C577.785,331.333,577.785,355.667,577.785,367.833L577.785,380"></path><path marker-end="url(#mermaid-svg-27_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_I_0" d="M677.297,230L687.046,242.833C696.795,255.667,716.292,281.333,726.04,306.333C735.789,331.333,735.789,355.667,735.789,367.833L735.789,380"></path></g><g class="edgeLabels"><g transform="translate(656.787109375, 99)" class="edgeLabel"><g transform="translate(-48, -12)" class="label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>任意两点距离</p></span></div></foreignObject></g></g><g transform="translate(291.435546875, 99)" class="edgeLabel"><g transform="translate(-40, -12)" class="label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>单点到单点</p></span></div></foreignObject></g></g><g transform="translate(190.0703125, 307)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(392.80078125, 307)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(97.25390625, 515)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(282.88671875, 515)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(577.78515625, 307)" class="edgeLabel"><g transform="translate(-16, -12)" class="label"><foreignObject height="24" width="32"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>小图</p></span></div></foreignObject></g></g><g transform="translate(735.7890625, 307)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>大稀疏图</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(462.9296875, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>需求类型</p></span></div></foreignObject></g></g><g transform="translate(656.787109375, 203)" id="flowchart-B-1" class="node default"><rect height="54" width="164.3984375" y="-27" x="-82.19921875" style="" class="basic label-container"></rect><g transform="translate(-52.19921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="104.3984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Floyd/Johnson</p></span></div></foreignObject></g></g><g transform="translate(291.435546875, 203)" id="flowchart-C-3" class="node default"><polygon transform="translate(-67,67)" class="label-container" points="67,0 134,-67 67,-134 0,-67"></polygon><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>有负权边？</p></span></div></foreignObject></g></g><g transform="translate(190.0703125, 411)" id="flowchart-D-5" class="node default"><polygon transform="translate(-67,67)" class="label-container" points="67,0 134,-67 67,-134 0,-67"></polygon><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>有负权环？</p></span></div></foreignObject></g></g><g transform="translate(392.80078125, 411)" id="flowchart-E-7" class="node default"><rect height="54" width="171.4609375" y="-27" x="-85.73046875" style="" class="basic label-container"></rect><g transform="translate(-55.73046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.4609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>双向Dijkstra/A*</p></span></div></foreignObject></g></g><g transform="translate(97.25390625, 579)" id="flowchart-F-9" class="node default"><rect height="54" width="178.5078125" y="-27" x="-89.25390625" style="" class="basic label-container"></rect><g transform="translate(-59.25390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="118.5078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Floyd检测并拒绝</p></span></div></foreignObject></g></g><g transform="translate(282.88671875, 579)" id="flowchart-G-11" class="node default"><rect height="54" width="92.7578125" y="-27" x="-46.37890625" style="" class="basic label-container"></rect><g transform="translate(-16.37890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="32.7578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SPFA</p></span></div></foreignObject></g></g><g transform="translate(577.78515625, 411)" id="flowchart-H-13" class="node default"><rect height="54" width="98.5078125" y="-27" x="-49.25390625" style="" class="basic label-container"></rect><g transform="translate(-19.25390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="38.5078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Floyd</p></span></div></foreignObject></g></g><g transform="translate(735.7890625, 411)" id="flowchart-I-15" class="node default"><rect height="54" width="117.5" y="-27" x="-58.75" style="" class="basic label-container"></rect><g transform="translate(-28.75, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="57.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Johnson</p></span></div></foreignObject></g></g></g></g></g></svg>