<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 831 406" style="max-width: 831px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-106"><style>#mermaid-svg-106{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#mermaid-svg-106 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#mermaid-svg-106 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#mermaid-svg-106 .error-icon{fill:#552222;}#mermaid-svg-106 .error-text{fill:#552222;stroke:#552222;}#mermaid-svg-106 .edge-thickness-normal{stroke-width:1px;}#mermaid-svg-106 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-106 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-106 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-svg-106 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-106 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-106 .marker{fill:#333333;stroke:#333333;}#mermaid-svg-106 .marker.cross{stroke:#333333;}#mermaid-svg-106 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-106 p{margin:0;}#mermaid-svg-106 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-svg-106 .cluster-label text{fill:#333;}#mermaid-svg-106 .cluster-label span{color:#333;}#mermaid-svg-106 .cluster-label span p{background-color:transparent;}#mermaid-svg-106 .label text,#mermaid-svg-106 span{fill:#333;color:#333;}#mermaid-svg-106 .node rect,#mermaid-svg-106 .node circle,#mermaid-svg-106 .node ellipse,#mermaid-svg-106 .node polygon,#mermaid-svg-106 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-svg-106 .rough-node .label text,#mermaid-svg-106 .node .label text,#mermaid-svg-106 .image-shape .label,#mermaid-svg-106 .icon-shape .label{text-anchor:middle;}#mermaid-svg-106 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-svg-106 .rough-node .label,#mermaid-svg-106 .node .label,#mermaid-svg-106 .image-shape .label,#mermaid-svg-106 .icon-shape .label{text-align:center;}#mermaid-svg-106 .node.clickable{cursor:pointer;}#mermaid-svg-106 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-svg-106 .arrowheadPath{fill:#333333;}#mermaid-svg-106 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-svg-106 .flowchart-link{stroke:#333333;fill:none;}#mermaid-svg-106 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-106 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-svg-106 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-106 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-svg-106 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-svg-106 .cluster text{fill:#333;}#mermaid-svg-106 .cluster span{color:#333;}#mermaid-svg-106 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-106 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-svg-106 rect.text{fill:none;stroke-width:0;}#mermaid-svg-106 .icon-shape,#mermaid-svg-106 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-106 .icon-shape p,#mermaid-svg-106 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-svg-106 .icon-shape rect,#mermaid-svg-106 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-106 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-106_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-106_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-106_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-106_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-106_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-106_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M346.896,62L337.837,66.167C328.778,70.333,310.661,78.667,301.602,86.333C292.543,94,292.543,101,292.543,104.5L292.543,108"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_C_0" d="M459.18,62L467.449,66.167C475.718,70.333,492.255,78.667,500.524,86.333C508.793,94,508.793,101,508.793,104.5L508.793,108"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D1_0" d="M230.543,154.639L206.518,160.699C182.492,166.759,134.441,178.88,110.416,188.44C86.391,198,86.391,205,86.391,208.5L86.391,212"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D2_0" d="M354.543,154.639L378.568,160.699C402.594,166.759,450.645,178.88,474.67,188.44C498.695,198,498.695,205,498.695,208.5L498.695,212"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E1_0" d="M446.793,153.925L421.124,160.104C395.456,166.283,344.118,178.642,318.45,188.321C292.781,198,292.781,205,292.781,208.5L292.781,212"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_E2_0" d="M570.793,153.925L596.462,160.104C622.13,166.283,673.467,178.642,699.136,188.321C724.805,198,724.805,205,724.805,208.5L724.805,212"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_F1_0" d="M86.391,294L86.391,298.167C86.391,302.333,86.391,310.667,94.064,318.7C101.738,326.733,117.085,334.467,124.758,338.333L132.432,342.2"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_F1_0" d="M292.781,294L292.781,298.167C292.781,302.333,292.781,310.667,285.108,318.7C277.434,326.733,262.087,334.467,254.414,338.333L246.74,342.2"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_F2_0" d="M498.695,294L498.695,298.167C498.695,302.333,498.695,310.667,507.149,318.721C515.602,326.776,532.508,334.552,540.961,338.44L549.415,342.329"></path><path marker-end="url(#mermaid-svg-106_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_F2_0" d="M724.805,294L724.805,298.167C724.805,302.333,724.805,310.667,716.351,318.721C707.898,326.776,690.992,334.552,682.539,338.44L674.085,342.329"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(405.59765625, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="161.3984375" y="-27" x="-80.69921875" style="" class="basic label-container"></rect><g transform="translate(-50.69921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.3984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TOTP核心机制</p></span></div></foreignObject></g></g><g transform="translate(292.54296875, 139)" id="flowchart-B-1" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>时间因子</p></span></div></foreignObject></g></g><g transform="translate(508.79296875, 139)" id="flowchart-C-3" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>密钥因子</p></span></div></foreignObject></g></g><g transform="translate(86.390625, 255)" id="flowchart-D1-5" class="node default"><rect height="78" width="156.78125" y="-39" x="-78.390625" style="" class="basic label-container"></rect><g transform="translate(-48.390625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>标准时间片<br/>（30秒周期）</p></span></div></foreignObject></g></g><g transform="translate(498.6953125, 255)" id="flowchart-D2-7" class="node default"><rect height="78" width="155.828125" y="-39" x="-77.9140625" style="" class="basic label-container"></rect><g transform="translate(-47.9140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="95.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>扩展时间片<br/>（90-180秒）</p></span></div></foreignObject></g></g><g transform="translate(292.78125, 255)" id="flowchart-E1-9" class="node default"><rect height="78" width="156" y="-39" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>静态密钥<br/>（用户绑定）</p></span></div></foreignObject></g></g><g transform="translate(724.8046875, 255)" id="flowchart-E2-11" class="node default"><rect height="78" width="196.390625" y="-39" x="-98.1953125" style="" class="basic label-container"></rect><g transform="translate(-68.1953125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>动态密钥<br/>（设备+位置绑定）</p></span></div></foreignObject></g></g><g transform="translate(189.5859375, 371)" id="flowchart-F1-14" class="node default"><rect height="54" width="156" y="-27" x="-78" style="fill:#cff !important;stroke:#333 !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>动态密码认证</p></span></div></foreignObject></g></g><g transform="translate(611.75, 371)" id="flowchart-F2-17" class="node default"><rect height="54" width="124" y="-27" x="-62" style="fill:#fcf !important;stroke:#333 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>离线支付</p></span></div></foreignObject></g></g></g></g></g></svg>