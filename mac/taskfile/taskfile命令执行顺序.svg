<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 578.958984375 1981.25" style="max-width: 578.958984375px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="shadow-svg-1750403629179-mermaid">
  <style>
    @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css');
  </style><style>#shadow-svg-1750403629179-mermaid{font-family:Comic Sans MS;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#shadow-svg-1750403629179-mermaid .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#shadow-svg-1750403629179-mermaid .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#shadow-svg-1750403629179-mermaid .error-icon{fill:#552222;}#shadow-svg-1750403629179-mermaid .error-text{fill:#552222;stroke:#552222;}#shadow-svg-1750403629179-mermaid .edge-thickness-normal{stroke-width:1px;}#shadow-svg-1750403629179-mermaid .edge-thickness-thick{stroke-width:3.5px;}#shadow-svg-1750403629179-mermaid .edge-pattern-solid{stroke-dasharray:0;}#shadow-svg-1750403629179-mermaid .edge-thickness-invisible{stroke-width:0;fill:none;}#shadow-svg-1750403629179-mermaid .edge-pattern-dashed{stroke-dasharray:3;}#shadow-svg-1750403629179-mermaid .edge-pattern-dotted{stroke-dasharray:2;}#shadow-svg-1750403629179-mermaid .marker{fill:#333333;stroke:#333333;}#shadow-svg-1750403629179-mermaid .marker.cross{stroke:#333333;}#shadow-svg-1750403629179-mermaid svg{font-family:Comic Sans MS;font-size:14px;}#shadow-svg-1750403629179-mermaid p{margin:0;}#shadow-svg-1750403629179-mermaid .label{font-family:Comic Sans MS;color:#333;}#shadow-svg-1750403629179-mermaid .cluster-label text{fill:#333;}#shadow-svg-1750403629179-mermaid .cluster-label span{color:#333;}#shadow-svg-1750403629179-mermaid .cluster-label span p{background-color:transparent;}#shadow-svg-1750403629179-mermaid .label text,#shadow-svg-1750403629179-mermaid span{fill:#333;color:#333;}#shadow-svg-1750403629179-mermaid .node rect,#shadow-svg-1750403629179-mermaid .node circle,#shadow-svg-1750403629179-mermaid .node ellipse,#shadow-svg-1750403629179-mermaid .node polygon,#shadow-svg-1750403629179-mermaid .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#shadow-svg-1750403629179-mermaid .rough-node .label text,#shadow-svg-1750403629179-mermaid .node .label text,#shadow-svg-1750403629179-mermaid .image-shape .label,#shadow-svg-1750403629179-mermaid .icon-shape .label{text-anchor:middle;}#shadow-svg-1750403629179-mermaid .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#shadow-svg-1750403629179-mermaid .rough-node .label,#shadow-svg-1750403629179-mermaid .node .label,#shadow-svg-1750403629179-mermaid .image-shape .label,#shadow-svg-1750403629179-mermaid .icon-shape .label{text-align:center;}#shadow-svg-1750403629179-mermaid .node.clickable{cursor:pointer;}#shadow-svg-1750403629179-mermaid .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#shadow-svg-1750403629179-mermaid .arrowheadPath{fill:#333333;}#shadow-svg-1750403629179-mermaid .edgePath .path{stroke:#333333;stroke-width:2.0px;}#shadow-svg-1750403629179-mermaid .flowchart-link{stroke:#333333;fill:none;}#shadow-svg-1750403629179-mermaid .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#shadow-svg-1750403629179-mermaid .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#shadow-svg-1750403629179-mermaid .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#shadow-svg-1750403629179-mermaid .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#shadow-svg-1750403629179-mermaid .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#shadow-svg-1750403629179-mermaid .cluster text{fill:#333;}#shadow-svg-1750403629179-mermaid .cluster span{color:#333;}#shadow-svg-1750403629179-mermaid div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:Comic Sans MS;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#shadow-svg-1750403629179-mermaid .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#shadow-svg-1750403629179-mermaid rect.text{fill:none;stroke-width:0;}#shadow-svg-1750403629179-mermaid .icon-shape,#shadow-svg-1750403629179-mermaid .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#shadow-svg-1750403629179-mermaid .icon-shape p,#shadow-svg-1750403629179-mermaid .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#shadow-svg-1750403629179-mermaid .icon-shape rect,#shadow-svg-1750403629179-mermaid .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#shadow-svg-1750403629179-mermaid :root{--mermaid-font-family:Comic Sans MS;}#shadow-svg-1750403629179-mermaid .precondition&gt;*{fill:#D6EAF8!important;stroke:#1F618D!important;color:#1F618D!important;}#shadow-svg-1750403629179-mermaid .precondition span{fill:#D6EAF8!important;stroke:#1F618D!important;color:#1F618D!important;}#shadow-svg-1750403629179-mermaid .precondition tspan{fill:#1F618D!important;}#shadow-svg-1750403629179-mermaid .deps&gt;*{fill:#D5F5E3!important;stroke:#27AE60!important;color:#154F30!important;}#shadow-svg-1750403629179-mermaid .deps span{fill:#D5F5E3!important;stroke:#27AE60!important;color:#154F30!important;}#shadow-svg-1750403629179-mermaid .deps tspan{fill:#154F30!important;}#shadow-svg-1750403629179-mermaid .status&gt;*{fill:#FCF3CF!important;stroke:#F39C12!important;color:#7D6608!important;}#shadow-svg-1750403629179-mermaid .status span{fill:#FCF3CF!important;stroke:#F39C12!important;color:#7D6608!important;}#shadow-svg-1750403629179-mermaid .status tspan{fill:#7D6608!important;}#shadow-svg-1750403629179-mermaid .cmds&gt;*{fill:#E8DAEF!important;stroke:#9B59B6!important;color:#5B2C6F!important;}#shadow-svg-1750403629179-mermaid .cmds span{fill:#E8DAEF!important;stroke:#9B59B6!important;color:#5B2C6F!important;}#shadow-svg-1750403629179-mermaid .cmds tspan{fill:#5B2C6F!important;}#shadow-svg-1750403629179-mermaid .defer&gt;*{fill:#FADBD8!important;stroke:#E74C3C!important;color:#943126!important;}#shadow-svg-1750403629179-mermaid .defer span{fill:#FADBD8!important;stroke:#E74C3C!important;color:#943126!important;}#shadow-svg-1750403629179-mermaid .defer tspan{fill:#943126!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750403629179-mermaid_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750403629179-mermaid_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750403629179-mermaid_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="shadow-svg-1750403629179-mermaid_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="shadow-svg-1750403629179-mermaid_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="收尾阶段" class="cluster defer"><rect height="202" width="330.53515625" y="1670.25" x="200.6328125" style="fill:#FADBD8 !important;stroke:#E74C3C !important"></rect><g transform="translate(337.900390625, 1670.25)" class="cluster-label"><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#943126 !important" class="nodeLabel"><p>收尾阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="命令执行阶段" class="cluster cmds"><rect height="101" width="195.296875" y="1519.25" x="180.927734375" style="fill:#E8DAEF !important;stroke:#9B59B6 !important"></rect><g transform="translate(236.576171875, 1519.25)" class="cluster-label"><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#5B2C6F !important" class="nodeLabel"><p>命令执行阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="状态检查阶段" class="cluster status"><rect height="404.7890625" width="404.79296875" y="1064.4609375" x="166.166015625" style="fill:#FCF3CF !important;stroke:#F39C12 !important"></rect><g transform="translate(326.5625, 1064.4609375)" class="cluster-label"><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#7D6608 !important" class="nodeLabel"><p>状态检查阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="依赖执行阶段" class="cluster deps"><rect height="101" width="280.5625" y="913.4609375" x="230.619140625" style="fill:#D5F5E3 !important;stroke:#27AE60 !important"></rect><g transform="translate(328.900390625, 913.4609375)" class="cluster-label"><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#154F30 !important" class="nodeLabel"><p>依赖执行阶段</p></span></div></foreignObject></g></g><g data-look="classic" id="前置检查阶段" class="cluster precondition"><rect height="653.4609375" width="486.9765625" y="210" x="8" style="fill:#D6EAF8 !important;stroke:#1F618D !important"></rect><g transform="translate(209.48828125, 210)" class="cluster-label"><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#1F618D !important" class="nodeLabel"><p>前置检查阶段</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M280.9,59L280.9,63.167C280.9,67.333,280.9,75.667,280.9,83.333C280.9,91,280.9,98,280.9,101.5L280.9,105"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M280.9,160L280.9,164.167C280.9,168.333,280.9,176.667,280.9,185C280.9,193.333,280.9,201.667,280.9,209.333C280.9,217,280.9,224,280.9,227.5L280.9,231"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_0" d="M280.9,286L280.9,290.167C280.9,294.333,280.9,302.667,280.9,310.333C280.9,318,280.9,325,280.9,328.5L280.9,332"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M280.9,387L280.9,391.167C280.9,395.333,280.9,403.667,280.9,411.333C280.9,419,280.9,426,280.9,429.5L280.9,433"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_0" d="M280.9,488L280.9,492.167C280.9,496.333,280.9,504.667,280.971,512.417C281.041,520.167,281.181,527.334,281.252,530.917L281.322,534.501"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_0" d="M318.8,679.562L327.483,691.628C336.167,703.695,353.533,727.828,362.217,745.144C370.9,762.461,370.9,772.961,370.9,778.211L370.9,783.461"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_H_0" d="M231.84,667.401L214.046,681.494C196.252,695.588,160.664,723.774,142.87,743.118C125.076,762.461,125.076,772.961,125.076,778.211L125.076,783.461"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_I_0" d="M370.9,838.461L370.9,842.628C370.9,846.794,370.9,855.128,370.9,863.461C370.9,871.794,370.9,880.128,370.9,888.461C370.9,896.794,370.9,905.128,370.9,912.794C370.9,920.461,370.9,927.461,370.9,930.961L370.9,934.461"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_0" d="M370.9,989.461L370.9,993.628C370.9,997.794,370.9,1006.128,370.9,1014.461C370.9,1022.794,370.9,1031.128,370.9,1039.461C370.9,1047.794,370.9,1056.128,370.9,1063.794C370.9,1071.461,370.9,1078.461,370.9,1081.961L370.9,1085.461"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_0" d="M370.9,1140.461L370.9,1144.628C370.9,1148.794,370.9,1157.128,370.971,1164.878C371.041,1172.628,371.181,1179.795,371.252,1183.378L371.322,1186.962"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_0" d="M402.805,1291.345L412.875,1302.413C422.945,1313.48,443.085,1335.615,453.155,1351.933C463.225,1368.25,463.225,1378.75,463.225,1384L463.225,1389.25"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_M_0" d="M339.996,1291.345L329.759,1302.413C319.523,1313.48,299.049,1335.615,288.813,1351.933C278.576,1368.25,278.576,1378.75,278.576,1384L278.576,1389.25"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_N_0" d="M278.576,1444.25L278.576,1448.417C278.576,1452.583,278.576,1460.917,278.576,1469.25C278.576,1477.583,278.576,1485.917,278.576,1494.25C278.576,1502.583,278.576,1510.917,278.576,1518.583C278.576,1526.25,278.576,1533.25,278.576,1536.75L278.576,1540.25"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_0" d="M278.576,1595.25L278.576,1599.417C278.576,1603.583,278.576,1611.917,278.576,1620.25C278.576,1628.583,278.576,1636.917,278.576,1645.25C278.576,1653.583,278.576,1661.917,285.609,1669.93C292.641,1677.943,306.707,1685.637,313.739,1689.484L320.772,1693.33"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_O_0" d="M463.225,1444.25L463.225,1448.417C463.225,1452.583,463.225,1460.917,463.225,1469.25C463.225,1477.583,463.225,1485.917,463.225,1494.25C463.225,1502.583,463.225,1510.917,463.225,1523.5C463.225,1536.083,463.225,1552.917,463.225,1569.75C463.225,1586.583,463.225,1603.417,463.225,1616C463.225,1628.583,463.225,1636.917,463.225,1645.25C463.225,1653.583,463.225,1661.917,456.192,1669.93C449.159,1677.943,435.094,1685.637,428.061,1689.484L421.029,1693.33"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_0" d="M370.9,1746.25L370.9,1750.417C370.9,1754.583,370.9,1762.917,370.9,1770.583C370.9,1778.25,370.9,1785.25,370.9,1788.75L370.9,1792.25"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_Z_0" d="M125.076,838.461L125.076,842.628C125.076,846.794,125.076,855.128,125.076,863.461C125.076,871.794,125.076,880.128,125.076,888.461C125.076,896.794,125.076,905.128,125.076,917.711C125.076,930.294,125.076,947.128,125.076,963.961C125.076,980.794,125.076,997.628,125.076,1010.211C125.076,1022.794,125.076,1031.128,125.076,1039.461C125.076,1047.794,125.076,1056.128,125.076,1068.711C125.076,1081.294,125.076,1098.128,125.076,1114.961C125.076,1131.794,125.076,1148.628,125.076,1172.193C125.076,1195.759,125.076,1226.057,125.076,1258.105C125.076,1290.154,125.076,1323.952,125.076,1351.018C125.076,1378.083,125.076,1398.417,125.076,1417C125.076,1435.583,125.076,1452.417,125.076,1465C125.076,1477.583,125.076,1485.917,125.076,1494.25C125.076,1502.583,125.076,1510.917,125.076,1523.5C125.076,1536.083,125.076,1552.917,125.076,1569.75C125.076,1586.583,125.076,1603.417,125.076,1616C125.076,1628.583,125.076,1636.917,125.076,1645.25C125.076,1653.583,125.076,1661.917,125.076,1674.5C125.076,1687.083,125.076,1703.917,125.076,1720.75C125.076,1737.583,125.076,1754.417,125.076,1771.25C125.076,1788.083,125.076,1804.917,125.076,1821.75C125.076,1838.583,125.076,1855.417,125.076,1868C125.076,1880.583,125.076,1888.917,140.746,1898.162C156.416,1907.407,187.756,1917.563,203.425,1922.642L219.095,1927.72"></path><path marker-end="url(#shadow-svg-1750403629179-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Z_0" d="M370.9,1847.25L370.9,1851.417C370.9,1855.583,370.9,1863.917,370.9,1872.25C370.9,1880.583,370.9,1888.917,364.056,1896.924C357.212,1904.931,343.523,1912.612,336.679,1916.452L329.834,1920.293"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(370.900390625, 751.9609375)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(125.076171875, 751.9609375)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(463.224609375, 1357.75)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(278.576171875, 1357.75)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(280.900390625, 33.5)" id="flowchart-A-0" class="node default"><rect height="51" width="116" y="-25.5" x="-58" style="" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开始任务</p></span></div></foreignObject></g></g><g transform="translate(280.900390625, 134.5)" id="flowchart-B-1" class="node default"><rect height="51" width="130" y="-25.5" x="-65" style="" class="basic label-container"></rect><g transform="translate(-35, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="70"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>全局初始化</p></span></div></foreignObject></g></g><g transform="translate(280.900390625, 260.5)" id="flowchart-C-3" class="node default"><rect height="51" width="130" y="-25.5" x="-65" style="" class="basic label-container"></rect><g transform="translate(-35, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="70"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>任务初始化</p></span></div></foreignObject></g></g><g transform="translate(280.900390625, 361.5)" id="flowchart-D-5" class="node default"><rect height="51" width="178.4609375" y="-25.5" x="-89.23046875" style="" class="basic label-container"></rect><g transform="translate(-59.23046875, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="118.4609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>检查 requires 变量</p></span></div></foreignObject></g></g><g transform="translate(280.900390625, 462.5)" id="flowchart-E-7" class="node default"><rect height="51" width="212.3046875" y="-25.5" x="-106.15234375" style="" class="basic label-container"></rect><g transform="translate(-76.15234375, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="152.3046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>执行 preconditions 检查</p></span></div></foreignObject></g></g><g transform="translate(280.900390625, 627.23046875)" id="flowchart-F-9" class="node default"><polygon transform="translate(-89.23046875,89.23046875)" class="label-container" points="89.23046875,0 178.4609375,-89.23046875 89.23046875,-178.4609375 0,-89.23046875"></polygon><g transform="translate(-63.73046875, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="127.4609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>preconditions 通过?</p></span></div></foreignObject></g></g><g transform="translate(370.900390625, 812.9609375)" id="flowchart-G-11" class="node default"><rect height="51" width="144" y="-25.5" x="-72" style="" class="basic label-container"></rect><g transform="translate(-42, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>进入依赖执行</p></span></div></foreignObject></g></g><g transform="translate(125.076171875, 812.9609375)" id="flowchart-H-13" class="node default"><rect height="51" width="116" y="-25.5" x="-58" style="" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>报错退出</p></span></div></foreignObject></g></g><g transform="translate(370.900390625, 963.9609375)" id="flowchart-I-15" class="node default"><rect height="51" width="210.5625" y="-25.5" x="-105.28125" style="" class="basic label-container"></rect><g transform="translate(-75.28125, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="150.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>并行执行 deps 依赖任务</p></span></div></foreignObject></g></g><g transform="translate(370.900390625, 1114.9609375)" id="flowchart-J-17" class="node default"><rect height="51" width="165.640625" y="-25.5" x="-82.8203125" style="" class="basic label-container"></rect><g transform="translate(-52.8203125, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="105.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>执行 status 检查</p></span></div></foreignObject></g></g><g transform="translate(370.900390625, 1256.35546875)" id="flowchart-K-19" class="node default"><polygon transform="translate(-65.89453125,65.89453125)" class="label-container" points="65.89453125,0 131.7890625,-65.89453125 65.89453125,-131.7890625 0,-65.89453125"></polygon><g transform="translate(-40.39453125, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="80.7890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>status 通过?</p></span></div></foreignObject></g></g><g transform="translate(463.224609375, 1418.75)" id="flowchart-L-21" class="node default"><rect height="51" width="125.296875" y="-25.5" x="-62.6484375" style="" class="basic label-container"></rect><g transform="translate(-32.6484375, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="65.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>跳过 cmds</p></span></div></foreignObject></g></g><g transform="translate(278.576171875, 1418.75)" id="flowchart-M-23" class="node default"><rect height="51" width="144" y="-25.5" x="-72" style="" class="basic label-container"></rect><g transform="translate(-42, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>进入命令执行</p></span></div></foreignObject></g></g><g transform="translate(278.576171875, 1569.75)" id="flowchart-N-25" class="node default"><rect height="51" width="125.296875" y="-25.5" x="-62.6484375" style="" class="basic label-container"></rect><g transform="translate(-32.6484375, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="65.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>执行 cmds</p></span></div></foreignObject></g></g><g transform="translate(370.900390625, 1720.75)" id="flowchart-O-27" class="node default"><rect height="51" width="161.7734375" y="-25.5" x="-80.88671875" style="" class="basic label-container"></rect><g transform="translate(-50.88671875, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="101.7734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>执行 defer 命令</p></span></div></foreignObject></g></g><g transform="translate(370.900390625, 1821.75)" id="flowchart-P-31" class="node default"><rect height="51" width="116" y="-25.5" x="-58" style="" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>结束任务</p></span></div></foreignObject></g></g><g transform="translate(280.900390625, 1947.75)" id="flowchart-Z-33" class="node default"><rect height="51" width="116" y="-25.5" x="-58" style="" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>任务失败</p></span></div></foreignObject></g></g></g></g></g></svg>