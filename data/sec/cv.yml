---

cv:
  name: "卢瑞洋"
  location: "上海"
  email: "<EMAIL>"
  website: "https://github.com/xbpk3t/"
  social_networks:
    - network: "GitHub"
      username: "xbpk3t"
  sections:
    个人优势:
      - bullet: "语言相关基本功扎实，熟练使用 k8s 工具链，熟悉云原生技术"
      - bullet: "熟练使用多种服务端技术栈，快速上手项目，常用业务组件经验丰富"
      - bullet: "具备电商、社交、工具产品商业化领域的产品思维"
      - bullet: "有团队管理经验，良好的沟通协调能力，主动承担责任"
    工作经历:
      - company: "西安趣联科技"
        position: "Golang 主程"
        location: "西安"
        start_date: "2021-04"
        end_date: "present"
        highlights:
          - "主导社交产品冷启动全流程，设计用户付费引导体系"
          - "负责技术难点攻关，协调日常开发与需求对齐"
      - company: "上海橙絮网络"
        position: "Golang 主程"
        location: "上海"
        start_date: "2019-03"
        end_date: "2021-04"
        highlights:
          - "实现和优化项目核心技术难点"
      - company: "上海东浩兰生集团"
        position: "Java 开发"
        location: "上海"
        start_date: "2018-03"
        end_date: "2019-03"
        highlights:
          - "开发商户财务系统，参与支付系统维护"
          - "保障2018进博会商务对接平台成功上线"
      - company: "西安亚购信息科技"
        position: "PHP 工程师"
        location: "西安"
        start_date: "2016-10"
        end_date: "2018-03"
        highlights:
          - "支持亚米网SKU扩充至15w+，MAU突破10w"
          - "开发反爬虫服务和限流服务"
    项目经历:
      - name: "约吧(PickU)"
        date: "2021-2023"
        highlights:
          - "技术栈 GoZero/k8s/PostgreSQL/Redis/EFK/Kafka/Prometheus"
          - "开发聊天机器人并升级至ChatGPT集成"
          - "实现直播模块（人气值/排行榜/PK玩法）和陪聊服务后台"
          - '优化匹配算法使DAU/MAU提升至40\%+' # 修改此处，转义 % 为 \%
      - name: "CashCare"
        date: "2019-2021"
        highlights:
          - "技术栈 SpringBoot/Eureka/MySQL/Redis/SSDB/Kafka"
          - "实现用户行为回溯系统，优化RFM模型提升复贷率70%"
          - "风控系统优化使不良率低于行业水平"
      - name: "亚米网"
        date: "2016-2018"
        highlights:
          - "开发商品推荐系统提升用户停留时间"
          - "优化数据库架构支持秒杀活动高并发"
          - "搭建毫秒级竞品监控系统"
    教育经历:
      - institution: "西安培华学院"
        degree: "本科"
        area: "计算机科学与技术"
        start_date: "2012-09"
        end_date: "2016-06"
    技术能力:
      - label: "编程语言"
        details: "Golang/Java/PHP"
      - label: "云原生"
        details: "Kubernetes/Docker/Prometheus"
      - label: "数据库"
        details: "PostgreSQL/MySQL/Redis"
      - label: "中间件"
        details: "Kafka/EFK"
