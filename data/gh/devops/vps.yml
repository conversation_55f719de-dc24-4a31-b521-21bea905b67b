---


# TODO 之后 cf worker可以部署docker之后，
- type: vps/webstack
  tag: devops
  score: 0
  using:
    url: https://github.com/xbpk3t/vps
    des: 个人自用的所有在线部署服务
    qs:
      - 一些VPS上可以部署的服务： # TODO [推荐及记录下自己在小鸡鸡上部署的服务们 - 资源荟萃 - LINUX DO](https://linux.do/t/topic/455456)
      - 【把手机当服务器用】 # TODO [你们都有 sv00 我没有，哎我有一部旧手机（cloudflare/Tunnels/Termux） - 开发调优 - LINUX DO](https://linux.do/t/topic/322143/5) 其实就是通过cloudflare-tunnel实现内网穿透，在安卓手机上通过安装termux作为linux模拟器。这样就可以直接在手机上跑服务，并且用公网域名访问了。 # 试着用这个跑了一下sealos
  repo:
    - url: https://github.com/xbpk3t/docs
      doc: https://docs.lucc.dev/
      record:
        - 【2025-07-25】最近很烦的一点就是，现在各种xxx撕的到处都是，就很乱。

    - url: https://github.com/xbpk3t/dotfiles
      record:
        - 【2025-07-08】移除【mac-dev-playbook】、【ansible-atriso-config】、【mac-playbook】这三个。


    - url: https://github.com/oneclickvirt/ecs
      des: 融合怪脚本。VPS主机必用工具。

    - url: https://github.com/xykt/NetQuality
      des: 有点意思，颇为实用。一键检测网络质量的开源脚本。这是一个方便的网络质量测试脚本，能够快速评估网络质量和性能，支持中英双语、三网 TCP 大包延迟、回程路由、网速测试、国际互联等功能。

    - url: https://github.com/DIYgod/RSSHub
      doc: https://docs.rsshub.app/zh/guide/
      score: 5

    - url: https://github.com/jooooock/wechat-article-exporter # TODO 有时间自己部署一下
      des: 在线批量导出微信公众号文章，支持内嵌的音视频导出，无需搭建任何环境，可100%还原文章样式，支持私有部署


    #      des: 【Gmail】账号******************，密码也是google. gmailctl是用来对gmail进行自动化操作的。举个例子具体说明：假设你是一个项目经理，你的Gmail邮箱里有很多工作相关的邮件，你希望能够自动地对这些邮件进行分类，以提高工作效率。你希望实现以下分类：1、将所有来自特定供应商的邮件自动标记为“供应商”标签。2、将所有包含“会议”关键词的邮件自动转发到你的会议邮箱。3、将所有来自团队成员的邮件自动分类到“团队”文件夹。使用gmailctl，你可以创建一个配置文件来定义这些规则，然后让gmailctl帮你自动在Gmail中设置这些过滤器。

    - url: https://github.com/pingcap/ossinsight
      doc: https://ossinsight.io/collections/
      des: 【OSS Insight】非常实用 Explore Collections，用来做技术选型的预选

    - url: https://github.com/Zhwt/yaml-to-go
      doc: https://zhwt.github.io/yaml-to-go/
      des: 【YAML-to-Go】Convert YAML to Go struct

    - url: https://github.com/mholt/json-to-go
      doc: https://mholt.github.io/json-to-go/
      des: 【JSON-to-Go】goland本身就支持该功能，复制JSON到golang文件，可以自动转换为struct。但是【JSON-to-Go】还支持 是否inline，是否omitempty. goland不支持该feat.

    - url: https://github.com/getcursor/cursor
      doc: https://docs.cursor.com/
      record:
        - 【2025-06-26】把cursor从LLM挪到webstack了，并且移除了之前的很多相关qs（都已经严重过时了，并且这些qs也都没啥意思）。
    #- 【代码规范工具】必须要用 linters 之类的工具，因为会有大块修改
    #- 【版本控制】1、善用 restore。2、一定要频繁 commit
    #- 【任务分解&指令清晰】现有的LLM很难处理大任务，所以需要我们自己手动拆解。举个例子：我需要把n个md文件的内容转到YAML文件里，看起来是机械的重复性工作，但是即使如此，也应该让LLM先执行其中一个markdown文件，当转化成功的YAML符合预期后，再让他执行其他md文件。给 Cursor 的指令越具体，得到的结果就越准确。例如，不要只说"优化这段代码"，而应该说"优化这段代码以减少 API 调用次数"。
    #- 为啥cursor会出现“降智问题”？ # “接之前的话题，Cursor 降智我觉得可能是由于模型的注意力机制有限，当我们将项目中很多代码作为 prompt（当然了这里是隐式的）喂给模型让模型去修改我们的代码，这样的一个过程经过多次循环后可能会出现注意力缺失（因为上下文太长），直观的说就是代码改着改着可能会越改越错或者怎么改都改不到点子上，出现这种情况。有一个不是方法的方法就是新建一个对话，在注意力机制尚且没有出现效果上的下降时让 cursor 去改代码。”



    - url: https://github.com/fkling/astexplorer
      doc: https://astexplorer.net/
      des: AST explorer

    - url: https://github.com/compiler-explorer/compiler-explorer
      doc: https://godbolt.org/
      des: Compiler Explorer

    - url: https://github.com/bitiful/send
      doc: https://send.bitiful.com/
      des: 【缤纷快传】，【奶牛快传】、【wetransfer】这种产品形态其实是在airdrop这种实时传输和网盘这种需要长期文件的中间产品。

    - url: https://github.com/ShouChenICU/FastSend
      doc: https://fastsend.ing/zh
      des: 一个基于 WebRTC 技术的点对点文件传输工具，支持快速的目录同步和文件传输。通过浏览器即可实现安全、高效的文件共享。【2025-06-24】很实用的工具。

    - url: https://github.com/cloudflare/speedtest
      doc: https://speed.cloudflare.com/
      des: 比其他的都好用

    - url: https://github.com/PaulSec/API-dnsdumpster.com
      doc: https://dnsdumpster.com/
      des: 【DNSdumpster】用来查找某个域名的所有子域名
      record:
        - 【2025-06-26】移除【nmmapper/dnsdumpster】，相较之下真的不好用，会查出来各种已经移除的不存在的DNS records。并且可视化也不好。

    - url: https://github.com/dmtolpeko/sqlines
      doc: https://www.sqlines.com/online
      des: 【sqlines】找 mysql sql to pgsql时找到的，非常牛逼，各种数据库的sql之间互相转。这个工具真的是牛逼plus。

    #- name: 【youtube music】
    #  des: ytb 使用 1.2h 后会自动停止播放（需要 YouTube NonStop 插件）；排序，ytb 只有按照“添加顺序”排序，没有按照“最近使用”排序，就很蠢；左侧栏只有“播放列表”，没有“专辑”选项；使用 spotify，不会污染 ytb 视频首页
    #- name: 【apple music】
    #  des: 资料库内容丢失（一旦退出icloud账号，数据就全部丢失），且数据同步特别慢。

    # 【2025-06-21】把【tldr】、【cheatsheets.zip】、【Fechin/reference】、【devhints.io】、【DevDocs】、【Dash】、【denisidoro/navi】、【cheat/cheat】全都删掉了。综合来说都不如这个好用。


    - url: https://github.com/JasonGrass/rename
      doc: https://rename.jgrass.xyz/ # [WebRename - 在线文件批量重命名工具](https://webrename.cn/)
      des: 【在线文件批量重命名】文件批量重命名(renamer) 的web工具。大部分人还是使用 GUI 应用来操作，比如 mac 上的Renamer，但是真的有必要用 GUI 来进行该操作吗？首先无论是 mac 还是 win 本身都支持简单的 batch rename 操作，mac 上支持 Replace (以及 Add Text), Format 操作，实际上已经很够用了，但是如果想进行一些复杂操作就需要第三方工具了。结论：如果日常使用，那肯定 web 工具对用户更友好。如果是高频操作或者不方便使用 web 的场景（比如 vps 上没有 browser 的话），那就用 cli 工具。
      record:
        - 【2025-06-26】移除【cyhuajuan/FreeReNamer】、【pipe-rename（通过在editor里编辑进行批量重命名，支持brew和cargo安装）】。简单来说，重命名有两个场景：本地和VPS，VPS上老老实实用cli工具（比如rnr），本地又分为简单和复杂两种场景，简单场景直接用finder内置的rename工具就可以了，复杂场景用rename这种web在线工具。

    - url: https://github.com/projectdiscovery/subfinder
      des: 【子域名发现工具，支持多个数据源和被动枚举】它已成为sublist3r项目的继承者。SubFinder使用被动源，搜索引擎，Pastebins，Internet Archives等来查找子域，然后使用灵感来自于altdns的置换模块来生成排列，并使用强大的bruteforcing引擎快速的解析它们。如果需要，它也可以执行纯粹的爆破。此外，SubFinder还具有高可定制性。其代码构建模块化的特点，使你能够轻松地添加功能或移除错误。

    - url: https://github.com/CorentinTh/it-tools
      doc: https://it-tools.tech/

    - url: https://github.com/sunmorgus/vscode-json-editor
      doc: https://jsoneditoronline.org/
      score: 2
      des: JSON Editor Online. 远比其他JSON编辑器好用

    - url: https://github.com/ankitkr0/gitreceipt
      doc: https://github-insights-pro.toolsnav.top/
      des: 用来评估某个开发的水平，还是有点用的



    - url: https://github.com/jsdelivr/globalping
      doc: https://globalping.io/


    #    - doc: https://ping.pe/
    #      des: 【ping.pe】Ping, mtr, dig and TCP port check from multiple locations. 站长工具里有这个功能。搬瓦工提供,支持 ping.dns.dig.mtr.port.chart.等功能。
    - url: https://github.com/Killeroo/PowerPing
      doc: https://ping.sx/ping
      des: 这两个网站功能完全相同（和ITDOG，但是Misaka的UI更漂亮）。拆开一看，其实就是 4 个功能，ping/TCPing/MTR 路由追踪/网页测速（其实就是 HTTP 测速），都是一些命令行就能实现的操作，所以就把这个网站废掉了。misaka 家提供,支持 ping.dns.dig.mtr.port 等功能

    - url: https://github.com/fre123-com/fre123-info-flow
      doc: https://www.fre321.com/weekly
      des: 【FRE123 技术周刊精选推荐】除了技术周刊，还有新闻热榜和技术热榜。作为 weekly 的 webstack 是很好的，按图索骥，找到了不少很好的周刊。但是注意到这个网站的几个缺点：1、抓取有些问题，部分网站无法抓到最新数据。2、网页列表没有标明时间。把里面的周刊挨个过了下，找到了几个还不错，用 rss 订阅了。

      record:
        - 【2024-10-09】给flomo写了一个用来批量清空的tampermonkey脚本。因为外出随手记录想法，到家之后把所有的 record 集中处理掉。最终决定用flomo（之前尝试过 网络剪贴板、微信文件传输助手）。那么基于以上场景，就会有定期全部清空的需求。但是flomo不支持批量删除，所以写了这个脚本。


    - url: https://github.com/xbpk3t/me
      des: 【portfolio作品集】
      record:
        - 【2025-03-04】仿照【satnaing/terminal-portfolio】和 https://lyc8503.net/ 修改，并发布到cf pages。相较于愿项目做了以下优化：1、样式更紧凑了（比如说行间距、字体、字体大小之类的）。2、删改了原项目提供的terminal（原项目过于繁琐，这点同样是照着lyc8503实现的）。3、ll命令展示所有项目，并且让url可以点击，直接跳转。

    - url: https://github.com/cloudflare/cloudflare-docs
      doc: https://dash.cloudflare.com/
      score: 5
      des: 【Cloudflare】账号密码是 outlook+cloudflare@ ，比较常用的cf服务有DNS托管+pages+R2。免费 CDN 流量 + 10G 的免费存储空间，对个人项目来说，可能是最好选择了。毕竟用其他流量付费的 CDN，还要担心跑个自己的小项目还要担“加个 CDN，信用卡被刷爆”的风险。后面需要升级的话，cf 的 PRO 一个月才 20 刀，提供不计量的 DDOS 防护，对于中小网站够用了。另外，关于xxx问题，之前用阿里云，因为域名没有备案，所以CDN“加速区域”只能选“除中国大陆外”，速度还不如cf的海外节点。
      record:
        - 【2025-06-23】移除【hono（开发serverless应用的首选。可以与vue/react结合使用。）】，因为突然意识到这玩意实际上是用来提供API的，也就是说可以看作是某种SSR框架。我很烦用ts去做后端的这种SSR的东西，就不需要这个了。
        - 【2025-06-25】移除了【zhuima/awesome-cloudflare】，搞来搞去无非是 图床、blog、临时邮箱、短链服务、GA 这些东西，除此之外就是，拨测工具、测速之类的网络服务。真的没啥意思。
      sub:
        - url: https://github.com/Charca/cloudflare-pages-auth
          score: 5
          des: 怎么给 cloudflare pages 部署的网站，添加密码来保护页面? # 目前给blog使用的cloudflare functions. 如果是MPA可以锁定指定route，如果是SPA则只能锁定全站。通过使用 cookie 就解决了上面的问题，并且支持自定义有效期（Max-Age/Expire），就很方便。
        - url: https://github.com/cmliu/CF-Workers-docker.io
          score: 5
          des: 【自建 Docker 镜像站工具】我的就是用这个实现的
      topics:
        - topic: cloudflare workers
          qs:
            - 先简要说说 workers 和 pages functions 的区别 # (pf is also a kind of worker, but inversion, file-based(worse migration)) # 最大的区别就是 workers 是非侵入性的，更通用，迁移性更好，pages functions 是侵入性的，只对当前 pages 生效。但是通常 functions 的功能更强大。
            - 只能用js开发cloudflare workers吗? 其他语言怎么开发workers? # cf worker 的第一语言是 js，如果想用 golang 的话，需要把 golang 编译成 wasm，用 js 调用. workers就是用来"Go package to run an HTTP server on Cloudflare Workers.", Support R2, KV, Cache API, Cron Triggers, etc. 需要注意的是golang打包的包体太大（超过了cf免费用户的3MB，以及付费用户的10MB），所以应该用 tinygo 打包。
            - How to One-click deploy cloudflare workers? # [savokiss/omnivore2rss: Simple cloudflare worker converts your omnivore to an RSS feed.](https://github.com/savokiss/omnivore2rss) As describes, Used to convert your omnivore to RSS feed, Learned how to One-Click deploy cloudflare workers



  record:
    - 【2025-02-24】移除【glance】（开源的自托管仪表板，可以将所有你的信息源放在一个地方来查看，比如说 RSS、Hacker News、天气预报、YouTube、股票价格、Docker 容器状态、自定义组件，有点儿像 All In One 的你的数据查看系统，整一个去玩玩去。）。玩了一下感觉一般，其实就是一些可以（也应该做成）rss feed的内容，用dashboard来体现了。有什么必要呢？
    - 【2025-06-24】移除【docker-wechat（在docker里部署微信。这样直接在网页上登录微信就可以了，就不需要在本地安装了）】。终究只是玩具，当时头脑一热就加进来了。
    - 【2025-06-24】移除【bastienwirtz/homer】，一个【Server Homepage】服务，相当于是webstack+简单的uptime。但是我确实用不到，我有docs-alfred，就已经上位替代这个东西了。
    - 【2025-04-30】移除【cfour-hi/gitstars】 # 一个基于vue实现的用来管理star repo的工具。但是我用不到。
    - 【2025-04-30】移除【OpenGithubs/github-monthly-rank】 # GitHub项目的月度排行榜，展示最受欢迎的开源项目
    - 【2025-04-30】移除【EvanLi/Github-Ranking】、【k0kubun/gitstar-ranking】 # 这两个都是gh的repo排名统计工具
    - 【2025-04-30】移除【vladkens/ghstats】 # 基于rust+sqlite实现的，用来追踪github repo traffic history 的工具

    - 【2025-06-06】删除了【DownGit】用不到了。其实压根没怎么用过，想了一下最方便直观的做法就是直接Download zip，然后解压缩之后，找到指定文件夹。而不是用DownGit下载指定文件夹，之后还要再解压缩。徒增心智负担。
    - 【2025-06-08】删除了【Notion】。其实很垃圾，我也懒得评价，优缺点跟语雀差不多。优点不多说，功能强大，。加载慢、不支持本地离线使用、没有 TOC、不是原生 Markdown。还有一些 bug，比如添加的图片不知道为啥就丢失了。总结一句话，这种线上文档的优缺点都是类似的。
    - 【2025-06-08】删除了【codetop】。之前是用来刷hot100的，但是leetcode本身就有这个题库了，所以用不到了。
    - 【2025-06-08】移除了【png2svg】（经过我的反复使用和验证，总结一下，png转svg很困难，如果是黑白图片，一些在线工具都能处理地还行，但是如果是彩色图片，则几乎无法处理。尝试使用png2svg在本地处理彩色图片，算是能处理，但是输出的svg文件会非常大(>10M)，并且效果也不算太好。所以还是算了。）
    - 【2025-06-08】移除了【tinypng-workflow】，那个TinyPNG的alfred workflow，
    - 【2025-06-08】移除了【dribbble】、【railway】、【algolia】、【gifsicle】、【readme.so】、【active-forks】、【claw cloud】
    - 【2025-06-25】移除【ExplainShell】。用 ChatGPT 代替了，这个网站不支持中文，且不如 ChatGPT 的对话式解释 shell 好用
    - 【2025-06-26】移除【koodo-reader】。在线电子书网站，类似的还有calibre或者talebook，很好用。但是我真用不到这东西。如果看小说，我还是喜欢直接下载到手机上，用手机看。微信读书支持 txt epub mobi azw3 pdf 常用格式。漫画我不看，kindle 我没有，pdf、富文本和超文本我用 chrome 看。不如直接用【微信读书】。
    - 【2025-06-30】把webstack和VPS这两个type整合到一起了。原本想直接把整个webstack整个干掉的，但是整理时发现仍有大量东西不好处理，只能暂时先挪到这。顺便移除掉【草料二维码】、【Reddit】、【transform.tools】、【Cost of Living（非常精准的一个查看生活成本的网站，通过当地会员主动上传商品订单截图，来计算平均数和中位数）】实际上用不到。
    - 【2025-06-30】移除了【mdx-notes】（经常遇到无法渲染的问题，或者说偶尔能渲染出来），用回了【HedgeDoc】，发现实际上这个才是功能上最好的。支持mermaid，编辑器类似IDE支持多cursor。直接放到alfred里了。 # 之前淘汰了 hedgedoc、editor.md、markdown-nice、stackedit、dillinger、markdown-live-preview。但是又说不太上来为啥淘汰掉了。之后如果对 mdxnotes 不满意的话，再画这个table。
  topics:
    - topic: 【技术选型】网盘
      table:
        - name: Cloudreve
          url: https://github.com/cloudreve/Cloudreve
          lang: Go
          核心定位: 多功能公私兼备网盘
          存储后端支持: ✅ 本地/OSS/COS/S3/OneDrive等10+种
          多用户管理: ✅ 多用户分组+精细权限
          文件分享功能: ✅ 链接密码/时效/下载限速
          在线预览能力: 视频/图片/Office/文本
          协作功能: ❌
          性能与扩展性: 中高（Go语言，支持集群）
          学习曲线: 中等
          最佳适用场景: 团队文件共享+多网盘统一管理

        - name: filebrowser
          url: https://github.com/filebrowser/filebrowser
          lang: Go
          核心定位: 轻量级文件管理器
          存储后端支持: ❌ 仅本地存储
          多用户管理: ✅ 基础用户权限
          文件分享功能: ✅ 基础分享链接
          在线预览能力: 图片/文本/基础视频
          协作功能: ❌
          性能与扩展性: 极高（轻量，<50MB内存）
          学习曲线: 极低
          最佳适用场景: 个人服务器文件管理

        - name: zfile
          url: https://github.com/zfile-dev/zfile
          lang: Java
          核心定位: 多存储源文件目录展示
          存储后端支持: ✅ 本地/OSS/S3/OneDrive/FTP等8+种
          多用户管理: ❌ 仅单用户
          文件分享功能: ✅ 直链+二维码+文件夹加密
          在线预览能力:
            - ✅ 最强：3D/Office/音视频/代码
          协作功能: ❌
          性能与扩展性: 中（Java资源占用较高）
          学习曲线: 低到中等
          最佳适用场景: 个人多网盘聚合浏览

        - name: seafile
          url: https://github.com/haiwen/seafile
          lang: C/Python
          核心定位: 企业级同步与协作平台
          存储后端支持: ✅ 本地/兼容S3协议存储
          多用户管理: ✅ 企业级多用户+群组权限
          文件分享功能: ✅ 链接+权限控制+版本历史
          在线预览能力: ✅ Office/PDF（需集成ONLYOFFICE）
          协作功能: ✅ 实时协作文档+Wiki+文件锁定
          性能与扩展性: 高（C/Python，增量同步）
          学习曲线: 高
          最佳适用场景: 企业文档协作+安全同步

        - name: CloudDrive2
          url: https://github.com/sublaim/clouddrive2
          lang: Go/Python
          核心定位: 多云盘聚合与本地挂载工具
          存储后端支持: ✅ 阿里云盘/百度网盘/天翼云盘/OneDrive/Google Drive/Dropbox/支持WebDAV协议的云盘（如坚果云）
          多用户管理: ✅ 免费版支持2网盘+1本地挂载（高级版无限制）
          文件分享功能: ✅ 基础文件分享（直链访问）
          在线预览能力: ✅ 图片/文本/音视频直接播放 + 调用第三方播放器（PotPlayer/VLC等）
          协作功能: ❌
          性能与扩展性: 高速缓存技术提升远程访问流畅度 + 多线程下载+断点续传 + 支持增量同步
          学习曲线: 中等（需Docker部署）
          最佳适用场景: 个人或小团队的多云盘聚合管理
      record:
        - 【2025-06-12】alist被卖给一个劣迹斑斑的公司，被投毒了，一票否决。



    - topic: 【技术选型】接口调试工具 # TODO 之后移除掉
      table:
        - name: Apifox
          url: https://github.com/apifox/apifox
          doc: https://app.apifox.com/
          技术栈: Chrome扩展
          数据存储: 云端同步
          是否付费: false
          团队协作: true
          文档生成: true
          Mock服务: true
          自动化测试: true
          环境变量: true
          支持CI集成: true

        - name: Fast-Request
          url: https://github.com/dromara/fast-request
          技术栈: IDEA插件
          数据存储: 本地存储
          是否付费: true
          团队协作: false
          文档生成: true
          Mock服务: false
          自动化测试: true
          环境变量: true
          支持CI集成: false

        - name: Posting
          url: https://github.com/darrenburns/posting
          des: TUI版本的postman
          技术栈: Python/TUI
          数据存储: YAML文件
          是否付费: false
          团队协作: true
          文档生成: false
          Mock服务: false
          自动化测试: true
          环境变量: true
          支持CI集成: true

        - name: Bruno
          url: https://github.com/usebruno/bruno
          技术栈: Electron
          数据存储: 本地文件+Git
          是否付费: false
          团队协作: true
          文档生成: true
          Mock服务: true
          自动化测试: true
          环境变量: true
          支持CI集成: true

        - name: Yaak
          url: https://github.com/mountain-loop/yaak
          技术栈: Tauri+Rust+React
          数据存储: 未知
          是否付费: false
          团队协作:  # 未知
          文档生成:  # 未知
          Mock服务:  # 未知
          自动化测试:  # 未知
          环境变量:  # 未知
          支持CI集成:  # 未知

        - name: Postman
          技术栈: Electron
          数据存储: 云端为主
          是否付费: true
          团队协作: true
          文档生成: true
          Mock服务: true
          自动化测试: true
          环境变量: true
          支持CI集成: true

        - name: HTTPie
          url: https://github.com/httpie/cli
          技术栈: Python/CLI
          数据存储: 配置文件
          是否付费: false
          团队协作: false
          文档生成: false
          Mock服务: false
          自动化测试: true
          环境变量: true
          支持CI集成: true

        - name: poster
        - name: insomnia
        - name: hoppscotch
        - name: paw (RapidAPI)



    - topic: 【技术选型】应用托管平台/Serverless平台
      table:
        - name: vercel
          url: https://github.com/vercel/vercel
          doc: https://vercel.com/docs
          类型: 前端优先平台
          部署单位: Serverless函数/静态文件
          冷启动: 极快（边缘网络）
          定价模型: 按使用量 + 免费层
          数据库集成: 需第三方
          适用场景: JAMStack/Next.js 应用

        - name: netlify
          类型: 前端优先平台
          部署单位: 静态文件/无服务器函数
          冷启动: 快（边缘网络）
          定价模型: 按带宽+函数调用
          数据库集成: 需第三方
          适用场景: 静态站点/轻量级Web应用

        - name: heroku
          类型: 传统PaaS
          部署单位: 容器化应用(Dyno)
          冷启动: 慢（分钟级）
          定价模型: 按运行时间计费
          数据库集成: 内置插件市场
          适用场景: 全栈应用/传统架构

        - name: railway
          类型: 容器化平台
          部署单位: Docker容器
          冷启动: 中等（秒级）
          定价模型: 按量付费，每个月 5 美元免费额度（"Free Trial, $5, 512 MB of RAM, 1 GB of Disk, and 2 vCPU"） # 官方说不停机，实际上 free-trailer 套餐受限很多，最恶心的是 500h 自动停机。最害怕的还是绑卡以后，扣费无止境。
          数据库集成: 一键部署
          适用场景: 微服务/数据库依赖应用

        - name: flyIo
          类型: 边缘容器平台
          部署单位: Firecracker微VM
          冷启动: 极快（毫秒级）
          定价模型: 按CPU/内存用量
          数据库集成: 全球分布式DB
          适用场景: 低延迟全局应用

        - name: pikapods
          类型: 轻量托管平台
          部署单位: Docker/Podman
          冷启动: 依赖镜像大小
          定价模型: 固定月费制
          数据库集成: 自托管方案
          适用场景: 小型自托管服务

        - name: zeabur
          类型: 开发者友好平台
          部署单位: 自动检测类型
          冷启动: 快（智能缓存）
          定价模型: 按资源消耗
          数据库集成: 内置支持
          适用场景: 全栈初学者项目

        - name: GCP app engine
          类型: 企业级PaaS
          部署单位: 容器/代码包
          冷启动: 慢（预热机制）
          定价模型: 复杂计费体系
          数据库集成: GCP生态集成
          适用场景: 企业级生产环境
      qs:
        - 应用托管平台根据架构可分为三类：前端优先型（Vercel/Netlify（优化静态内容交付））、容器化平台（Railway/Fly.io/Heroku（侧重应用运行时））、混合解决方案（Zeabur/Pikapods（平衡易用性与灵活性））




    - topic: 图片格式
      table:
        - name: JPG
          压缩类型: 有损压缩
          透明度支持: 不支持
          动画支持: 不支持
          色彩深度: 24位色(1600万色)
          适用场景: 照片/渐变图像
          文件大小: 中小(高压缩比)
          边缘清晰度: 有锯齿(高压缩时)
          浏览器兼容性: 100%
          特殊优势: 成熟的色彩压缩算法
          主要缺点: 不支持透明/多次编辑失真

        - name: PNG
          压缩类型: 无损压缩
          透明度支持: 支持Alpha透明
          动画支持: 不支持
          色彩深度: 最高48位色(带Alpha通道)
          适用场景: 图标/透明图/文字图像
          文件大小: 较大(无损导致)
          边缘清晰度: 锐利清晰
          浏览器兼容性: 100%
          特殊优势: 完美保留细节
          主要缺点: 文件体积大

        - name: SVG
          压缩类型: 矢量无损
          透明度支持: 支持全透明
          动画支持: 支持（SMIL）
          色彩深度: 无限色(矢量计算)
          适用场景: 图标/Logo/矢量图形
          文件大小: 极小(简单图形)
          边缘清晰度: 无限缩放不模糊
          浏览器兼容性: 98%+ (IE9+)
          特殊优势: 分辨率无关/可编辑代码
          主要缺点: 复杂图像渲染慢

        - name: WebP
          压缩类型: 支持有损与无损
          透明度支持: 支持Alpha透明
          动画支持: 支持
          色彩深度: 24位色+Alpha通道
          适用场景: 网页图像综合应用
          文件大小: 比JPEG小25-35%
          边缘清晰度: 接近PNG
          浏览器兼容性: 96%+ (Edge18+全支持)
          特殊优势: 谷歌力推/高压缩率
          主要缺点: 旧浏览器需兼容方案










#- url: https://github.com/yanfeizhang/coder-kung-fu
#  doc: https://kfngxl.cn/ # TODO
#  des: 【开发内功修炼@张彦飞】


# TODO 并非高频操作，只是偶尔看看
#- url: https://github.com/btw-so/open-source-alternatives
#  des: 一些比较主流的商业应用的开源替代品
#- url: https://github.com/piotrkulpinski/openalternative
#  doc: https://openalternative.co/
#  des: OpenAlternative
#- url: https://github.com/junaid33/opensource.builders
#  doc: https://opensource.builders/
#  des: 【Opensource.builders】用来找各种各种付费/闭源服务的开源替代服务



#- url: https://qinglian.tencent.com/apps/list/
#  name: 【腾讯轻联】
#  des: 在企业应用场景（因为主要是飞书、企微、金蝶云、Notion、抖音）下比其他automation工具更好用 # [基于 n8n 的开源自动化：以滴答清单同步 Notion 为例 ｜ 少数派会员 π+Prime](https://sspai.com/prime/story/automation-n8n)

#- url: https://gamma.app/
#  des: 用AI生成PPT，据说很实用。真的比 notegpt.io 之类的类似工具好用。
#
#- url: https://www.semanticscholar.org/
#
#- url: https://dappradar.com/
#  des: SocialFi
#
#- url: https://v0.dev/chat
#  des: vercel给的用来生成js项目的，但是必须用shadcn。做个demo或者小项目还行
#  name: 【v0】
#
#- name: 【golang 数据转换工具集】
#  url: https://www.printlove.cn/tools/yaml2go
#  des: 除了【YAML-to-Go】还支持 sql转ES, go-zero, gorm, mongodb.

#- name: Pack Hacker
#  url: https://packhacker.com/
#  des: 这网站确实不错，非常有用。每篇帖子都是大长篇，非常系统地提供某个问题的解决方案，或者描述某个物品的各种子分类之间的区别
#- name: 黑猫投诉
#  url: https://tousu.sina.com.cn/
#  des: 相当于 1818
#- name: 七麦数据
#  url: https://www.qimai.cn/
#
#- name: MacScripter
#  url: https://www.macscripter.net/
#  des: AppleScript. 主要围绕 macOS 和 iOS 自动化展开，支持通过 AppleScript、Shortcuts、Automator、JavaScript for Automation、shell scripting 等多种方式实现系统自动化操作。
#
#- name: Website Metadata
#  url: https://websitemetadata.com/
#  des: 查看网页 meta 标签信息的工具。输入网址，就可以看到在各个搜索引擎和社交媒体平台上面显示的效果。还可以在线编辑生成 meta 标签。
#- name: 超级鹰
#  url: https://www.chaojiying.com/
#  des: 打码平台，还不错

#- name: 【scmp】
#  url: https://www.scmp.com/
#  des: South China Morning Post. 比联合早报靠谱

#- url: https://www.wired.com/
#  name: 【Wired】
#  des: 也就是《连线》，非常有名的科技类内容网站了，我非常喜欢阅读他们的专题内容，会用几篇文章不同角度来探讨一个话题。但受限于时间，23 年应该会取消订阅。广告太多，取消rss订阅了。
#  rel:
#    - name: 【The Information】
#      des: the information 需要付费，$33/Month
#    - name: 【ft news】
#      des: 年费¥358，稍微有点干货的文章都要付费，看看标题得了。只订阅【hot10】，其他版块都没啥意思

#- url: https://nymag.com/intelligencer/
#  des: 挺蠢的
#
#- url: https://www.caixin.com/
#  name: 【财新】
#  des: 看啥都得付费，财新通会员¥648/Year，有点贵

#- name: 【烯牛数据】
#  url: https://www.xiniudata.com/
#
#- name: 【IT 桔子】
#  url: https://www.itjuzi.com/addedOrangeSpeak
#  des: 看看新行业
#- name: 【DB-ENGINEs】
#  url: https://db-engines.com/en/ranking
#  des: 比较权威的db网站，还支持db之间的对比功能，非常好用
#
#- url: https://abtest.design/
#  des: AB测试网站效果评估。真心不错，收集了不少知名应用的 AB 测试效果对比，看了看挺值得学习的，甚至有时候一个简单的改动带来的效果会比其他方式好不少。其实可以从行为经济学的角度分析这些case。
#
#
#- url: https://pitchhub.36kr.com/investevent
#  des: 融资事件 - 36氪

#- name: 【sojson】
#  url: https://www.sojson.com/jshtml.html
#  des: js, css代码的格式化、压缩、混淆
#
#- name: 【聚合数据】
#  url: https://www.juhe.cn/
#- url: https://ossrank.com/cat
#
#- name: 【SVG Repo】
#  url: https://www.svgrepo.com/collections/
#  des: 资源多，下载svg格式免费


#- url: https://github.com/sertraline/annas-archive-bot
#  doc: https://zh.annas-archive.org/
#  des: 【Anna’s Archive】用来找书的网站，很好用。世界最大开源及开放数据图书馆（类似论文之于zlib）。其实这些找书网站对我来说，也没有那么大的用处，我找书的顺序是 微信读书 - 这种找书网站 - 买书。所以上面有更方便的微信读书，下面有直接买书的兜底。是否真的能从网上下载到pdf就没有那么重要了。之前用过【z-lib】、【雅书】、【鸠摩搜索】、【24hBook】


#- url: https://github.com/TimothyYe/namebeta
#  doc: https://namebeta.com/
#  des: 【NameBeta】域名比价网站（某域名在各域名注册商的价格），本身就支持【DomainHacks】提供域名生成工具（主要是能够提供一些 ele.me 这样关键字作为后缀的域名）。
