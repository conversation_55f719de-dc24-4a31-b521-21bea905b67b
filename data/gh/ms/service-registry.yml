---

- type: service-registry
  tag: ms
  score: 5
  using:
    url: https://github.com/etcd-io/etcd
    doc: https://etcd.io/docs/v3.5/
    des: 【ETCD】etcd = Eternal Consistency Distributed. etcd 是基于 raft 的 kv 存储，其核心是强一致性的 kv 读写，其使用场景如服务发现、分布式锁、master 选举都是基于这点实现的。etcd 的前身是 boltDB，参考的是 LMDB，支持事务，ACID，MVCC，ZeroCopy，BTree 等特性。
    topics:


      #        - topic: ETCD 基本认知
      #          qs:
      #            - etcdV2 支持哪些 feats?
      #            - etcdV2 的 cons?
      #            - etcdV3 是怎么解决这些 etcdV2 的 cons?
      #            - etcd feats? # ectd 支持动态扩容缩容, MVCC, 可靠的事件监控，并且基于 MVCC 实现指定版本重放 (zk 的 watcher 没有 MVCC), 租约机制将 connection 和 session 分离

      - topic: "***ETCD 架构（Client层、API网络层、Raft算法层、功能逻辑层、存储层、选举机制、）***" # etcd server(WAL(Entry + snapshot)) + raft + storage(treeIndex + boltdb)
        url: https://mp.weixin.qq.com/s?__biz=MzI4NDM0MzIyMg==&mid=2247489043&idx=2&sn=d2a5bdb76f65df8ad93d18487b17f823
        picDir: ms/service-discovery/etcd
        table:
          - name: etcd 0.1 (2013-08)
            存储引擎: 简单键值存储，无历史版本追溯
            共识机制: 实验性Raft实现
            API协议: |
              - HTTP/JSON v1 API
              - 基础Get/Set/Watch
            分布式特性: |
              - 无自动发现机制
              - 读一致性无保证

          - name: etcd 0.2 (2013-12)
            存储引擎: 保留简单键值结构
            共识机制: 基础Leader选举
            API协议: |
              - 升级至v2 API
              - 支持CAS操作
              - 支持consistent read(需读Leader)
            分布式特性: 仍依赖手动配置

          - name: etcd 0.3 (2014-02)
            存储引擎: 无重大变更
            共识机制: 选举超时优化
            API协议: 新增Compare And Delete
            分布式特性: |
              - 引入Discovery API
              - 支持集群动态发现

          - name: etcd 2.0 (2015-01)
            存储引擎: 基于B树的存储引擎
            共识机制: |
              - Raft模块网络抽象
              - 支持Quorum读机制
            API协议: |
              - 稳定v2 API
              - 插件化架构
            分布式特性: |
              - Discovery API正式支持
              - Kubernetes生产验证

          - name: etcd 2.1 (2015-07)
            API协议: 实现鉴权和授权Auth API

          - name: etcd 2.2 (2015-09)
            API协议: 改进client库,提升错误处理等能力

          - name: etcd 3.0 (2016-06)
            存储引擎: |
              - MVCC多版本并发控制
              - 键空间隔离
            共识机制: 线性读一致性保证
            API协议: |
              - 引入gRPC v3 API
              - 租约(Lease)机制
            分布式特性: |
              - 数据分片实验支持
              - 支撑5000节点集群

          - name: etcd 3.4 (2018-02)
            存储引擎: boltdb稳定性提升
            共识机制: Learner节点预加入
            API协议: 长连接保活优化
            分布式特性: |
              - 支持全并发读
              - 数据毁坏检测机制

          - name: etcd 3.5 (2021)
            存储引擎: WAL日志压缩增强
            共识机制: Learner正式支持
            API协议: 降级流程支持(v3.5↔v3.4)
            分布式特性: |
              - 集群自动修复能力
              - 磁盘空间预测告警

          - name: etcd 3.6 (2025)
            存储引擎: |
              - WAL分段写入引擎
              - IO争用降低80%
            共识机制: |
              - 跨地域自动路由
              - 时延降低45%
            API协议: |
              - 流式API性能3倍提升
              - 动态QoS控制
            分布式特性: |
              - 分钟级弹性伸缩
              - 无感节点增减
        qs:
          - etcd不就是用boltdb做存储层，用raft解决分布式一致性问题，真正自己实现的只有 auth, MVCC, lease, compact

          - raft 层：raft 是 etcd 节点之间同步数据的基本机制，它的性能受限于网络 IO、节点之间的 rtt 等，WAL 受到磁盘 IO 写入延迟
          - 存储层：负责持久化存储底层 kv, 它的性能受限于磁盘 IO，例如：fdatasync 延迟、内存 treeIndex 索引层锁的 block、boltdb Tx 锁的 block 以及 boltdb 本身的性能
          - 其他还有诸如宿主机内核参数、grpc api 层等性能影响因子
          - "***etcd v2 从功能局限性、Watch事件的 稳定性、性能问题、扩展性、内存开销来说存在哪些问题? v3相较于v2 有哪些提升?*** “etcd如何从HTTP/1.x API到gRPC API、单版本数据库到多版本数据库、内存树到boltdb、TTL到Lease、单key原子更新到支持多key事务的演进过程”"
          #  首先是功能局限性问题。它主要是指etcd v2不支持范围和分页查询、不支持多key事务。
          #
          #  第一，etcd v2不支持范围查询和分页。分页对于数据较多的场景是必不可少的。在Kubernetes中，在集群规模增大后，Pod、Event等资源可能会出现数千个以上，但是etcd v2不支持分页，不支持范围查询，大包等expensive request会导致严重的性能乃至雪崩问题。
          #
          #  第二，etcd v2不支持多key事务。在实际转账等业务场景中，往往我们需要在一个事务中同时更新多个key。
          #
          #  然后是Watch机制可靠性问题。Kubernetes项目严重依赖etcd Watch机制，然而etcd v2是内存型、不支持保存key历史版本的数据库，只在内存中使用滑动窗口保存了最近的1000条变更事件，当etcd server写请求较多、网络波动时等场景，很容易出现事件丢失问题，进而又触发client数据全量拉取，产生大量expensive request，甚至导致etcd雪崩。
          #
          #  另一方面，etcd v2 client会通过HTTP长连接轮询Watch事件，当watcher较多的时候，因HTTP/1.x不支持多路复用，会创建大量的连接，消耗server端过多的socket和内存资源。
          #
          #  同时etcd v2支持为每个key设置TTL过期时间，client为了防止key的TTL过期后被删除，需要周期性刷新key的TTL。
          #
          #  实际业务中很有可能若干key拥有相同的TTL，可是在etcd v2中，即使大量key TTL一样，你也需要分别为每个key发起续期操作，当key较多的时候，这会显著增加集群负载、导致集群性能显著下降。
          #
          #  最后是内存开销问题。etcd v2在内存维护了一颗树来保存所有节点key及value。在数据量场景略大的场景，如配置项较多、存储了大量Kubernetes Events， 它会导致较大的内存开销，同时etcd需要定时把全量内存树持久化到磁盘。这会消耗大量的CPU和磁盘 I/O资源，对系统的稳定性造成一定影响。
          #  在内存开销、Watch事件可靠性、功能局限上，它通过引入B-tree、boltdb实现一个MVCC数据库，数据模型从层次型目录结构改成扁平的key-value，提供稳定可靠的事件通知，实现了事务，支持多key原子更新，同时基于boltdb的持久化存储，显著降低了etcd的内存占用、避免了etcd v2定期生成快照时的昂贵的资源开销。
          #  性能上，首先etcd v3使用了gRPC API，使用protobuf定义消息，消息编解码性能相比JSON超过2倍以上，并通过HTTP/2.0多路复用机制，减少了大量watcher等场景下的连接数。
          #  其次使用Lease优化TTL机制，每个Lease具有一个TTL，相同的TTL的key关联一个Lease，Lease过期的时候自动删除相关联的所有key，不再需要为每个key单独续期。
          #  最后是etcd v3支持范围、分页查询，可避免大包等expensive request。

          - "***etcd arch?***"
          #  Client层：Client层包括client v2和v3两个大版本API客户端库，提供了简洁易用的API，同时支持负载均衡、节点间故障自动转移，可极大降低业务使用etcd复杂度，提升开发效率、服务可用性。
          #  API网络层：API网络层主要包括client访问server和server节点之间的通信协议。一方面，client访问etcd server的API分为v2和v3两个大版本。v2 API使用HTTP/1.x协议，v3 API使用gRPC协议。同时v3通过etcd grpc-gateway组件也支持HTTP/1.x协议，便于各种语言的服务调用。另一方面，server之间通信协议，是指节点间通过Raft算法实现数据复制和Leader选举等功能时使用的HTTP协议。
          #  Raft算法层：Raft算法层实现了Leader选举、日志复制、ReadIndex等核心算法特性，用于保障etcd多个节点间的数据一致性、提升服务可用性等，是etcd的基石和亮点。
          #  功能逻辑层：etcd核心特性实现层，如典型的KVServer模块、MVCC模块、Auth鉴权模块、Lease租约模块、Compactor压缩模块等，其中MVCC模块主要由treeIndex模块和boltdb模块组成。
          #  存储层：存储层包含预写日志(WAL)模块、快照(Snapshot)模块、boltdb模块。其中WAL可保障etcd crash后数据不丢失，boltdb则保存了集群元数据和用户写入的数据。

          - "***etcd一个读请求是如何执行的？*** “一个读请求从client通过Round-robin负载均衡算法，选择一个etcd server节点，发出gRPC请求，经过etcd server的KVServer模块、线性读模块、MVCC的treeIndex和boltdb模块紧密协作，完成了一个读请求。”" #

          - client的节点故障自动转移和线性读(Linearizable Read)
          #  一方面， client的通过负载均衡、错误处理等机制实现了etcd节点之间的故障的自动转移，它可助你的业务实现服务高可用，建议使用etcd 3.4分支的client版本。
          #  另一方面，我详细解释了etcd提供的两种读机制(串行读和线性读)原理和应用场景。通过线性读，对业务而言，访问多个节点的etcd集群就如访问一个节点一样简单，能简洁、快速的获取到集群最新共识数据。
          #  早期etcd线性读使用的Raft log read，也就是说把读请求像写请求一样走一遍Raft的协议，基于Raft的日志的有序性，实现线性读。但此方案读涉及磁盘IO开销，性能较差，后来实现了ReadIndex读机制来提升读性能，满足了Kubernetes等业务的诉求。

          - etcd的串行读和线性读的原理和使用场景

          - "***etcd的一个写请求是如何执行的? （Quota、WAL、Apply模块 是如何协作的?）***"
          #  首先我们介绍了Quota模块工作原理和我们熟悉的database space exceeded错误触发原因，写请求导致db大小增加、compact策略不合理、boltdb Bug等都会导致db大小超限。
          #  其次介绍了WAL模块的存储结构，它由一条条记录顺序写入组成，每个记录含有Type、CRC、Data，每个提案被提交前都会被持久化到WAL文件中，以保证集群的一致性和可恢复性。
          #  随后我们介绍了Apply模块基于consistent index和事务实现了幂等性，保证了节点在异常情况下不会重复执行重放的提案。
          #  最后我们介绍了MVCC模块是如何维护索引版本号、重启后如何从boltdb模块中获取内存索引结构的。以及etcd通过异步、批量提交事务机制，以提升写QPS和吞吐量。

          - expensive read请求（如Kubernetes场景中查询大量pod）会影响写请求的性能吗？

          - 哪些场景会出现Follower日志与Leader冲突，我们知道etcd WAL模块只能持续追加日志条目，那冲突后Follower是如何删除无效的日志条目呢？
          - 哪些场景会出现 Follower 日志与 Leader 冲突？ # leader崩溃的情况下可能(如老的leader可能还没有完全复制所有的日志条目)，如果leader和follower出现持续崩溃会加剧这个现象。follower可能会丢失一些在新的leader中有的日志条目，他也可能拥有一些leader没有的日志条目，或者两者都发生。

          - follower如何删除无效日志？ # leader处理不一致是通过强制follower直接复制自己的日志来解决。因此在follower中的冲突的日志条目会被leader的日志覆盖。leader会记录follower的日志复制进度nextIndex，如果follower在追加日志时一致性检查失败，就会拒绝请求，此时leader就会减小 nextIndex 值并进行重试，最终在某个位置让follower跟leader一致。

          - 为什么WAL日志模块只通过追加，也能删除已持久化冲突的日志条目呢？ # 其实这里etcd在实现上采用了一些比较有技巧的方法，在WAL日志中的确没删除废弃的日志条目，你可以在其中搜索到冲突的日志条目。只是etcd加载WAL日志时，发现一个raft log index位置上有多个日志条目的时候，会通过覆盖的方式，将最后写入的日志条目追加到raft log中，实现了删除冲突日志条目效果

          - 为啥etcd需要compact机制？ （回收历史版本数据，控制索引内存占用和db大小） # etcd中的每一次更新、删除key操作，treeIndex的keyIndex索引中都会追加一个版本号，在boltdb中会生成一个新版本boltdb key和value。也就是随着你不停更新、删除，你的etcd进程内存占用和db文件就会越来越大。很显然，这会导致etcd OOM和db大小增长到最大db配额，最终不可写。

          - etcd执行压缩的完整流程？ # 当你通过API发起一个Compact请求后，KV Server收到Compact请求提交到Raft模块处理，在Raft模块中提交后，Apply模块就会通过MVCC模块的Compact接口执行此压缩任务。Compact接口首先会更新当前server已压缩的版本号，并将耗时昂贵的压缩任务保存到FIFO队列中异步执行。压缩任务执行时，它首先会压缩treeIndex模块中的keyIndex索引，其次会遍历boltdb中的key，删除已废弃的key。

          - 如何发起一个Compact操作？

          - 精细化控制压缩机制：“etcd压缩的本质是回收历史版本”，但是如果直接 etcdctl compact 会导致MVCC无法回滚，以及Watch机制无法工作（Watch特性中的历史版本数据同步，依赖于MVCC中是否还保存了相关数据），所以不应该简单粗暴地回收所有历史版本。所以怎么精细化控制压缩机制呢？为啥更建议使用etcd自动压缩机制而不是手动压缩？ # 这两种方法其实有点类似GC至于各种语言 # 主要有两种方案。1、使用etcd server的自带的自动压缩机制，根据你的业务场景，配置合适的压缩策略即可。2、第二种比较复杂，可以基于etcd的Compact API，在业务逻辑代码中、或定时任务中主动触发压缩操作。你需要确保发起Compact操作的程序高可用，压缩的频率、保留的历史版本在合理范围内，并最终能使etcd的db 大小保持平稳，否则会导致db大小不断增长，直至db配额满，无法写入。

          - etcd的两种压缩模式（时间周期性压缩periodic 和 版本号压缩revision）分别是啥？各自怎么配置？各自的使用场景？各自的工作机制？
          # 配置：auto-compaction-mode=periodic/revision, auto-compaction-retention=1h/10000 注意如果 auto-compaction-retention=0 就是关闭自动压缩策略

          - 为什么压缩后db大小不减少呢?

          - Lease创建、关联key、续期、淘汰、checkpoint机制

          - etcd鉴权包括认证（密码认证和证书认证）和RBAC授权两部分
          - 鉴权：“鉴权作为了一个核心的前置模块，性能上不能拖后腿，不能成为影响业务性能的一个核心瓶颈。etcd的解决方案是通过Token降低频繁、昂贵的密码验证开销，可应用在内网、小规模业务场景，同时支持使用证书认证，不存在Token过期，巧妙的取CN字段作为用户名，可满足较大规模的业务场景鉴权诉求。” # 首先鉴权目的是为了保证安全，必须防止恶意用户绕过鉴权系统、伪造、篡改、越权等行为，同时设计上要有前瞻性，做到即使被拖库也影响可控。etcd的解决方案是通过密码安全加密存储、证书认证、RBAC等机制保证其安全性。
          - 复杂业务场景的拓展性怎么保证：etcd的 TokenProvider 和RBAC扩展机制，都具备较好的扩展性

          - TX机制：mini-transactions机制
          - 选举机制：
          - "***etcd 的 leader 选举的过程？etcd 如何实现配置下发和服务发现？etcd 怎么选主？***"
          - (lease)
          - Proxy 模式 (etcd 作为一个反向代理把客户的请求转发给可用的 etcd 集群)
          - Linearizable Read，snapshot 机制，WAL 的存储与回放
          - MVCC机制：ETCD如何实现的 watch？mvcc & streaming watch (bptree, mmap) # [一文了解 etcd watch 实现](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=**********&idx=3&sn=43bcb53845660775f73f03fa359865a6)
          - 02 基础架构：etcd一个读请求是如何执行的？.md
          - 03 基础架构：etcd一个写请求是如何执行的？.md
          - 04 Raft协议：etcd如何实现高可用、数据强一致的？.md
          - 05 鉴权：如何保护你的数据安全？.md
          - 06 租约：如何检测你的客户端存活？.md
          - 07 MVCC：如何实现多版本并发控制？.md
          - 08 Watch：如何高效获取数据变化通知？.md
          - 09 事务：如何安全地实现多key操作？.md
          - 10 boltdb：如何持久化存储你的key-value数据？.md
          - 11 压缩：如何回收旧版本数据？.md

          - EtcdServer在收到Raft库通过Ready channel通知的可以持久化数据之后，都做了什么操作？

      - topic: ETCD 使用
        url: https://etcd.io/docs/v3.5/learning/api/ # [核心 API 参考文档 | etcd官方文档中文版](https://doczhcn.gitbook.io/etcd/index-1)
        qs:
          - etcd API (Put()/Delete(), Get(), Watch(), Transactions(), Leases(Grant/Revoke/KeepAlive))

      - topic: ETCD 运维
        qs:
          # [etcd 的性能怎么样？需要优化吗？](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651441737&idx=2&sn=84d797acc722fdc23c67678aa2d875be)
          - "***How to optimize etcd?***" # 拆解一下很简单，就是实现和使用两方面（也就是服务端和客户端），使用方面没啥意思（比如不要创建大量lease以及不要频繁修改kv）。实现方面无非是raft和botldb，raft没有什么优化点，所以就集中在boltdb上（具体如下）
          #etcd 的服务端优化
          #
          #- *etcd 对 cpu，内存，磁盘要求很高，至少要 4C8G 的独立宿主机部署。通过调节不同的 batch size 和 interval，使我们可以根据不同硬件和工作负载优化性能*
          #- 内存索引层。由于索引层大量使用锁机制同步对性能影响很大，通过优化锁的使用，提升了读写性能。
          #- lease 规模化使用。lease 是 etcd 支持 key 使用 ttl 过期的机制。在之前的版本中 scalability 较差，当有大量 lease 时性能下降的较为严重，通过优化 lease revoke 和过期失效的算法，解决了 lease 规模性的问题。
          #- 后端 boltdb 使用优化。etcd 使用 boltdb 作为底层数据库存储 kv，对整体性能影响很大。
          #
          #etcd 的客户端优化
          #
          #- *性能优化除了服务端要做的事情外，还需要客户端的帮助。保持客户端使用最佳实践将保证 etcd 集群稳定高效地运行*，这里我们分享 3 个最佳实践
          #- put 数据时避免大的 value, 大的 value 会严重影响 etcd 性能，例如：需要注意 Kubernetes 下 crd 的使用；
          #- 避免创建频繁变化的 key/value, 例如：Kubernetes 下 node 数据上传更新；
          #- 避免创建大量 lease 对象，尽量选择复用过期时间接近的 lease, 例如 Kubernetes 下 event 数据的管理。

          - raft 层：raft 是 etcd 节点之间同步数据的基本机制，它的性能受限于网络 IO、节点之间的 rtt 等，WAL 受到磁盘 IO 写入延迟
          - 存储层：负责持久化存储底层 kv, 它的性能受限于磁盘 IO，例如：fdatasync 延迟、内存 treeIndex 索引层锁的 block、boltdb Tx 锁的 block 以及 boltdb 本身的性能

          - 一致性：为什么基于Raft实现的etcd还会出现数据不一致？

          - db大小：为什么etcd社区建议db大小不超过8G？（启动耗时、节点内存、treeIndex索引性能、boltdb性能、集群稳定性、快照）
          #  首先是启动耗时。etcd启动的时候，需打开boltdb db文件，读取db文件所有key-value数据，用于重建内存treeIndex模块。因此在大量key导致db文件过大的场景中，这会导致etcd启动较慢。
          #  其次是节点内存配置。较大db文件会导致etcd依赖更高配置的节点内存规格，etcd通过mmap将db文件映射到内存中。etcd启动后，正常情况下读etcd过程不涉及磁盘IO，若节点内存不够，可能会导致缺页中断，引起延时抖动、服务性能下降。
          #  接着是treeIndex索引性能。因etcd不支持数据分片，内存中的treeIndex若保存了几十万到上千万的key，这会增加查询、修改操作的整体延时。当treeIndex中含有百万级key时，在treeIndex中搜索指定范围的key的开销是不能忽略的，此开销可能高达上百毫秒。
          #  然后是boltdb性能。大db文件场景会导致事务提交耗时增长、抖动。当db文件过大后，boltdb本身连续空闲页的申请、释放、存储都会存在一定的开销。etcd社区已通过新的freelist管理数据结构hashmap对其进行优化，将时间复杂度降低到了O(1)，同时支持事务提交时不持久化freelist，而是通过重启时扫描page重建，以提升etcd写性能、降低db大小。
          #  再次是集群稳定性。大db文件场景下，无论你是百万级别小key还是上千个大value场景，一旦出现expensive request后，很容易导致etcd OOM、节点带宽满而丢包。
          #  最后是快照。当Follower节点落后Leader较多数据的时候，会触发Leader生成快照重建发送给Follower节点，Follower基于它进行还原重建操作。较大的db文件会导致Leader发送快照需要消耗较多的CPU、网络带宽资源，同时Follower节点重建还原慢。大db文件意味着更长的备份时间，而更长的只读事务则可能会导致db文件增长。同时Leader发送快照与Follower基于快照重建都需要较长时间，在集群写请求较大的情况下，可能会陷入死循环，导致落后的Follower节点一直无法追赶上Leader。

          - 延时：为什么你的etcd请求会出现超时？
          - 内存：为什么你的etcd内存占用那么高？
          - 性能及稳定性：如何优化及扩展etcd性能？
          - 实战：如何基于Raft从0到1构建一个支持多存储引擎分布式KV服务？
          - Kubernetes基础应用：创建一个Pod背后etcd发生了什么？
          - Kubernetes高级应用：如何优化业务场景使etcd能支撑上万节点集群？
          - 分布式锁：为什么基于etcd实现分布式锁比Redis锁更安全？
          - 配置及服务发现：解析etcd在API Gateway开源项目中应用
          - 选型：etcd_ZooKeeper_Consul等我们该如何选择？
          - 运维：如何构建高可靠的etcd集群运维体系？

          - “Raft日志复制确保了etcd多节点间的数据一致性，我通过一个etcd日志复制整体流程图为你详细介绍了etcd写请求从提交到Raft模块，到被应用到状态机执行的各个流程，剖析了日志复制的核心原理，即一个日志条目只有被Leader同步到一半以上节点上，此日志条目才能称之为成功复制、已提交。Raft的安全性，通过对Leader选举和日志复制增加一系列规则，保证了整个集群的一致性、完整性。”
          - "***基于Raft实现的etcd还会出现数据不一致的核心原因？怎么避免产生数据不一致问题呢？***"
          # 核心原因：Apply到MVCC的这步，ApplyEntryNormal里没有做失败时的重试机制。“etcd数据不一致的核心原因：Raft算法只能保证各个节点日志同步的一致性，但Apply流程是异步的，它从一致性模块获取日志命令，应用到状态机的准确性取决于业务逻辑，这块是没有机制保证的。同时，defrag等运维管理操作，会直接修改底层存储数据，异常场景处理不严谨也会导致数据不一致。”
          # 解决方案：1、开启etcd的数据毁坏检测功能。2、应用层的数据一致性检测。3、定时数据备份。4、良好的运维规范（比如使用较新稳定版本、确保版本一致性、灰度变更）。

          - etcd为什么需要对db增加Quota限制？为什么不建议etcd集群db大小超过8G呢？ # db过大会导致启动变慢、

          - 过大的db文件对集群性能和稳定性有哪些影响？

          - 使用etcd过程中，你遇到了哪些案例导致了etcd db大小突增呢？ 它们的本质原因是什么呢？

          - 为啥etcd请求超时了？ # 这点还是得结合etcd读写操作来看
          # 网络质量，如节点之间RTT延时、网卡带宽满，出现丢包；
          # 磁盘I/O抖动，会导致WAL日志持久化、boltdb事务提交出现抖动，Leader出现切换等；
          # expensive request，比如大包请求、涉及到大量key遍历、Authenticate密码鉴权等操作；
          # 容量瓶颈，太多写请求导致线性读请求性能下降等；
          # 节点配置，CPU繁忙导致请求处理延时、内存不够导致swap等。

          - 为啥etcd内存占用突然飙高，可能存在哪些问题？
          # 首先是raftLog。为了帮助slow Follower同步数据，它至少要保留5000条最近收到的写请求在内存中。若你的key非常大，你更新5000次会产生较大的内存开销。

          #  其次是treeIndex。 每个key-value会在内存中保留一个索引项。索引项的开销跟key长度、保留的历史版本有关，你可以通过compact命令压缩。
          #
          #  然后是boltdb。etcd启动的时候，会通过mmap系统调用，将文件映射到虚拟内存中。你可以通过compact命令回收旧版本，defrag命令进行碎片整理。
          #
          #  接着是watcher。它的内存占用跟连接数、gRPC Watch Stream数、watcher数有关。watch机制一个不可忽视的内存开销其实是事件堆积的占用缓存，你可以通过相关metrics及时发现堆积的事件以及slow watcher。
          #
          #  最后我介绍了一些典型的场景导致的内存异常，如大包查询等expensive request，etcd中存储了v2 API写入的key， goroutines泄露以及etcd lease bug等。

      - topic: temp
        qs:
          - 如何确定 etcd Cluster 的节点数？
          - Store问题
          - 什么需要Snapshot快照
          - WAL问题
          - WAL具体的结构？
          - etcd 实现的 Raft 算法性能如何？
          - 视图问题
          - 逻辑视图
          - 物理视图
          - Proxy 模式取代 Standby 模式的原因？
  topics:
    - topic: 【技术选型】注册中心
      des: Compare (etcd, consul, zk, nacos, eureka) 这些组件都能实现服务发现、pubsub(消息发布与订阅)、负载均衡、分布式通知与协调、分布式锁、分布式队列、集群监控与 Leader 竞选，所以也应该从这些aspects进行比较
      table:
        - name: etcd
          CAP类型: CP
          服务发现: true # 基于键值存储
          pubsub: true # 有限支持（通过Watch机制）
          负载均衡: false # 需客户端实现
          分布式通知与协调: true # Watch机制
          分布式锁: true # 基于租约
          分布式队列: false # 需自行构建
          集群监控: true # 基础监控（metrics接口）
          Leader竞选: true # 原生支持（选举API）
          一致性协议: Raft
          Watch机制: "基于键值变化的长轮询监听，支持前缀监听"
          存储结构: "键值对（KV存储）"
          健康检查机制: "依赖外部工具（无内置）"
          数据持久化: "持久化到磁盘（WAL日志+快照）"
          多数据中心支持: false

        - name: consul
          url: https://github.com/hashicorp/consul
          CAP类型: CP
          服务发现: true # 强支持（健康检查+DNS/HTTP接口）
          pubsub: true # 有限支持（事件系统）
          负载均衡: true # 内置（通过Fabio等工具链）
          分布式通知与协调: true # 会话和KV存储
          分布式锁: true # 会话机制
          分布式队列: false # 需自行构建
          集群监控: true # 完善（服务健康监控）
          Leader竞选: true # 需通过会话机制实现
          一致性协议: Raft
          Watch机制: "基于HTTP长轮询或事件系统，支持阻塞查询"
          存储结构: "键值对（KV存储）"
          健康检查机制: "主动检查（HTTP/TCP/gRPC）+被动心跳"
          数据持久化: "持久化到磁盘（支持多后端存储）"
          多数据中心支持: true

        - name: zk # 虽然一些新的分布式协调系统如 etcd 和 consul 的性能和功能更好，但是 zk 作为 java 生态中的一环，使用还是非常广泛的，但是在比较新的项目里，很少使用 zk，如果还不了解的话，简单了解一下基础概念和使用就可以了，不需要深入了解使用
          url: https://github.com/apache/zookeeper
          CAP类型: CP
          服务发现: true # 临时节点机制
          pubsub: true # Watch机制
          负载均衡: false # 需客户端实现
          分布式通知与协调: true # Zab协议
          分布式锁: true # 临时顺序节点
          分布式队列: true # 顺序节点实现
          集群监控: true # 基础（节点状态监控）
          Leader竞选: true # 原生选举机制
          一致性协议: ZAB
          Watch机制: "基于Znode的一次性监听，需反复注册"
          存储结构: "层次化Znode树（类似文件系统）"
          健康检查机制: "会话心跳（临时节点自动删除）"
          数据持久化: "持久化到磁盘（事务日志+快照）"
          多数据中心支持: false

        - name: Nacos
          url: https://github.com/alibaba/nacos
          CAP类型: 默认AP（支持切换CP模式）
          服务发现: true # 动态服务管理
          pubsub: true # 配置变更推送
          负载均衡: true # 客户端SDK
          分布式通知与协调: true # 有限支持（配置监听）
          分布式锁: false # 需扩展实现
          分布式队列: false
          集群监控: true # 完善（服务健康监测）
          Leader竞选: false
          一致性协议: "Raft（CP模式）/自研协议（AP模式）"
          Watch机制: "基于UDP推送或长轮询，支持配置/服务变更监听"
          存储结构: "独立服务元数据+配置数据（支持多格式）"
          健康检查机制: "主动探测+心跳上报（支持TCP/HTTP/MYSQL）"
          数据持久化: "支持嵌入DB（Derby）或外接MySQL"
          多数据中心支持: true

        - name: eureka
          url: https://github.com/Netflix/eureka
          CAP类型: AP（牺牲强一致性）
          服务发现: true # AP设计
          pubsub: false
          负载均衡: true # 需配合Ribbon
          分布式通知与协调: true # 有限支持（服务心跳机制）
          分布式锁: false
          分布式队列: false
          集群监控: true # 基础（实例状态）
          Leader竞选: false
          一致性协议: "无协议（最终一致性）"
          Watch机制: "无原生Watch，依赖客户端定时拉取"
          存储结构: "内存注册表（无持久化）"
          健康检查机制: "客户端心跳上报（服务端被动接收）"
          数据持久化: "仅内存（重启数据丢失）"
          多数据中心支持: false
      qs:
        - "***怎么理解“AP适合做注册中心，CP适合做配置中心”？***"


    - url: https://github.com/apache/zookeeper
      qs:
        - 聊聊zk的zab协议、node存储模型、watcher机制
        - zk 如何保证 ap？etcd 如何保证 cp？如果想要在 zk 上实现 cp 应该如何处理？
        - zk的顺序一致性是啥? # [什么是顺序一致性(Sequential Consistency) | 三点水](https://lotabout.me/2019/QQA-What-is-Sequential-Consistency/)


    - topic: 服务发现组件 基本认知
      qs:
        - "***“服务注册”和“服务发现”的具体流程？***" # [微服务注册中心设计原理与Golang实现](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247497005&idx=1&sn=f147d1aaeebdfe075946593f5765d9b9)
        #  > 服务注册（ 针对服务端，服务启动后需要注册，分为三个阶段：）
        #
        #  - 启动注册：当一个服务节点启动后，需要把自己注册到 `Service Registry` 上，以便其他节点来发现自己。注册需要在服务启动完成，并可以接收请求时才去注册自己，并且会设置有效期，防止进程异常退出后依然被访问
        #  - 定时续期：定时续期相当于 `keep alive`，定期告诉 `Service Registry` 自己还在，还能够继续服务
        #  - 退出撤销：进程退出时，我们应该主动撤销注册信息，以便调用方及时将请求分发到别的节点 (同时，go-zero 通过自适应的负载均衡来保证即使节点退出没有主动注销，也能及时摘除该节点。)
        #
        #  > 服务发现（服务发现是针对调用端的）
        #
        #  - 存量获取：`Service B`从 `Service Registry` 获取已有节点列表，然后根据自己的负载均衡算法选择合适的节点，发送请求
        #  - 增量侦听：通知新增节点
        #  - 应用`服务发现`故障：发生故障后，以 etcd 为例，遇到 etcd 故障时，我们就需要冻结 `Service B`的节点信息，不修改不清空，(gozero 会自动隔离和恢复故障节点)
