---



# TODO k8s教程
#- url: https://github.com/ahmetb/kubernetes-network-policy-recipes
#  des: k8s network相关的一些deployment.yml示例
#- url: https://github.com/wuYin/k8s-in-action
#  des: 可以作为k8s syntax手册使用，需要时直接查。其实只需要知道每个key大概是啥意思，会修改就行了。





# TODO
#- url: https://github.com/p8952/bocker
#  des: 这个有点意思了。【学习docker实现的最佳资料】。仅用百行 shell 实现的 Docker。该项目通过大约 100 行 Bash 代码实现了 Docker 的基本功能，适合用于学习如何使用简单的脚本语言实现容器的核心功能。


#- url: https://github.com/docker/awesome-compose
#- url: https://github.com/laradock/laradock
#  des: 这个repo本身是为了搭建php开发环境，但是实际上



# TODO 比较istio和linkerd的CPU benchmark，怎么设计具体测试流程？ # [Istio和Linkerd的CPU基准测试报告](https://mp.weixin.qq.com/s/bry4g11lacH1eyuh5uVcHw)





- type: k8s
  tag: ms
  score: 5
  using:
    url: https://github.com/kubernetes/kubernetes
    doc: https://kubernetes.io/zh-cn/docs/home/
    sub:
      - url: https://github.com/cri-o/cri-o
        des: k8s CRI
      - url: https://github.com/opencontainers/runc
        des: runc = run container
  repo:
    - url: https://github.com/helm/helm
      doc: https://helm.sh/docs/
      score: 5
      des: 当一个应用包含了很多微服务时，手动在k8s集群中部署、升级、回滚这些微服务是一件非常复杂的工作，所以我们使用helm来进行服务编排。
      record:
        - 【2025-07-14】移除了【komodorio/helm-dashboard】 # A Better UI for Helm. Helm-Dashboard 是 ValidKube 之后 Komodor 的第二个开源项目，Komodor 的愿景是通过构建有助于理解分布式云原生系统引入的复杂性的工具，使k8s的操作和故障排除变得简单易行，Helm-Dashboard 就是实现该愿景的一个步骤之一。Helm Dashboard 插件提供了一种 UI 驱动的方式来查看已安装的 Helm Chart、查看其修订历史记录和相应的 K8s 资源。此外，你还可以执行简单的操作，例如回滚到修订版或升级到新版本。总结：helm-dashboard涵盖主流功能和最常见的使用场景，这样只有最特殊的情况下才会手动使用 Helm CLI，这可以留给经验更丰富的 DevOps 或 SRE 人员。此外 Helm-Dashboard 还集成了一些额外的功能，并希望它能激发社区用户集成更多有用的工具。例如，安全和漏洞扫描也是改善 Helm 操作和整体可靠性的重要事情，因此我们集成了两个比较火热的工具：Aqua Security 的 Trivy 和 Bridgecrew 的 Checkov。
        - 【2025-07-14】给【helm】、【helmfile】、【chart-releaser】、【helm-diff】都写了taskfile里的task，所以也就移除掉了。
      qs:
        - 在Helm中，有Chart、Repository和Release三大基本概念。Chart 代表一个Helm包，里面包含了运行Kubernetes应用需要的所有资源定义YAML文件；Repository是Chart仓库，用来存放和共享 Helm Chart；Release是运行在 Kubernetes 集群中的 Chart 的实例。 # 从repo下载chart，然后把chart部署为release（到k8s集群中）




    # TODO [yuyatinnefeld/istio: 🚀 Hands-On Project 🚀 | Service Mesh with Istio](https://github.com/yuyatinnefeld/istio)
    - url: https://github.com/istio/istio
      doc: https://istio.io/latest/docs/
      score: 5
      des: 【Istio】Istio 是 ServiceMesh 的产品化落地。起到两个作用：帮助微服务之间建立连接，可以更好地管理和监控微服务。帮助微服务分层解耦，解耦后的 proxy 层能够更加专注于提供基础架构能力。Istio采用的方法是暴露和追踪应用程序行为，而不需要接触任何一行代码。这得益于“sidecar”（边车）概念，它是一个与我们的应用程序并肩运行的容器，并向一个中央遥测组件提供数据。由于能够识别正在使用的协议(redis、mongo、http、grpc等)，sidecar能够捕获许多关于请求的信息。sidecar可以理解为跟班（而非插件），用来提高可观测性（相比于普通ms）。
      record:
        - 【2025-07-14】移除【Envoy（数据面代理）】、【mosn/mosn（没有控制面，只有数据面）】
        - 【2025-07-14】移除掉“【技术选型】ServiceMesh（比较项：开销、流量管理、零信任安全、多集群/多云支持、可观测性、多租户能力、适用场景）”，所以也移除【Linkerd】、【Cilium Service Mesh】、【kuma】这三个不是那么主流的ServiceMesh方案。【Linkerd】虽然轻量化安装便捷，但其功能覆盖范围较窄（如缺乏复杂流量拆分、多租户隔离），难以满足中大型企业全场景需求，市场份额（~22%）远低于 Istio。【CSM】创新性 eBPF 架构虽能免去 Sidecar 开销，但强制依赖 Linux 内核 ≥5.8 且配置复杂度高，实际生产落地门槛显著（当前采用率仅 ~12%），仅适合特定性能敏感场景。【Kuma】尽管在多集群/混合云管理上表现优异，但其社区生态和行业渗透率最低（~7%），且功能与 Istio 高度重合却缺乏云厂商深度支持，性价比较弱。
        - 【2025-07-15】移除除了4个同时支持南北向和东西向流量的组件之外的其他组件（【Caddy】、【Traefik】、【Nginx】、【MetalLB】、【Pingora（与依靠lua或者openresty的主流gateway（比如nginx, nginx-ingress, apisix, kong等）不同，pingora直接写rust代码来构建module（相比之下，没有额外的学习成本））】、【OpenResty】、【Easegress】、【Tyk】、【Portkey AI Gateway】）。这里还需要澄清一个认知，
        - 【2025-07-15】移除了【easegress】、【apisix】之类的几个业务网关。有需要再加回来，暂时来看是不需要的。业务网关都属于南北向，用来处理流量入口的相关业务，可以简单理解为之前在单体时代，我们把部分前置中间件，从gateway的后面，前移到了gateway里面，比如OpenResty这种我们写lua脚本来实现相关功能的。另外，这种业务网关相较于通用网关的核心优势在于“通过声明式 Pipeline 实现业务逻辑的“配置化”表达，以分钟级热更新支撑高频业务迭代，而 Istio 需依赖代码级 Wasm 扩展且变更需基础设施层滚动重启。”。当然，我这里并非非要证明通用网关就是比业务网关好，只不过确实通用网关本身就能满足70%的类似需求，暂时也确实用不到。
      rel:
        - url: https://github.com/caddyserver/caddy
          doc: https://authp.github.io/docs/intro
          des: Automatic SSL
          record:
            - 【2025-07-04】移除【caddy-docker-proxy（caddy官方image需要手动维护 Caddyfile，就有点麻烦了。简化了 Caddy 与 Docker 的集成。通过扫描 Docker 容器的标签，此插件可以自动生成并动态更新 Caddyfile，从而实现无停机时间的自动代理配置。这使得管理大量 Docker 容器变得更加容易和高效，而无需手动更新 Caddyfile。）】其实没啥意思，还是官方image更靠谱。

        #- name: Nginx
        #  内存模型: nginx内存池+glibc的malloc内存分配
        #  网络模型: epoll ET模式
        #  线程模型: nginx的多线程只能用在aio模型中对本地文件的操作上，正常请求还是使用epoll模式进行处理2
        #  进程模型: 多进程 master-worker
        #  性能: 优秀，生产环境验证
        #  可扩展性: 通过负载均衡器水平扩展
        #  易用性: 配置较复杂
        #  功能: 基础路由，负载均衡，缓存
        #  集成性: 广泛支持，微服务需额外工具
        #  成本: 开源，另有企业版
        #  社区支持: 非常强大
        #- url: https://github.com/BeyondXinXin/nixvis
        #  des: Nginx 网站日志分析工具
        #- url: https://github.com/NginxProxyManager/nginx-proxy-manager
        #  des: Docker container for managing Nginx proxy hosts with a simple, powerful interface.
        #- url: https://github.com/nginx-proxy/nginx-proxy
        #  des: 【印象中好像还有个类似工具？】为 Docker 容器自动配置 Nginx 反向代理。该项目可以自动为 Docker 容器提供 Nginx 反向代理服务。它能够实时监听 Docker 容器的启动和停止事件，自动为每个 Docker 容器配置 Nginx 反向代理，无需手动干预，极大简化了容器环境下的 Nginx 配置流程。
        #- url: https://github.com/russelltao/geektime-nginx
        #  des: 陶辉的geektime的nginx课程，对应的源码，里面提供了不少nginx.conf，仅供参考
        #- url: https://github.com/JonasAlfredsson/docker-nginx-certbot
        #  des: 用 let's encrypt 实现对nginx的自动加HTTPS证书。那我为啥不用caddy呢？
        - url: https://github.com/nginx/nginx
          topics:
            - topic: Nginx架构
              qs:
                - "***工作原理/运行机制：nginx 可以负载千万级并发连接 (C10M)、百万级 QPS。使用`内事请`这些机制的服务很多，为啥 nginx 支持高并发? How does nginx works? 概述 nginx 的内存池、进程模型、线程模型和网络模型？***" # (master->conf, control worker(restart when exception)), (worker->handle request using reactor, accept_mutex)
                # 总的来说，*基于`master-worker`的多进程设计 + 每个 worker 进程都支持多线程 + 每个线程通过`主从reactor多线程模型`开子线程*
                #假设进程数 M，每个 worker 的线程数 N，每个线程的子线程数 L，那么开一个 master 进程，就可以处理`M*N*L`个请求
                #
                #- `内存池`
                #- `非阻塞epoll的事件驱动架构`
                #- `请求的多阶段异步处理`（异步）
                #- `管理进程`和`多工作进程设计`？？？
                #- `模块化设计`，良好的扩展性，可以通过模块方式进行功能扩展 (五大类型：*`核心模块`、`配置模块`、`事件模块`、`HTTP模块`、`mail模块`*)
                # *由于 nginx 的`进程架构`，可以充分利用多核 CPU*。Nginx 充分利用了分时操作系统的特点，比如增加 CPU 时间片、提高 CPU 二级缓存命中率、用异步 IO 和线程池的方式回避磁盘的阻塞读操作等等，只有清楚了 Nginx 的这些招数，你才能将 Nginx 的性能最大化发挥出来。

                - nginx 是怎么处理请求的？请详细说说

                - multi-process(master-worker) + reactor(worker process), thread-pool, network model(epoll(ET))

                - nginx 使用的`master-worker进程管理模型`是什么？ # 概括来说，*master 进程负责接收请求并队列化，然后转发给 worker 进程，让 worker 进程处理具体请求*。在 nginx 启动过程中，主进程在启动各个 worker 进程之后，就会进入一个无限循环中，以处理客户端发送过来的控制指令；而 worker 进程则会进入一个循环中，从而不断接收客户端的连接请求以及处理请求
                - worker 进程的主要工作？ # 1、*nginx 的请求由 worker 进程管理*，从 master 进程获取到 socket 之后，接收 socket 的连接请求，然后对请求根据 nginx 配置进行处理。2、*nginx 的 worker 进程，为了应对高并发场景，使用`Reactor模型`*


            - topic: Nginx使用
              qs:
                # [Nginx服务器性能优化的三大方面 - Linux大神博客](https://www.linuxdashen.com/nginx%E6%9C%8D%E5%8A%A1%E5%99%A8%E6%80%A7%E8%83%BD%E4%BC%98%E5%8C%96)
                # [Nginx配置调优](http://liuqh.icu/2019/03/28/service/nginx-optimization/)  # 1、降费就是压缩数据。2、增效就是线程模型使用多路复用，TCP 层面优化长连接，IO 层面使用 aio，以及其他机制，尽量并行增加连接数。3、超时限制就是设定阈值，避免 TIME_WAIT。
                - "***【优化nginx配置】主要是优化进程线程/TCP/IO 等机制，从 增效、降费 和 超时限制 三个方面入手。具体有哪些优化项？***"

                - (process, TCP, IO, OCSP stapling, Strict SNI,) # [Nginx SSL/TLS 配置优化](https://blog.lv5.moe/p/nginx-ssl-tls-configuration-optimization)
                - dynamic modules
                - "***nginx security? (SELinux, iptables, delete useless nginx modules, async-about)***"
                #- 配置 SELinux
                #- 通过分区挂载，允许最少特权
                #- 配置/etc/sysctl.conf 强化 Linux 安全
                #- 删除所有不需要的 Nginx 模块
                #- 基于 Iptables 防火墙的限制
                #- 控制缓冲区溢出攻击
                #- 控制并发连接
                #- 限制可用的请求方法
                #- 如何防止图片盗链
                #- 在防火墙限制每个 IP 的连接数

                - k8s 给容器配置 CPU 的 limit 为 2 时，容器其实并不是真的分配了 2 个 CPU，而是通过 cgroup 进行限制; 而容器内的 nginx 无法正确读取到 worker 数，所以需要经过如下配置才能正确读取。所以，如何解决容器中nginx worker process自动设置的问题? # [如何解决容器中 nginx worker process 自动设置的问题](https://ieevee.com/tech/2020/05/11/nginx-process-auto.html) worker_processes, /sys/devices/system/cpu/online, lxcfs

                - "***nginx使用规范***" # [Nginx管理维护运维规范 | 运维小弟](https://srebro.cn/archives/1733155340544)

            # [个人常用 NGINX 配置片段速查 - Harry Chen's Blog](https://harrychen.xyz/2023/05/17/nginx-config-snippets/)
            - topic: nginx配置
              qs:
                - nginx默认最优配置？
                - nginx怎么设置HTTPS? 并且开启HTTPS强制跳转？
                - 带缓存的反向代理
                - 客户端限制：怎么限制请求数？怎么限制连接数？
                - 静态路径映射：恒等映射、非恒等映射

                - 怎么在docker环境下自动更新nginx的let's encrypt证书? # [Docker 环境下自动更新 Let’ s Encrypt SSL 证书 · Ruby China](https://ruby-china.org/topics/38061)
                - nginx keepalive_requests配置项导致TCP连接数过多，怎么解决 # [Nginx keepalive_requests 踩坑总结 · Issue #37 · jinhailang/blog](https://github.com/jinhailang/blog/issues/37)

        - url: https://github.com/kubernetes/ingress-nginx
          des: gateway. ingress策略+ingress controller结合之后实现一个完整的ingress LB，使用ingress进行LB时，ingress controller基于ingress规则将客户端请求直接转发到service对应的后端pod上，从而跳过kube-proxy的转发功能，kube-proxy不再起作用，全过程为ingress controller + ingress规则 ---> services

      topics:

        # TODO [2025-06-06] 可能要思考一下 是否要添加 内存模型、网络模型、线程模型、进程模型。这几个key作为比较项。
        - topic: gateway 技术选型
          isX: true
          table:
            - name: Kong
              url: https://github.com/Kong/kong # 基于 OpenResty 实现的，Nginx需要搭配OpenResty才更适合在微服务架构常用的灵活场景下使用，也就是把lua直接嵌入nginx，来动态拓展nginx的功能。而kong则可以看作是把OpenResty的功能插件化了，提供了60多种插件。
              性能: 良好，基于 OpenResty
              可扩展性: 企业级设计，水平扩展
              易用性: 中等，声明式配置
              功能: 丰富插件，数据库支持
              集成性: 支持微服务，Kubernetes 入口控制器
              成本: 开源核心，企业功能需付费
              社区支持: 强大，商业支持

            - name: Linkerd
              url: https://github.com/linkerd/linkerd2 # service-mesh的请求多路转发、链路追踪. v1 is implemented based on scala, v2 is refactored with golang.
              性能: 有额外开销但优化良好
              可扩展性: 适合微服务，大规模集群
              易用性: CRD 配置，初学者较复杂
              功能: 服务网格：mTLS、追踪、指标、网关
              集成性: 与 Kubernetes 集成优秀
              成本: 开源免费
              社区支持: 服务网格领域较强
          qs:
            - nginx 这种web服务器跟微服务的gateway有啥区别？二者各自的侧重点在什么地方？为啥nginx这些web server通常不作为ms的gateway使用？（微服务 Gateway 需要更细粒度的控制和更丰富的功能（如服务发现、动态路由、统一认证等）、）


            - 揭秘Nginx中如何使用红黑树优化数据操作 # [揭秘Nginx中如何使用红黑树优化数据操作-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/2486708)

            - Apache 和 Nginx 性能差异的原因 # “当一个系统保持了 n 个网络连接的时候，传统的 select(2) 和 poll(2) 的复杂度都是 O(n)，而 Apache 在对 TCP 连接和进程进行匹配的时候甚至能搞出 O(n^2) 的超高复杂度，而 epoll 的复杂度只有 O(log n)。”
            #  epoll 和 prefork 的优劣势对比 # apache使用prefork
            #  优势
            #  Nginx 每个 worker 进程可以高效地处理上千个 TCP 连接，同时消耗较少的内存和 CPU 资源。这使得单台服务器能够承载比 Apache 多两个数量级的用户量，相较于 Apache 单机 5K 的 TCP 连接数上限（对应于 2000 个在线用户），这是一个巨大的进步。
            #
            #  Nginx 对 TCP 的复用使其非常擅长应对海量客户端的直接连接。根据实际测试，在 HTTP 高并发环境下，Nginx 的活跃 TCP 连接数仅为 Apache 的五分之一，并且随着用户量的增加，复用效果更加显著。
            #
            #  在架构上，基于 FastCGI 网络协议进行架构扩展，可以更轻松地利用多台物理服务器的并行计算能力，从而提升整个系统的性能上限。



        - topic: 服务网格
          des: "***有哪些主流k8s服务网格工具? 请比较***"
          qs:
            - 服务网关里的流量网关和业务网关分别是啥? gateway = 路由转发+过滤器 # 这块的“路由转发”就是“流量网关”，“过滤器”就是“业务gateway”。可以理解为整个整个集群的前置中间件，把权限校验、限流、监控、API 日志收集等功能写到过滤器里。
            #            - 流量网关通常只专注于全局的 Api 管理策略，比如全局流量监控、日志记录、全局限流、黑白名单控制、接入请求到业务系统的负载均衡等，有点类似防火墙。Kong 就是典型的流量网关。
            #            - 业务网关就是权限控制、日志输出、数据加密、熔断限流等每个微服务应用都需要的通用服务。
            - What's service mesh? keypoint of service mesh? How to implement?
            - Why does ms need gateway?
            - sidecar (sidecarless(cilium), ambient(istio))




        #- [从一个例子入手 Istio -](https://www.luozhiyun.com/archives/393)
        #- [1.深入 Istio：Sidecar 自动注入如何实现的？ -](https://www.luozhiyun.com/archives/397)
        #- [2.深入 Istio 源码：Pilot 服务发现 -](https://www.luozhiyun.com/archives/401)
        #- [3.深入 Istio 源码：Pilot 配置规则 ConfigController -](https://www.luozhiyun.com/archives/403)
        #- [4.深入 Istio 源码：Pilot 的 Discovery Server 如何执行 xDS 异步分发？ -](https://www.luozhiyun.com/archives/408)
        #- [5.深入 Istio 源码：Pilot-agent 作用及其源码分析 -](https://www.luozhiyun.com/archives/410)

        #- [Istio 分层架构？80% 的人有误解](https://mp.weixin.qq.com/s/1xoZounMG-azUqP564oppA)
        #- [Istio 监控详解](https://mp.weixin.qq.com/s/0NG85NnGSuGKxNUK1KdLNg)
        #- [基于 Go、gRPC 和 Protobuf 的微服务的 Istio 可观察性](https://mp.weixin.qq.com/s/sDMX7ICSasyzrFQVeK4vmw)
        #- [Istio 遥测和可观察性探索](https://mp.weixin.qq.com/s/2EJhxb5OapMIX5ExEs1J_Q)
        #- [Istio 流控，服务发现，负载均衡，核心流程是如何实现的？](https://mp.weixin.qq.com/s/LMtCFleBp1Si1iDHNbEBVQ)
        #- [Istio，灰度发布从未如此轻松！！！](https://mp.weixin.qq.com/s?__biz=MjM5ODYxMDA5OQ==&mid=2651962280&idx=1&sn=31e105ae7cc3ce20e9950ddfb020eea3&chksm=bd2d0e748a5a8762b16441fa7244f8ed79f33de74d158210a728cfcb1ee65010f2aa50c546ff&scene=21#wechat_redirect)
        #- [Istio 和 Linkerd 的 CPU 基准测试报告](https://mp.weixin.qq.com/s/bry4g11lacH1eyuh5uVcHw)
        - topic: Istio
          picDir: ms/service-mesh/istio
          qs:
            - "***Mixer 属性处理器 (Attribute, Adapter, Handler, Instance, Template)***"
            #  Attribute: mixer处理的一段数据。大多数情况下，这来自一个sidecar，但它也可以由adapter生成。Attribute在实例中用于将所需的数据映射到后端。
            #  Adapter: mixer组件中嵌入的逻辑，用于管理将数据转发到特定后端。“适配器”是一个附加到“Mixer”的处理程序，负责为后端调整属性数据。后端可以是对该数据感兴趣的任何外部服务。例如，监视工具(如Prometheus或Stackdriver)、授权后端或日志堆栈。
            #  Handler: Adapter的配置。由于适配器可以服务于多个用例，所以配置是解耦的，因此可以使用多个设置运行同一个adapter。
            #  Instance:  是将来自Istio的数据绑定到adapter模型的实体。Istio有一组由它的sidecar容器收集的统一属性。这些数据必须被翻译成后端语言。
            #  Template: 定义instance模板的公共接口。

            - Istio Arch? # Data plane(由网格内的 Proxy 代理和应用组成), Control plane(用于控制和管理数据平面中的 sidecar 代理)
            - Sidecar 代理是如何实现自动注入的？ # 由k8s Admission Controller实现，当为应用部署的命名空间打上特定的标签，Istio 会自动在新建的 Pod 中注入 Sidecar 容器。
            - Pilot 服务是如何进行服务发现的？ # Pilot 服务通过监听 k8s API Server来缓存 Istio 服务模型，并在服务模型更新时触发相关事件回调处理函数的执行。
            - Pilot 配置规则 ConfigController 是如何工作的？ # ConfigController 用于管理各种配置数据，包括用户创建的流量管理规则和策略。它通过监听 k8s API Server 中的配置规则资源，维护所有资源的缓存，并触发事件处理回调函数。
            - Pilot 的 Discovery Server 是如何执行 xDS 异步分发的？ # Discovery Server 接收来自 Envoy 端的 xDS 请求，从 Config Controller 和 Service Controller 中获取配置和服务信息，生成响应消息发送给 Envoy，并监听配置变化消息，将变化内容通过 xDS 接口推送到 Envoy。
            - Pilot-agent 的作用及其源码分析？ # Pilot-agent 负责启动 Envoy 代理，并提供健康检查、监视证书变化、Envoy 守护功能、通知 Envoy 优雅退出等功能。它通过生成 Envoy 的 Bootstrap 配置文件，监控证书变化，并在必要时重启 Envoy 来实现证书的热加载。

        - topic: Istio 使用
          qs:
            - 怎么用istio搭配prom收集数据？
    #            Istio的控制平面由几个不同部分组成，其中一个是Mixer。Mixer自身在Kubernetes里面又有两个不同的独立部署。一个叫做istio-policy，另一个叫istio-telemetry。就像它们的名字，这些组件负责提供控制策略和遥测数据收集功能。
    #
    #            应用pod的sidecar在发起每一个请求前调用istio-policy来进行前置条件检查，并在请求结束后发送遥测数据。sidecar本地缓存了一大批前置检查，使得大量的检查只需要通过缓存就能获得结果。此外，sidecar还对输出的遥测数据进行了缓存，以减少调用mixer的频率。
    #
    #            在Istio的控制平面上运行mixer是可选的，如果你不需要集中式的策略检查和遥测，那么你可以把这些组件彻底关掉。这些组件具有非常高的扩展性，并且能够在自定义资源配置中进行完整配置。如果不想涉及Istio配置过深，或者不想使用自己的后端基础设施去收集日志和遥测数据，而想完全采用默认值（stdio logs，Prometheus指标），你完全可以一点不操心这些。


    - url: https://github.com/projectcalico/calico
      doc: https://docs.tigera.io/calico/latest/about/
      score: 5
      record:
        - 【2025-07-14】移除【Canal（部署模式，不是独立插件）】、【CNI-Genie（管理框架，不直接提供网络）】、【Contrail】、【Knitter（核心在多网卡，底层依赖其他插件提供基础网络）】、【Multus（核心在多网络接口，不提供主网络）】、【Nodus（社区影响力和采用度相对较低，定位接近传统OVS/OVN方案）】这几个并非k8s网络插件的工具（没有提供主网络接口的独立或核心网络解决方案）。
        - 【2025-07-14】移除【Antrea】、【Weave Net】、【OVN-Kubernetes】这三个相对非主流的k8s网络插件。【Antrea】是基于OVS实现的高性能数据平面，适合 VMware 技术栈用户，提供企业级网络策略与安全审计。【Weave Net】则是【flannel】的类似服务，性能开销较大，单点故障风险高，不适合超大规模集群。【OVN-Kubernetes】服务于 OpenStack 生态整合，为复杂网络逻辑提供可编程能力。
        - 【2025-07-14】移除【Flannel】


    # TODO 打算用 containerd (+nerdctl) + minikube 代替。尝试一下nix安装。containerd相比Docker有更低的内存开销（内存占用只有Docker的1/3，大概是30MB vs 100MB），以及更快的容器启动，并且最为关键的与k8s天然集成。minikube则是k8s官方推荐的开发环境部署工具。

    # TODO [shuguangnet/dcoker_backup_script: 检测docker容器并自动备份挂载卷以及本地挂载目录](https://github.com/shuguangnet/dcoker_backup_script)

    - url: https://github.com/containerd/containerd
      score: 5
      rel:
        - url: https://github.com/containerd/nerdctl
          des: 相当于docker之于containerd. 给containerd提供了cli操作。高度兼容docker的所有核心操作。
        - url: https://github.com/containrrr/watchtower
          des: 怎么自动 fetch 容器的 latest image?
      record:
        - 【2025-06-24】移除所有docker的管理工具，比如【lazydocker】、【moncho/dry】、【dockly】这三个TUI工具，以及【dpanel】这个web工具。还是那句话“开发环境不如直接用goland内置工具，生产环境不需要这个，也不如Portainer或者rancher之类的方便直观好用。”。所以移除掉。
        - 【2025-07-14】移除【AliyunContainerService/image-syncer（阿里云开源的容器镜像同步工具，支持多个镜像仓库间的同步）】、【】
        - 【2025-07-14】移除【moby/moby】、【docker/compose】
        - 【2025-07-14】移除【Podman】。应该说，podman可以笼统地认为是介于docker和containerd之间的工具（具体来说，一方面他兼容docker cli，另一方面他和containerd定位类似）。Podman = 无守护进程 + 兼容 Docker CLI + 原生 Pod 支持。但是为啥移除呢？因为minikube, colima 这些都没有拿  Podman作为 runtime的，所以就有点四不像了，没什么使用和学习的价值，ROI略低。
      topics:
        - topic: 基本认知
          picDir: ms/docker
          qs:


    - url: https://github.com/kubernetes/minikube
      score: 5
      des: 内置了k8s dashboard，非常棒，可以完美替换掉colima+k8s dashboard，并且解决了很多colima的小毛病。
      topics:
        - topic: 【技术选型】k8s 开发环境部署
          picDir: ms/k8s/ops/dev-deploy
          table:
            - name: colima
              url: https://github.com/abiosoft/colima
              启动速度: 极快(容器方案)
              资源消耗: 约600MB(轻量)
              CNCF认证: false
              默认架构支持: x86, ARM64
              单节点支持: true
              多节点集群支持: false
              高可用支持: false
              附加功能: containerd/Docker双模式
              容器运行时: Docker, containerd
              网络支持: 基础网络(需手动配置)
              默认存储: Hostpath存储
              GPU加速: true # (需配置)
              适用场景: macOS/Linux容器开发

            - name: minikube
              url: https://github.com/kubernetes/minikube
              启动速度: 中(VM方案)
              资源消耗: 644MB(中等)
              CNCF认证: true
              默认架构支持: x86, ARM64, ARMv7, ppc64, s390x
              单节点支持: true
              多节点集群支持: true # 模拟支持
              高可用支持: false
              附加功能: 内置dashboard/ingress/metrics插件
              容器运行时: Docker, containerd, CRI-O
              网络支持: Calico/Cilium/Flannel多种选择
              默认存储: Hostpath存储
              GPU加速: true
              适用场景: 标准K8s学习与开发

            - name: orbstack
              url: https://github.com/orbstack/orbstack
              启动速度: 极快(macOS优化)
              资源消耗: 550MB(轻量)
              CNCF认证: false
              默认架构支持: x86, ARM64(M芯片优化)
              单节点支持: true
              多节点集群支持: false
              高可用支持: false
              附加功能: 系统集成优化
              容器运行时: containerd
              网络支持: 增强型网络
              默认存储: Hostpath存储
              GPU加速: true # (Apple M原生)
              适用场景: macOS开发环境优化

            - name: k3s
              url: https://github.com/k3s-io/k3s
              doc: https://k3s.io
              启动速度: 快(轻量设计)
              资源消耗: 512MB(轻量)
              CNCF认证: true
              默认架构支持: x86, ARM64, ARMhf
              单节点支持: true
              多节点集群支持: true
              高可用支持: true # (嵌入式)
              附加功能: 内置Traefik ingress
              容器运行时: CRI-O, containerd
              网络支持: Flannel/Traefik/CoreDNS
              默认存储: Hostpath存储, Longhorn
              GPU加速: true
              适用场景: 边缘计算/资源敏感环境

            - name: microk8s
              url: https://github.com/canonical/microk8s
              doc: https://microk8s.io
              启动速度: 中(完整K8s)
              资源消耗: 540MB(中等)
              CNCF认证: true
              默认架构支持: x86, ARM64, s390x, POWER9
              单节点支持: true
              多节点集群支持: true
              高可用支持: true # (自动配置)
              附加功能: 丰富插件系统
              容器运行时: containerd, kata
              网络支持: Calico/Cilium/Traefik/NGINX
              默认存储: Hostpath存储, OpenEBS, Ceph
              GPU加速: true
              适用场景: 本地开发与小型生产

            - name: k0s
              url: https://github.com/k0sproject/k0s
              doc: https://k0sproject.io
              启动速度: 极快(单二进制)
              资源消耗: 450MB(最轻量)
              CNCF认证: true
              默认架构支持: x86, ARM64
              单节点支持: true
              多节点集群支持: true
              高可用支持: true # (内置etcd)
              附加功能: 内置组件高度集成
              容器运行时: 内置CRI
              网络支持: 内置Calico
              默认存储: 内置本地存储
              GPU加速: true # (实验性)
              适用场景: 最小化K8s环境

            - name: kind
              url: https://github.com/kubernetes-sigs/kind

        - topic: 【技术选型】k8s 生产环境部署
          isFold: true
          picDir: ms/k8s/ops/deploy
          table:
            - name: kubeadm
              url: https://github.com/kubernetes/kubeadm
              doc:
              核心目标: 集群生命周期管理
              高可用支持: 基础(需额外配置LB)
              部署复杂度: 高(专家级配置)
              多云支持: 支持任何环境
              网络插件支持: 灵活选择(需手动配置)
              存储集成: 需自行集成
              自动化程度: 低(基于命令)
              安全特性: 标准K8s安全
              适用集群规模: 中小到大型
              最佳适用场景: 自定义程度高的核心生产环境

            - name: kubeasz
              url: https://github.com/easzlab/kubeasz
              doc: https://www.luozhiyun.com/archives/314
              核心目标: 自动化高可用部署
              高可用支持: 完整(多master+etcd集群)
              部署复杂度: 中(Ansible脚本)
              多云支持: 裸机/私有云/公有云VM
              网络插件支持: Calico/Flannel/Cilium等
              存储集成: NFS/CEPH/LocalPV等
              自动化程度: 高(全自动部署)
              安全特性: 增强(安全加固)
              适用集群规模: 大型集群
              最佳适用场景: 私有化环境标准化部署

            - name: kubespray
              url: https://github.com/kubernetes-sigs/kubespray
              doc:
              核心目标: 生产就绪集群
              高可用支持: 完整(基于Kubeadm)
              部署复杂度: 高(复杂配置项)
              多云支持: 全支持(包括OpenStack/GCP/AWS等)
              网络插件支持: 广泛支持(多种CNI)
              存储集成: 云存储+CEPH/Rook
              自动化程度: 高(Ansible)
              安全特性: 全面(合规配置)
              适用集群规模: 大型集群
              最佳适用场景: 混合云环境部署

            - name: rancher
              url: https://github.com/rancher/rancher
              doc: https://rancher.com/
              核心目标: 多集群管理平台
              高可用支持: 是(通过下游集群实现)
              部署复杂度: 高(平台本身复杂)
              多云支持: 是(集中管理多云集群)
              网络插件支持: 依赖下游集群
              存储集成: 依赖下游集群
              自动化程度: 高(GUI驱动)
              安全特性: RBAC/策略管理
              适用集群规模: 任意规模(管理角度)
              最佳适用场景: 企业级多集群管理

            - name: kubesphere
              url: https://github.com/kubesphere/kubesphere
              doc: https://kubesphere.io/
              核心目标: 全栈K8s平台
              高可用支持: 是(平台+集群HA)
              部署复杂度: 高(组件众多)
              多云支持: 是(需安装代理)
              网络插件支持: 依赖下游集群
              存储集成: 依赖下游集群
              自动化程度: 高(集成DevOps)
              安全特性: 多租户隔离
              适用集群规模: 大中型
              最佳适用场景: 开发运维一体化平台

            - name: kind
              url: https://github.com/kubernetes-sigs/kind
              doc:
              核心目标: K8s测试环境
              高可用支持: 否
              部署复杂度: 低(容器化)
              多云支持: 否
              网络插件支持: 有限(容器网络)
              存储集成: 基础
              自动化程度: 中(CI/CD集成)
              安全特性: 基础
              适用集群规模: 小型测试
              最佳适用场景: CI/CD流水线测试

            - name: k0smotron
              url: https://github.com/k0sproject/k0smotron
              doc:
              核心目标: k0s集群管理
              高可用支持: 是(内置)
              部署复杂度: 低(单二进制)
              多云支持: 边缘/云环境
              网络插件支持: 内置Calico
              存储集成: 轻量存储
              自动化程度: 中(控制平面管理)
              安全特性: 轻量化安全
              适用集群规模: 中小规模
              最佳适用场景: 轻量级生产环境


    - url: https://github.com/cert-manager/cert-manager
      score: 5
      des: 【TLS证书管理】该工具之所以存在，归根到底在于k8s本身不原生支持自动化的、全生命周期的 TLS 证书管理（获取、颁发、续订、存储、配置轮换）。当然，对k8s来说也并非刚需，可以简单认为是个自动续期的工具，类似caddy这种。胜在省心。


    - url: https://github.com/open-policy-agent/gatekeeper
      score: 4
      des: 除了 PSP, RBAC, SecurityContext 等内置方案之外，在 k8s 还可以通过策略来实现一些额外的管理、安全方面的限制，比如 Gatekeeper 这个基于 OPA 的方案。
      sub:
        - url: https://github.com/open-policy-agent/opa
          des: OPA 用来解决云原生应用的访问控制/授权和策略
      rel:
        - url: https://github.com/kubernetes-sigs/security-profiles-operator
          des: SPO(Security Profiles Operator) k8s的sec方面最难搞的就是SecurityContext 里面的 SELinux、Seccomp 和 AppArmor 三大块了。Security Profiles Operator 项目为此而来，希望能够降低在 k8s 集群中使用这些安全技术的难度。在项目网页上转了转，发现他所说的简化，除了定义几个 CRD 封装这样的 Operator 传统技能之外；还有一个使用 CRD 在节点间传输 Security Profile 的能力；最后也是最重要的，提供了很方便的录制功能，这倒是真的戳中了痛点——手写 Profile 固然酷炫，录制生成才是生产力啊。
        - url: https://github.com/kyverno/kyverno


    - url: https://github.com/kubernetes-sigs/kustomize
      score: 4
      des: Kubernetes 原生的配置管理工具，支持声明式配置定制，常与Helm结合使用。


    - url: https://github.com/vmware-tanzu/velero
      score: 4
      des: 核心场景就是灾备（快速恢复因集群崩溃、人为误删、配置错误导致的应用及数据丢失（如数据库、持久化存储卷））。拓展场景：跨集群/云迁移（在 AWS/Azure/GCP 或本地集群之间迁移应用+数据（无需重建））、安全沙盒测试（备份生产集群后克隆到测试环境，验证升级或新功能）、合规性存档（定期备份保留 K8s 资源及数据快照，满足审计要求）。


    - url: https://github.com/external-secrets/external-secrets
      des: 相比于内置secrets，使用外置secrets管理的好处是，使用内置secrets必须在k8s-cluster内创建和更新，不够灵活。并且不支持权限控制，所有pod和服务都能访问。但是应该说，外置和内置secrets各有使用场景。
  record:
    - 【2025-06-03】移除几个【Daemon Process Manager(Process Monitor)服务（也就是“进程保活”服务，比如 pm2, supervisor, forever）】，在2025年的现在已经没啥用了。进程保活的场景完全被k8s上位替代了，所以这类保活工具也只在单机或者小集群等没有使用k8s的场景下还有用。但是为什么不用k8s呢？ # k8s已经完全取代pm2和supervisor 这种用来进程保活的进程管理工具了。进程保活的场景完全被k8s上位替代了，所以这类保活工具也只在单机或者小集群等没有使用k8s的场景下还有用。
    - 【2025-07-14】移除【kubewall】、【k8m】这两个都是k8s dashboard的替代品，但是很显然只适用于开发环境（因为生产环境k8s通常都内置dashboard了），而开发环境我用minikube本身也内置了dashboard，所以就不需要了。
    - 【2025-07-14】移除【kubermatic】、【openyurt】、【kubeclipper】、【cybozu-go/cke】这几个混合云方案（统一管理跨异构基础设施（公有云/私有云/边缘/裸金属）的多个集群）。确实用不到。
    - 【2025-07-14】移除【nomad】。nomad跟k8s都是服务编排工具，但是nomad适用于中小规模集群。天然集成了Hashicorp全家桶（用Consul做服务发现，用Vault管理密钥证书，需Terraform部署基础设施）。但是咋说呢，生态肯定不如k8s所以移除了。 # Nomad = Consul + Vault. 可以把Nomad理解为某种单节点的非常轻量的类似k8s这样的服务编排工具，但是并没有实现K8s所有的特性,比如对Pod和服务的完整生命周期管理等。Nomad 内置了Consul和Vault的客户端库,这使它可以直接利用Consul提供的服务发现和Vault提供的加密存储能力,但Nomad本身的主要目的是工作负载的编排与部署。
    - 【2025-07-14】移除【rook】 # 【使用 Rook 构建生产可用存储环境】用k8s的CRD来实现存储的自动自动化编排。用rook可以轻松地在 k8s 集群上创建和管理各种类型的存储资源，如块存储、文件存储和对象存储。它支持多种存储后端，包括 Ceph、EdgeFS、Minio 等，并提供了一致的接口和工具来管理这些存储资源。不仅提供了存储编排功能，还包括了一些高级功能，如数据保护、快照、复制、调整大小等。存储的整个生命周期包括部署、启动、配置、申请、扩展、升级、迁移、灾难恢复、监控和资源管理等，看着就让笔者觉得事情不少，Rook 的目标就是降低运维的难度，让 Kubernetes 和 Rook 来帮你托管解决这些任务。
    - 【2025-07-14】移除【vault】、【vault-secrets-operator】。vault本身就是CA（可以签发TLS证书），而 cert-manager则是管理TLS机构签发的证书（其实就是集成了Let's Encrypt）。当然，在TLS证书来说，二者都支持签发、续签、重签之类的操作。除了TLS证书以外，vault还支持 内部服务mTLS、DB/中间价证书、硬件加密保护证书。那么我们就可以得到结论，二者在功能上确实相当部分是重合的，如果只是需要TLS证书管理的话，用cert-manager是更简易的方案。“cert-manager 是便捷的"证书快递员"，而 Vault 是自主可控的"证书工厂"”。因为删除了【vault】，所以也移除VSO，VSO并非vault跟k8s之间的适配器，他是用来更方便地在k8s里使用vault高级功能的。怎么理解呢？适配器指的是“有无问题”，但是VSO实际上是用来“锦上添花”方便使用的。
  topics:
    - topic: k8s架构
      picDir: ms/k8s/arch
      qs:
        - 【CRI迭代】



    - topic: k8s 调度器（调度器介绍、Pod 调度）
      qs:
        - 【调度亲和性】
        - 【污点】
        - 【固定节点调度】



    - topic: 工作负载管理 控制器（ReplicaSet、Deployment、StatefulSet、DaemonSet、Job、HPA）
      qs:
        - 【restart策略】(pod就是docker容器嘛，所以跟docker一样，NAO)
        - 【healthcheck】三种probe (LivenessProbe 存活检查 判断pod是否存活(running状态), ReadinessProbe 流量准入 判断pod是否启动完成(ready状态), StartupProbe 就绪检查（慢启动避免误杀） )
        - 【pod的生命周期】(创建一个pod的主要流程)? 资源配置? # 实际开发不会用到pod，而是controller
        - 【PodDisruptionBudget(PDB)】
        - terminate pod时的具体流程，以及为什么因此报错502/504。如果使用sidecar作为gateway，应该怎么处理？


        - "***【自动扩容缩容(HPA)的工作原理】***" # [13.深入k8s：Pod 水平自动扩缩HPA及其源码分析 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/381)
        - 【HPA的扩容和缩容算法】HPA的期望值设置为多少合适？


    - topic: k8s网络 (网络插件、网络策略、Service 服务、Ingress)
      qs:
        -

    - topic: 存储管理（Local 本地存储、Ceph 存储、存储原理）
      qs:
        -
    - topic: 配置管理 (ConfigMap, Secret, ServiceAccount)
      qs:
        - “k8s提供ConfigMap和secret来存储数据，但是ConfigMap是明文，secret则适用于存放密文。但是证书的签发、续签、重签都很麻烦，并且直接存放在secret中仍然安全性不足（这也是为啥有vault这样的工具）。”


    - topic: 安全 (RBAC, Security Context, 准入控制器)


    - topic: k8s 运维
      qs:
        - "***k8s deployment升级策略？升级过程？***" # recreate, rollingupdate. 创建新RS并新增其pod，减少旧RS的pod，逐个增加，最后旧RS的pod被减少到0
        - How to do blue/green and canary deployments with Argo Rollouts?
        - "***不要使用Deployment内置的rollingUpdate，如果想实现灰度发布，需要给每个服务的每个版本都创建不同的Deployment+HPA+PodDisruptionBudget***"

        - k8s 实现金丝雀发布？ # [k8s 如何完成金丝雀发布 - 知乎](https://zhuanlan.zhihu.com/p/*********)

        - k8s 灰度发布怎么调整流量？怎么加入灰度标签？
        - "*为啥“不要使用Deployment内置的rollingUpdate，如果想实现灰度发布，需要给每个服务的每个版本都创建不同的Deployment+HPA+PodDisruptionBudget”？*" # [Kubernetes 微服务最佳实践 - This Cute World](https://thiscute.world/posts/kubernetes-best-practices/)
        - terminate pod时的具体流程，以及为什么因此报错502/504。如果使用sidecar作为gateway，应该怎么处理？
        - HPA，HPA的扩容和缩容算法？HPA的期望值设置为多少合适？
        - 我的意思是，如果http header里有v1就自动转发到v1对应的服务器，如果是v3就转发到v3的服务，包括v3.1.0就转发到v3.1.0对应的服务器上，这样我就能保证在更新功能时不会污染原来的代码，怎么用k8s实现？

        - 优化k8s的informer # [Informer 内存使用优化 - 2 - kaku's blog](https://www.likakuli.com/posts/informer-memory-opt2/)


    - topic: 如何维护好一个微服务
      url: https://www.cyningsun.com/03-31-2021/how-maintain-a-micro-service.html


    # [Microservice Architecture and Design Patterns for Microservices | by madhuka udantha | Medium](https://medium.com/@madhukaudantha/microservice-architecture-and-design-patterns-for-microservices-e0e5013fd58a)
    # [Selecting Microservices Design Patterns for Business - Qentelli](https://qentelli.com/thought-leadership/insights/selecting-microservices-design-patterns-your-business)
    # [【IT老齐738】微服务架构组织三模式：编排、协作、链式_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1vT5DzDEjh/)
    - topic: 《微服务架构5类26种设计模式》 # [【IT老齐737】微服务架构5类26种设计模式概述_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1pW52zWEh8/) # FIXME
      isX: true




















#- url: https://github.com/unxsist/jet-pilot
#  des: JET Pilot is an open-source Kubernetes desktop client that focuses on less clutter, speed and good looks.
#  rel:
#    - url: https://github.com/derailed/popeye
#      des: k8s cluster resource sanitizer
#    - url: https://github.com/k8sgpt-ai/k8sgpt
#      des: k8sgpt 是一个用于扫描 Kubernetes 集群、诊断和处理问题的工具，旨在以简单的英语提供帮助。1、通过内置分析器将 SRE 经验编码，快速识别集群中的相关信息。2、与 OpenAI、Azure、Cohere 等多种 AI 模型无缝集成。3、提供 CLI 安装支持，兼容多种操作系统（Linux/Mac/Windows）。4、支持自定义分析器，可以根据需求编写自己的分析逻辑。5、可与现有监控工具（如 Prometheus 和 Alertmanager）结合使用，实现持续监控。
#    - url: https://github.com/polarsignals/kubezonnet
#      des: KubeZoneNet 旨在帮助监控和优化 Kubernetes 集群中的跨可用区（Cross-Zone）网络流量。这个项目提供了一种简便的方式来跟踪和分析 Kubernetes 集群中跨不同可用区的通信，帮助用户优化集群的网络架构、提高资源利用效率并减少网络延迟。通过实时监控和数据分析，KubeZoneNet 能有效地识别跨可用区的网络瓶颈，并提供改进建议，以支持 Kubernetes 集群在大规模分布式环境中的高效运行。
#    - url: https://github.com/SlinkyProject/slurm-operator
#      des: Slurm-operator 是一个高效可扩展的框架，用于在 K8s 环境中部署和运行 Slurm 工作负载。 它结合了 Slurm 的可靠性和 Kubernetes 的灵活性，支持快速部署 Slurm 集群、动态扩展 HPC 工作负载，并提供高度灵活的定制配置，同时充分利用 K8s 的云原生功能（如监控、日志和服务发现）。该项目旨在帮助组织将高性能计算（HPC）任务现代化，构建统一的云原生和传统 HPC 应用平台。
#    - url: https://github.com/padok-team/burrito
#      des: Burrito 是一款 TACoS（Terraform Automation and Collaboration Software）Kubernetes Operator，旨在提供类似 Argo CD 的体验，用于管理和自动化 Terraform 工作流。通过 Burrito，用户可以在 Kubernetes 集群中轻松实现 Terraform 配置的声明式管理、自动化执行和版本控制，从而简化基础设施即代码（IaC）的操作流程，提升部署效率和团队协作能力。
#    - url: https://github.com/leptonai/gpud
#      des: GPUd automates monitoring, diagnostics, and issue identification for GPUs. GPUd 专注于实现 GPU 的自动化监控、诊断和问题识别。它支持实时跟踪 GPU 性能指标（如温度、内存使用率和利用率），自动诊断硬件或软件相关问题，并通过主动告警帮助用户提前发现潜在风险，确保系统的稳定性和高效运行。GPUd 具有高扩展性，适用于多 GPU 分布式系统，并支持根据不同需求进行定制，非常适合 AI/ML 训练、游戏开发和数据密集型应用等场景。
#    - url: https://github.com/nullswan/bpfsnitch
#      des: Real-time network & syscall monitoring tool for Linux systems and Kubernetes clusters. BPFSnitch 是一款开源的实时网络和系统调用监控工具，专为 Linux 系统和 Kubernetes 集群设计。基于 eBPF 技术，它能够高效捕获和分析系统行为，提供网络流量和系统调用的详细可视化，帮助用户实时发现安全威胁、性能瓶颈及异常活动。BPFSnitch 轻量高效，具备高度可扩展性，是网络监控、故障排查和安全审计的理想选择，为现代化运维和安全管理提供强大支持。





#- url: https://github.com/configu/configu
#  des: 类似vault. 给整个应用提供统一的secret管理。配置即代码（Configuration-as-Code）平台，支持版本控制、环境隔离和自动化测试。特色：解决微服务架构中配置管理的碎片化问题，替代传统 .env 文件或 Consul 等复杂方案。
#
#
#- url: https://github.com/TwiN/gatus
#  des: 【拨测工具】某种uptime类似产品，而非prom的替代品。pull模式的healthcheck，不提供各种metrics，只提供HTTP, TCP, DNS之类状态的alert。
#  record:
#    - 【2025-06-24】之前以为这类都没啥用，今天才了解到其实...。移除掉了【uptime-kuma（UI更好看的uptime, 功能类似（也是监控HTTP/TCP/DNS这些），但是给每个服务提供了dashboard来查看详细数据。额外内置了所有主流平台的notification。）】、【tianji（相当于集成了 GA + uptime + gatus 的服务，ts实现的）】、【UptimeFlare（基于cloudflare workers 实现的 uptime服务，零成本挺好用的。这哥们的个人landing page也不错。）】、【cachet（基于 PHP实现的）】、【online-inspection-tracker（网址状态定时巡检工具，防止指定网址出现白屏、服务无响应等问题，有异常会发送钉钉、企业微信、飞书警报）】
#    - 【2025-06-24】如果要用拨测工具做第三方服务监控到话，那么这类工具都需要直接跟prom集成，所以我更倾向于使用gatus这种协议支持更全，YAML配置的。
#
#- url: https://github.com/FairwindsOps/polaris
#  topics:
#    - topic: 【技术选型】k8s静态检查
#      des: k8s yaml静态检查工具大概分为三类（API验证器、内置检查器、自定义验证器） Compare polaris, kubeval, kube-score, config-lint, copper, conftest? # [不确定K8S YAML文件是否符合最佳实践？这6个工具可以帮你！_软件工程_Rancher_InfoQ精选文章](https://www.infoq.cn/article/dzc6evcihvvzfmuxf62n)
#      table:
#        - name: Copper
#          url: https://github.com/cloud66-oss/copper
#          类型: 自定义验证器
#          验证方法: JavaScript脚本
#          内置检查: 无（纯自定义）
#          针对指定k8s版本检查: 是
#          CRD支持: 是（需手动转换Schema）
#          输出格式: Text
#          离线支持: 是
#          独特功能: JavaScript表达式验证，Docker镜像解析
#          最佳适用场景: 复杂自定义策略验证
#
#        - name: Kubeval
#          url: https://github.com/instrumenta/kubeval
#          类型: API验证器
#          验证方法: Kubernetes OpenAPI规范
#          内置检查: 基础API校验
#          针对指定k8s版本检查: 是（多版本）
#          CRD支持: 否
#          输出格式: Text/JSON/TAP
#          离线支持: 是（需下载schemas）
#          独特功能: 严格API规范校验
#          最佳适用场景: 基础语法和API版本验证
#
#        - name: Kube-score
#          url: https://github.com/zegl/kube-score
#          类型: 内置检查器
#          验证方法: 预定义最佳实践
#          内置检查: 30+安全与可靠性检查
#          针对指定k8s版本检查: 有限
#          CRD支持: 否
#          输出格式: Table/JSON/CI
#          离线支持: 是
#          独特功能: 风险分级（CRITICAL/WARNING）
#          最佳适用场景: 生产就绪性检查
#
#        - name: Polaris
#          url: https://github.com/FairwindsOps/polaris
#          类型: 混合型（内置+自定义）
#          验证方法: JSON Schema + 内置规则
#          内置检查: 30+内置检查
#          针对指定k8s版本检查: 是
#          CRD支持: 是（通过JSON Schema）
#          输出格式: JSON/Dashboard
#          离线支持: 是
#          独特功能: Webhook实时拦截，Prometheus指标
#          最佳适用场景: 策略即代码实施
#
#        - name: Conftest
#          url: https://github.com/open-policy-agent/conftest
#          类型: 自定义验证器
#          验证方法: Rego策略语言
#          内置检查: 无（纯自定义）
#          针对指定k8s版本检查: 否
#          CRD支持: 是
#          输出格式: Text/JSON/TAP
#          离线支持: 是
#          独特功能: OPA策略引擎，OCI策略分发
#          最佳适用场景: 高级策略即代码
#
#        - name: Kube-linter
#          url: https://github.com/stackrox/kube-linter
#          类型: 内置检查器
#          验证方法: YAML规则定义
#          内置检查: 40+生产就绪检查
#          针对指定k8s版本检查: 是
#          CRD支持: 是（通过自定义检查）
#          输出格式: Text/SARIF
#          离线支持: 是
#          独特功能: CRD自动检测，镜像签名验证
#          最佳适用场景: 安全基线检查
#
#        - name: Kubeconform
#          url: https://github.com/yannh/kubeconform
#          类型: API验证器
#          验证方法: Kubernetes JSON Schema
#          内置检查: 基础API校验
#          针对指定k8s版本检查: 是（实时更新schemas）
#          CRD支持: 是（通过自定义schemas）
#          输出格式: JSON/Text
#          离线支持: 是
#          独特功能: 并行验证（16x速度提升）
#          最佳适用场景: 大型集群快速验证
#      qs:
#        - polaris 是否可以覆盖掉其他工具绝大部分功能的？ # Polaris 是最接近"全能型"的工具：
#        #  内置30+开箱即用的检查规则
#        #  支持JSON Schema自定义扩展
#        #  提供Webhook实时拦截能力
#        #  生成Prometheus监控指标
#        #  支持CRD验证
#        #  虽然Conftest在自定义策略方面更强大（Rego语言），但缺少开箱即用的内置检查。对于大多数场景，Polaris能覆盖API验证、内置检查和自定义验证三大核心需求，但极端场景可能需要配合Kubeconform（高性能API验证）或Conftest（复杂策略）。



# TODO 把这个给弄到taskfile里
#- url: https://github.com/ahmetb/kubectx
#  des: switch between contexts (clusters) on kubectl faster.
#  rel:
#    - url: https://github.com/sbstp/kubie
#      des: A more powerful alternative to kubectx and kubens.
#    - url: https://github.com/txn2/kubefwd
#      des:
#    - url: https://github.com/steveteuber/kubectl-graph
#      des: 最近接手了一个规模比较大的集群，光是整理集群中的资源就使人头昏眼花，虽然我自认 kubectl 使用的已经十分熟练，但是上千个 k8s Resource 看下来还是不堪重负。在不能为集群安装任何其他工具的情况下，可以改造的就只有我自己的 Client 端，也就是 kubectl 了。本文就介绍一个有趣的 kubectl 插件：kubectl-graph。
#    - url: https://github.com/robinovitch61/kl
#      des: An interactive Kubernetes log viewer for your terminal. KL 是一个为终端提供交互式的 Kubernetes 日志查看工具。它支持实时跟踪和过滤日志，方便用户快速定位容器的运行状态和问题。通过直观的界面和高效的交互功能，KL 简化了日志管理，提升了对 Kubernetes 集群的调试和监控效率，非常适合开发者和运维团队使用。
#    - url: https://github.com/grampelberg/kty
#      des: Kty 是一个专为 Kubernetes 设计的终端工具，旨在简化与 Kubernetes 集群的交互。它通过直观的命令线界面，帮助用户快速查看和管理 Kubernetes 资源，提供高效的操作体验。Kty 聚焦于操作便捷性和高效性，适合开发者和运维人员在日常工作中快速处理 Kubernetes 集群任务，例如查看 Pod 状态、管理配置和排查问题等。
#    - url: https://github.com/hcavarsan/kftray
#      des: 一个用于管理和监控Kubernetes端口转发的系统托盘应用
#    - url: https://github.com/robscott/kube-capacity
#      des: 颇为实用的工具。k8s的命令行工具kubectl用来查看集群的整体资源情况往往操作会比较复杂，可能需要多条命令配合在一起才能拿得到想要的结果。kube-capacity命令行工具用来快速查看集群中的资源使用情况，包括node、pod维度。



#- url: https://github.com/kubernetes/sample-controller
#  des: 用来学怎么写k8s的operator
#  rel:
#    - url: https://github.com/crossplane/crossplane
#      des: 面向多云环境的资源管理框架,可以轻松开发支持多云的控制器。
#    - url: https://github.com/reddit/achilles-sdk
#      des: SDK for building k8s controllers. Achilles-SDK 是一个专为构建 Kubernetes 控制器而设计的开源开发工具包。它简化了控制器的开发流程，提供了强大的 API 和高效的抽象层，使开发者能够专注于业务逻辑的实现，而无需处理底层复杂性。Achilles-SDK 支持快速构建高性能、可扩展的 Kubernetes 控制器，是开发 Kubernetes 原生应用和自动化操作的理想选择。
#    - url: https://github.com/kubernetes-sigs/controller-runtime
#      des: controller-runtime 是 Kubernetes controller-runtime 项目的一个子项目，是一组用于构建控制器的 Go 库。它被 Kubebuilder 和 Operator SDK 所利用，适合新项目入门。 主要功能和优势包括：1、提供基本控制器使用构建器。2、创建管理者和控制器。3、支持快速 PR 模板及贡献指南 该项目还提供了完整的文档以及社区支持渠道，并且与 client-go 和其他 k8s.io/* 依赖具有特定兼容性版本关系。
#    - url: https://github.com/rabbitmq/cluster-operator
#      des: 最近接到一个在 K8s 中部署一个 RabbitMQ 集群的任务，既然是部署在 K8s 集群中，首选的当然是 RabbitMQ Operator 了。不过在浏览官方文档时，意外的官方也有开发一个 kubectl-rabbitmq 的插件来帮助部署和运维 RabbitMQ Operator，在试用后发现体验意外的不错。那么本文我们就使用 kubectl-rabbitmq 来部署一个 RabbitMQ 集群吧！




## Kubernetes 监控和诊断工具
#- url: https://github.com/deepflowio/deepflow
#  score: 3
#  des: 用eBPF进行monitor的工具
#  rel:
#    - url: https://github.com/naver/lobster
#      des: Lobster 是一款专为 K8s 环境设计的分布式容器日志系统，提供实时日志采集、聚合和集中化管理。 它采用分布式架构，支持大规模日志处理，具有高效的资源使用和灵活的日志管道，可无缝集成 ELK、Fluentd 等外部系统，同时优化 K8s 集群的性能。Lobster 帮助 DevOps 和 SRE 团队快速诊断问题，提升监控能力，为大规模云原生和企业级部署提供可靠的日志管理解决方案。
#    - url: https://github.com/resmoio/kubernetes-event-exporter
#      des: k8s 集群事件采集工具，支持导出到标准输出，kafka，es 等渠道。默认情况下，k8s 集群的事件会保留一个小时，当你在遇到容器异常重启，想要追溯更早的事件时，会发现已经看不到了，因此集群事件的导出并采集也是集群管理的一个基本事项。可以使用 k8s-event-exporter 解决这个问题。
#    - url: https://github.com/erda-project/kubeprober
#      des: 在k8s集群运维的过程中，诊断能力非常重要，可用来快速的定位发现问题。Kubeprober为一款定位为k8s多集群的诊断框架，提供了非常好的扩展性来接入诊断项，诊断结果可以通过grafana来统一展示。社区里类似的解决方案还有Kubehealthy和Kubeeye。
#    - url: https://github.com/weaveworks/scope
#      des: Weave Scope 是一款开源的可视化监控工具，专为 Docker 和 K8s 环境设计。它能够自动生成应用程序的拓扑图，提供自上而下的应用视图和整个基础架构视图，帮助用户实时诊断和监控分布式容器化应用程序。通过 Weave Scope，用户可以轻松查看容器、Pod、主机等资源的状态、资源使用情况和应用拓扑，还支持直接通过浏览器进入容器内部进行调试。
#
## Kubernetes 镜像构建
#- url: https://github.com/GoogleContainerTools/kaniko
#  score: 3
#  des: 用来构建容器镜像的工具




##- url: https://github.com/neargle/my-re0-k8s-security
##  des: k8s 攻防
##- url: https://github.com/cilium/hubble
##  des: Hubble 利用 eBPF 技术来实时监控和分析 k8s 网络流量、服务通信和安全事件，以提供全面的可观察性和安全性。它可以帮助您了解和调试应用程序之间的网络流量、服务依赖关系，并提供强大的安全分析和审计功能。
##- url: https://github.com/kubeshark/kubeshark
##  des: wireshark in k8s, 可以实时捕获和分析k8s控制平面和数据平面中的所有网络流量,包括Pod、节点和集群之间的流量。它可以查看k8s API请求和响应的细节,例如HTTP/gRPC方法、请求和响应主体等信息。与其他类似工具相比,Kubeshark的一个优点是可以不影响原生k8s性能就可以收集流量。它通过内嵌式控制平面和数据平面代理来捕获流量,无需严重修改k8s组件或仰赖外部插件。
#- url: https://github.com/falcosecurity/falco
#  score: 3
#  des: Cloud Native Runtime Security 用来实时监控容器、k8s、主机和云服务中的潜在威胁，依托 Linux 内核模块和 eBPF 技术实现高效检测。同时，这款开源项目还与 50 多个第三方系统成功集成，提供 JSON 格式的警报通知，方便用户进行存储、分析和触发操作。
#
#
## Ingress 控制器和 API 网关
##- url: https://github.com/traefik/traefik-helm-chart
##  des: Traefik 反向代理的 Helm Chart。Helm 是 k8s 的包管理工具，而 Helm Chart 则是一种描述 k8s 应用部署的文件。Traefik 是一个功能强大的开源反向代理和负载均衡器，它能够在 k8s 中自动发现并动态配置路由规则，提供高可用性和可伸缩性的服务访问。使用 Traefik Helm Chart 可以方便地在 k8s 集群中部署和管理 Traefik 反向代理。
##- url: https://github.com/projectcontour/contour
##  des: 以数据平面为导向的控理器,主要用于Ingress等负载均衡场景。
##- url: https://github.com/krakend/krakend-ce
##  des: KrakenD 是一个面向 Kubernetes 的 API 网关，专注于高性能和低延迟，旨在优化微服务之间的 API 调用。支持流量路由、负载均衡、速率限制和 API 聚合，极简配置。
##- url: https://github.com/DioCrafts/flusso
##  des: Flusso 是一个安全、高性能的 API 网关和 Ingress 控制器，专为 Kubernetes 环境设计，采用 Rust 语言编写。Flusso 旨在提供高效、可靠的 API 管理解决方案，同时具备强大的安全性和极低的延迟。它支持通过 Kubernetes 集群管理流量，并能够轻松地进行流量控制、认证、路由和监控等操作。
##- url: https://github.com/kube-vip/kube-vip
##  des: k8s Control Plane Virtual IP and Load-Balancer
#- url: https://github.com/nginxinc/kubernetes-ingress
#  score: 3
#  des: use nginx to replace k8s default ingress



## Kubernetes 分布式运行时
#- url: https://github.com/dapr/dapr
#  score: 4
#  des: Dapr is a portable, event-driven, runtime for building distributed applications across cloud and edge.
#  rel:
#    - url: https://github.com/mosn/layotto
#      des: A fast and efficient cloud native application runtime.
#    - url: https://github.com/temporalio/temporal
#      des: Temporal 是一个可靠的微服务编排平台，用于构建长时运行、容错的工作流应用程序。
#
## Kubernetes 应用扩展
#- url: https://github.com/openkruise/kruise
#  score: 3
#  des: Automated management of large-scale applications on Kubernetes (incubating project under CNCF).
#  rel:
#    - url: https://github.com/numaproj/numaflow
#      des:
#    - url: https://github.com/kubevirt/kubevirt
#      des: Kubernetes Virtualization API and runtime in order to define and manage virtual machines.
#    - url: https://github.com/weibaohui/kom
#      des: 一个用于 Kubernetes 操作的工具，SDK级的kubectl、client-go的使用封装。它提供了一系列功能来管理 Kubernetes 资源，包括创建、更新、删除和获取资源，甚至使用SQL查询k8s资源。之前周刊介绍过一个类似的项目是：hcnmp
#
#
## Kubernetes 自动扩缩容
#- url: https://github.com/kedacore/keda
#  doc: https://keda.sh/docs/
#  score: 4
#  des: keda = K8s-based Event Driven Autoscaling. 用来替换k8s内置HPA，来实现更好的autoscaling的服务。因为HPA只能根据自身负载情况来进行扩缩容决策（而不是服务整体负载情况），这就很容易导致在一个多级微服务调用的业务场景里，压力逐级传递，不仅扩容很慢（被动扩容），而且很容易导致一个服务被"冲垮"后形成多米诺骨牌效应，所有服务都挂掉。
#  rel:
#    - url: https://github.com/keploy/keploy
#      des: Shadow Test generation for Developers. Generate tests and stubs for your application that actually work!
#
#
#
#
#
#- url: https://github.com/kubernetes-sigs/kubebuilder
#  score: 4
#  des: 怎么自己实现 k8s controller? # k8s 是一个高度自动化的系统，k8s 内置了服务发现，负载均衡，HPA 等功能；但是需求总是永无止境的，当我们有未被 k8s 内置工具满足的需求时，可以使用`Operator`和`Custom Webstack`来实现；比较常见的需求如，部署一个数据库，节点自动化运维，日志采集组件配置等等。kubebuilder 就是用来实现k8s controller的（controller是一个控制循环,它会监视指定类型的对象(如 Deployment 或 StatefulSet 等),并在对象有变化时采取相应的动作），使用 KubeBuilder, 用户可以更快速方便地开发各种类型的控制器,如管理 DaemonSet、 StatefulSet、自定义资源等。控制器也可以集成各种外部系统。虽然不是 k8s 核心组件,但 KubeBuilder 是开发 k8s 原生应用的重要工具。它大大降低了开发控制器的难度,有利于扩展 k8s 功能和定制化开发。 [3. KubeBuilder 简明教程 - Mohuishou](https://lailin.xyz/post/operator-03-kubebuilder-tutorial.html)
#  rel:
#    - url: https://github.com/operator-framework/operator-sdk
#  topics:
#    - topic: operator, kubebuilder? GV(Api Group&Version), GVK(Group Version Kind), GVR(Group Version Webstack)
#      qs:
#        - GV 是相关 API 功能的集合，每个 group 拥有一个或多个 Versions
#        - 每个 GV 都包含 N 个 api 类型 (也就是 kinds)，不同 Version 的 kinds 可能不同
#        - Resource 是 Kind 的对象标识，一般来说 kind 和 resource 是 1:1 的，但是有时候存在 1:N 的关系，不过对于 operator 来说都是 1:1 的关系
#
#    - topic: x
#      qs:
#        - What's CRD (CustomResourceDefinition)? How to write Operator? (Operator = CRD + Controller) “提供一套跨厂商的标准结构和语义来声明核心基础设施（Pod/Service/Volume/ServiceAccount/……）， 是 Kubernetes 成功的基础。在此基础上，它又通过 CRD（Custom Resource Definition），将这个结构扩展到任何/所有基础设施资源。”
#        - 如何实现支持多集群的 k8s Operator?
#
#
## Kubernetes 开发调试工具
#- url: https://github.com/telepresenceio/telepresence
#  score: 3
#  des: Local development against a remote Kubernetes or OpenShift cluster.
#  rel:
#    - url: https://github.com/kubenetworks/kubevpn
#      des: KubeVPN 提供一个云原生开发环境。通过连接云端 kubernetes 网络，可以在本地使用 k8s dns 或者 Pod IP / Service IP 直接访问远端集群中的服务。拦截远端集群中的工作负载的入流量到本地电脑，配合服务网格便于调试及开发。同时还可以使用开发模式，直接在本地使用 Docker 模拟 k8s pod runtime 将容器运行在本地 (具有相同的环境变量，磁盘和网络)。
#    - url: https://github.com/solo-io/squash
#      des: The debugger for microservices.
#
#
#- url: https://github.com/ccfos/nightingale
#  score: 3
#  des: nightingale相当于带运维面板的kibana，就是说在web端配置各种prom参数。还可以接入VictoriaMetrics、Thanos、Mimir、M3DB之类的各种tsdb。也可以接入Categraf、Telegraf、Grafana-agent、Datadog-agent、各种 Exporter 作为采集器。“你可以在 WebUI 上管理和配置告警策略，也可以对分布在多个 Region 的指标、日志、链路追踪数据进行统一的可视化和分析”。感觉也没啥用。
#  rel:
#    - url: https://github.com/kubernetes-sigs/metrics-server
#      doc: https://kubernetes-sigs.github.io/metrics-server/
#      des: Scalable and efficient source of container resource metrics for k8s built-in autoscaling pipelines. metrics-server 也是一个集群范围内的资源数据聚合工具，是 Heapster 的替代品，Heapster现在已经停止维护和使用，同样的，metrics-server也只是显示数据，并不提供数据存储服务。metrics-server主要监控数据主要是kubelet和集成的cadvisor中暴露的容器和集群节点的资源情况，包括CPU使用情况、内存使用情况、网络吞吐量及文件系统使用情况，以及kubelet对于同期的维护情况。
#    - url: https://github.com/ddosify/ddosify
#      des: 更易用的prom，内建了一些基本metrics，不需要额外配置（所以理所当然是push监控）。当然，也可以通过插件自定义metrics。相比于prom，Ddosify监控简单易用,但定制程度相对低些。Prom采用pull模式,Ddosify采用push模式。
#
#
#
#- url: https://github.com/kubernetes-sigs/cluster-api
#  score: 3
#  des: k8s-cluster 提供的API，让我们用来通过API来进行一些自定义操作。
#  rel:
#    - url: https://github.com/kubewharf/godel-scheduler
#      des: godel-scheduler 则是为 k8s 提供了一个自定义的调度器实现。它可以根据自定义的策略和规则，智能地将容器化的应用程序和任务分配到集群中的节点上，以实现高效的资源利用和负载均衡。godel-scheduler 提供了丰富的配置选项和扩展能力，可以根据具体需求进行定制化配置和扩展。
#    - url: https://github.com/karmada-io/karmada
#      des: 跨云和跨集群的k8s编排平台。提供了一个开放的、多云和多集群的 Kubernetes 编排平台。它的目标是简化多云和多集群环境下的应用程序部署和管理。
#
#
#- url: https://github.com/kubernetes/kops
#  score: 4
#  des: 可以理解为k8s环境下的terraform。kops支持在多种基础架构上快速安全地部署高可用性的k8s集群,比如AWS、GCE、Azure、VMware等公有云,也支持Hybrid和Multi-Cloud环境。体验上和原生Kubeadm相比更友好高效,同时也提供了很多友好的管理功能来运维生产级K8s集群。需要注意的是，相比于rancher等集成了安装和管理的工具，kops只提供安装功能，不支持管理k8s集群。
#  rel:
#    - url: https://github.com/kubesphere/ks-installer
#    - url: https://github.com/kubesphere/kubekey
#      des: 1、支持部署 IPv4/IPv6 双栈集群。2、支持部署 Hybrident 网络插件。3、支持自定义配置集群 DNS 参数。4、支持自定义配置集群 Etcd 参数。
#    - url: https://github.com/kubernetes-sigs/kwok
#      des: kwok可以在几秒钟内设置一个由数千个节点组成的集群。该工具包会模拟这些节点的行为，使其表现得像真实的节点一样，因此它的资源消耗非常低，你可以在笔记本电脑上轻松地进行测试和操作。KWOK 的主要使用场景是帮助开发人员和系统管理员在本地环境中进行大规模集群的测试和调试。它可以用于测试应用程序在大型 k8s 集群中的行为，评估集群的性能和稳定性，以及进行容错和扩展性测试。此外，KWOK 还可以用于演示、培训和学习目的，让用户可以快速搭建和操作一个大规模的 k8s 集群。总体而言，KWOK 是一个用于模拟大规模 k8s 集群的工具包，可以在本地环境中进行快速而低资源消耗的测试和操作。
#    - url: https://github.com/labring/sealos
#      doc: https://sealos.run/docs/Intro/
#      des: sealos的“应用部署”可以可视化写k8s deployment文件。需要的硬件资源不低，建议8C16G，最低配置的1M1S需要4C4G（master需要2C2G，每个slave需要1C1G）
#    - url: https://github.com/sunny0826/kubecm
#      des: 该项目脱胎于 mergeKubeConfig 项目，最早写该项目的目的是在一堆杂乱无章的 kubeconfig 中自由的切换。随着需要操作的 k8s 集群越来越多，在不同的集群之间切换也越来越麻烦，而操作 k8s 集群的本质不过是通过 kubeconfig 访问 k8s 集群的 API Server，以操作 k8s 的各种资源，而 kubeconfig 不过是一个 YAML 文件，用来保存访问集群的密钥，最早的 mergeKubeConfig 不过是一个操作 YAML 文件的 Python 脚本。而随着 Go 学习的深入，也就动了重写这个项目的念头，就这样 kubecm 诞生了。
#
## Kubernetes 应用管理平台
#- url: https://github.com/kubevela/kubevela
#  score: 3
#  des: The Modern Application Platform.
#  rel:
#    - url: https://github.com/nitrictech/nitric
#      des: Nitric is a multi-language framework for cloud applications with infrastructure from code.
#    - url: https://github.com/devtron-labs/devtron
#      des: Tool integration platform for Kubernetes.
#    - url: https://github.com/kubero-dev/kubero
#      des: Kubero 是一个自托管的 PaaS 平台，旨在为 K8s 用户提供类似 Heroku、Netlify 等服务的体验。它支持 GitOps 流程，允许用户通过 Git 提交来自动部署应用程序。此外，Kubero 提供了应用程序管理、自动缩放和监控等功能，简化了应用程序的部署和运维过程。




# TODO 看看是否需要给 cilium-cli 写一个taskfile
#  一键安装 Cilium：自动检测集群类型（如 minikube、GKE、EKS）并适配配置。
#  集群诊断：运行连接性测试（cilium connectivity test）、查看状态（cilium status）。
#  高级功能管理：启用 Hubble（网络流量可视化）、ClusterM esh（多集群互联）、IPsec 加密等。
#  版本管理：支持安装/升级到指定 Cilium 版本。
