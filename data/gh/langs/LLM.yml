---

# TODO 部分文档
#- task: 《机器学习》（西瓜书）
#  url: https://github.com/datawhalechina/pumpkin-book
#  author: 周志华
#- task: 《机器学习（西瓜书）》
#  url: https://github.com/datawhalechina/pumpkin-book
#- task: 《李宏毅深度学习教程（苹果书）》
#  url: https://github.com/datawhalechina/leedl-tutorial
#
#- task: 《大模型基础》
#  url: https://github.com/ZJU-LLMs/Foundations-of-LLMs
#  des: 《大模型基础》。该书是由浙江大学 DAILY 实验室开源的大语言模型教材，内容涵盖传统语言模型、大语言模型架构演化、Prompt 工程、参数高效微调、模型编辑、检索增强生成等方面。
#- url: https://github.com/microsoft/ML-For-Beginners
#
#- url: https://github.com/ZJU-Turing/TuringCourses/
#  doc: https://zju-turing.github.io/TuringCourses/
#  des: 浙江大学图灵班课程学习指南


#- url: https://github.com/huggingface/transformers
#- url: https://github.com/huggingface/diffusers
#- url: https://github.com/lm-sys/FastChat
#- url: https://github.com/langchain-ai/opengpts
#- url: https://github.com/liguodongiot/llm-action
#  des: LLM相关文档和项目实战
#- url: https://github.com/tensorchord/Awesome-LLMOps
#  des: LLMOps
#- url: https://github.com/netease-youdao/QAnything
#  des: 这个就是我一直想要的“基于LLM的本地知识问答库”
#- url: https://github.com/plandex-ai/plandex
#  des: 用来编排GPT任务的workflow工具。它利用AI编码引擎来处理复杂的任务，并提供了一种在终端中管理上下文、执行任务和获取结果的方式。
#- url: https://github.com/infiniflow/infinity
#  des: AI 原生数据库


#- url: https://github.com/meta-llama/llama3
#  des: llama3
#- url: https://github.com/abi/screenshot-to-code
#  des: 相当于某种更牛逼的low-code? 只需要截图就能直接获取vue/react代码
#- url: https://github.com/truefoundry/cognita
#  des: 用于构建模块化、开源 RAG（Retrieval-Augmented Generation）应用程序的框架，适用于生产环境。使用 Langchain/LlamaIndex 作为底层技术，提供了代码组织结构，使每个 RAG 组件都是模块化、API 驱动和易于扩展的。它还支持增量索引，并提供了无代码 UI 支持。可以利用 Cognita 来组织和管理 RAG 代码库，通过本地设置或 UI 组件进行测试，并部署到生产环境中。
#- url: https://github.com/RVC-Boss/GPT-SoVITS
#  des: 用来生成AI声音
#  rel:
#    - url: https://github.com/FunAudioLLM/CosyVoice
#      des: 阿里开源的，不如 GPT-SoVITS好用。但是支持 3s快速克隆，在小数据集下好用
#
#
## [Tag: langchain | eleven26](https://blog.baiguiren.com/tags/langchain/)
#- url: https://github.com/langchain-ai/langchain
#  doc: https://python.langchain.com/docs/
#  des: langchain # [Archive | eleven26](https://blog.baiguiren.com/archives/)
#  topics:
#    - topic: 核心架构与开发配置
#      des:
#        1. LangChain 的模块化设计需要清晰的架构理解
#        2. 环境配置是开发流程的起点
#      qs:
#        - 如何配置 LangChain 开发环境？  # 安装 langchain 和 langchain-openai 包，设置 OPENAI_API_KEY 环境变量
#        - LangChain 的六大核心模块是什么？  # Models/Prompts/Chains/Indexes/Agents/Memory
#        - 如何实现模块化组件解耦？  # 通过标准接口定义组件交互，使用 LCEL 表达式连接
#
#    - topic: Prompt 工程优化
#      des:
#        1. 基础 LLM 输出格式不可控且缺乏上下文感知
#        2. 需要动态模板生成和语义路由技术
#      qs:
#        - 如何做好 Prompt 设计？  # 使用 ChatPromptTemplate 分离系统指令与用户输入，通过 FewShotPromptTemplate 注入示例
#        - 如何创建动态提示模板？  # 组合 System/HumanMessage，支持变量注入
#        - 如何自动路由不同 Prompt？  # 基于 RouterChain 实现语义匹配，配置多目标链动态切换模板
#        - 如何使用提示词模板？  # 定义带变量的 Jinja2 模板，运行时动态注入参数
#
#    - topic: 模型协同管理
#      des:
#        1. 单一模型无法满足成本/性能平衡需求
#        2. 需要解决多模型路由和格式兼容问题
#      qs:
#        - 如何切换不同大语言模型？  # 配置 LLMRouterChain 实现输入特征识别，建立模型注册表热切换
#        - 如何强制输出 JSON 格式？  # 使用 with_structured_output 绑定 Pydantic 模型，自动校验数据结构
#        - 如何实现多模态输入处理？  # 集成 GPT-4V 图像模型，使用 MultiModalChain 协调处理流程
#        - 如何实现多模态 chatbot？  # 集成视觉/语音专用模型，通过 MultiModalChain 协调处理流程
#
#    - topic: 工作流与任务编排
#      des:
#        1. 复杂业务场景需要多步骤协作
#        2. 涉及流程编排和异常处理工程需求
#      qs:
#        - 如何构建顺序处理链？  # 使用 LCEL 管道符组合检索器/模型/解析器组件
#        - 如何实现链式请求？  # 采用 LCEL 表达式语言，通过管道符组合组件
#        - 如何实现带条件的流程分支？  # 通过 RouterChain 动态选择后续处理步骤
#        - 如何处理链式调用异常？  # 配置自动重试机制和错误回退策略
#
#    - topic: 数据集成与处理
#      des:
#        1. 需要实时数据接入和标准化处理
#        2. 解决外部系统集成难题
#      qs:
#        - 如何接入互联网实时数据？  # 集成 SerpAPIWrapper 或 AsyncChromiumLoader
#        - 如何让 AI 从互联网获取信息？  # 配置自动爬取解析流程
#        - 如何实现 SQL 数据库问答？  # 使用 SQLDatabaseChain 转换自然语言查询
#        - 如何处理长文本分块？  # 采用 RecursiveCharacterTextSplitter 按 Token 分割
#
#    - topic: 记忆与上下文管理
#      des: 实现多轮对话的智能上下文维护
#      qs:
#        - 如何保存短期对话记录？  # 使用 ConversationBufferWindowMemory 保留最近 N 轮对话
#        - 如何构建长期知识记忆？  # 通过 VectorStoreRetrieverMemory 向量化存储历史对话
#        - 如何实现记忆权重衰减？  # 配置时间衰减系数自动降低旧记忆优先级
#        - 如何管理对话记忆？  # 组合短期记忆与长期记忆系统
#
#    - topic: 生产部署与运维
#      des:
#        1. 实验环境到生产环境需要工程化改造
#        2. 涉及性能优化和安全控制多维度
#      qs:
#        - 如何计算 API 调用成本？  # 通过 tiktoken 预计算 Token 或捕获 usage_metadata
#        - 如何实现服务高可用？  # 使用 LangServe 部署集群，配置负载均衡
#        - 如何监控模型性能？  # 集成 LangSmith 实现全链路追踪
#        - 如何做好权限控制？  # 配置 JWT 验证，实施 RBAC 权限模型
#        - 如何优化语义搜索效率？  # 采用多级缓存策略，结合 FAISS 量化索引
#
#
#
#
#
#- url: https://github.com/chatchat-space/Langchain-Chatchat
#
#- url: https://github.com/openai/openai-cookbook
#  des: OpenAI案例集锦
#- url: https://github.com/dair-ai/Prompt-Engineering-Guide
#  des: 提示工程
#- url: https://github.com/run-llama/llama_index
#  des:
#- url: https://github.com/microsoft/autogen
#  des: Agent编排
#- url: https://github.com/Significant-Gravitas/AutoGPT
#  des:
#- url: https://github.com/joaomdmoura/crewAI
#  des:
#- url: https://github.com/stanfordnlp/dspy
#  des: 提示语优化
#- url: https://github.com/ollama/ollama
#  des: 模型推理。Ollama 是一个开源的 AI 工具，旨在为用户提供简单而强大的本地部署语言模型解决方案。它支持直接在本地计算机上运行多个预训练的语言模型，能够提供与云端类似的体验，但无需依赖外部服务器或网络连接。Ollama 的主要特点包括对多个大型模型的高效管理、灵活的 API 接口和用户友好的安装过程，使得开发者能够方便地将其集成到不同的应用程序中。通过 Ollama，用户可以更容易地实现自定义对话系统和自然语言处理任务，提供高效的本地化部署方案。
#
#- url: https://github.com/hiyouga/LLaMA-Factory
#  des: 增量预训练微调
#- url: https://github.com/explodinggradients/ragas
#  des: 大模型评估工具
#- url: https://github.com/microsoft/graphrag
#  des: 基于图谱构建RAG
#- url: https://github.com/iryna-kondr/scikit-llm
#  des: 大模型应用
#- url: https://github.com/PaddlePaddle/PaddleNLP
#  des: 自然语言处理库
#- url: https://github.com/promptfoo/promptfoo
#  des: LLM 红队测试
#- url: https://github.com/ydataai/ydata-profiling
#  des: 数据探索分析工具
#- url: https://github.com/blue-yonder/tsfresh
#  des: 时间序列特征提取
#- url: https://github.com/feature-engine/feature_engine
#  des: 特征工程库
#- url: https://github.com/alteryx/featuretools
#  des: 自动化特征工程
#
#- url: https://github.com/pycaret/pycaret
#  des: 低代码机器学习
#- url: https://github.com/autogluon/autogluon
#  des: 高效自动机器学习框架
#- url: https://github.com/ludwig-ai/ludwig
#  des: 无代码深度学习框架
#- url: https://github.com/h2oai/h2o-3
#  des: 机器学习和AI平台
#- url: https://github.com/dmlc/xgboost
#  des: 梯度提升决策树
#- url: https://github.com/stanfordmlgroup/ngboost
#  des: 自然梯度提升
#- url: https://github.com/microsoft/LightGBM
#  des: 梯度提升框架
#- url: https://github.com/catboost/catboost
#  des: 分类提升决策树
#- url: https://github.com/google/yggdrasil-decision-forests
#  des: 决策森林库
#- url: https://github.com/rasbt/mlxtend
#  des: 机器学习库
#- url: https://github.com/slundberg/shap
#  des: 解释机器学习模型
#- url: https://github.com/optuna/optuna
#  des: 自动化超参数优化
#- url: https://github.com/hyperopt/hyperopt
#  des:
#- url: https://github.com/jupyter/jupyter
#  des: 交互式编程笔记本
#- url: https://github.com/jupyterlab/jupyter-ai
#  des: Jupyter AI扩展
#- url: https://github.com/matplotlib/matplotlib
#  des: 数据可视化
#- url: https://github.com/mwaskom/seaborn
#  des:
#- url: https://github.com/sktime/sktime
#  des: 时间序列分析库
#- url: https://github.com/thuml/Time-Series-Library
#  des: 时间序列库
#- url: https://github.com/unit8co/darts
#  des: 时间序列预测库
#- url: https://github.com/Nixtla/nixtla
#  des: 大模型时间序列预测工库
#- url: https://github.com/Nixtla/neuralforecast
#  des: 深度学习时间序列预测库
#- url: https://github.com/Nixtla/mlforecast
#  des: 机器学习时间序列预测库
#- url: https://github.com/Nixtla/hierarchicalforecast
#  des: 层次时间序列预测库
#- url: https://github.com/Nixtla/statsforecast
#  des: 统计时间序列预测库
#
#- url: https://github.com/keras-team/keras
#  des: 深度学习建模
#- url: https://github.com/pytorch/pytorch
#  des: PyTorch
#- url: https://github.com/tensorflow/tensorflow
#  des:
#- url: https://github.com/google/jax
#  des:
#- url: https://github.com/fastai/fastai
#  des: 深度学习建模库
#- url: https://github.com/manujosephv/pytorch_tabular
#  des:
#- url: https://github.com/basf/mamba-tabular
#  des: 深度学习封装库
#- url: https://github.com/DataCanvasIO/DeepTables
#  des: DeepTables
#- url: https://github.com/numba/numba
#  des: JIT编译的加速库
#
#
#- url: https://github.com/ray-project/ray
#  des:
#- url: https://github.com/pola-rs/polars
#  des: 快速的数据帧处理库
#- url: https://github.com/rapidsai/cupy
#  des: GPU加速的Numpy
#- url: https://github.com/rapidsai/cudf
#  des: GPU加速的DataFrame
#- url: https://github.com/apache/superset
#  des: 商业智能工具
#- url: https://github.com/ydataai/ydata-synthetic
#  des: 生成合成数据的工具
#- url: https://github.com/Valdecy/pyDecision
#  des: 多准则决策工具
#
#
#- url: https://github.com/Farama-Foundation/Gymnasium
#  des: 强化学习算法工具包
#- url: https://github.com/yzhao062/pyod
#  des: 异常检测库
#- url: https://github.com/datamllab/tods
#  des: TODS
#- url: https://github.com/pygod-team/pygod
#  des: PyGOD
#- url: https://github.com/networkx/networkx
#  des: 图挖掘库
#- url: https://github.com/benedekrozemberczki/karateclub
#  des: Karateclub
#- url: https://github.com/igraph/igraph
#  des: iGraph
#- url: https://github.com/benedekrozemberczki/littleballoffur
#  des: Littleballoffur
#- url: https://github.com/GiulioRossetti/cdlib
#  des: CDlib
#- url: https://github.com/graspologic-org/graspologic
#  des: Graspologic
#- url: https://github.com/python-graphblas/graphblas-algorithms
#  des: GraphBLAS Algorithms
#- url: https://github.com/rapidsai/cugraph
#  des: cuGraph
#- url: https://github.com/dmlc/dgl
#  des: DGL
#- url: https://github.com/pyg-team/pytorch_geometric
#  des: PyTorch Geometric
#- url: https://github.com/Valdecy/pyCombinatorial
#  des: 组合优化算法库
#- url: https://github.com/guofei9987/scikit-opt
#  des: 元启发式算法库
#- url: https://github.com/DEAP/deap
#  des:
#- url: https://github.com/Valdecy/pyMetaheuristic
#  des: pyMetaheuristic
#- url: https://github.com/Valdecy/pyMultiobjective
#  des: 多目标启发式算法库
#- url: https://github.com/Valdecy/ga_scheduler
#  des: 遗传算法调度程序
#- url: https://github.com/Valdecy/mcdm_scheduler
#  des: MCDM Scheduler
#- url: https://github.com/Valdecy/pyInterDemand
#  des: 间歇需求库
#- url: https://github.com/Valdecy/J-Horizon
#  des: 车辆路径规划
#- url: https://github.com/Valdecy/pyVRP
#  des: pyVRP
#- url: https://github.com/opencv/opencv
#  des: 【图像处理库】支持图像处理、实时物体检测和人脸识别。
#
#
#- url: https://github.com/scikit-image/scikit-image
#  des:
#
#- url: https://github.com/ultralytics/ultralytics
#  des: YOLO. ultralytics提供了一个全新的部署工具，支持将YOLOv8模型从PyTorch转换到多种平台，包括ONNX、OpenVINO、CoreML和TFLite。对于计算机视觉开发者和研究人员，这个工具能够帮助快速将YOLOv8模型部署到不同平台上，提升模型的应用性能。
#
#- url: https://github.com/CVHub520/X-AnyLabeling
#  des: YOLO目标检测 + 可视化数据图形数据标注工具
#
#- url: https://github.com/jgm/pandoc
#  des: 文件转换
#- url: https://github.com/apache/incubator-hugegraph
#  des:
#
#- url: https://github.com/streamlit/streamlit
#  des: 快速构建数据应用 # [数据可视化利器 —— Streamlit 的有趣哲学 | 木鸟杂记](https://www.qtmuniao.com/2025/03/18/streamlit/)
#
#
#
#- url: https://github.com/gradio-app/gradio
#  des: Gradio
#
#- url: https://github.com/Codium-ai/codiumai-jetbrains-release
#  des: Code, test and review with confidence
#
#- url: https://github.com/langgptai/LangGPT
#  des: used to generate structured prompt. # [求生成提示词的提示词 - 开发调优 - LINUX DO](https://linux.do/t/topic/285874)
#
#
#- url: https://github.com/Kaggle/kaggle-api
#  doc: https://www.kaggle.com/
#  des: 【Kaggle】相当于 ML 的 leetcode。首先你要明白kaggle是一个综合项目平台，上面各种项目都有，基于 cv/大模型nlp/时序tabular数据 不同项目所要求和使用的技能/工具完全不一样。建议先想清楚自己需要哪一方面的经验，或者只是做着玩那自己更喜欢那一类，再去进行特定方向的学习。其次具体的kaggle项目是一种处在学术/工业界之间的东西，解决方案的差异一般是一些关键技巧，基本模型一般有公认的工业界最优解而且默认使用者已经掌握的非常熟练，在论坛上不会有太多讨论。如果想了解基本的模型详细使用方法需要在其他地方找使用教程。建议找个你感兴趣的比赛，把coed页面的baseline和分数最好的解决方案一行一行完全弄懂具体有什么用。如果有一定的项目基础应该理解不会有太大困难。这类机器学习比赛，和实际落地之间存在差异。当然，这也和行业有关，有的行业差异较大，有的行业差异小一些。其次，建议你按照你喜欢的方向去做比赛，而不是啥都做。说白了，不同类型的数据，有各自一套的玩法，比如做CV的和做dataframe数据的玩法完全不一样，你先把其中一个方向玩熟练了再说。无论是老的比赛，还是新的比赛，作为萌新，先从kernel看baseline方案。其次，除了机器学习的基本概念外，个人认为比赛比任何课程都更适合快速入门，很多书上喜欢扯淡一些自以为很牛逼的数据处理技巧，实际上到了数据集里面，有时候会逆操作。也许叫面向loss函数的编程，更好。按照多年以前的经验，努力➕悟性还可以的，一两年就可以打到几个自己满意的名次。不过，那时候玩ML比赛的都相对比较单纯，基本没人卖代刷服务的。准确的说，差不多是同时代一些赛友退下来后才开始逐渐产生代刷服务的，差不多2020年左右吧。
#
#- url: https://github.com/datawhalechina/llm-cookbook
#
#- url: https://github.com/youssefHosni/Weekly-Top-LLM-Papers
#
#- url: https://github.com/Mintplex-Labs/anything-llm
#  des: A full-stack application that turns any documents into an intelligent chatbot with a sleek UI and easier way to manage your workspaces. 基于Git Repo建立RAG知识库？
#
#- url: https://github.com/wkentaro/labelme
#  des: 大模型数据标注工具
#- url: https://github.com/opendatalab/labelU
#  des: 大模型数据标注工具
#- url: https://github.com/opendatalab/LabelLLM
#  des: 大模型数据标注工具
#
#- url: https://github.com/stackblitz/bolt.new
#  des: 类似v0，但是更好用。相比之下，支持 4 大功能，1、一键部署，直接部署到Netlify 和 Cloudflare。2、自动同步 GitHub，项目的每次修改都会实时保存并同步到你的仓库中。
#
#- url: https://github.com/NirDiamant/RAG_Techniques
#  des: 检索增强生成（RAG）教程集合。该项目提供了 20 多种先进的 RAG 技术教程，包含实现指南和示例代码，并定期更新。内容涵盖检索查询、上下文增强、融合检索（Fusion Retrieval）、分层索引、上下文压缩、知识图谱整合等多种 RAG 技术。
#
#- url: https://github.com/serengil/deepface
#  des: 面部识别框架。deepface 是一个轻量级的面部识别和面部属性分析（年龄、性别、情绪和种族）Python 库。1、使用 VGG-Face，FaceNet，OpenFace 等最先进模型。1、提供人脸验证、查找和分析功能。3、通过调用单行代码即可完成现代人脸识别流程中的常见阶段：检测、对齐、归一化、表示和验证。
#
#- url: https://github.com/QwenLM/Qwen2.5
#  des: qwen
#- url: https://github.com/QwenLM/Qwen2.5-Coder
#  des: Qwen2.5-Coder 是由阿里云 Qwen 团队开发的大型语言模型系列 Qwen2.5 的代码版本。1、强大：Qwen2.5-Coder-32B-Instruct 成为当前 SOTA 开源代码模型，具备与 GPT-4o 相匹配的编码能力。2、多样：提供四种不同规模的模型（0.5B / 3B / 14B / 32B），满足不同开发者的需求。3、实用：在代码助手和实际应用场景中展示了其潜在应用价值。4、支持长上下文理解和生成，最大上下文长度达到 128K 个标记。
#- url: https://github.com/QwenLM/Qwen2.5-VL
#  des: Qwen的多模态模型
#
#
#
#
#
#- url: https://github.com/myshell-ai/OpenVoice
#  des: 这是 MIT 和 MyShell 合作开发的即时声音克隆项目，它可以准确复制参考音色并支持多语言语音生成。
#
#- url: https://github.com/SparkAudio/Spark-TTS
#  doc: https://sparkaudio.github.io/spark-tts/
#  des: 还是TTS项目，貌似比OpenVoice和xxx好用
#
#
#- url: https://github.com/tonyljx/TextWordExplain
#  des: 相当于李继刚【汉语新解】的 Prompt 模板的开源服务
#
#- url: https://github.com/huggingface/awesome-huggingface
#  doc: https://huggingface.co/
#  des: 【Hugging Face】相当于 ML 的 gh
#
#- url: https://github.com/huggingface/huggingface_hub
#  des: python client for huggingface
#
#- url: https://github.com/faridrashidi/kaggle-solutions
#
#- url: https://github.com/edtechre/pybroker
#  des: pybroker 是用 Python 进行机器学习的算法交易。 这个项目旨在通过 Python 和机器学习增强您的交易策略。PyBroker 是一个专为开发算法交易策略而设计的 Python 框架，重点放在使用机器学习的策略上。 其主要功能和核心优势包括：1、使用 NumPy 构建并借助 Numba 加速的超快速回测引擎。2、能够轻松创建和执行跨多种工具执行交易规则和模型。3、可访问来自 Alpaca、Yahoo Finance、AKShare 或自己数据提供者历史数据。4、提供使用 Walkforward Analysis 进行模型训练和回测，模拟实际交易中策略表现情况。5、更可靠地利用随机 Bootstrap 方法提供更准确结果的贸易指标等。
#
#- url: https://github.com/kuangdd2024/auto-video-generateor
#  des: 自动视频生成器，给定主题，自动生成解说视频。用户输入主题文字，系统调用大语言模型生成故事或解说的文字，然后进一步调用语音合成接口生成解说的语音，调用文生图接口生成契合文字内容的配图，最后融合语音和配图生成解说视频。
#
#- url: https://github.com/ikaijua/Awesome-AITools
#  des: 一些AI相关工具
#
#- url: https://github.com/Comfy-Org/desktop
#  des: ComfyUI 官方推出的桌面端，支持 Windows 和 Mac 系统。简洁直观的用户界面、一键安装的便利性、具有自动更新功能、轻量级的安装包、预配置的 Pvthon 环境、支持用户通过连接不同的节点来构建复杂的图像生成流程，精确控制每个步骤的参数。为用户提供了一个高效、稳定且易于使用的人工智能生图创作环境。
#
#- url: https://github.com/AILab-CVC/YOLO-World
#  des: YOLO-World 是一个实时的开放词汇目标检测工具。 该项目解决了实时开放词汇目标检测的问题。 它的主要功能和核心优势包括：1、使用 PyTorch 实现。2、预训练模型权重和代码。3、在大规模数据集上进行预训练，包括检测、定位和图像文本数据集。4、具有强大的开放词汇检测能力和定位能力。5、提供了一种高效用户词汇推断方法，无需额外训练或微调即可导出自己的检测模型 该项目还在积极发展中，并计划提供更多完整文档、COCO 和 LVIS 微调等功能。
#
#- url: https://github.com/laugh12321/TensorRT-YOLO
#  des: 灵活易用的 YOLO 部署工具。这是一款专为 NVIDIA 设备优化的 YOLO 部署工具。它通过集成 TensorRT 插件和 CUDA 技术，提供 C++ 和 Python API，显著提升了推理速度和易用性，支持多种 YOLO 版本，适用于目标检测、实例分割、姿态识别、旋转目标检测和视频分析等多种场景。
#
#- url: https://github.com/stephansturges/WALDO
#  des: WALDO 是一个基于 YOLO-v8 的检测 AI 模型，专为低矮可探测物体的定位而设计。1、支持多种类别的物体检测，如车辆、人员、建筑等。2、适用于从 30 英尺到卫星影像范围内的高空图像。3、可应用于灾后恢复、野生动物监测、基础设施监控等多个领域。4、提供了易于使用的代码模板，方便用户快速上手。
#
#- url: https://github.com/s0md3v/roop
#  des: roop 是一个一键换脸的项目。 该项目可以通过一张目标人物的照片，实现对视频中人脸进行替换，无需数据集和训练。其主要功能、关键特性和核心优势包括：1、提供多种参数选项来控制程序行为。2、实施了措施以防止软件被用于不当内容，并鼓励用户遵守当地法律并负责任地使用软件。
#
#- url: https://github.com/microsoft/autogen
#  des: 一个创新的框架，它允许开发者使用多个代理构建和开发LLM（大型语言模型）应用程序。这些代理可以相互交谈，共同解决任务，使应用程序更具智能性。AutoGen代理是可定制的、可对话的，并且无缝地融合了人工智能和人类参与，以提供更广泛的功能。
#
#- url: https://github.com/mediar-ai/screenpipe
#  des: screenpipe 是一个 24/7 本地 AI 屏幕和麦克风录制工具，可构建具有完整上下文的 AI 应用程序，与 Ollama 配合使用，是 Rewind.ai 的替代品。 该项目开源、安全，并且您拥有自己的数据。基于 Rust 语言。提供 24/7 屏幕和音频捕获功能。可构建个性化 AI 应用程序。支持插件系统 “pipe”。试用了一下还不错。
#
#- url: https://github.com/lijigang/write-prompt
#  des: 李继刚的公开 Prompt 集合
#
#- url: https://github.com/haoheliu/AudioLDM2
#  des: AudioLDM2 是一个支持文本转音频（包括音乐）和文本转语音生成的项目。1、支持高保真度的 48kHz AudioLDM 模型。2、提供 16kHz 改进版的 AudioLDM 模型。3、可以生成长于 10 秒的音频。4、集成 Diffusers 库，支持风格迁移和修复代码。5、提供 Web APP 和命令行使用方式。
#
#- url: https://github.com/microsoft/lida
#  des: lida 是使用大型语言模型自动生成可视化和信息图表的库。
#
#- url: https://github.com/imoneoi/openchat
#  des: openchat 是一个开源语言模型项目。使用 C-RLFT 策略进行微调，从混合质量数据中学习，无需偏好标签，在消费级 GPU 上运行 7B 模型表现出色。提供与 ChatGPT 相媲美的性能。
#
#- url: https://github.com/pingcap/autoflow
#  des: autoflow 是一个基于图形 RAG 的对话知识库工具，构建于 TiDB 无服务器向量存储之上。1、具备复杂性风格的对话搜索页面，配备先进的网站爬虫以提升浏览体验。2、支持编辑知识图谱，以添加信息或纠正不准确内容，从而增强搜索体验。3、提供可嵌入的 JavaScript 代码片段，方便将对话搜索窗口集成到网站中。4、使用 Docker Compose 进行部署，支持 4 个 CPU 核心和 8GB 内存。
#
#- url: https://github.com/richards199999/Thinking-Claude
#  des: 给 Claude 也加上思维链，回答会把推理过程也显示出来，精准度也变得更高了。使用方法：1、从 model_instructions 文件夹复制最新版本的思考协议。2、在 Claude.ai 中创建新项目。3、将指令粘贴到自定义指令部分。4、安装浏览器扩展。
#
#- url: https://github.com/ahmetbersoz/chatgpt-prompts-for-academic-writing
#  des: 是一个提供各种学术写作提示的资源，旨在帮助学生、研究人员和学术专业人士提升他们的写作能力。1、提供多种主题和任务的写作提示，包括头脑风暴研究想法、改善语言与风格、进行文献综述及制定研究计划。2、新增文献综述生成器，可以高效解析 PDF 文件并提取关键主题，自动创建文献综述部分。3、支持直接在 ChatGPT 中使用，提高了便利性。
#
#- url: https://github.com/ml-tooling/best-of-ml-python
#
#- url: https://github.com/andrewyng/aisuite
#
#- url: https://github.com/lightly-ai/lightly
#  des: DeepLearning的数据标注工具
#
#- url: https://github.com/huggingface/tokenizers
#- url: https://github.com/OwO-Network/DeepLX
#
#- url: https://github.com/OthersideAI/self-operating-computer
#  des: ???
#
#- url: https://github.com/comet-ml/opik
#  des: Opik 是一个开源的端到端 LLM 开发平台。1、支持评估、测试和监控 LLM 应用程序。2、跟踪所有 LLM 调用和痕迹，便于开发与生产环境中的监控。3、自动化评估过程，包括数据集存储和实验运行。4、提供 CI/CD 集成，支持在持续集成/持续交付管道中运行评估。5、设计用于高容量的生产追踪日志记录，并提供监控仪表板查看反馈分数等信息。
#
#- url: https://github.com/chathub-dev/chathub
#  des: 一个集成多个AI聊天服务的统一聊天界面应用
#
#- url: https://github.com/getomni-ai/zerox
#  des: 一个基于AI的文档处理和数据提取平台，支持多种文档格式和自定义模型
#
#- url: https://github.com/Byaidu/PDFMathTranslate
#  des: 基于 AI 完整保留排版的 PDF 文档全文双语翻译，支持 Google/DeepL/Ollama/OpenAI 等服务，提供 CLI/GUI/Docker
#
#- url: https://github.com/instill-ai/vdp
#  des: ChatGPT pipeline工具，用来编排各种GPT服务，来直接按照预期直接输出
#
#- url: https://github.com/comfyanonymous/ComfyUI
#  des: ComfyUI
#  sub:
#    - url: https://github.com/huchenlei/ComfyUI-layerdiffuse
#      des: ComfyUI-layerdiffuse 是 Layer Diffusion 的自定义节点实现。1、可以生成带 RGB 和 alpha 通道的前景。2、支持混合 (FG/BG)。3、提供了多种工作流程，如 Forge impl’s sanity check sets Stop at to 0.5 等。
#
#- url: https://github.com/datastax/ragbot-starter
#  des:
#
#- url: https://github.com/pathwaycom/llm-app
#  des: llm-app 是一个为 RAG、AI 管道和企业搜索提供即用云模板的项目，支持实时数据同步。1、提供高准确度的 AI 应用程序快速部署。2、支持与多种数据源（如 Sharepoint、Google Drive、S3 等）实时同步。3、包含内置的数据索引功能，实现向量搜索和全文搜索。4、应用模板可扩展至数百万页文档，适应不同需求。
#
#- url: https://github.com/h2oai/h2ogpt
#  des: Private chat with local GPT with document, images, video, etc. Supports oLLaMa, Mixtral, llama.cpp, and more.
#
#- url: https://github.com/xszyou/Fay
#  des: Fay 是一个开源的数字人框架，集成了语言模型和数字角色，适用于虚拟购物指南、播报员、助手等多种应用场景。1、完全开源，商用免责。2、支持全离线使用。3、毫秒级回复速度。4、自由匹配各种数字人模型、大语言模型及 ASR、TTS 模型。5、支持自动播报模式（如虚拟教师和主播）。6、多用户并发支持。
#
#- url: https://github.com/SillyTavern/SillyTavern
#  des: 这个就是传说中的“酒馆”。一款用于AI角色扮演的软件，基本上说的角色卡扮演就是用这个来实现的。可以实现破限，也就是瑟瑟或者一些敏感话题。也可以实现几个AI角色互动，就像是现实中的酒馆一样。 # [问一下大佬，这个是什么UI？ - 开发调优 - LINUX DO](https://linux.do/t/topic/353512)
#
#- url: https://github.com/modelscope/ms-swift
#
#- url: https://github.com/modelscope/modelscope
#  doc: https://www.modelscope.cn/my/myspace
#  des: 【魔搭社区 Model Scope】国产Hugging Face
#
#- url: https://github.com/vastxie/99AI
#  des: 99AI
#
#- url: https://github.com/deepseek-ai/DeepSeek-R1
#  doc: https://chat.deepseek.com/
#  sub:
#    - url: https://github.com/deepseek-ai/awesome-deepseek-integration
#      des: 一些集成了ds的开源服务，可以参考其具体实现
#
#- url: https://github.com/anthropics/claude-code # [Introducing Claude Code - YouTube](https://www.youtube.com/watch?v=AJpK3YTTKZ4)
#  des: # [Claude Code overview - Anthropic](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/overview)
#
#
#
## [深入了解 .clinerules：为 Cline 配置规则的终极指南](https://www.ifb.me/zh/blog/zh/ai/shen-ru-liao-jie-cli)
## [Cline 发布 3.2：白嫖 Claude 3.5、秒变架构师！](https://mp.weixin.qq.com/s/fLzoK9zWuOHvu_jAfuvtnw)
#- url: https://github.com/cline/cline
#  des: cline, 就是那个vscode插件. Roo Cline.
#
#- url: https://github.com/datawhalechina/handy-ollama
#  des: 动手学 Ollama 教程，轻松上手实现大模型本地化部署，快速在本地管理以及运行大模型，让 CPU 也可以玩转大模型推理部署！
#
#- url: https://github.com/rag-web-ui/rag-web-ui
#  des: 一个基于 RAG (Retrieval-Augmented Generation) 技术的智能对话系统，它能够帮助构建基于自有知识库的智能问答系统。通过结合文档检索和大语言模型，实现了准确、可靠的知识问答服务。
#
#- url: https://github.com/buxuku/video-subtitle-master
#  des: 批量为视频或者音频生成字幕，并可批量将字幕翻译成其它语言。翻译服务支持配置接入各大语言模型。


#- url: https://github.com/RockChinQ/LangBot
#  des: 用来部署LLM+通信机器人（其实就是聊天机器人）
#
#- url: https://github.com/woniu9524/CodeAsk
#  des: ???
#
#- url: https://github.com/stanfordnlp/dspy
#  des: 自动化Prompt框架，用来简化了复杂的Prompt生成和管理过程。
#
#- url: https://github.com/volcengine/verl
#  des: 字节的火山引擎
#
#- url: https://github.com/langchain-ai/langgraph
#  doc: https://langchain-ai.github.io/langgraph/
#  des: langgraph
#
#- url: https://github.com/vellum-ai/vellum-python-sdks
#  doc: https://www.vellum.ai/
#  des: vellum
#
#- url: https://github.com/MartialBE/one-hub
#  des: 用来管理所有主流LLM API的后台管理系统
#
#- url: https://github.com/microsoft/OmniParser
#  des: A simple screen parsing tool towards pure vision based GUI agent. 一个由微软推出的强大解析器项目。OmniParser 可能在数据解析和处理方面有出色的表现，为开发者提供便利。凭借微软的技术实力，这个项目值得期待。
#
#- url: https://github.com/MoonshotAI/Kimi-k1.5
#  doc: https://kimi.moonshot.cn/
#  des: 【kimi】目前介于GPT3.5到GPT4之间，之前用过 【Poe】、【FlowGPT】、【Lepton AI】
#
#- url: https://github.com/unslothai/unsloth
#  des:
#
#
#- url: https://github.com/lobehub/lobe-chat
#  des: lobe-chat?
#
#
#- url: https://github.com/chenzomi12/AIInfra
#  des: zomi的AI教程
#- url: https://github.com/chenzomi12/AISystem
#  des: 也是zomi提供的AI教程
#
#- url: https://github.com/geekan/MetaGPT
#  des: AI软件公司？一键提供PRD到API文档到代码？
#
#- url: https://github.com/kaito-project/kaito
#  des: k8s AI Toolchain Operator. Kaito 是一个基于 AI 的搜索引擎，旨在帮助用户更高效地检索和组织信息。它结合了大模型技术与搜索能力，使用户能够从多个数据源（如 GitHub、论文、网站等）中提取关键信息，提升研究和开发效率。Kaito 适用于开发者、研究人员和知识工作者，提供更智能的搜索体验。
#
#- url: https://github.com/dstackai/dstack
#  des: Dstack 是一个开源的 AI 计算管理平台，旨在简化 AI 任务的部署和管理。它支持本地和云端运行 AI 工作负载，并提供自动化的 GPU 资源调度，使开发者能够更高效地利用计算资源。Dstack 兼容 K8s，可以无缝集成到现有的基础设施中，适用于 AI 模型训练、推理和 MLOps 工作流。
#- url: https://github.com/skypilot-org/skypilot
#  des: SkyPilot 是一个开源的云端任务调度和优化平台，旨在帮助用户高效地在多云环境中运行 AI 训练和计算任务。它支持自动选择最优的云提供商、智能分配计算资源，并提供低成本、高性能的计算优化方案。SkyPilot 适用于 AI 研究、分布式计算和大规模云端任务管理，可无缝集成 K8s 和多种云平台。
#
#
#- url: https://github.com/ragapp/ragapp
#  des: RagApp 是一个开源的 RAG（检索增强生成）应用开发框架，旨在帮助用户构建基于大语言模型的智能问答和知识检索系统。它支持从多种数据源（如文档、数据库、API）提取信息，并结合 LLM 进行智能回答，使 AI 更精准地提供基于上下文的响应。RagApp 适用于企业知识管理、聊天机器人和智能搜索等应用场景。
#
#
#- url: https://github.com/ImagineAILab/ai-by-hand-excel
#  des: 用 Excel 手搓各种 AI 算法和模型。该项目是通过 Excel 的形式实现并演示人工智能与深度学习的核心算法和概念，让初学者可以动手操作并理解 AI 的运行原理，包括矩阵乘法、MLP、RNN、Transformer、ResNet 等，以独特且浅显易懂的形式，降低了 AI 学习的门槛。
#
#- url: https://github.com/AgentOps-AI/agentops
#  des: agentops 是一个用于 AI 代理监控、LLM 成本跟踪、基准测试等的 Python SDK。1、支持与大多数 LLM 和代理框架集成，包括 OpenAI Agents SDK、CrewAI、Langchain 等。2、提供逐步执行图，便于重放分析和调试。3、具备 LLM 成本管理功能，可以追踪支出。4、提供超过 1000 个评估的代理基准测试。
#
#- url: https://github.com/PeterH0323/Streamer-Sales
#  des: 乍看挺有意思，但是仔细看了一下，其实没啥用。细节打磨很差，一堆功能的堆砌。Streamer-Sales 是一个能够根据给定的商品特点从激发用户购买意愿角度出发进行商品解说的卖货主播大模型。 该项目具有以下主要功能、关键特性和核心优势：1、主播文案一键生成。2、KV cache + Turbomind 推理加速。3、TTS 文字转语音。
#
#
#- url: https://github.com/TeamWiseFlow/wiseflow
#  des: 使用大模型从海量信息、各类信源中每日挖掘你真正感兴趣的信息！我们缺的不是信息，而是从海量信息中过滤噪音，从而让有价值的信息显露出来🌱看看AI情报官是如何帮您节省时间，过滤无关信息，并整理关注要点的吧！【毕竟是基于爬虫，稳定性是个比较大的问题。】
#
#
#- url: https://github.com/ThinkInAIXYZ/go-mcp
#  des: 一个功能强大且易于使用的 Go 客户端库，专为与 Model Context Protocol (MCP) 进行交互而设计。该 SDK 提供了完整的 API 覆盖，包括资源管理、配置、监控和自动化操作等核心功能。 # [使用Golang 写一个百度搜索的MCP - 开发调优 - LINUX DO](https://linux.do/t/topic/546797)
#
#- url: https://github.com/huccihuang/bilibili-mcp-server
#  des: 用于哔哩哔哩 API 的 MCP（模型上下文协议）服务器
#
#- url: https://github.com/xai-org/grok-1
#  doc: https://grok.com/chat/
#  des: Grok
#
#- url: https://github.com/jlowin/fastmcp
#  des: fastmcp 是一种快速、Pythonic 的方式来构建模型上下文协议（MCP）服务器。1、快速：高层接口意味着更少的代码和更快的开发速度。2、简单：以最小的样板代码构建 MCP 服务器。
#
#- url: https://github.com/cloudflare/mcp-server-cloudflare
#  des: mcp-server-cloudflare 是一个用于管理大型语言模型（LLMs）与外部系统之间上下文的标准化协议（MCP）的服务器，专为 Cloudflare API 设计。1、支持 KV 存储、R2 存储和 D1 数据库的管理。2、包含对 Workers 和 Durable Objects 的全面支持。3、提供自然语言命令来操作 Cloudflare 账户。
#
#
#- url: https://github.com/julycoding/ChatGPT_principle_fine-tuning_code_paper
#
#- url: https://github.com/earth-mover/icechunk
#  des: Icechunk 是一款开源的云原生事务性张量存储引擎，专为深度学习、机器学习和科学计算等领域设计，提供高效的张量数据存储与管理。它支持强一致性和 ACID 事务，结合云原生架构与对象存储优化，具备高吞吐量、低延迟和分布式计算能力。Icechunk 与主流机器学习框架无缝集成，适用于模型训练、实时数据分析等场景，为开发者和研究人员提供了一个灵活可靠的高性能解决方案。
#
#- url: https://github.com/mendableai/firecrawl
#  des: Turn entire websites into LLM-ready markdown or structured data. Scrape, crawl and extract with a single API.
#
#- url: https://github.com/instill-ai/instill-core
#  des: 一个开源的MLOps平台核心组件，用于管理机器学习模型生命周期
#
#
#- url: https://github.com/APIParkLab/APIPark
#  des: 帮助开发者和企业轻松管理、集成和部署 AI 和 REST 服务。
#
#- url: https://github.com/boy1dr/SpleeterGui
#  des: 功能：基于 AI 的音频人声分离工具，可一键提取歌曲中的伴奏或人声，支持批量处理。特色：无需复杂参数调整，拖拽文件即可生成结果，适合音乐制作或翻唱剪辑^2。技术背景：底层使用 Deezer 开源的 Spleeter 模型，精度远超传统滤波工具。
#
#
#- url: https://github.com/browserbase/stagehand
#  des: stagehand 是一个专注于简单性和可扩展性的 AI 网页浏览框架。1、提供三个简单的 API（act、extract 和 observe），便于自然语言驱动的网页自动化。2、轻量级且可配置，避免过于复杂的抽象。3、支持多种模型和模型提供者，默认使用 OpenAI 模型。
#
#- url: https://github.com/aliyun/alibabacloud-jindodata
#  des: JindoData 是里云开源大数据团队自研的数据湖存储加速套件，面向大数据和 AI 生态，为阿里云和业界主要数据湖存储系统提供全方位访问加速解决方案。JindoData 套件基于统一架构和内核实现，主要包括 JindoFS 存储系统（原 JindoFS Block 模式），JindoCache 存储加速系统（原 JindoFS Cache 模式），JindoSDK 大数据万能 SDK 和全面兼容的生态工具（JindoFuse、JindoDistCp）、插件支持。
#
#- url: https://github.com/THUDM/CogVideo
#  des: 【文生视频LLM】该项目的主要功能、关键特性、核心优势包括：1、CogVideoX 和 CogVideo 为中文和英文用户提供了详细的阅读体验和论文查看。2、提供了更大规模商业视频生成模型的体验和 API 平台。3、提供了最新更新和消息，包括对 CogVideoX 的集成以及发布的一系列视频生成模型。







- type: LLM
  tag: langs
  record:
    - 【2025-07-17】移除“【技术选型】RAG应用”这个table（其实就是一些知识库应用）。【Quivr】、【AnythingLLM】、【Cherry】、【ChatBotKit】、【DocsBot】、【FastGPT】、【RAGFlow】
    - 【2025-07-17】
  topics:
    - topic: x
      qs:
        - 层次聚类算法
        - BIRCH
        - chmeleon
        - CUBE
        - 贝叶斯
        - 朴素贝叶斯
        - 贝叶斯公式
        - LCA 算法
        - DHP 频繁模式挖掘
        - 后向传播算法 backpropagation
        - 数据挖掘中常见的“异常检测”算法有哪些？
        - HMM 隐马尔科夫模型
        - Viterbi 算法
        - 随机森林、bagging、boosting、Adaboost、GBDT、XGBoost
        - SVM 支持向量机
        - LDA 线性判别分析
        - 线性回归 Liner Regression
        - 逻辑回归 Logistics Regression
        - 决策树`Desision Tree`
        - 随机森林
        - 梯度提升决策树 GBDT
        - XGBoost
        - LightGBM
        - 支持向量机 SVM
        - 贝叶斯网络
        - 马尔科夫 Markov
        - 主题模型
        - 最大期望算法 EM
        - 聚类 clustering
        - ML 特征工程和优化方法
        - K 近邻算法 (KNN)
        - 神经网络 Neural Network
        - 卷积神经网络 CNN
        - 循环神经网络 RNN
        - 门控循环单元 GRU
        - 长短期记忆 LSTM
        - 迁移学习 Transfer
        - 词嵌入 Word2Vec
        - 字词嵌入 fastText
        - 全局向量词嵌入 GloVe
        - BERT 模型
        - XLNet 模型
        - 回归算法、聚类算法、决策树、随机森林、神经网络、贝叶斯算法、支持向量机回等十大机器学习算法

    - topic: x
      qs:
        - DeepSOCIAL 社交距离监测
        - 退火算法，尽量有更大可能获得局部最优解
        - 马尔科夫饭：今天的米饭只和昨天的有关 # 其实马尔科夫链在游戏中也很常见，经典的装备强化就是例子，比如 +1 几率 80% 成功，+2 有 60% 成功，10% 几率保级，30% 失败倒退回上一级...后面的概率越来越低，假设该武器最高可以 +15，问，该武器强化到 +15 的期望次数是多少？这就是典型的马尔科夫链问题
        - 斐波那契汤：今天的汤等于昨天的菜加前天的菜









#- url: https://github.com/numpy/numpy
#  des: 【多维数组运算的加速引擎】NumPy提供高效的数值计算能力，适合处理大规模数据
#  qs:
#    - np.array()
#    - ndim 查看数据维度
#    - "*numpy 有哪些常用方法？* 基本运算 sum、mean（median、average、mode）、shape 和 reshape、排序 sort、标准差 arr.std(ddof=1)"
#
#    - 向量是啥？numpy 怎么定义向量（ndarray）？行向量和列向量？ *向量可以理解为一维矩阵，矩阵可以理解为多维向量*。因为定义行向量很麻烦，所以通常定义列向量，直接模（值相等），或者转成行向量。
#    - 矩阵是什么？numpy 怎么定义矩阵？矩阵有啥用？用 matrix 或者 array 定义即可，矩阵可以用来横向对比二维数据（一个常用场景，就是结合多维度，计算某个人的劳动回报；对比不同营销策略的效果）
#    - 向量的内积和外积？
#    - 逆矩阵是什么？逆矩阵有啥用？矩阵只有乘法，没有除法，所以需要用逆矩阵来实现除法。*逆矩阵可以用来实现文本加密和解密（想想怎么搞？）*。
#    - 练习：怎么用矩阵对图片颜色进行反转？把图片进行按比例截取？把图片对角线截取？把图片变成圆角图片？怎么上下翻转、左右翻转图片？
#
#- url: https://github.com/scipy/scipy
#  qs:
#    - 为啥要用 scipy，而不用 numpy 计算距离？
#    - 怎么对数据进行多维数据排序（比如根据商品的销量、点击量、评论数和收藏数）？很简单，构造一个最强物品的数据，然后`distance.euclidean(v1, v2)`（欧氏距离）计算每个商品和这个商品之间的距离。
#    - 方差和协方差 `np.var(A, ddof=1)`和`np.cov(A)`
#    - 什么是“相关系数”（皮尔森相关系数）？
#
#
#
#- url: https://github.com/tensorflow/probability
#  doc: https://www.tensorflow.org/probability?hl=zh-cn
#  des: TFP是一个基于 TensorFlow 构建的 Python 库，使我们能够通过该库在现代硬件（TPU、GPU）上轻松结合使用概率模型和深度学习。TFP 适合数据科学家、统计人员、机器学习研究人员，以及希望运用领域知识了解数据和做出预测的从业人员使用。TFP 包括：1、大量可供选择的概率分布和 Bijector。2、用于构建深度概率模型的工具，包括概率层和 `JointDistribution` 抽象。3、变分推断和马尔可夫链蒙特卡洛方法。4、优化器，例如 Nelder-Mead 算法、BFGS 和 SGLD。
#
#
#
#- url: https://github.com/pandas-dev/pandas
#  des: 【结构化数据处理的全能选手】数据分析和操作。Pandas擅长处理表格数据，提供高效的数据清洗与转换功能。
#  qs:
#    - pandas 查询功能怎么用？怎么过滤数据？分组函数？
#    - 有哪些格式化日期的方法？
#    - 排序方法？sort_index(ascending=False)
#    - 定位函数？怎么取自定义的行数和列数？iloc
#
#
#- url: https://github.com/automl/auto-sklearn
#  des: 其实只要了解贝叶斯的基本概念，实际使用时直接用 sklearn 就可以了，没必要研究得太深。搜索关键字“sklearn 贝叶斯”即可。
#  qs:
#    - sklearn 包含了很多基本 ML 算法，比如回归（包括线性回归、逻辑回归、曲线回归等），贝叶斯、kNN、决策树、SVM。
#    - numpy 的 shape() 和 reshape()
#    - 怎么计算用户偏好？ # “修正的余弦相似性”用户相似性 User-Based 商品相似性 Item-Based
#    - 不同服务的选择不同，一般来说应该选择数量相对较小的、变动不频繁的作为 base（比如电商网站应该根据 Item-Based，新闻网站应该根据 User-Based）
#    - 怎么计算“余弦相似度”？ # sklearn.metrics.pairwise.cosine_similarity
#    - 文本分类怎么处理？ # 使用`tf-idf + jieba分词 + sklearn`来实现文本分类。使用 tf-idf（词频 - 逆文档）方法进行统计，tf-idf 的思路是什么？如果某个词频很高，但是在其他文档很少出现，就认为这些词适合用来做区分。
#    - 怎么用 sklearn 根据新闻数据 + 朴素贝叶斯，对新闻进行分类，并验证准确率（后验）？
#
#
#- url: https://github.com/MaartenGr/BERTopic
#  des: TF-IDF, 使用 tf-idf（词频 - 逆文档）方法进行统计，tf-idf 的思路是什么？如果某个词频很高，但是在其他文档很少出现，就认为这些词适合用来做区分。使用`tf-idf + jieba分词 + sklearn`来实现文本分类。
#
#- url: https://github.com/flowable/flowable-engine
#  des: BPM
#- url: https://github.com/camunda/camunda-bpm-platform
#  des: BPM
#- url: https://github.com/alibaba/DataX
#  des: DataX, Talend, kettle
#- url: https://github.com/Talend/ui
#  des: Talend UI
#- url: https://github.com/DataV-Team/DataV
#  des: 搭配DataX使用的数据可视化工具
#- url: https://github.com/matplotlib/matplotlib
#  des: 【基础图表绘制的瑞士军刀】python 可视化库，就是用来画各种图的，什么折线图、曲线图、柱状图、散点图
#- url: https://github.com/mwaskom/seaborn
#  des: 【统计可视化的颜值担当】python可视化pkg。Seaborn基于Matplotlib，能生成更美观的统计图表。
#
#
#
#
#- url: https://github.com/plotly/plotly.py
#  des: 【交互式图表的动态专家】Plotly + Dash 组合覆盖所有交互需求。
#  record:
#    - 【2025-06-24】移除【Bokeh】。功能与 Plotly 高度重叠，但 Plotly 的交互性更强且生态更完善（支持 Dash 框架）。
#  sub:
#    - url: https://github.com/plotly/plotly.js
#      des: Plotly 提供了丰富多样的交互式绘图功能
#    - url: https://github.com/plotly/dash
#      des: Dash 则是一个用于构建交互式 Web 应用的 Python 框架
#
#
#- url: https://github.com/sympy/sympy
#  des: 符号计算库
#- url: https://github.com/statsmodels/statsmodels
#  des: 统计建模工具
#
#- url: https://github.com/cvxpy/cvxpy
#  des: 凸优化建模工具
#  record:
#    - 【2025-06-24】删除掉【pulp】、【pyomo】。线性规划场景已被 CVXPY 和 OR-Tools 覆盖，后者支持更广泛的优化算法。
#
#- url: https://github.com/google/or-tools
#  des: Google优化工具
#
#- url: https://github.com/pymc-devs/pymc
#  des: 概率编程库，贝叶斯统计，支持马尔可夫链蒙特卡洛（MCMC）模拟。可以用来构建贝叶斯线性回归模型。
#
#
#- url: https://github.com/scikit-learn/scikit-learn
#  des: 【机器学习预处理的瑞士军刀】机器学习建模。Scikit-learn提供数据预处理和模型训练工具。特征工程：标准化（StandardScaler）、分箱（KBinsDiscretizer）。评估指标：AUC、F1-score、交叉验证等
#  record:
#    - 【2025-06-24】【strsim】字符串相似度计算功能单一，scikit-learn 的 cosine_similarity 或 pairwise_distances 可完全替代。所以删掉。
#
#
#- url: https://github.com/dask/dask
#  des: 【分布式计算的并行先锋】并行计算库。Dask用于处理超大数据集，支持分布式计算。
#
#- url: https://github.com/mahmoudparsian/pyspark-tutorial
#  des: 【大数据分析的分布式引擎】PySpark适合处理海量数据，支持分布式计算
#
#
#- url: https://github.com/facebook/prophet
#  des: 【时间序列预测的神器】Prophet擅长处理时间序列数据，提供高精度预测
#
#
#- url: https://github.com/jf-tech/omniparser
#  des: a native Golang ETL streaming parser and transform library for CSV, JSON, XML, EDI, text, etc.




# TODO 机器学习之概率统计基础、矩阵论、凸优化 [Mathematics for Machine Learning — Mathematics for Machine Learning 1.0 alpha 文档](https://math-ml.readthedocs.io/zh/latest/)
#- type: Big-Data
#  tag: langs
#  topics:
#    - topic: 距离
#      table:
#        - type: 字符串数据
#          name: 编辑距离 (Levenshtein)
#          所属领域: NLP, 生物信息学
#          核心用途: 拼写纠错, DNA序列比对
#          特性: 计算字符串转换的最小操作次数
#          计算库: jellyfish.levenshtein_distance
#
#        - name: 汉明距离
#          所属领域: 信息论, 基因分析
#          核心用途: 错误检测, 等长基因序列比较
#          特性: 相同位置差异计数
#          计算库: scipy.spatial.distance.hamming
#
#        - type: 数值向量
#          name: 余弦距离
#          所属领域: NLP, 推荐系统
#          核心用途: 文本相似性, 用户偏好分析
#          特性: 忽略向量大小, 专注方向相似性
#          计算库: scipy.spatial.distance.cosine
#
#        - name: 曼哈顿距离
#          所属领域: 地理信息, 数据挖掘
#          核心用途: 网格路径规划, 高维数据聚类
#          特性: 沿轴移动距离之和
#          计算库: scipy.spatial.distance.cityblock
#
#        - name: 欧氏距离
#          所属领域: 机器学习, 计算机视觉
#          核心用途: 聚类分析, 图像特征匹配
#          特性: 空间直线距离
#          计算库: scipy.spatial.distance.euclidean
#
#        - name: 切比雪夫距离
#          所属领域: 路径规划, 棋盘游戏
#          核心用途: 机器人移动, 国王移动距离
#          特性: 各维度最大差值
#          计算库: scipy.spatial.distance.chebyshev
#
#        - name: 马氏距离
#          所属领域: 金融, 异常检测
#          核心用途: 投资组合分析, 多元异常点识别
#          特性: 考虑特征协方差结构
#          计算库: scipy.spatial.distance.mahalanobis
#
#        - type: 集合数据
#          name: 杰卡德距离
#          所属领域: 推荐系统, 生态学
#          核心用途: 集合相似性, 物种多样性分析
#          特性: 关注集合交集与并集比例
#          计算库: scipy.spatial.distance.jaccard
#
#        - type: 时间序列
#          name: 动态时间规整
#          所属领域: 时间序列分析, 语音识别
#          核心用途: 不等长序列对齐, 股票走势匹配
#          特性: 弹性时间轴对齐
#          计算库: fastdtw.dtw
#
#        - type: 概率分布
#          name: 沃瑟斯坦距离
#          所属领域: 计算机视觉, 生成模型
#          核心用途: 图像直方图匹配, GAN训练
#          特性: 分布间最小转换成本
#          计算库: scipy.stats.wasserstein_distance
#
#        - name: KL散度
#          所属领域: 信息论, NLP
#          核心用途: 概率分布差异, 主题模型评估
#          特性: 非对称性分布差异
#          计算库: scipy.stats.entropy
#
#        - type: 统计相关性
#          name: 皮尔逊相关系数
#          所属领域: 统计分析, 推荐系统
#          核心用途: 变量线性相关分析, 用户偏好相关性
#          特性: 区间线性关系度量
#          计算库: scipy.stats.pearsonr




#- task: 《R语言与数据挖掘最佳实践和经典案例》
#- task: 《PRML》
#  url: https://www.52nlp.cn/category/pattern-recognition-and-machine-learning-2
#- task: 《R语言入门经典》 # [R语言入门经典 (豆瓣)](https://book.douban.com/subject/30370786/)
#- task: 《应用时间序列分析》
#  url: https://book.douban.com/subject/3351597/
#- type: Rlang
#  tag: langs
#  score: -1
#  repo:
#    - url: https://github.com/qinwf/awesome-R
#    - url: https://github.com/YinLiLin/CMplot
#    - url: https://github.com/christophergandrud/networkD3
#      des: 用来画图的，折线图、力导向图、桑基图
#
#    - url: https://github.com/tidyverse/dplyr
#      des:
#
#    - url: https://github.com/therneau/survival
#    - url: https://github.com/sebp/scikit-survival
#    - url: https://github.com/CamDavidsonPilon/lifelines
#
#    - url: https://github.com/r-spatial/spdep
#
#    - url: https://github.com/rpy2/rpy2
#      des:
#    - url: https://github.com/rstudio/reticulate
#      des:
#
#
#    - url: https://github.com/paul-buerkner/brms
