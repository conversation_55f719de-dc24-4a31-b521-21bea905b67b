---




#- url: https://github.com/1191226989/go-zero-lol # 【gozero开发的单体应用】确实偏简单，只有API文件夹的单体应用。提供了一些基本代码结构。
#- url: https://github.com/wuyan94zl/IM # 【gozero单体应用】，说是“聊天室”，实际上核心是用户模块和好友模块，我的gozero-feeds中借鉴了很多代码
#
#- url: https://github.com/timzzx/go-zero-antd-backend # gozero+antd实现的后台
#- url: https://github.com/nivin-studio/go-zero-mall # [带你十天轻松搞定 Go 微服务系列全集+勘误]()
#- url: https://github.com/zhoushuguang/lebron # gozero实现电商项目
#- url: https://github.com/xiaopenggithub/zindle # 【gozero实现bookstore】1、app 提供了 uni-app提供了移动端。 2、backend 就是 gozero实现的后端 3、backendweb 和 backendwebvue3 都是后台，只不过一个是vue2，一个是vue3。




#- url: https://github.com/gphper/cinema-shop
#  des: 基于go-zero的影票售卖系统。项目用到了 mysql, redis, etcd, rabbitMQ.
#
#- url: https://github.com/zeromicro/zeromall
#  doc: https://zeromicro.github.io/zeromall/
#
#- url: https://github.com/Mikaelemmmm/go-zero-looklook
#  des: doc 文件夹下就有该项目的全部教程，但是这个教程只有大概思路和关键代码讲解，不是那种跟着一步一步实现的。bz也有视频，但是不用看，直接看文档教程就行。【技术栈】gozero(go-queue) + mysql + redis + EFK(+ go-stash) + PAG + jaeger + kafka + asynq
#- url: https://github.com/nivin-studio/go-zero-mall
#  doc: https://mp.weixin.qq.com/s?__biz=Mzg2ODU1MTI0OA==&mid=2247485055&idx=1&sn=474d13c6a31a9dbbce953bd6fada5daa
#  des: 一个gozero实现的电商项目
#- url: https://github.com/FCY316/OROG_WEB
#  des: 基于gozero实现的sol交易工具。这项目是真够复杂的。
#- url: https://github.com/space-water-bear/tg-im
#  des: 基于gozero实现的SSE



- type: gozero
  tag: langs
  score: 5
  using:
    url: https://github.com/zeromicro/go-zero
    des: go-zero 整体上做为一个稍重的微服务框架，提供了微服务框架需要具备的通用能力，同时也只带一部分的强约束，例如针对 web 和 rpc 服务需要按照其定义的 DSL 的协议格式进行定义，日志配置、服务配置、apm 配置等都要按照框架定义的最佳实践来走。
    doc: https://go-zero.dev/docs/tasks
    score: 3
    sub:
      - url: https://github.com/zeromicro/go-queue
        des: Kafka, Beanstalkd Pub/Sub framework. 搭配gozero使用的MQ # TODO go-queue的dq和kq有啥区别? # [分布式任务 + 消息队列框架 go-queue - 简书](https://www.jianshu.com/p/c11dc05d3999)
      - url: https://github.com/kevwan/mapreduce
        des: mapreduce, 并发编排任务。用 mapreduce 把正交逻辑并行化，就可以大幅度降低服务响应时长 (不需要优化 DB/缓存，重写业务逻辑)。MapReduce就是 服务并发执行，类似RPC服务的waitgroup.generate, mapper, reducer (生产, 加工, 聚合). MapReduce的源码可以多看看，写的非常牛逼。
      - url: https://github.com/SpectatorNan/gorm-zero
        des: gozero 中用 gorm 代替默认的 sqlx
      - url: https://github.com/Mikaelemmmm/gozerodtm
        des: gozero中使用dtm的教程
      - url: https://github.com/zeromicro/goctl-swagger
        des: gozero swagger
      - url: https://github.com/zeromicro/awesome-zero
  topics:
    - topic: 【技术选型】golang微服务框架
      table:
        - name: Kratos
          url: https://github.com/go-kratos/kratos
          doc: https://go-kratos.dev/
          依赖注入: ✅ Wire自动生成
          服务发现: ✅ 插件化注册中心
          配置管理: ✅ 多源动态热更新
          代码生成: ✅ Protobuf工具链
          API文档: ✅ 自动生成Swagger
          可观测性: ✅ 日志+追踪接口
          错误处理: ✅ Proto错误规范
          协议支持: HTTP/gRPC
          中间件生态: ✅ 标准中间件包
          维护状态: ✅ B站活跃维护
          架构分层: ✅ 清晰逻辑分层
          服务治理: ❌ 需自行扩展
          脚手架工具: ⚠️ Docker引导
          事件驱动: ❌ 不支持
          认证安全: ⚠️ JWT扩展

        - name: go-zero
          url: https://github.com/zeromicro/go-zero
          doc: https://go-zero.dev/
          依赖注入: ❌ 无内置
          服务发现: ✅ 多注册中心支持
          配置管理: ✅ 文件/环境变量/远程
          代码生成: ✅ goctl强大生成器
          API文档: ✅ goctl文档生成
          可观测性: ✅ 指标+链路追踪
          错误处理: ✅ 统一错误编码
          协议支持: HTTP/gRPC
          中间件生态: ✅ 丰富内置中间件
          维护状态: ✅ 持续高频更新
          架构分层: ✅ DDD领域驱动
          服务治理: ✅ 熔断/限流/负载均衡
          脚手架工具: ✅ goctl全功能CLI
          事件驱动: ❌ 不支持
          认证安全: ✅ 内置签名验证

        - name: Hertz
          url: https://github.com/cloudwego/hertz
          doc: https://www.cloudwego.io/zh/docs/hertz/
          依赖注入: ❌ 无内置
          服务发现: ✅ 扩展注册中心
          配置管理: ❌ 未提供
          代码生成: ✅ hz生成器
          API文档: ✅ Swagger插件
          可观测性: ✅ OpenTelemetry集成
          错误处理: ❌ 未明确机制
          协议支持: HTTP/1-3, WebSocket, RPC (搭配Kitex)
          中间件生态: ✅ 官方/社区扩展
          维护状态: ✅ 字节强力维护
          架构分层: ❌ 未强制规范
          服务治理: ⚠️ 限流中间件
          脚手架工具: ✅ hz命令行
          事件驱动: ❌ 不支持
          认证安全: ⚠️ 中间件实现

        - name: Go Micro v5
          url: https://github.com/go-micro/go-micro
          doc: https://go-micro.dev/
          依赖注入: ❌ 无内置
          服务发现: ✅ 默认mDNS+可插拔
          配置管理: ✅ 多源热加载
          代码生成: ❌ 无工具
          API文档: ❌ 不支持
          可观测性: ⚠️ 需插件扩展
          错误处理: ✅ 标准错误接口
          协议支持: ✅ HTTP/gRPC
          中间件生态: ✅ 可插拔中间件
          维护状态: ⚠️ v5不兼容旧版
          架构分层: ⚠️ 自由实现
          服务治理: ✅ 内置负载均衡+重试
          脚手架工具: ❌ 无工具
          事件驱动: ✅ 内置Pub/Sub
          认证安全: ✅ 零信任内置
      record:
        - 【2025-06-23】移除【gofr】、【go-kit】，缺少太多MS框架本身应有的feats了，与其他几个MS相比，不具有可比性。
      qs:
        - 为啥说“用gozero这种天然微服务，但是也能做单体的框架是ROI最高的选择”？单体应用也能用k8s吗，还是说只有ms才能用k8s?




    - topic: xxx
      isX: true
      des: 找到一个不错的切入点，就是gozero哪些pkg是第三方？哪些是自己实现的？哪些是二次开发的？然后再顺着这个把几个核心pkg的源码讲解一下
      table:
        - type: 第三方
          name: Prometheus
          des: 直接使用Prometheus客户端库记录指标
        - name: pprof
          des: 直接使用Go语言原生性能剖析工具
        - name: Swagger
          des: 直接使用go-swagger生成API文档
        - name: gRPC
          des: 直接调用gRPC基础通信接口
        - name: Zap
          des: 直接集成Uber高性能日志库（需手动配置）

        - type: 二次开发
          name: JWT
          des: 封装golang-jwt提供标准化中间件
        - name: Trace
          des: 封装OpenTelemetry提供统一追踪接口
        - name: go-redis
          des: 基于go-redis封装简化操作工具类

        - type: 自研
          name: TokenLimit
          des: 自研令牌桶限流算法
        - name: PeriodLimit
          des: 自研滑动窗口限流算法
        - name: errorx
          des: 统一错误码管理和跨服务传递
        - name: logx/logc
          des: 高性能日志核心系统
          url: https://go-zero.dev/docs/components/logx

        - name: goctl
          des: 代码生成器（API/RPC/CRUD）
        - name: RESTful规范
          des: 统一HTTP响应格式实现
        - name: conf
          des: 多格式配置加载+环境变量注入
        - name: executors
          des: 任务批量处理与调度系统
        - name: 中间件框架
          des: 熔断器/请求日志等核心中间件
        - name: 数据库操作
          des: 原生SQL代码生成器（非ORM封装）


    - topic: gozero 实现
      qs:
        - 介绍 core 中好用的组件，比如 timingwheel 等
        - gozero 怎么实现自动管理缓存？
        - "***gozero 的自适应熔断算法？***" # [go-zero 的自适应熔断器](https://mp.weixin.qq.com/s/r1kTYUK_r-JalvhzAKKQwg)
        - gozero ShardCalls 怎么实现的类似对于请求的 singleflight 操作? # [微服务缓存原理与最佳实践 - go-zero - Go 夜读](https://talkgo.org/t/topic/1505) gozero ShardCalls，这种 map+lock 存储并限制请求操作，和groupcache 中的 singleflight 类似，都是防止缓存击穿的利器。go-zero 中的 ShardCalls 可以使得同时多个请求只需要发起一次拿结果的调用，其他请求"坐享其成"，这种设计有效减少了资源服务的并发压力，可以有效防止缓存击穿。
        - gozero内置链路追踪是怎么实现的？
        - 日志和监控和链路追踪的设计和实现思路？
        - gozero 在 api 的 middleware 与 rpc 的 interceptor的tracing是怎么实现的？ jaeger
        - 聊聊gozero是怎么基于grpc实现了服务注册、负载均衡、拦截器（自适应降载、自适应熔断、权限验证、prometheus 指标收集等拦截器功能）等附加功能？ # zrpc 实现了 gRPC 的 resolver.Builder 接口和 balancer 接口，自定义了 resolver 和 balancer。

        - go-zero 有哪些池化技术？
        - go-zero 用到了哪些性能测试框架？
        - 能否聊聊 gozero 的缓存的设计和案例
        #  1. 分布式多 redis 集群，线上最大几十个集群为同一个服务提供缓存服务。
        #  2. `无缝扩容`和`无缝缩容`
        #  3. 不存在没有过期时间的缓存，避免大量不常使用的数据占用资源，默认一周。
        #  4. `缓存穿透`没有的数据会短暂缓存一分钟，`避免刷接口`或者`大量不存在的请求`拖跨系统。
        #  5. 缓存击穿。一个进程只会刷新一次同一个数据，避免热点数据被大量同时加载。
        #  6. 缓存雪崩。对缓存过期时间自动做了 jitter(抖动)，5% 的标准变差，使得一周的过期时间分布在 16 小时内，有效防止了雪崩
        #  7. 自动缓存管理已经内置于 go-zero，我们可以通过 goctl 自动生成代码。




    - topic: gozero 使用
      qs:
        - goctl 解决了哪些问题？
        - gozero 中布隆过滤器的使用
        - gozero 怎么无缝接入dtm？
        - gozero 的redis只支持0库，因为默认支持redis-cluster
        - 怎么快速增加一种 rpc 协议支持，将跨机发现改为本机节点，并关闭复杂 filter 和负载均衡功能？

        - ms架构下，gozero目录结构怎么组织最合理？goctl api/rpc生成的几个目录(internal/handler, logic, svc, types)，分别有啥用？
        # - internal/handler 目录：API 文件里定义的路由对应的 handler 实现
        # - internal/logic 目录：用来放每个路由对应的业务处理逻辑，之所以区分 handler 和 logic 是为了让业务处理部分尽可能减少依赖，把 HTTP requests 和逻辑处理代码隔离开，便于后续按需拆分成 RPC service
        # - internal/svc 目录：用来定义业务逻辑处理的依赖，我们可以在 main 里面创建依赖的资源，然后通过 ServiceContext 传递给 handler 和 logic
        # - internal/types 目录：定义了 API 请求和返回数据结构

        - 添加配置
        # 1. Add Config struct
        # 2. Add ServiceContext struct

        - gozero的logging怎么配置 # 用 go-stash 代替 logstash. filebeat 收集我们的业务日志，然后将日志输出到 kafka 中作为缓冲，go-stash 获取 kafka 中日志根据配置过滤字段，然后将过滤后的字段输出到 elasticsearch 中，最后由 kibana 负责呈现日志
        - gozero怎么解决多环境下配置管理方案？给我具体代码 # 无非是两种方案
        # - env + 各环境单独配置文件
        # - 如果是ms的话，就需要etcd之类配置中心了，更安全（配置隔离，否则）并且实时生效，本身就适合低频修改但是需要保证分布式节点强一致性的需求。那就是viper+etcd嘛
        - gozero mapreduce 怎么使用 # [通过MapReduce降低服务响应时间 - go-zero - Go 夜读](https://talkgo.org/t/topic/1452)
        - gozero stream流处理 fx mr
        - gozero validator # [I am planning to implement Auto Validation · Issue #4199 · zeromicro/go-zero](https://github.com/zeromicro/go-zero/issues/4199)

        - gozero中跑cron（尤其是k8s cron）？ # [Mikaelemmmm/zerok8scron: go-zero use k8s cronjob scheduler](https://github.com/Mikaelemmmm/zerok8scron)

        - gozero提供的模板不适配，怎么修改模板？ # [我的业务不一样，用 go-zero 怎么搞？-阿里云开发者社区](https://developer.aliyun.com/article/1597865)

        - httpx
        - 不理解为啥gozero的controller都返回 http.HandlerFunc? 这个不是md返回的吗？

        - 微服务该如何应对过量请求？ # [微服务该如何应对过量请求？](https://mp.weixin.qq.com/s?__biz=Mzg2ODU1MTI0OA==&mid=2247486163&idx=1&sn=67b65d1263f674c014089c67e6fe33e3)

    - topic: 多级 goroutine 的异常捕获，怎么设计？
      qs:
        - 1. 微服务系统请求异常，应该隔离，不能让单个异常请求带崩整个进程。
        - 2. go-zero 自带了 RunSafe/GoSafe，用来防止单个异常请求导致进程崩溃。
        - 3. 需要监控，防止异常过量。
        - 4. fail fast 和故障隔离的矛盾


    - topic: 如果打算换 go-zero 框架重构业务，如何做好线上业务稳定安全用户无感切换？另外咨询下如何进行服务划分？
      qs:
        - 1. 逐步替换，从内到外。加个 proxy 来校对，校对一周后可以切换。
        - 2. 如果数据库重构，就需要做好新老同步。
        - 3. 服务划分按照业务来，遵循从粗到细的原则，避免一个 api 一个微服务。
        - 4. 数据拆分对于微服务来讲，尤为重要。上层好拆，数据难拆，尽可能保证按照业务来拆分数据。


    - topic: gozero开发规范
      qs:
        - 请求最好单独配置，最好以 Req 开头。相应的，响应也单独配置，以Resp开头。
        - 如果我们修改了 api 文件，可以追加生成吗？
        - "同一个微服务的所有 api 文件，可以放在一起。比如说，用户系统，会涉及到登录相关操作、用户信息、关注和被关注等模块。这些模块可以加个 `group: friend`，就会自动生成到一个子文件夹。"
        - 用好api的import，分开写，然后import到主api，不要写在一起（需要各自的 service name 相同）
        - api文件的@server，group, prefix, middleware

    - topic: "***golang微服务项目上线前的checklist***"
      url: https://mp.weixin.qq.com/s?__biz=MzAxNzY0NDE3NA==&mid=2247489917&idx=1&sn=7e6a00887d3492cca18b9147822ecb31







#- url: https://github.com/hzde0128/gomicro_note
#  des: go-micro 学习笔记
#- url: https://github.com/Junedayday/micro_web_service
#  des: build a micro web service for go users
#- url: https://github.com/xbox1994/go-micro-example
#  des: A micro service architecture example based on go-micro
#- url: https://github.com/sksmith/go-micro-example
#  des: A production-ready microservice using Go and a few lightweight libraries
#- url: https://github.com/YangCheng0121/go-micro-examples
#  des: Go Micro v3 项目实战
#- url: https://github.com/wotmshuaisi/gomicroexample
#  des: go-micro example project for starter
#- url: https://github.com/gmsec/gmsec
#  des: golang 微服务集成框架，基于go-micro实现的
#- url: https://github.com/liangjfblue/gmicro
#  des: go-micro social server with gin, mysql, redis, mongo, docker, k8s, jaeger, grafana
#- url: https://github.com/guaosi/go-micro-build
#  des: go+gin+ 网关+go-micro+traefik+etcd+docker-compose+kubernets 从 0 到 1 部署基于 GO 的微服务
#- url: https://github.com/win5do/go-microservice-demo
#  des: go-microservice-demo
#- url: https://github.com/MarioCarrion/todo-api-microservice-example
#  des: Go microservice tutorial project using Domain Driven Design and Onion Architecture
#- url: https://github.com/sksmith/go-micro-example
#  des: 一个使用 Go-Micro 框架的微服务示例项目
#- url: https://github.com/YangCheng0121/renting
#  des: 一个基于 Go 语言实现的房屋租赁管理系统




#- url: https://github.com/tx7do/kratos-transport
#  des: kratos生态，把消息队列、任务队列，以及Websocket、HTTP3等网络协议实现为微服务框架 Kratos 的transport.Server。在使用的时候,可以调用kratos.Server()方法，将之注册成为一个Server。
#- url: https://github.com/go-kratos/kratos-layout
#  des: Kratos Project Template
#- url: https://github.com/starryrbs/kfan
#  des: 基于 Golang+Kratos+MySQL+Redis+Kafka+elk+Opentracing 实现的微服务项目
#- url: https://github.com/go-kratos/beer-shop
#  des: An online shop application, the complete microservices demo for kratos.




#- url: https://github.com/goflyfox/gtoken
#  des: Better JWT.
#- url: https://github.com/vbenjs/gf-vben
#- url: https://github.com/xinjiayu/NoticeServices
#- url: https://github.com/pibigstar/go-todo
#- url: https://github.com/xuanyanwow/LogkitByGo
#- url: https://github.com/xuanyanwow/GoFrameNotifyConsumer
#- url: https://github.com/CrazyRocks/autocreate
#- url: https://github.com/PasteUs/PasteMeGoBackend
