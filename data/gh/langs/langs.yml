---





- type: langs
  tag: langs
  score: 5
  topics:
    - topic: 语言比较
      isX: true
      table:
        - name: PHP
          编译模型: 解释型 (Zend引擎 + Opcache JIT)
          线程模型: 多进程（PHP-FPM）
          并发模型: 多进程阻塞
          GC: 引用计数 + 同步回收 # PHP 的 GC 机制，5.3 之前使用引用机制，一样因为引用计数会带来 内存泄漏的问题，使用了“同步回收”机制，来将内存使用限制在一定的阈值以下
          是否静态类型: false

        - name: Java
          编译模型: AOT (生成字节码) + JIT (运行时优化) # java可以看作是 semi-compiled 语言。具体来说，解释型语言在运行时使用 sapi 解释执行 PHP 时，通过 zend 转 opcode 码，再转为机器码。而java 则是运行前 JVM 先转为 bytecode(其实也是 opcode)，运行的时候 JVM 再将 bytecode 转为机器码。
          线程模型: 原生多线程（JVM线程）
          并发模型: 共享内存模型（线程池 + NIO）
          GC: 分代回收
          是否静态类型: true

        - name: Scala
          编译模型: AOT+JIT (编译为JVM字节码)
          线程模型: JVM多线程
          并发模型: Actor模型（Akka）
          GC: 分代回收（JVM）
          是否静态类型: true

        - name: Clojure
          编译模型: AOT+JIT (编译为JVM字节码)
          线程模型: JVM线程 + 不可变数据
          并发模型: STM（软件事务内存）
          GC: 分代回收（JVM）
          是否静态类型: false

        - name: Go
          编译模型: AOT
          线程模型: 轻量级协程（goroutine）
          并发模型: CSP并发模型
          GC: 三色标记
          是否静态类型: true

        - name: Python
          编译模型: 解释型 (CPython + JIT)
          线程模型: GIL限制的多线程
          并发模型: 共享内存模型（多进程/异步IO）
          GC: 引用计数 + 标记清除 + 分代回收 # *python 采用的是引用`引用计数`为主（但是众所周知引用计数会带来“循环引用”问题，为了解决这个问题，所以引入）`标记清除`和`分代回收(隔代回收)`两种机制为辅的策略。*
          是否静态类型: false
          # Python 解释器有哪些？ # *pypy 和 cpython 比较常用，其他还有 Jython 和 IPython* （py 解释器类似 php 的 sapi（pyc 文件相当于 opcode 缓存），用来根据语法规则，从上向下让 CPU 翻译 Python 程序中的代码。CPU 负责执行翻译完成的代码）
          # python 的执行原理？ # 参考 php 执行原理，*操作系统会首先让 CPU 把`py解释器`的程序复制到`内存`中，py 解释器让 cpu 翻译 py 代码，最终 cpu 负责执行*

        - name: Rust
          编译模型: AOT
          线程模型: 基于线程的所有权模型
          并发模型: 无畏并发
          GC: ---
          是否静态类型: true

        - name: TypeScript
          编译模型: AOT (转译到JS，然后解释型(依赖浏览器/Node.js 的 JIT 优化))
          线程模型: 事件循环（单线程）
          并发模型: 异步回调/Promise
          GC: 分代回收（V8引擎）
          是否静态类型: true

        - name: Node.js
          编译模型: 解释型（V8引擎 JIT优化）
          线程模型: 事件循环 + Worker线程
          并发模型: 非阻塞IO
          GC: 分代回收
          是否静态类型: false

        - name: C++
          编译模型: AOT
          线程模型: 原生多线程
          并发模型: 共享内存模型（线程 + 锁）
          GC: ---
          是否静态类型: true

        - name: C#
          编译模型: AOT (编译为CIL) + JIT
          线程模型: 异步任务（Task）
          并发模型: async/await
          GC: 分代回收
          是否静态类型: true

        - name: Ruby
          编译模型: 解释型
          线程模型: GIL限制的多线程
          并发模型: 多进程/纤程
          GC: 分代回收
          是否静态类型: false

        - name: Lua
          编译模型: 解释型 (LuaJIT)
          线程模型: 协程（单线程基础）
          并发模型: 协程协作式调度
          GC: 标记清除
          是否静态类型: false

        - name: Prolog
          编译模型: 解释型
          线程模型: 单线程为主
          并发模型: 有限逻辑并行
          GC: 标记清除
          是否静态类型: false

        - name: Erlang
          编译模型: AOT+JIT (编译为BEAM字节码)
          线程模型: 轻量级进程（Actor）
          并发模型: 消息传递（OTP）
          GC: 分代回收（私有堆） # erlang 的私有堆使用分代回收，共享堆使用引用计数
          是否静态类型: false

        - name: Haskell
          编译模型: AOT（GHC）
          线程模型: 绿色线程（轻量级）
          并发模型: STM + 异步IO
          GC: 分代并发回收
          是否静态类型: true
      qs:
        - 【性能】编程语言的性能由 是否编译 > 线程模型 > 并发模型 决定，那么能够给这些语言的性能，分别打分（1-5），并且做个排序？
        - 【】我一直以为 强类型就是静态语言，其实这两个是两码事，是吗？静态动态的核心在于在编译时，还是运行时才做类型检查，这个是众所周知的。而强弱类型的核心在于该语言在类型转换上的严格程度（也就是是否有隐式转换）。总之，静态强类型和动态弱类型都不用提了，举些特例，比如说C/Cpp就是静态+弱类型，python和ruby就是动态+强类型。
        - 【OOP】有哪些是OOP的特征？我知道的是，封装、继承、多态，那除了这三个以外，构造函数、方法重载、泛型、抽象、组合、接口，这些算是OOP特征吗？

        - 为啥“C/C++ 都是编译型语言，而 java/C#/PHP 都是解释型语言”？
        #- 虽然 java 在运行之前也有一个编译过程，但是并不是将代码编译成机器语言，而是编译成字节码，在运行的时候，由 JVM 将字节码再翻译成编译码，机器可以直接执行。python 同样。
        #- js、PHP 之类的脚本语言不需要编译，可以直接使用，由解释器负责解释
        #- 只说语言本身，PHP 和 java 都解释执行的时候，其实是一样的，都是先编译执行再解释执行，唯一的区别在于 java 是先编译，运行时直接在 jvm 里编译成机器码；但是 PHP 运行时既需要解释执行也需要编译执行，带来的好处很多比如说热部署方便；坏处就在于性能变差；
        #- 使用 sapi 解释执行 PHP 时，通过 zend 转 opcode 码，再转为机器码；
        #- java 则是运行前 JVM 先转为 bytecode(其实也是 opcode)，运行的时候 JVM 再将 bytecode 转为机器码
        - Compare compilers such as (LLVM, goc, rustc, HHVM, v8(Ignition), tsc, cpython, pypy)? # arch, feats, philosophy
        - 为啥普遍认为PHP服务的“有效负载、有效榨取太差”？为啥PHP服务的“php-fpm 进程”很容易被打满？




    # [第06章 编译器基本流程 — 自己动手写编译器](https://pandolia.net/tinyc/ch6_compiler_overview.html)
    # https://github.com/Yvan0329/lianglianglee/blob/main/book/%E4%B8%93%E6%A0%8F/%E7%BC%96%E8%AF%91%E5%8E%9F%E7%90%86%E5%AE%9E%E6%88%98%E8%AF%BE/%E7%9F%A5%E8%AF%86%E5%9C%B0%E5%9B%BE%20%E4%B8%80%E8%B5%B7%E6%9D%A5%E5%A4%8D%E4%B9%A0%E7%BC%96%E8%AF%91%E6%8A%80%E6%9C%AF%E6%A0%B8%E5%BF%83%E6%A6%82%E5%BF%B5%E4%B8%8E%E7%AE%97%E6%B3%95.md
    - topic: Compiler基本认知
      picDir: langs/compiler/basics
      table:
        - name: 1. 词法分析器
          技术作用: 识别源码中的单词符号
          物流类比: 【货物分类贴标】将零散货物（字符）分类为电子产品（标识符）、易碎品（常量）、危险品（关键字）并贴标签
          输出物: 词法单元序列 + 符号表

        - name: 2. 语法分析器
          技术作用: 构建语法树（程序结构）
          物流类比: 【装箱方案设计】按规则堆叠货物（语法树），如“重货在下，轻货在上”（表达式优先级）
          输出物: 抽象语法树（AST）

        - name: 3. 语义分析器
          技术作用: 检查变量声明、类型匹配等
          物流类比: 【装箱合规审查】检查货物是否超重（类型溢出）、锂电池（未声明变量）是否单独隔离
          输出物: 带语义标注的AST

        - name: 4. 中间代码生成器
          技术作用: 生成平台无关中间表示（IR）
          物流类比: 【封装标准集装箱】货物装入ISO标准集装箱（IR），标注尺寸/重量（数据类型），接口统一（角件=指令集抽象）
          输出物: 中间代码（IR）

        - name: 5. 机器无关优化器
          技术作用: 优化IR（如删除无用代码）
          物流类比: 【集装箱装载优化】合并小箱为大箱（常量传播），移除空箱（死代码消除），优化后货物总量不变（等价变换）
          输出物: 优化后的IR

        - name: 6. 代码生成器
          技术作用: 将IR转为目标机器码
          物流类比: 【选择运输工具】货轮（x86）用起重机装卸（MOV指令），火车（ARM）用叉车（LDR/STR指令），适配不同载具接口
          输出物: 目标机器码

        - name: 7. 机器相关优化器
          技术作用: 针对硬件特性优化（如缓存命中）
          物流类比: 【运输路线调整】货轮预载舱位图（指令预取），火车编组顺序优化（指令调度），利用港口吊机特性（专用指令）
          输出物: 高度优化的机器码

        - name: 最终执行
          技术作用: CPU执行指令 → 物理结果
          物流类比: 【多式联运交付】集装箱经货轮/火车/卡车（CPU核心）转运 → 货物送达（结果输出）
          输出物: 程序运行结果
      qs:
        - 【速记】lssIRgo
        - Compare compiler lang and interpreter lang? # *编译和解释的区别只在于代码是什么时候被翻译成目标 CPU 的指令的*，编译型语言在编译过程中生成目标平台的指令，解释型语言在运行过程中才生成目标平台的指令。(虚拟机的任务是在运行过程中将中间代码翻译成目标平台的指令)

        - How compiler works? What are the roles of FE and BE? And how does each step works? # 这里的 FE 指的是编译器对程序代码的分析和理解过程。它通常只跟语言的语法有关，跟目标机器无关。而与之对应的 BE 则是生成目标代码的过程，跟目标机器有关。
        - CST(Parse Tree), AST

        - "***怎么学习“编译原理”？***"
        #- 我们想要学习编译原理，也只是想自己写一个编译器，从而更好地理解这些高级语言。但是，对于新手，一开始就写编译器不一定是很好的选择，因为编译器的重点在于后端，而后端知识对于普通程序员而言用处不大。
        #- 应该尝试实现一个简单但是完整的编译器，尽早尽快地把编译器的整个流程过一遍，从而在整体上形成对编译器的认识。而不是看太多理论知识。
        #- 先学习解释器的实现，这样可以把学习重点放在类型、作用域等知识上，所谓解释器就是直接在语法树上运行，而不编译为目标语言，推荐一本很好的书：Essentials of Programming Languages，该书同为实践向教材，浅入深地讲解了各种解释器的实现，内容覆盖了环境的表示、continuation、类型检查、类型推导、OO 语言的基本实现
        # 结论：编译原理相关的知识，其实对普通程序员而言用处不大。因为compiler的重点是后端，而后端对普通开发没啥用。应该先学着写一个interpreter，然后再实现一个简单而完整的compiler，基本上就足够了。

        - AOT, JIT，“AOT 就像 C/C++ 等运行之前就编译好，而 JIT 则是运行时才进行编译，保证了可移植性的需求”
        - “多重分派能够根据方法参数的类型，确定其分派到哪个实现。它的优点是容易让同一个操作，扩展到支持不同的数据类型。” 多重分派是泛型实现吗？
        - SSA只允许给变量赋一次值，如果是循环的话就意味着要创建循环次数那么多的临时变量了？不是矛盾了吗？





    - topic: 编译器优化
      picDir: langs/compiler/optimization
      table:
        - type: 【基础优化：集装箱基础处理（单箱内优化）】
          优化技术: 常量折叠 (Constant Folding)
          技术本质: 编译时计算常量表达式，消除运行时计算开销
          物流类比: 提前拆箱计算：集装箱内已知货物（`3+5`）在装车前直接合并为单个货物（`8`）

        - 优化技术: 常量传播 (Constant Propagation)
          技术本质: 用常量值替换变量，减少内存访问
          物流类比: 标签统一：集装箱内所有关联货物标记同一编号（变量`x=5`后所有`x`替换为`5`）

        - 优化技术: 死码消除 (Dead Code Elimination)
          技术本质: 删除无效代码，减少二进制体积
          物流类比: 移除空箱：扫描集装箱时移出无货物标签的箱子（如未使用的变量/代码）

        - 优化技术: 公共子表达式消除 (CSE)
          技术本质: 重用计算结果，减少重复计算
          物流类比: 合并同类项：多个集装箱内相同货物（`a*b`重复计算）合并到共享货柜，避免重复运输

        - type: 【循环优化：物流流水线重组】
          优化技术: 循环不变代码外提 (LICM)
          技术本质: 将循环内不变量提到循环外，避免重复计算
          物流类比: 移出流水线固定项：将传送带上不变的货物分拣设备（如`固定扫描仪`）移至装货区入口

        - 优化技术: 循环展开 (Loop Unrolling)
          技术本质: 减少循环控制开销，增加指令级并行
          物流类比: 增大单次运输量：将小件货物分箱（循环迭代）合并为大箱（`4次迭代→1次处理4个元素`）

        - 优化技术: 边界检查消除 (BCE)
          技术本质: 消除数组越界检查，提升循环速度
          物流类比: 智能安检：对已验货的集装箱（数组索引范围已知）免检，直接放行

        - type: 【过程间优化：多式联运协调】
          优化技术: 内联 (Inlining)
          技术本质: 消除调用开销，并触发更深层优化（如常量传播）
          物流类比: 定制运输工具：将小件货物（函数）从标准箱拆出，直接装入当前货车（调用处）

        - 优化技术: 去虚拟化 (Devirtualization)
          技术本质: 将虚调用转为静态调用，便于内联
          物流类比: 指定运输路线：将模糊的电子产品专线（接口调用）转为明确的手机专线（具体方法）

        - 优化技术: 过程间分析 (IPA)
          技术本质: 跨函数数据流分析，支持全局优化
          物流类比: 多仓库协同调度：分析全物流链依赖（如A仓输出为B仓输入），优化整体运输计划

        - 优化技术: 链接时优化 (LTO)
          技术本质: 链接阶段跨模块优化，实现全局内联/常量传播/死码消除
          物流类比: 多仓库联合运输：整合多个物流中心（编译单元）的货物，统一规划运输路线

        - type: 【高级优化：智能物流系统】
          优化技术: SSA (Static Single Assignment)
          技术本质: 变量单次赋值，简化数据流分析，优化寄存器分配
          物流类比: 集装箱唯一编码：每个货物（变量）分配独立ID，全程溯源（如`t1=a*b; t2=t1+c`）

        - 优化技术: 向量化 (Vectorization)
          技术本质: 利用CPU单指令多数据能力，加速计算
          物流类比: 专用货柜车：将散货（标量计算）打包为整箱（SIMD指令），一次运输4箱货物

        - 优化技术: 反馈导向优化 (PGO/FDO)
          技术本质: 基于运行时数据重编译，优化热点代码
          物流类比: 动态调度系统：根据历史运输数据（热点路径）增派卡车（缓存预热/分支预测）

        - 优化技术: 别名分析 (Alias Analysis)
          技术本质: 确定指针是否指向相同内存区域，保障优化安全性
          物流类比: 货物专属通道：为不同来源货物（指针）分配独立运输路线，避免交叉污染

        - 优化技术: 数据依赖分析 (Dependence Analysis)
          技术本质: 识别指令间读写依赖关系，支持指令重排/并行化
          物流类比: 运输依赖检测：识别货物运输的先后顺序（如原材料→成品），调整并发运输策略
      qs:
        - 【编译器优化】常量传播 cp、常量折叠 cf、死码消除、公共子表达式消除 CSE、循环不变代码外提 LICM (loop-invariant code motion)、BCE(Bounds Check Elimination)、IPA、LTO、PGO、FDO、SSA、Alias Analysis、Dependence Analysis、Vectorization、Data-flow
        #常量传播 (constant propagation) 可以在编译时计算常量表达式的值，避免了运行时的计算开销。
        #逃逸分析 (Escape analysis) 可以避免为局部作用域的对象分配堆内存，从而避免 GC 的开销。
        #内联 (Inlining) 会将简单函数的函数体拷贝到调用者中，这通常可以在调用者中启用进一步的优化 (例如额外的常量传播或更好的逃逸分析)。
        #去虚拟化 (Devirtualization) 会将接口值上的间接调用 (如果可以静态确定其类型) 转换为对具体方法的直接调用 (这通常可以内联该调用)。


        - 【循环展开的代价】展开增加指令数，可能耗尽寄存器资源（大箱超重：单次运输量过大（过度展开）导致货车超载（寄存器压力增加））
        - 【PGO/FDO的冷启动问题】首次运行缺乏性能数据，优化效果有限（新路线盲区：无历史数据的运输线（新代码路径）无法优化）

        - SSA *SSA-IR（Single Static Assignment）是一种介于高级语言和汇编语言的中间形态的伪语言*，
        # - `Single`：每个表达式只能做一个简单运算，对于复杂的表达式 `a*b+c*d` 要拆分成：`t0=a*b; t1=c*d; t2=t0+t1;` 三个简单表达式
        # - `Static`：每个变量只能赋值一次（因此应该叫常量更合适）
        # - SSA 可以最大提高软件的可复用性，SSA 是产品级编译器的唯一解决方案，把 M*N 的问题转化成了 M+N
        # - *SSA 使用`BCE`(Bounds Check Elimination 边界检测消除) 和`CSE`(Common Subexpression Elimination) 优化可以让 golang 编译器生成更高效的代码*

        - 【内联】什么是内联？内联做了什么？内联的限制？ # [一文搞懂 Go 内联优化](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651453599&idx=1&sn=6925019b1150ffd68b36092cff97ea64)
        # 内联是一个基本的编译器优化，*用被调用函数的主体替换函数调用，以消除调用开销，更重要的是启用了其他编译器优化*(这是在编译过程中自动执行的一种基本优化方法)
        #- 消除了函数调用本身的开销
        #- 允许编译器更有效地应用其他优化策略（比如`常量传播 cp`，`常量折叠 cf`，`死码消除`，`公共子表达式消除 CSE`，`循环不变代码移动`和`更好的寄存器分配`等）
        - 【内联导致代码膨胀】内联的越多开销就越少，为什么不尽可能使用内联呢？ # 内联增加二进制体积，可能降低指令缓存命中率（过度拆箱：小件货物直接塞入货车（内联），导致车厢拥挤（缓存溢出）） *内联可能会以增加程序大小来换取更快的执行时间*限制内联的最主要原因是，创建许多函数的内联副本会增加编译时间，并生成更大的二进制文件；内联收益最大的是“小函数”，相对于调用他们的开销来说，这些函数做很少的工作；随着函数大小的增长，函数内部做的工作与函数调用的开销相比，省下的时间越来越少；函数越大，通常越复杂，因此优化其内联形式相对于原地优化的好处会减少；

        - "***逃逸分析（escape analysis）：可能出现逃逸的场景？有哪几种情况会发生逃逸呢？怎么确定是否逃逸？***"
        #- *变量类型不确定*，比如使用 interface 类型会逃逸，因为编译时无法确定类型；优化方案是将类型设置为固定类型；
        #- *变量大小不确定*
        #- *返回指针类型，会发生逃逸*；优化方案视情况而定；传参的时候，从逃逸分析的角度，什么时候应该传结构体，什么时候应该传结构体的指针呢？
        #  - 如果结构体很大，就应该传指针。
        #  - 直接传结构体，需要值拷贝，但是这是在栈上完成的操作，开销比变量逃逸后动态地在堆上分配内存少很多。
        #- *栈空间不足，就会发生逃逸；比如变量所占内存较大等情况*；优化方案就是尽量设置容量，如果容量实在过大就没有办法了；

        - 什么是逃逸分析？怎么理解“逃逸分析是一种确定指针动态范围的方法，简单来说就是分析在程序的哪些地方可以访问到该指针”？为什么编译器要进行逃逸分析？为什么我们要了解逃逸分析？
        #- **简单的说，它是在对变量放到堆上还是栈上进行分析，该分析在编译阶段完成。如果一个变量超过了函数调用的生命周期，也就是这个变量在函数外部存在引用，编译器会把这个变量分配到堆上，这时我们就说这个变量发生逃逸了。
        #- *golang 编译器通过`逃逸分析`，决定把一个变量放在栈上，还是堆上。变量作用域跑出函数范围，就分配在栈上。否则就分配在堆上。*
        #- *检查“变量的生命周期”是否是完全可知的，如果通过检查，就分配在栈上。否则，就逃逸到堆上。

        - 逃逸分析是怎么完成的？怎么理解“编译器会根据变量是否被外部引用来决定是否逃逸”？ # 对我们来说，我们不需要掌握编译器的这些逃逸分析规则，我们只需要通过`go build -gcflags '-m'`命令查看逃逸分析的情况就可以了。另外，写代码的时候，也要注意“不要盲目使用变量的指针作为函数参数，虽然他会减少复制操作；但是当参数是变量自身的时候，复制是在栈上完成的操作，开销远比变量逃逸后动态地在堆上分配内存少得多”；我们应该尽量少写逃逸的代码，提升程序的运行效率。
        - 可能出现逃逸的场景？有哪几种情况会发生逃逸呢？
        #- *变量类型不确定*，比如使用 interface 类型会逃逸，因为编译时无法确定类型；优化方案是将类型设置为固定类型；
        #- *变量大小不确定*
        #- *返回指针类型，会发生逃逸*；优化方案视情况而定；传参的时候，从逃逸分析的角度，什么时候应该传结构体，什么时候应该传结构体的指针呢？1、如果结构体很大，就应该传指针。2、直接传结构体，需要值拷贝，但是这是在栈上完成的操作，开销比变量逃逸后动态地在堆上分配内存少很多。
        #- *栈空间不足，就会发生逃逸；比如变量所占内存较大等情况*；优化方案就是尽量设置容量，如果容量实在过大就没有办法了；

        - 为啥说“golang compiler是入门compiler的最佳案例”？
        #Go语言的编译器完全用Go语言本身来实现，它完全实现了从前端到后端的所有工作，而不像Java要分成多个编译器来实现不同的功能模块，不像Python缺少了后端，也不像Julia用了太多的语言。所以你研究它所采用的编译技术会更方便。
        #
        #Go编译器里基本上使用的都是经典的算法：经典的递归下降算法、经典的SSA格式的IR和CFG、经典的优化算法、经典的Lower和代码生成，因此你可以通过一个编译器就把这些算法都贯穿起来。
        #
        #除了编译器，你还可以学习到一门语言的其他构成部分的实现思路，包括运行时（垃圾收集器、并发调度机制等）、标准库和工具链，甚至连链接器都是用Go语言自己实现的，从而对实现一门语言所需要做的工作有更完整的认识。

        - "***golang 编译由哪几个阶段构成（编译前端（词法分`、语法分析、类型检查）、编译后端（生成中间码、生成机器码））？***" # [万字图文 | 你写的代码是如何跑起来的？](https://mp.weixin.qq.com/s?__biz=MjM5Njg5NDgwNA==&mid=2247490743&idx=1&sn=45fd166a87c01b511be1fb2b5f20e38e)
        # 编译前端（词法分析（把源码翻译成 token，token 分为变量名、字面量、操作符、分隔符和关键字。早期 golang 使用通用词法分析器 lex，后来改用了 golang 自己实现的）、语法分析（将 token 转成 AST 树，有`包名ast.Indent`、`导入声明ast.GenDecl`、`函数声明ast.FuncDecl`三个子节点）、类型检查）
        #
        # 编译后端
        #
        #1. `生成中间码`，AST 转 IR(中间码)，golang 使用`SSA特性的IR`，这种形式的中间码，最重要的一个特性就是在使用变量之前总是定义变量，并且每个变量只分配一次
        #2. `生成机器码`，*优化后的中间码，首先会被转成汇编代码 (Plan9)，而汇编代码只是机器码的文本表示，还不能执行。所以这个阶段会调用汇编器，汇编器会根据我们在执行编译时设置的架构，调用对应代码来生成目标机器码*

        - 怎么通过 AST 树对 golang 源码进行分析？ # [利用 go/ast 语法树做代码生成 - 无风的内存空间 - SegmentFault 思否](https://segmentfault.com/a/1190000039215176) 语法检查、单测框架生成

        # [PGO: 为你的 Go 程序提效 5%](https://colobu.com/2023/09/13/pgo/)
        # [PGO 是啥，咋就让 Go 更快更猛了？](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247497141&idx=1&sn=0459901d47536d1460d8b025838245c8)
        - PGO：什么是“基于 profile 指导的优化”? # 使用应用程序行为的 profile 进行编译器优化称为基于 profile 指导的优化 (PGO)(也称为性能分析引导优化、反馈导向优化 (FDO))。


        # [汇编入门 - 知乎](https://www.zhihu.com/column/c_144694924)
        # [Go 汇编器快速指南 - Go 编程语言 --- A Quick Guide to Go's Assembler - The Go Programming Language](https://go.dev/doc/asm)
        - Plan9 汇编有哪些常用指令？（操作方向、栈扩大缩小、数据copy、计算指令、跳转、变量声明）
        - 进程内存布局（运行时栈、运行时堆、数据区、代码区）
        - 寄存器的作用是什么？ # 寄存器是编译器用来维护上下文、特殊标识的。所有用户空间的数据都可以通过 FP/SP(局部数据、输入参数、返回值) 和 SB(全局数据) 访问。通常情况下，不会对 SB/FP 寄存器进行运算操作，会以 SB/FP/SP 作为基准地址，进行便宜、解引用等操作
        - golang 的4种寄存器（SB、FP、SP、PC）分别有啥用？
        # - SB(static base pointer): 静态全局基本指针，一般用在声明函数、全局变量中
        # - FP(frame pointer):
        # - SP(stack pointer):
        # - PC(program counter):
        - 汇编操作指令（SUBQ、MOVQ、ADDQ、CALL）





    - topic: 各语言Compiler
      picDir: langs/compiler/langs
      table:
        - name: LLVM
          前端: Clang等语言前端
          中间表示（IR）: LLVM IR（SSA形式）
          后端/执行引擎: 可插拔后端（x86/ARM等）
          优化策略: 多阶段Pass优化

        - name: rustc
          前端: Rust语法解析
          中间表示（IR）: MIR → LLVM IR
          后端/执行引擎: LLVM后端
          优化策略: 借用检查 + LLVM优化

        - name: V8
          前端: Ignition解释器
          中间表示（IR）: 字节码 → TurboFan IR
          后端/执行引擎: TurboFan JIT编译器
          优化策略: 内联缓存/函数级JIT

        - name: PyPy
          前端: Python解析器
          中间表示（IR）: RPython IR
          后端/执行引擎: JIT生成机器码
          优化策略: 追踪JIT + 垃圾回收优化

        - name: HHVM
          前端: Hack/PHP解析器
          中间表示（IR）: HHIR
          后端/执行引擎: JIT编译器（x64/ARM）
          优化策略: 类型推断 + IR优化

        - name: tsc
          前端: TypeScript类型检查
          中间表示（IR）: 无独立IR
          后端/执行引擎: 输出JavaScript代码
          优化策略: 静态类型擦除



    - topic: 《七周七并发模型》
      table:
        - name: 共享内存模型线程与锁
          代表语言: Java/C++/Python
          核心机制: 线程+锁（Mutex, RWLock）
          通信机制: 共享内存
          同步方式: 锁(Locks)、信号量
          适用场景: 计算密集型本地任务
          优缺点: 高频锁竞争时性能骤降 ❌

        - name: Actor模型
          代表语言: Erlang/Scala(Akka)
          核心机制: 独立Actor异步消息信箱
          通信机制: 异步消息传递
          同步方式: 消息队列
          适用场景: 分布式系统、容错场景
          优缺点: |
            高并发、跨节点扩展性强 ✅
            消息传递可能无序 ❌

        - name: CSP模型
          代表语言: Go/Clojure(core.async)
          核心机制: Goroutine+Channel同步通信
          通信机制: 通道(Channel)通信
          同步方式: 通道阻塞/选择
          适用场景: 高并发微服务、数据流水线
          优缺点: |
            解耦生产-消费；低延迟、协程轻量级 ✅
            通道管理复杂度高 ❌

        - name: STM模型
          代表语言: Clojure
          核心机制: 事务内存（原子性读写）
          通信机制: 软件事务内存(STM)
          同步方式: 事务提交/回滚
          适用场景: 复杂事务逻辑、高并发状态共享
          优缺点: |
            原子性保证 ✅
            无锁但冲突回滚开销大 ❌（备注：lock-free的两个经典问题了，ABA和回滚开销）

        - name: 数据级并行
          核心机制: SIMD指令集
          通信机制: 共享显存(GPU)
          同步方式: 屏障同步
          适用场景: 科学计算/图形处理
          优缺点: 吞吐量极高；仅适合规则数据并行任务

        - name: Lambda架构
          核心机制: 批处理层+速度层
          通信机制: 分布式消息队列
          同步方式: 时间窗口合并
          适用场景: 大数据实时分析
          优缺点: 容错性强；需维护两套系统



    - topic: d
      des: |
        ***Compare memory allocators. (jemalloc, TCMalloc, PTMalloc(glibc默认Malloc), Mimalloc)***

        (mechanism, )

        两个方面

        - 系统向：看内存管理库是如何管理空闲内存的
        - 用户向：看用户程序如何向内存管理库申请内存(释放大致相似，可以参考申请)

        应该从 锁机制、内存利用率、内存分配粒度和效率、内存碎片 这5点来进行比较，其实都是“切分内存多级管理”来提高内存利用率和减少内存碎片的（当然具体切分粒度不同，比如Jemalloc的small, large, huge分别是56KB, 4MB，而TCMalloc则是256KB, 1MB），最大的区别在于锁机制（为了减少锁竞争，提高并发）的优化，比如PTMalloc直接mutex了，而其他三个则都有针对性优化，分别选择了“多个arena”、“小对象pool+大对象spinlock”、“lock-free”。

        ---

        ```markdown
        Compare

        The biggest advantage of jemalloc is its powerful multi-core/multi-thread allocation capability. The more cores the CPU has, the more program threads, and the faster jemalloc allocates

        When allocating a lot of small memory, the space for recording meta data of jemalloc will be slightly more than tcmalloc.

        When allocating large memory allocations, there will also be less memory fragmentation than tcmalloc.

        Jemalloc classifies memory allocation granularity more finely, it leads to less lock contention than ptmalloc.

        TEMERAIRE enhancement is beneficial in scenarios where high-performance memory allocation is crucial, and where optimizing for hugepages can lead to significant efficiency gains in large-scale environments
        ```

      table:
        - name: TCMalloc #  TCMalloc是专门对多线并发的内存管理而设计的，TCMalloc主要是在线程级实现了缓存，使得用户在申请内存时大多情况下是无锁内存分配。整个 TCMalloc 实现了三级缓存，分别是ThreadCache(线程级缓存)，Central Cache(CentralFreeeList)，PageHeap(页缓存)，最后两级需要加锁访问。*TCMalloc 的核心思想是，切分内存多级管理降低锁粒度*(把内存切成几块，通过多级管理降低锁的粒度)；将可用的堆内存采用二级分配的方式进行管理：每个线程都会维护一个独立的内存池，进行内存分配时优先从该内存池中分配，当内存池不足时，才会向全局内存池申请，以避免不同线程对全局内存池的频繁竞争。*使用多级缓存将对象大小分类，并按照类别使用不同的分配策略*。*TCMalloc 的核心思想是，切分内存多级管理降低锁粒度*(把内存切成几块，通过多级管理降低锁的粒度)；将可用的堆内存采用二级分配的方式进行管理：每个线程都会维护一个独立的内存池，进行内存分配时优先从该内存池中分配，当内存池不足时，才会向全局内存池申请，以避免不同线程对全局内存池的频繁竞争。TCMalloc 把内存分成若干大小的 class 块，这种算法由于需要将对象的内存映射到最接近的 class，因此会有内存浪费的问题。但是分成的若干级别可以 free-lock 进行访问，这在多线程程序中，性能会大大提升。
          url: https://github.com/google/tcmalloc
          锁机制: 线程缓存无锁 + 中央堆自旋锁
          内存利用率: 较高（两级缓存结构）
          分配粒度: "small(256KB), large(1MB)"
          分配效率: 小对象极致优化
          内存碎片: 中等（spans管理机制）
          适用场景: 高频小对象分配
          核心优势: 基于RSEQ的免锁设计
          内存回收: 定期垃圾回收线程缓存
          系统调用: 用户态无中断分配

        - name: jemalloc
          url: https://github.com/jemalloc/jemalloc
          des: facebook.  来自FreeBSD的内存分配器，在Firefox和Rust里被使用.
          锁机制: 多arena分区锁
          内存利用率: 高（细粒度三级管理）
          分配粒度: "small(56KB), large(4MB), huge"
          分配效率: 多核线性扩展
          内存碎片: 最少（隔离不同尺寸对象）
          适用场景: 多核高并发服务/数据库
          核心优势: 通过线程绑arena减少竞争
          大内存策略: 直接mmap分配


        - name: PTMalloc (glibc)
          锁机制: 全局互斥锁
          内存利用率: 较低（单一堆管理）
          分配粒度: 统一brk分配
          分配效率: 多线程瓶颈严重
          内存碎片: 最多（外碎片问题）
          适用场景: 简单单线程程序
          核心优势: 有限thread arena支持

        - name: Mimalloc
          url: https://github.com/microsoft/mimalloc
          des: ms. better perf than others. 在微软的云服务, 虚幻引擎中使用.
          锁机制: 分段原子操作（非完全lock-free）
          内存利用率: 极高（分页块延迟重用）
          分配粒度: 统一分页策略
          分配效率: 高并发友好
          内存碎片: 极少（局部分配策略）
          适用场景: 云原生/高频分配
          核心优势: 安全指针防护
          内存安全: 防use-after-free
          优化方向: 对GC友好设计
      qs:
        - How TCMalloc works? (CCMalloc, local memory-pool(multi-level, BestFit, free-lock), RSEQ)
        - TCMalloc (Thread Caching Malloc) = CCMalloc (CPU Caching Malloc)
        - “为每个线程预先分配一块缓存，线程申请小内存时，可以从缓存分配内存”，这样有 3 个好处：
        #  - 引入虚拟内存后，让内存的并发访问问题的粒度从多进程级别，降低到多线程级别，这是快速分配内存的第一个层次
        #  - 为线程预分配缓存需要进行一次系统调用，后续线程申请小内存时，从缓存分配，都是在用户态执行，没有系统调用，*缩短了内存总体的分配和释放时间*，这是快速分配内存的第二个层次
        #  - 多个线程同时申请小内存时，从各自的缓存分配，访问的是不同的地址空间，无需加锁，*把内存并发访问的粒度进一步降低了*，这是快速分配内存的第三个层次
        - jemalloc 是怎么通过使用多个 arena 来减少多线程间锁竞争? # [从“丰巢”快递柜看 jemalloc 的内存管理](https://mp.weixin.qq.com/s?__biz=Mzg5NTcxNzY2OQ==&mid=2247486582&idx=1&sn=5fc658ca11f3750d59c0818297c3301a)
        - 怎么用 jeprof 来定位MySQL的内存OOM问题?
        - MiMalloc 是lock-free的吗? 怎么实现的呢?



    - topic: "***GC 基本认知***"
      picDir: langs/GC
      qs:
        - 首先要从什么是 GC，以及为什么需要 GC 说起（堆空间和栈空间） # GC 是一种自动管理内存的技术，用来回收堆空间中不再使用的对象。
        #- 栈空间一般较小，在一个函数调用时用来分配其内部变量，在函数调用结束后自动回收，不需要 GC
        #- 堆空间一般较大，可以在多个函数间共享数据，需要 Allocator 进行分配，需要 Collector 进行回收

        - GC的收集和清理两个阶段 # GC 的两个阶段，说白了就是首先区分有用对象和垃圾对象，然后清理掉垃圾对象回收内存 (从而让我们可以重复利用这部分内存)
        - 几个GC相关的常见概念（mutator、collector、locality、STW、根对象）
        #根对象：GC 在标记过程中最先检查的对象，比如
        #  全局变量：程序在编译期就能确定的存在于程序整个生命周期的变量
        #  执行栈：每个协程都包含自己的执行栈，比如栈上的变量以及指向分配的堆内存区块的指针
        #  寄存器：寄存器的值可能表示一个指针，参与计算的这些指针可能指向某些赋值器分配的堆内存区块
        #STW: 用户程序 mutator 在 collector 执行回收过程中不可用
        #mutator: 用户程序的线程，可以修改堆空间
        #collector: 用来 GC 的线程
        #本地性 locality: 现代 CPU 在访问内存时，有多级缓存缓存以`cache line`为最小操作单位，所以当访问内存中连续的数据时，会比较高效

        - "***GC的核心需求：如何设计一个好的 GC 算法（设计 GC 算法的时候，要考虑哪些因素？）？所有的方案都是为了满足这些需求（关注的指标：CPU(utilization and throughput, locality), STW(time, freq), memory fragmentation）? 简单来说就是STW的时间和频率、CPU利用率和吞吐量***"
        #- 吞吐量和开销
        #  - `程序吞吐量`：回收算法会在多大程度上拖慢程序？有时候，这个是通过回收占用的 CPU 时间与其它 CPU 时间的百分比来描述的
        #  - `GC吞吐量`：在给定的 CPU 时间内，回收器可以回收多少垃圾？
        #  - `堆内存开销`：回收器最少需要多少额外的内存开销？如果回收器在回收垃圾时需要分配临时的内存，对于程序的内存使用是否会有严重影响？
        #- 暂停
        #  - `暂停时间`：回收器会造成多长时间的停顿？
        #  - `暂停频率`：回收器造成的停顿频率是怎样的？
        #  - `暂停分布`：停顿有时候很短暂，有时候很长？还是选择长一点但保持一致的停顿时间？
        #- 分配性能：新内存的分配是快、慢还是无法预测？
        #- 压缩
        #  - 当堆内存里还有小块碎片化的内存可用时，回收器是否仍然抛出内存不足 (OOM) 的错误？
        #  - 如果不是，那么你是否发现程序越来越慢，并最终死掉，尽管仍然还有足够的内存可用？
        #- 并发：回收器是如何利用多核机器的？
        #- 伸缩：当堆内存变大时，回收器该如何工作？
        #- 调优：回收器的默认使用或在进行调优时，它的配置有多复杂？
        #- 预热时间：回收算法是否会根据已发生的行为进行自我调节？如果是，需要多长时间？
        #- 页释放：回收算法会把未使用的内存释放回给操作系统吗？如果会，会在什么时候发生？
        #- 可移植性：回收器是否能够在提供了较弱内存一致性保证的 CPU 架构上运行？
        #- 兼容性：回收器可以跟哪些编程语言和编译器一起工作？它可以跟那些并非为 GC 设计的编程语言一起工作吗，比如 C++？它要求对编译器作出改动吗？如果是，那么是否意味着改变 GC 算法就需要对程序和依赖项进行重新编译？

        #  GC 关注的指标有哪些？
        #  Go 的 GC 被设计为成比例触发、大部分工作与赋值器并发、不分代、无内存移动且会主动向操作系统归还申请的内存。因此最主要关注的、能够影响赋值器的性能指标有：
        #  CPU 利用率：回收算法会在多大程度上拖慢程序？有时候，这个是通过回收占用的 CPU 时间与其它 CPU 时间的百分比来描述的。
        #  GC 停顿时间：回收器会造成多长时间的停顿？目前的 GC 中需要考虑 STW 和 Mark Assist 两个部分可能造成的停顿。
        #  GC 停顿频率：回收器造成的停顿频率是怎样的？目前的 GC 中需要考虑 STW 和 Mark Assist 两个部分可能造成的停顿。
        #  GC 可扩展性：当堆内存变大时，垃圾回收器的性能如何？但大部分的程序可能并不一定关心这个问题。


        - 为啥我们现在都是用Tracing类GC（比如MS），而不是RC呢？ # 需要说明一点，RC 类 GC 同样有前两个问题，但是对于 RC 来说，并没有好的优化措施来缓解。

        - MS是什么：MS的mark和sweep两步的具体机制？ # 标记有用的对象（也就是不需要回收的对象），而不是垃圾。举个例子，就是打扫房间，把有用的东西打上标签，放在原处不动，再清理房间。但是这种方法，STW 肯定会很长，并且肯定无法清理干净，因为房间角落里肯定有很多清扫不到的灰尘，所以就有了很多优化方法。
        #- mark，从 root 开始进行树遍历，每个访问的对象标注为「使用中」
        #- sweep，扫描整个内存区域，对于标注为「使用中」的对象去掉该标志，对于没有该标注的对象直接回收掉

        - "***MS本身的四大问题***"
        #   MS 有以下问题：
        #   - heap 容易出现碎片
        #   - 破坏引用本地性（由于对象不会被移动，存活的对象与空闲空间交错在一起）
        #   - GC 时间与 heap 空间大小成正比
        #   - 在进行 GC 期间，整个系统会被挂起，即 stop-the-world

        - MS优化方案：针对以上四大问题，MS 有哪些优化方案？ （分别针对mark阶段（Bitmap Marking, Incremental GC）和sweep阶段（Mark Compact, Lazy Sweeping, semispace(cheney, baker), 分代回收））请分别聊聊这几种MS优化方案的工作机制
        #  比如 Mark Compact 相比于 MS 优点在于能够解决`内存碎片`问题。缺点是更耗时，需要多次遍历堆空间，第一次清除对象要真正移到的新位置，接下来的遍历来真正移动对象和更新指针。具体来说，就是把有用的东西码到一起，把房间剩余空间清理干净，这样就会比 MS 干净很多。
        #  比如 Incremental GC 就是高频标记和清理，房间稍有杂物，就马上标记并清理，这样每次 STW 时间很短，并且也很干净。
        #  再比如分代式，就是按照物品在房间内存在时间分为刚需品和其他物品，刚需品不参与回收，其他物品按照拥有该物品的时间长短分为新物品和老物品，对新物品高频清理，对老物品低频清理。

        - 分代标记：分代回收要详细看看，因为 js/python/JVM 都使用了分代的思想。分代回收是根据生命周期的特点来优化 GC。分代的基本思想是 *"大部分对象在产生后，就会很快变成垃圾而已经存在很久的对象，往往不会变成垃圾"* ，所以我们要把对象分代，避免在老生代上浪费时间。 # 分代回收的核心就三条：1、“高频地对新生代进行小回收，低频地对新生代和老生代一起进行大回收” 2、小回收后还存活的对象就是老生代，小回收遇到老生代直接跳过。 3、对新生代使用复制收集，因为小回收中垃圾的比例高。

        - 增量式标记（三色标记）：黑色（可到达对象，对象被 collector 访问过）、灰色（可到达对象，但是子节点还没被扫描到）、白色（未到达对象 (如果本轮遍历结束时还是白色，就会被 GC)）。说白了就是，已经访问过的就是黑色，还没被扫描的是白色，其他的就是灰色。 # 增量式标记指的是，允许 collector 分多次执行，所以会导致 STW 变短（经过优化可以达到近似实时的效果）。难点在于，在 collector 遍历引用关系图时，mutator 可能会改变对象间的引用关系。举个例子，已经完成扫描和标记的对象被修改，对新对象产生引用，那么这个新对象就不会被标记，最终会被回收。但是本身是应该被标记的，不被回收的。 *说白了就是新对象存在老对象对他的引用，不应该是垃圾。* 既然增量式 GC，那么就不是传统 MS 的非黑即白，所以需要引入灰色对象这也就是三色标记。
        - 狭义上来说，我们所说的增量式GC实际上指的就是三色标记，但是广义上来说，也有其他增量式GC方案，比如说增量复制和增量压缩（实际上就是增量式分别和copying GC和mark compact的结合使用）。三色标记的问题在于无法并行执行增量，所以我们需要内存屏障来解决“mutator 可能在 GC 的标记过程中修改对象指针”的问题

    # [垃圾回收的认识 | Go 程序员面试笔试宝典](https://golang.design/go-questions/memgc/principal/) 这个其实就是那个“Go GC 20 问”
    # [Go语言GC实现原理及源码分析 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/475)
    - topic: golang GC
      picDir: langs/GC
      qs:
        - "***三色标记：为什么golang会选择使用三色标记（并发增量），而不是分代或者复制了？那么为什么 golang 不使用分代或者复制 (整理) 方案？***" # 不分代是因为compiler本身实现了对新生代和老生代的分配。不复制是因为golang使用TCMalloc内存分配算法，不存在内存碎片问题。

        #  不分代的原因：
        #- *golang 编译器会通过逃逸分析把大部分新对象放到栈上，只有长期使用的老对象才分配到堆上*(协程结束后，栈直接回收，不需要 GC 参与)
        #- golang GC 的目标除了减少 STW，更重要的是让 mutator 和 collector 并行执行，而因为并发执行，所以 STW 的时间和对象的代际和 size 没有关系
        #  不复制的原因：
        #- *golang 使用 TCMalloc 内存分配算法，不存在内存碎片问题*(复制收集的原理是 xxx)
        #- 顺序内存分配器无法在并行场景下使用

        #  为什么 golang 要使用三色标记？
        #- 我们知道 golang 比较适合网络高并发的服务场景，那么如果使用 STW 时间较⻓的 GC 算法，对服务来说是致命的，故而要选用 STW 时间较少的算法，在标记清除的基础上发展来的三色标记出现了。
        #- 三色标记的思想其实是尽量把标记阶段、清除阶段与程序同时跑，它其实是一种增量式 GC 算法，所谓增量式其实就是把 GC 过程拆分出来的意思，跟我们要把最大的 STW 时间减少的思想吻合。

        - Golang的内存模型中为什么小对象多了会造成GC压力 # 通常小对象过多会导致GC三色法消耗过多的GPU。优化思路是，减少对象分配

        # [从源码剖析Go语言基于信号抢占式调度 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/485)
        - golang GC相关源码?
        # gc.go - 这个文件包含了垃圾回收的主要逻辑和控制流程。
        # mheap.go - 管理堆内存的源码，包括内存的分配和回收。
        # mgc.go - 包含了与并发垃圾回收相关的后台标记和清扫工作。
        # gcbss.go - 包含了BSS段的垃圾回收逻辑。
        # gcmark.go - 包含了标记阶段的实现细节。
        # gcsweep.go - 包含了清扫阶段的实现细节。
        # https://github.com/golang/go/tree/master/src/cmd/compile/internal/gc
        # [go/src/runtime/mgc.go at master · golang/go](https://github.com/golang/go/blob/master/src/runtime/mgc.go)

        - golang GC的演进历史? # [一文搞懂Go GC演进史，讲的太细致了！_Go_王中阳Go_InfoQ写作社区](https://xie.infoq.cn/article/f56b419e9de2e8ca3d44ee0ce)

        - 内存屏障是啥？为啥有了MESI还需要内存屏障？ “内存屏障其实就是编译器帮你生成的一段 hook 代码，这三种写屏障的本质区别就是 hook 的时机不同而已。内存屏障是 mutator 读取/创建/更新对象时，执行的一段代码我们用`内存屏障`保证内存操作的`有序性`，也就是，内存屏障前的指令优先于屏障后的指令执行”

        - 读屏障和写屏障的对比：为啥不用读屏障，而用写屏障呢？ # 归根到底是因为读操作远多于写操作，并且。读屏障需要在读操作中加入代码片段，对 mutator 的性能影响很大，所以不使用。写屏障则是 (通过记录新对象对老对象的引用)，对所有涉及修改的内容进行保护。写屏障类似一种开关，在 GC 的特定时机开启，开启后指针传递时，会把指针标记 也就是说，*本轮不回收，下次 GC 时再确定*。
        - 写屏障到底做了哪些操作？ # 写屏障就是在编译时hook住一部分写操作（并非全部写操作），把这部分操作放到buffer里，然后批量置灰
        #- hook 写操作
        #- hook 住了写操作之后，把赋值语句的前后两个值都记录下来，投入 buffer 队列
        #- buffer 攒满之后，批量刷到扫描队列（置灰）（这是 GO 1.10 左右引入的优化）

        - Dijkstra 插入屏障 和 Yuasa 删除屏障 # “为了确保强弱三色不变性的并发指针更新操作，需要通过赋值器屏障技术来保证指针的读写操作一致。因此我们所说的 Go 中的写屏障、混合写屏障，其实是指赋值器的写屏障，赋值器的写屏障作为一种同步机制，使赋值器在进行指针写操作时，能够“通知”回收器，进而不会破坏弱三色不变性。”

        # [golang 垃圾回收（五）混合写屏障](https://liqingqiya.github.io/golang/gc/%E5%9E%83%E5%9C%BE%E5%9B%9E%E6%94%B6/%E5%86%99%E5%B1%8F%E9%9A%9C/2020/07/24/gc5.html)
        - GC 为什么需要混合写屏障？ # Go 在 1.8 的时候为了简化 GC 的流程，同时减少标记终止阶段的重扫成本，将 Dijkstra 插入屏障和 Yuasa 删除屏障进行混合，形成混合写屏障。该屏障提出时的基本思想是：对正在被覆盖的对象进行着色，且如果当前栈未扫描完成，则同样对指针进行着色。【golang就是结合两种写屏障，实现了混合写屏障。】

        - 插入写屏障和删除写屏障的时机和区别？golang 中怎么实现的？
        - 混合写屏障
        #- 混合写屏障继承了插入写屏障的优点，起始无需 STW 打快照，直接井发扫描垃圾即可；
        #- 混合写屏障继承了删除写屏障的优点，赋值器是黑色赋值器，GC 期间，任何在栈上创建的新对象，均为黑色。扫描过一次就不需要扫描了，这样就消除了插入写屏障时期最后 STW 的重新扫描栈；混合写屏障扫描精度继承了删除写屏障，比插入写屏障更低，随着带来的 是 GC 过程全程无

        - 强三色和弱三色
        #- 强三色：黑色对象不能指向白色对象，只能灰色或者黑色对象
        #- 弱三色：黑色对象指向的白色对象，必须包含一条从灰色对象经由多个白色对象的可达路径

        - 强三色不变式：灰色或者黑色对象的引用改为白色对象的时候，Golang是该如何操作的？ # 这时候，写屏障机制被触发，向GC发送信号，GC重新扫描对象并标位灰色。因此，gc一旦开始，无论是创建对象还是对象的引用改变，都会先变为灰色。

        - "***golang GC的大概执行过程（四个阶段）？STW 发生在什么时候（GC中STW时机）？***"
        - golang如何实现的“并发三色标记扫描”？
        - golang GC现在还存在哪些问题
        - golang GC有哪些优化？为何需要辅助标记和辅助清扫？
        - golang为什么选择使用三色标记，而不是分代或者复制？
        - 什么时候启动GC？
        - 通过哪些指标来判断要启动GC？
        - GC应该如何与scheduler进行交互？
        - 如何暂停一个mutator线程足够长时间，以扫描器stack？
        - 如何表示white、grey和black三种颜色来实现高效地查找、扫描grey对象？
        - 如何知道roots对象在哪里？
        - 如何知道一个指向对象的指针的位置？
        - 如何最小化内存碎片？
        - 如何解决cache性能问题？
        - heap应该设置为多大？
        - 怎么调优golang GC？

        - GC 的四个阶段？
        - 为什么需要辅助标记和辅助清扫？
        - GC 的四个阶段，STW 发生在什么时候？GC 中 stw 时机，各个阶段是怎么解决的？
        #以下是Go GC中的STW时机和如何解决的：
        #- 开始STW：在开始标记阶段之前，需要进行一次短暂的STW，以确保所有的P（处理器）都停止在安全点，即一个不会修改堆的位置。这个STW时机是必要的，因为它能确保在标记阶段开始时，所有的goroutine都不会创建新的对象或修改现有对象的指针。这个STW的时间通常非常短。
        #- 结束STW：在标记阶段结束后，需要进行一次短暂的STW，以确保所有的goroutine都停止在安全点，然后进行最后一次的标记，并开始清除阶段。这个STW时机也是必要的，因为它能确保在清除阶段开始时，所有的对象都已经被正确地标记。这个STW的时间也通常非常短。
        #在标记阶段，Go的GC采用了并发标记的策略，即在goroutine运行的同时进行标记。这是通过写屏障（Write Barrier）实现的，写屏障在每次写入指针时都会标记该指针。这样，即使在标记阶段有新的对象被分配或旧的对象被更新，GC也能正确地标记这些对象。
        #在清除阶段，Go的GC采用了并发清除的策略，即在goroutine运行的同时进行清除。这是通过延迟清除（Lazy Sweeping）实现的，即只在需要分配新的对象时才清除那个对象所在的内存块。

        - GC 触发时机？主动触发 runtime.GC，周期被动触发，Pacing 算法（其核心思想是控制内存增长的比例。如 Go 的 GC 是一种比例 GC, 下一次 GC 结束时的堆大小和上一次 GC 存活堆大小成比例.）

        - 描述一下 GC 调步算法的实现？
        - GC 清扫阶段 对象回收和内存单元的联系和差异？
        - 根对象到底是什么？
        - 有了 GC，为什么还会发生内存泄露？
        - 并发标记清除法的难点是什么？ # 在没有用户态代码并发修改 三色抽象的情况下，回收可以正常结束。但是并发回收的根本问题在于，用户态代码在回收过程中会并发地更新对象图，从而造成赋值器和回收器可能对对象图的结构产生不同的认知。这时以一个固定的三色波面作为回收过程前进的边界则不再合理。【总结：并发标记清除中面临的一个根本问题就是如何保证标记与清除过程的正确性。】

        - 有了 GC，为什么还会发生内存泄露？（1、预期能被快速释放的内存，因为被根对象引用，而没有得到迅速释放。2、goroutine 泄露）
        - "***通过保证三色不变式来保证回收的正确性，通过写屏障来实现业务赋值器和 gc 回收器正确的并发的逻辑***"

        - “STW 是全局的赋值器挂起，我们一直说 golang 消除了 STW 说的是没有了全局性的挂起，但是局部的赋值器挂起是一直有的，包括现在也是有的。”




    - topic: temp
      qs:

        - graph的点边图（node, edge, graph）分别是啥？分别可以类比为网站的什么？"tree的本质也是graph" # [浅谈理论：为什么图论是当今各行各业必备的知识？](https://mp.weixin.qq.com/s?__biz=MzI2MzEwNTY3OQ==&mid=2648987348&idx=1&sn=e13b2fdc8ba957c1a5a8723e539ecb18#rd) "咱们可以将某个网站的每个「网页」（Web Page）想象成一个「节点」，页面上「超链接」（Hyperlink）就是对应的「边」，而「网站」（Site）就是「图」，其包含所有的网页（节点）以及网页关系（边）"

        - "***排查问题***" # *[一次 Rancher 内存占用过高问题排查 - 掘金](https://juejin.cn/post/7319330542101069836)* 非常精彩的排查过程，抽丝剥茧，应该说是目前所有开发团队主程都应有的水平

        - “通过半结构化的采访中可以看出，礼貌用语、自信有力和教科书式的答案，再加上全面性和答案中的因果关系，这些能让完全错误的答案显得正确” 相比于使用GPT，更重要的是学习GPT的思考方式，怎么学习"GPT的思考方式"？ # [ChatGPT 正确回答代码问题的几率比抛硬币还要差_生成式 AI_Thomas Claburn_InfoQ精选文章](https://www.infoq.cn/article/s112l2YWcRwmIDkRaNqF) 理不直，气也壮是吧

        - 相比于技术，工程能力可能是更好的护城河。"技术不会一直是壁垒，算法尤其传播得快。工程能力可能更关键。因为一个算法上的好点子，往往被执行后 ROI 极高，所以大家都会做；工程优化则是每���单点的 ROI 都不大，需要日积月累，到最后真正会做的团队非常少。" # [58: 光年之外联创再出发，与袁进辉聊 AI Infra 到底做什么？| AI 大爆炸 - 晚点聊 LateTalk | 小宇宙 - 听播客，上小宇宙](https://www.xiaoyuzhoufm.com/episode/65b19c9bc2bedd4be809a48a) 之前我把LLM类比为linux，但是听完这期播客，感觉相比于linux，实际上LLM更类似搜索引擎在第三次工业革命中的地位。某种看上去高不可攀，用起来奇妙无穷，但是会快速普及的某种应用。可能会产生一两家big tech，但也就仅此而已了。

        - 为啥说"云服务的本质是某种智商税和保护费"？但是事实上云服务在攻城拔寨呢？ # [FinOps��点是下云](https://mp.weixin.qq.com/s/Yp_PU8nmyK-NVq0clD98RQ) "这里的核心问题是：硬件资源的价格随着摩尔定律以指数规律下降，但节省的成本并没有穿透云厂商这个中间层，传导到终端用户的服务价格上。 逆水行舟，不进则退，降价速度跟不上摩尔定律就是其实就是变相涨价。以S3为例，在过去十几年里，云厂商S3虽然名义上降价6倍，但硬件资源却便宜了 26 倍，所以 S3 的价格实际上是涨了 4 倍。"、"或者说公有云的本质就是某种"共享经济"，共享顶级运维团队。但是这些公有云服务真的提供与价格匹配的服务了吗？绝大部分都是及格线水平的"大锅饭"。"。 反方意见："以我司为例出海的。如果使用aws，那么很多合规的问题，我们可以不考虑，自己下云的时候，那就考虑很多合规的问题。比如GDPR，加州隐私条令等，或者是数据传输，连SCCs这种他自己也做了对应合规。我很同意公有云的东西就是杀猪盘，但是他们确实解决了很多潜在问题，除去合规以外。还有高可用，简单，无需运维团队&dba等。"

        - "[从三方调用到最终一致！手把手教你零侵入实现高可靠本地消息表_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1h3Q8YBEpb/) 分析的很好很细致，尤其是LocalMessageService.invoke那里save的执行顺序和RPC放到了事务外都考虑到了，使用也基本做到了无侵入+易扩展的效果，但这次本地消息表主要还是要做分布式事务，所以不用判断当前不存在事务的情况，或则再严格一点不存在事务直接抛异常。"

        - “一个男人应该有多少个钱包？三只。第一只就是你实际有多少钱。第二只o就是你的信用，人家钱包里的钞票你可以调动多少。第三只就是人家认为你有多少钱。” 影响力就是第三只钱包，自身实力就是第一只钱包。从自身实力到影响力就是一个xxx的过程。
        - 沟通也是施加影响力的一种方式 # 横向沟通、向下沟通、向上沟通

        - 王沪宁在指导复旦辩论队时说过“辩论是否会赢，不在于你驳倒说服对方，而在于评委和观众听了以后觉得有道理。怎么能让他们觉得你有道理呢？关键是要建立一个比对方宏大的理论框架，这个框架要基本包含对方有道理的论点，然后有条不紊地展现给听众说：你们看，虽然对方说的有对的地方，但他对的部分都在我方的逻辑框架之内，事物还有另外一面，是他的框架里不包括的，那些真理都在我方的理论框架范围之内！......听完觉得实在是精辟啊！”
        - 影响力，官方背书总是最方便有效的信任背书，但是这里的影响力，当然不是说爬到多高的位置，获得多大的权力。很简单，人和人之间是竞合关系，
        - "[曾经连续三年百大，美食区的顶流，如今欠债几百万，这几年我都经历了什么？！_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1xfXpYjEJP/) 有点意思，其实这事很难说一开始就是骗局，毕竟那个人饭店豪宅都是实打实的，只不过遇到疫情几年餐饮崩溃才开始东挪西凑骗钱。换个问题就清楚了，乐视是不是骗局？是从什么时候开始变成骗局的？再说现在的蔚来是不是骗局？"


        - 如何理解「对称性是高一维的拓扑序」？ # [如何理解「对称性是高一维的拓扑序」？ - 知乎](https://www.zhihu.com/question/15442729471/answer/128762768026) “对称性是高一维的拓扑序”意指对称性在物理系统中的保护机制或分类需借助更高维度的拓扑结构（如拓扑场论或不变量）描述。例如，d维的对称性效应可由d+1维的拓扑响应刻画，从而将对称性视为高一维的拓扑序表现。这一观点揭示了对称性与拓扑序在描述量子物态中的深层联系，尤其在SPT相分类和体边对应中体现其重要性。

        - "***怎么提高说话的信息密度？为啥有的人说话就很啰嗦？说话信息密度低的人，有哪些表达时的坏习惯？*** 提升建议：每日进行10分钟「概念快照」训练，随机选取专业术语，用15秒向外行解释清楚，持续21天可提升37%的信息压缩能力（基于MIT认知实验室数据）。"
        - "跟一个没有所长的人交往、交谈就像读一本乏味无聊的书。但是每个人身上总是有一些能够学到的东西，正如再烂的书也有一些可以值得深思的点。那么与这类人交往，应该从哪些方面来尝试进行思考，以提升自己呢？还是说“见贤思齐焉，见不贤而内自省也”、“三人行，必有我师焉，择其善者而从之，其不善者而改之。”"
        # 您提出的问题触及了人际交往中一个深刻的智慧命题。孔子的"三人行必有我师"并非简单的道德劝诫，而是蕴含着深刻的认识论和方法论。面对看似"乏味"的交往对象，我们可以从以下几个维度构建认知框架：
        # 一、认知训练维度
        #观察力培养：通过对方单调的言行模式，训练自己捕捉细节的能力。如记录其语言逻辑的断裂点，分析思维局限的成因。
        #反思机制建立：将对方的"无趣"作为镜子，反思自己是否存在类似的思维定式。例如其话题的单一性可能提示我们检查自身知识结构的盲区。
        #
        #二、社会认知维度
        #3. 生存策略解构：观察其社会适应方式，分析其选择"平庸"背后的心理防御机制。这种观察能增强对社会多样性认知的包容度。
        #4. 群体动力学观察：在群体互动中研究其角色定位，理解"普通"个体在群体生态中的功能价值。
        #
        #三、自我完善维度
        #5. 逆境沟通训练：将乏味对话视为心智锻炼，如同禅宗"枯坐"般修炼专注力与同理心。
        #6. 价值重构实践：主动发掘其人生经历中的非常规选择，例如其职业稳定背后可能蕴含的风险规避智慧。
        #
        #四、哲学思辨维度
        #7. 存在本质追问：通过观察"普通"的生命状态，思考"卓越"与"平庸"的评判标准的主观性。
        #8. 镜像认知建构：将其视为自我可能性的倒影，思考环境与选择如何塑造不同的人生轨迹。
        #
        #这种认知框架的建立，本质上是将人际交往转化为认知实验场域。正如现象学强调"悬置判断"，我们应当将先入为主的"乏味"标签进行现象学还原，转而以现象描述的方式重构认知路径。在这个过程中，交往对象成为认知工具，而认知主体（我们自身）的思维系统则获得升级迭代的机会。

        - 任务管理和日程管理分别以任务和时间为维度，二者怎么结合比较好。一个任务通常是跨越多天的。有什么二者结合的比较好的工具，可供参考。能否认为日程管理是纵向的，任务管理是横向的？


        - 流量染色：流量染色 是啥？有哪些可以实现 流量染色 的方案？
        #  在微服务架构中，实现流量染色的组件主要包括以下几个方面：
        #  API网关：API网关是流量进入的第一站，可以在这里对请求进行染色。染色可以基于多种规则，如host、header字段、agent终端信息、用户筛选或流量比例等 。
        #  服务发现组件：服务发现组件如Nacos，可以为服务设置元数据，标记服务实例的特征，例如灰度版本 。
        #  微服务框架：微服务框架可以提供对流量染色的支持，例如go-micro框架提供了实现多链路染色的能力，染色信息可以通过http请求的header或rpc请求的metadata在服务间传递 。
        #  Service Mesh：如Istio，可以在服务间透明地拦截网络通信并按指定规则转发，实现全链路流量路由，无需对现有代码进行修改 。
        #  分布式链路追踪工具：如Skywalking和OpenTelemetry，它们提供Tracing Baggage机制，可以携带用户自定义的键值对，实现全链路数据透传 。
        #  灰度发布平台：灰度发布平台可以结合微服务治理策略，实现基于内容或比例的灰度发布 。
        #  Java Agent技术：通过Java Agent技术，可以在运行时对服务进行增强，实现流量染色和路由，而无需修改业务代码 。
        #  容器编排平台：如Kubernetes，可以与灰度发布平台结合，实现自动化的灰度环境创建和管理 。
        #  通过这些组件的协同工作，可以实现微服务架构中的流量染色，从而进行有效的流量治理和灰度发布，确保新版本服务的平滑过渡和风险控制。

        #  流量染色是一种软件技术，它通过在流量入口处对请求添加标识，然后在基础框架层对这些标识进行解析、透传和服务路由，从而对不同标识的流量进行不同的处理。这种方法可以帮助实现环境轻量化、测试提效、降低成本和环境治理可控等目标 。
        #  实现流量染色的方案主要包括以下几个步骤：
        #  定义规则：在元数据中心维护每个环境对应的服务列表，并确保每个服务至少部署一个稳定分支 。
        #  流量染色：在流量的入口处，对请求添加标识，这些标识可以是标签或参数 。
        #  解析和透传：在基础框架层，对流量标识进行解析和透传，这可以通过解析请求头部信息或请求参数实现 。
        #  服务路由：根据流量标识，将请求路由到相应的服务实例，可以通过修改请求的URL或改变请求的路由规则来实现 。
        #  在实际应用中，流量染色可以应用于多种场景，例如：
        #  环境管理：通过流量染色可以轻松管理不同环境的服务实例，控制不同环境的访问权限和服务实例 。
        #  测试提效：对不同标识的流量进行不同处理，提高测试效率，如将一部分流量染色为异常流量，发送到待测试的服务实例以评估性能和稳定性 。
        #  降低成本：通过将不同标识的流量路由到不同服务实例，实现资源合理利用和成本降低。例如，添加缓存标识的流量可以路由到缓存服务实例，减少对后端服务的访问次数 。
        #  环境治理可控：通过流量染色更好地管理不同环境的访问权限和服务实例，避免开发人员误操作影响生产环境，同时监控和记录不同环境的访问情况，掌握系统的运行和安全状况 。
        #  此外，还有其他实践方案，如使用Istio进行服务网格的流量路由，以及使用分布式链路追踪框架进行数据透传，这些方案可以有效地支持微服务架构下的全链路灰度发布 。还有实践提到了在全链路压测中应用流量染色，通过在请求头上添加压测标识，确保整个程序上下文持有该标识，并能穿透微服务及各种中间件，实现数据隔离和风险控制 。



        - 到底啥是笛卡尔积？为啥RDB里的join是笛卡尔积？
        - union all 相较于 left join 有啥区别？

        - 之所以golang代码里，能定义const的就尽量定义成const，因为const会被compiler进行各种优化（CF之类的），而var则可能被分配到堆乃至栈上。

        - 我们的业务需要频繁的访问一个外部API，这个外部APl已经封装成了一个方法doApiCall(string)(string,error)，并且这个外部API在多个goroutine中被调用，但是此外部API要求不能有并发；两次调用至少间隔1s（以返回完成~下次调用时间为准）；我们也希望在调用出现失败时候能够重试最多3次（失败以error!=nil为准) 请用Go给出一个实现。如果doApiCall最多允许两个并发，你的实现思路是? # 之前面试的一个线上笔试题

        - 我有个问题哈，我能理解用哈希环能有效降低增删节点的成本，但是为啥普通哈希算法增删节点的成本会很高呢？这个我不太理解
        - 我们可以笼统地认为“QPS和TPS分别对应读写操作”，那如果具体来说，有哪个指标可以对应写操作指标呢？
        #  吞吐量（Throughput）：
        #  吞吐量是指系统在单位时间内处理的数据量，可以是字节数、记录数等。对于写操作来说，吞吐量可以指每秒写入的数据量。
        #  写入速率（Write Rate）：
        #  写入速率是指系统每秒可以写入的数据量，通常以字节/秒或记录/秒来衡量。
        #  IOPS（Input/Output Operations Per Second）：
        #  IOPS是衡量存储设备性能的指标，指每秒可以完成的读写操作次数。对于写操作，可以特别关注写IOPS（Write IOPS），即每秒写操作的次数。
        #  延迟（Latency）：
        #  延迟是指从发起写请求到写操作完成所需的时间。对于写操作，低延迟意味着写入速度快。
        #  TPS（Transactions Per Second）：
        #  虽然TPS是衡量事务处理能力的指标，但事务中可能包含写操作。因此，TPS也可以间接反映写操作的性能，特别是当事务主要包含写操作时。
        #  并发写入（Concurrent Writes）：
        #  并发写入能力是指系统同时处理多个写操作的能力，这反映了系统在高并发写入场景下的性能。
        #  持久性（Durability）：
        #  持久性是指数据写入后，即使系统发生故障也能保持数据不丢失的能力。对于写操作来说，持久性是一个重要的考量因素。
        #  数据一致性（Consistency）：
        #  在分布式系统中，数据一致性是指写操作后，系统内所有副本的数据状态是否一致。这是衡量写操作影响的另一个重要指标。
        #  这些指标可以帮助评估和优化系统的写操作性能。在实际应用中，可能需要根据具体的业务需求和系统架构来选择和关注最合适的指标。

        - DeviceID、IMEI、IDFA、UDID 和 UUID 这些移动端设备标识码具体都是什么？ # 安卓最好使用 UDID 作为唯一标识，苹果使用 IDFA 作为唯一标识
        - APP 首次激活，各弹窗的优先级和顺序? # 闪屏页、引导、系统提示、弹窗广告

        - 单主（single leader）复制、多主（multi-leader）复制、无主（leaderless）复制 # [数据库的数据复制与分区 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/805)

        - "***CPU密集型应用、IO密集型应用、内存密集型应用，尝试各自举几个例子***。这个问题是从THP引发的，THP适用于内存密集型应用，但是我实在想不到有哪些应用是内存密集型的。" # 内存密集型应用至少是TB级的内存吧，
        #  数据库应用：在某些复杂的数据库应用场景中，内存需求可能会非常高。例如，对于一个包含大量数据和高并发的数据库集群，单个节点的内存需求可能在10GB到20GB之间，而在10个类似SQL场景并发时，单个节点的数据库运算内存需求可能超过100GB。在另一个例子中，对于需要缓存全部热数据的GBase集群，内存需求可能达到6TB。
        #  大数据分析：在大数据分析领域，内存计算架构可以显著提高数据处理速度。例如，生命科学领域中的基因分析可能需要在短时间内处理几百GB或TB级别的数据。
        #  科学计算：科学计算任务通常需要大量内存来存储数据和中间结果。根据任务的规模和复杂度，所需内存容量会有所不同，但对于一些大规模计算任务，内存需求可能会非常高。
        #  容器化部署：在容器化部署中，大数据分析和高性能计算场景需要满足更大的内存、存储级内存、固态硬盘、NVMe、更大的存储容量等基础设施要求。

        - 如果说redis属于内存密集型应用，为啥redis优化方案都推荐关闭THP呢？ # 尽管Redis是内存密集型应用，但关闭THP是为了减少写操作的延迟，提高内存复制效率，以及优化fork操作的性能，从而提升Redis的整体性能。

        - 用户态线程到了内核空间， 会变成内核态线程？还是说还是用户态线程？ # 用户态线程在内核空间中并不会变成内核态线程，但是它们的请求会被内核态线程代理执行。用户态线程对操作系统内核是不可见的，它们通过系统调用来请求内核服务。
        - CPU访问内存以字长为单位。 # 字长是指CPU在单位时间内能够同时处理的二进制数据的位数。例如，一个64位的CPU，其字长就是64位。这个概念主要描述的是CPU处理数据的能力。例如，对于一个32位的CPU，其字长为4字节（32位），那么它在访问内存时，就会以4字节为单位进行数据的读取和写入。
        - im系统是不是都是基于UDP实现的？但是如果用UDP的话，怎么保证消息有序呢？
        - redis-sentinel和redis-cluster是不是各自都使用了raft和gossip? sentinel 用 gossip 接收 master 是否下线，cluster 使用的epoch，其实就是raft
        - 我有一个电商应用，我需要在搜索结果中过滤掉该用户所有之前购买过的商品。用es怎么实现 # 使用es的must_not

        - 线性表有两种存储方式：顺序存储和链式存储。采用顺序存储的线性表被称为顺序表，采用链式存储的线性表被称为链表。
        - "***加载配置文件的几种方式? local 还是 remote?***" # [论Golang加载配置文件的几种方式 ｜ 青训营论Golang加载配置文件的几种方式 | 青训营 在Golang项目中， - 掘金](https://juejin.cn/post/7270831230077632570) 其实操作时感觉类似灰度？

        - “无论处于人生的哪个阶段，努力的目标都是不断提高自己的独立性，而不是升职加薪。拥有独立性，为自己独特的产出后果负债（而不是像打工一样为投入的时间负责），这才是最理想的状态。人类在不断进化。曾几何时，... 后来人类发明了杠杆，发明了资本、合作、科技、生产力等各种手段，人类社会进入杠杆时代。在这样的时代，作为一名劳动者，只有最大化地发挥杠杆效应，才能利用有限的时间和体力...” 这就是我这段时间，一直在说的“人的主体性”
        - “才不足则多谋，识不足则多虑。威不足则多怒，信不足则多言。勇不足则多劳，明不足则多察。理不足则多辩，情不足则多仪。”

        - cf不同付费计划，cloudflare规则和WAF规则不同？免费用户支持五秒盾？

        - "***这类代码复用方面的解决方案，无非几种：泛型、接口、反射、代码生成、设计模式。还有其他方法吗？这些方法有啥区别？***"


        - 怎么理解“antd 的 accessibility 支持的没有 MUI 好，是很大的原因。国外比较看重 accessiblity.”。这个是啥意思？MUI相对于ANTD在accessibility方面具体有哪些优化？分别怎么实现的？ # 3点：1、键盘操作（比如说键盘可以直接通过上下键选择sidebar，可以直接enter查看内容。整个页面都可以通过键盘操作。）2、视觉无障碍（MUI提供了对比度更高的文本颜色和背景颜色，使得低视力用户能够清晰地分辨按钮的状态。）3、语义化HTML（比如说ele用div模拟button，就不便于理解，MUI则完全遵循HTML5的语义化规范）
        - golang的代码文件名是不是推荐使用下划线，而不是camel case? golang的文件名推荐下划线，变量名推荐camalcase是吗？
        - rustdesk自建 相比于 网易UU 这种远程工具，真的更流畅吗？那相比于时下流行的 tailscale/netbird 怎么说？还有，rustdesk 也是内网穿透，那和ngrok是一码事吗，有啥区别？

        # 1、可拓展性（同名interface可以直接合并，但是type一旦定义就无法拓展了，若要添加新属性，只能重新定义类型）
        # 2、类型定义范围（interface只能用于定义对象类型，type除了对象还能定义基本类型、联合类型、交叉类型、元组等）
        # 3、实现方式（interface可以被class实现，type不能被class直接实现，但可借助交叉类型模拟实现）
        # 4、alias（interface 并非真正的alias，只是对类型结构的定义。type 是类型的alias，能为任意类型创建别名。）
        - ts 的 interface 和 type 有啥区别？语法定义、可拓展性、类型定义范围、实现方式、alias使用

        - 我有个问题，我有一个vps项目，里面是我需要部署到vps的所有服务。我通常使用ansible来初始化服务器，我是否需要把ansible相关代码作为vps项目的一部分？这样可能便于维护？


        # [简单易懂的幂等（附示例）](https://mp.weixin.qq.com/s?__biz=Mzg2ODU1MTI0OA==&mid=2247486390&idx=1&sn=3242a300d41c3845ac578ed19e1ba33f)
        - 幂等到底是啥？其实我对幂等这个概念本身都有一定质疑，很多文档里说的是，“多次操作产生的影响只会跟第一次执行的结果相同。也就是多次执行的结果，跟第一次都是相同的”里面举的例子是，如果多次写操作，如果成功就必然成功，失败就必然失败。不存在第一次成功，后面就失败了的情况。但是如果API被限流了呢？服务被降级了呢？所以我对这个概念本身都有一定质疑。我认为并发请求幂等是合理的，做API请求的合法性校验也是合理的，但是这个东西理解不了。对编程来说，幂等更接近于函数式编程的感觉，也就是返回数据只跟参数有关。f(x)= y 是吗？
        - 到底啥是“胶水层”，能否给我用 feed流模块 这种实际场景作为举例来说明？ 可以狭义地认为后端curd就是胶水层
        - ts yield 有哪些实际使用场景吗？或者说，在什么场景下用yield更好？
        - navmesh多边形网格寻路：德劳内三角剖分、漏斗算法是如何实现导航系统？


        - golang sync.Pool 有哪些问题？怎么改造？ # [别再用 sync.Pool 了！Go 对象池的正确打开方式 - 掘金](https://juejin.cn/post/7468203211725242422)

        - "***API幂等和防重放提交有啥区别？二者分别怎么解决？***" #

        - 错误处理：1、错误处理的历史？2、到底用exception还是error？哪个才是正确的错误处理方式？说白了，到底使用java/python/php这种 try...catch 的错误处理，还是golang/rust这种直接返回的错误处理？ # [什么是正确的错误处理方法【让编程再次伟大#21】\_哔哩哔哩\_bilibili](https://www.bilibili.com/video/BV1gJS9YeEsz/)


        - 列举了图数据库的10大使用场景：1、欺诈检测 2、实时推荐引擎 3、知识图谱 4、反洗钱 5、主数据库管理 6、供应链管理 7、增强网络和IT运营管理能力 8、数据谱系 9、身份和访问管理 10、材料清单 # [Neo4j-Top-Use-Cases-ZH.pdf](https://go.neo4j.com/rs/710-RRC-335/images/Neo4j-Top-Use-Cases-ZH.pdf)

        - 是否有必要囤积长期低配VPS？ 这个问题有两个关键词：长期、低配。作者基于以下原因反对：1、长期：硬件发展速度很快，现在觉得性价比高的，过个两三年可能什么都干不了。2、低配：随便什么服务都动辄4C8G起步，低配的1C1G机器啥都干不了。有这个钱不如直接买mac mini，把mac服务穿透到公网使用。或者买高配VPS。 个人观点：双方都有自己符合实际情况的理由，还是看个人情况。 # [【讨论】2025 年了，还在囤积长期低配云服务器？来聊聊性价比这件事 - 搞七捻三 - LINUX DO](https://linux.do/t/topic/379862) 颇有道理

        - 域名：那就是只要在域名到期之前，我转移到cf，就可以续期一年是吗？转入费用通常就是一年的续期费用是吗？意思是只要我提前把域名转移到cf，就可以在cf这边直接续费了是吗？那我的问题是，我想买的域名在dynadot更便宜，所以我首年在这里买。但是cf上买10年更便宜。这种情况的话，是没办法在dynadot直接续期的，怎么避免被抢注呢？
        - 我想买的域名被人抢注了怎么办？大概就是说我有一个域名，然后我本来想的是到期之后再续费的，然后我在 Godaddy 上面去托管的，结果我的域名到期之后，Godaddy 直接帮我直接把我这个域名抢住了，然后然后又加价，我之前这个域名大概 15 块钱一年吧，然后给我代理抢住之后，现在价格变成 170 了，170 一年的话，我现在这个怎么办呢？然后我怎么能够防止现在我重新注册了另外一个新的域名嘛？然后我这之前的域名我也不用了，然后我现在打算去我想注册一个新的域名，然后但我又不想说是直接用，直接注册 10 年嘛？那你觉得我是我怎么能够防止说是他提前，他提前去注册我，是不是相当于是我到时候提前去续费就好了？比方说我不要等到那个过期之后再去续费，我比方说提前还有一个月的时候，我就把这个域名提前续费一下，是这样子吗？可以吗？

        - 能否以列表形式对px、em、vw/vh三种移动端适配方案的比较？
        #**px（像素）**
        #- **特点**：是绝对单位，在屏幕上具有固定的物理尺寸，1px代表屏幕上的一个物理像素点。
        #- **适配优势**：在设计稿到移动端实现的过程中，如果设计稿精度高且移动端设备屏幕分辨率单一，使用px可以精准还原设计稿，对于一些不需要考虑适配多种屏幕尺寸的特定场景，如某些固定尺寸的广告展示区域，使用px能确保元素尺寸和位置的准确性。
        #- **适配劣势**：当面对不同屏幕尺寸和分辨率的移动设备时，使用px作为单位的元素无法自适应调整大小，会导致在大屏幕上元素显得过小，在小屏幕上元素可能超出屏幕范围，布局错乱，用户体验差。
        #- **使用场景**：适用于对设计精度要求极高且目标设备屏幕尺寸固定的情况，比如特定的智能手表应用界面设计，其屏幕尺寸和分辨率相对固定。
        #
        #**em**
        #- **特点**：是相对单位，相对于父元素的字体大小来计算自身的尺寸。如果自身没有设置字体大小，则继承父元素的字体大小作为基准。
        #- **适配优势**：在文本排版方面具有优势，当父元素字体大小发生变化时，子元素可以通过em单位根据父元素字体大小的变化而相应调整，实现相对一致的视觉效果。可以用于实现一些基于字体大小的响应式布局，如导航栏的高度随着字体大小的变化而自适应调整。
        #- **适配劣势**：由于其相对父元素字体大小的特性，在多层嵌套的元素中，计算其实际尺寸会变得复杂，容易出现意想不到的结果。而且在进行非文本元素的布局时，如宽度、高度等，可能需要额外的计算和调整，不如vw/vh直观和方便。
        #- **使用场景**：主要用于文本相关的元素尺寸设置，如段落文本的行高、字间距等，以及一些需要根据字体大小进行自适应的简单布局场景，如简单的列表项。
        #
        #**vw/vh**
        #- **特点**：也是相对单位，vw相对于视口宽度，vh相对于视口高度，1vw等于视口宽度的1%，1vh等于视口高度的1%。
        #- **适配优势**：能够直接根据视口大小进行自适应布局，无论设备的屏幕尺寸和分辨率如何变化，都能保证元素与视口之间的相对比例关系，使页面在不同设备上具有较好的视觉一致性。在实现全屏布局、响应式图片和视频尺寸调整等方面非常方便。
        #- **适配劣势**：在一些需要精确控制元素尺寸的场景下，可能不太方便，因为它是基于视口的相对单位，对于一些微小的调整可能不够精确。并且在某些旧版本的浏览器中，可能存在兼容性问题。
        #- **使用场景**：适用于各种需要自适应不同移动设备屏幕尺寸的场景，如全屏背景图片、响应式导航栏、根据屏幕宽度动态调整列数的布局等。

        - “其实公司文化是由利润决定的” 这是一个更好的解释模型，前几周和老爸聊华为和小米，我用的是任何形态的人类社会都和森林生态一样，有参天大树，有灌木，有苔藓。参天大树是很了不起，但是这是基于无数苔藓、无数灌木给他提供养分，才能支撑的。 # *[No.131 对谈少楠：内部一切皆成本，对外才能创造价值 - 三五环 | 小宇宙 - 听播客，上小宇宙](https://www.xiaoyuzhoufm.com/episode/655aecd7b95673db21172d86)*

        - “女人是男人的大学，女人是男人的锁链”

        - 塔西佗陷阱、黄宗羲定律
        - S2B2C 是啥商业模式？ # 集合供货商赋能于渠道商并共同服务于顾客的全新电子商务营销模式

        - veb树 是一种时间复杂度为O(lglgU) 的查找算法。基于比较操作的查找算法最佳时间复杂度为O(lgn)，

        - 在做微服务时，如果先发API，但是没有发其依赖的RPC服务，就会出错。怎么在release服务时，保证发布的API，一定已经发布其依赖的RPC服务了？有什么工具可以解决这个问题呢？ # 核心问题在于，CI里缺少了用来检查RPC的workflow，那么怎么实现该workflow呢？

        - Google搜索的界面是SERP，里面包含了搜索推荐，10个网站，有一些带有snippet。 官方的api一个账号每天只能获取100个，每增加1000个需要5美元 请教各位大佬，有什么比较好的方案获取大量的关键词SERP

        - "***使用raw sql，还是gorm？什么情况下应该使用raw sql,什么情况下应该使用gorm? 推荐使用 Raw SQL：对于复杂 CTE 和 UNION 操作，直接编写 SQL 更易于维护和调试。 灵活选择方式：如果查询动态性强（例如条件变化多），可尝试链式构造；否则 Raw SQL 更简洁。***"

        - 通过我对cursor写代码的想法的变化，类比某种中年妇女对老公的心态。只能指望这玩意了

        - “线程与锁模型其实是对底层硬件运行过程的形式化。这种形式化既是该模型最大的优点，也是它最大的缺点。”

        - "***算法属于元技能，kernel属于核心技能，其他的哪些（无论是ms 分布式架构还是mysql之类服务的源码）都属于附加技能*** 或者说kernel属于内功，其他技能属于外功。算法属于元技能。"
        - 为啥现在这种搞法更好？因为之前那种做法无法解决一个核心问题“如果之前笔记没用了，怎么办？如果想法变化了怎么办？”那种“文档式”的笔记无法解决这个问题，也体现不出来想法和认知的变化。对某个知识点的想法会变化、认知会更新。
        - "***费曼读书法已经不适合现在这个知识膨胀的时代了，用问题而非“简单地复述或者总结”来提炼内容是更好的选择。***" # 说白了，用费曼读书法已经无法解决现代人需要的如此庞杂的知识了（看qs.md可知，即使只写question和keywords，现在也1000多行了）
        - “搜索大于记录”
        - "图、类比、问题、关键字、源码：1、不要文字描述，文字描述是最蠢的，文字是苍白的。2、类比是为了通过生活中的例子，更形象地理解。3、图。4、问题和关键字。5、源码。"

        - 当固话理解就行：内网IP地址是分机号，没有任何意义，格式对就行。掩码是分机号的补充。网关是总机。总机的IP受管制，要按国际协定（Abc）。网关就是总机的运营商（比如电信） # [一个技巧学会IP地址、子网掩码、DNS_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV19A62YZEvQ/)

        - “赞成一件事就百分之百的执行他，反对一件事就百分之三百的执行他”

        - golang 的内存管理借鉴了 TCMalloc，但是随着 golang 的迭代，内存管理与 TCMalloc 不同的地方越来越多，但是其主要思想、原理和概念都和 TCMalloc 一致。golang内存管理相较于TCMalloc有哪些优化？
        - "***当 mutator 申请内存时，会通过 allocator 申请新内存，而 allocator 会负责从堆中初始化相应的内存区域***"
        - mspan, mcache, mcentral, mheap 之间的关系，可以类比成么
        - 我不知道语言的内存管理到底是啥东西。我们知道kernel本身有内存管理，也有进程的内存管理（当进程需要分配内存时，都需要通过系统调用陷入到内核空间分配，再虚拟内存起始地址返回到用户态；内核提供了多个系统调用来分配虚拟内存，包brk、mmap和mremap等），但是我实在不知道语言的内存管理是啥。我的意思是对kernel来说，进程的内存管理和语言的内存管理，是不是一码事？

        - 内存管理=分配 + 回收。狭义上来说，内存管理只针对堆内存而言。栈内存由编译器自动分配和���收（比如说栈上的函数参数、局部变量、调用函数栈），这些都自动随着函数创建而分配，随着函数执行完成而销毁。

        - 横向比较几种常用的主备机制（以及 redis, kafka, es, hadoop, tikv, zk之类的服务） # [分布式系统常见同步机制 | 三点水](https://lotabout.me/2019/Data-Synchronization-in-Distributed-System/)
        - 加权随机采样 (Weighted Random Sampling) 可以看作是某种PR算法吗？ # [加权随机采样 (Weighted Random Sampling) | 三点水](https://lotabout.me/2018/Weighted-Random-Sampling/)

        - hashmap 和 hashtable 有啥区别：能否分别用golang实现？意思是二者的区别就在于，hashmap用buckets来存kv，而hashtable加锁（以保证线程安全）是吗？java里也是这么实现的吗？

        - ctx.Request.URL.Path 和 ctx.FullPath() 这两个方法的区别
        - 内存布局优化：也就是说跟我之前的理解不同，其实从编译器角度出发，通常应该把struct里更长字节的字段，放在前面，而非后面。目的是用来减少末尾的填充字段，是吗？ # int 类型通常需要 4 字节对齐  string 类型通常需要 8 字节对齐

        - 微服务之间用MQ来通信的话，是不是只有数据流，但是无法直接通过http或者rpc调用来获取数据呢？我没做过这种的，能否告诉我这种具体怎么操作
        #  回调模式：适用于需要即时响应的场景
        #  请求-响应队列：适用于同步操作场景
        #  状态查询：适用于长时间处理的场景
        - ck 订单表 mth_orders. partating by toYear(cttime)  对性能有影响吗. 如何做二级索引, 比如 sd, ed，in_chan, out_chan







# TODO Compiler
#- url: https://github.com/goccy/p5-Compiler-Lexer
#- url: https://github.com/jamiebuilds/the-super-tiny-compiler
#  des: 基于js实现的compiler，合计不到1000行代码
#- url: https://github.com/skx/monkey
#  des: 用golang实现的interpreter，是用来学习compiler的好资料
#- url: https://github.com/chai2010/go-ast-book
#  des: 《Go语言定制指南》(原名：Go语法树入门/开源免费图书/Go语言进阶/掌握抽象语法树/Go语言AST)
#- url: https://github.com/wa-lang/ugo-compiler-book
#  des: 同样是用golang实现的interpreter
#- url: https://github.com/jacksplwxy/JavaScript-compiler
#- url: https://github.com/robertkrimen/otto
#  des: golang call js. otto is much easier to use than goja. 可以理解为golang实现的js interpreter.









#- type: cpp
#  tag: langs
#  score: -1
#  repo:
#    - url: https://github.com/bminor/glibc
#    - url: https://github.com/llvm/llvm-project
#      des: llvm
#    - url: https://github.com/gcc-mirror/gcc
#      des: gcc
#    - url: https://github.com/FFmpeg/FFmpeg # wget -N --no-check-certificate https://raw.githubusercontent.com/IITII/Useless/master/ffmpeg.sh
#      cmd:
#        - ffmpeg -i concat:'<1.wav|...>' -c copy <output.wav> # 合并多个音频文件，保持音频格式一致即可。例如 ffmpeg -i concat:"1.wav|2.wav|3.wav|4.wav" -c copy output.wav
#        - ffmpeg -i <input.mp4> -vn -acodec copy <output.aac> # 视频转换为音频，-vn 表示不处理视频流，-acodec copy 表示直接拷贝音频流，不做编解码
#    - url: https://github.com/danmar/cppcheck
#      des: cpp static analyze tool.
#    - url: https://github.com/gperftools/gperftools
#      des: = pprof for golang.
#    - url: https://github.com/changkun/modern-cpp-tutorial
#      doc: https://changkun.de/modern-cpp/
#      des: Modern Cpp Tutorial.
#
#    - url: https://github.com/Tencent/rapidjson
#      des: A fast JSON parser/generator for C++ with both SAX/DOM style API
#  topics:
#    - topic: cpp
#      qs:
#        - Compare cpp and c?
#        - cpp, memory (arena, heap, chunk, memory)
#        - cpp, STL
#        - cpp, virtual function, pure virtual function
#
#        # [你工作中最推荐的 C/C++ 程序库有哪些，为什么？ - 知乎](https://www.zhihu.com/question/51134387)
#        - cpp有哪些常用pkg?
#        #  字符串算法库 Boost String Algorithms Library
#        #  IO框架 Boost.asio boost.fiber boost.beast 组合起来就是一个基于协程的io框架堆栈跟踪 Boost.Stacktrace 需要配合libbacktrace库
#        #  CRC验证 Boost.CRC
#        #  日期时间库 Boost.Date_Time
#        #  uuid  Boost.Uuid
#        #  类型转换 Boost.Lexical_Cast 注,这货可以转boost里的很多类型,比如 uuid ptime date等，不需要其它处理
#        #  命令行输入参数解析 Boost.Program_options随机数 Boost.Random
#
#        - 如何编写 C++ 20 协程(Coroutines) # [如何编写 C++ 20 协程(Coroutines)](https://mp.weixin.qq.com/s?__biz=MzIwODA2NjIxOA==&mid=2247484221&idx=1&sn=4c9bebb3236a4dc83f3d5b3dbaf54264)



#- type: rust
#  tag: langs
#  score: -1
#  using:
#    url: https://github.com/rust-lang/rust
#    topics:
#      - topic: rust基础
#        qs:
#          - 关于 rust 没有 killer APP 及在挖 Linux 内核和数据库等领域墙角的问题，有以下新想法：
#          #1. 上有 C，下有 modern C++，Linux 有 30 多年演进历史，相关公司众多，rust 难以撼动其根本，且挖墙角方式慢且前景不明，只能作为 patch 使用。
#          #2. C++也在不断迭代，如 C++21 语法风格现代化，rust 与之相比无太大优势，且被 Linux 内核开发人员不认可，在这种情况下谈 rust 好不好用意义不大。
#          #3. 目前 rust 在内核方面不可观，最好有自己的专有领域，如 golang 之于云原生、python 之于机器学习、java 之于企业级应用等。
#
#          - mac 环境的 rust 怎么安装？
#          - 为啥报错“No Cargo projects found!”？需要用`cargo new`生成项目，否则 IDE 识别不了
#          - 如何看待 rust？编译时间长，语法独特，工具不多，性能没有想象中好。适合对内存安全要求特别高的场景。
#          - rust 有哪些特点？没有 GC；默认变量绑定；默认移动语义，变量之间不能共享堆上数据；
#          - rust 的内存安全是怎么保证的？ownership 所有权，borrowing 租借和 lifetime 生命周期保证了 rust 的内存安全，也是 rust 的核心
#          - rust 的生命周期是什么？
#          - 有哪些好用的拓展包？(留着以后玩)
#          - 包管理工具 cargo 有哪些常用命令？new、build、run、add（添加依赖，相当于 install）
#          - cargo 在 idea 怎么运行 rust 程序？
#          - idea gdb 调试 rust？ `native debugging support`插件
#          - rust+wasm 没啥意思，不搞
#          - rust 有哪些变量类型？其中哪些是标量类型？哪些是复合类型？
#          - rust 怎么定义变量类型？rust 也有类型推导，所以不需要标注具体类型
#          - 几种 int 的取值范围是多少？超出范围取值会溢出？
#          - rust 怎么声明函数？声明 mod？变量名规范？
#          - rust 怎么调用其他文件的函数？同级文件？不同级文件的 mod？ *一定要定义`mod.rs`，main.rs 里要引用。其他模块里的函数直接用`use`关键字引用（类似 py）* 同级文件的函数可以直接调用。
#          - if 语句、for 语句、match 语法
#          - rust 有哪些常用符号？#  unspecified ? 0 x X p b
#          - rust 的 String 和 str 有啥区别？堆分配和栈分配（性能不同），可变和不可变
#          - 怎么拼接字符串？`push_str()`
#          - 怎么修改字符串？转移和不转移变量控制权
#
#      - topic: rust语法（struct、集合、trait、macro、错误处理）
#        qs:
#          - rust 怎么声明结构体？impl 关键字
#          - 怎么在 struct 中用泛型？
#          - rust 的 trait 是什么？ [Trait 使用及原理分析 - Keep Coding](https://liujiacai.net/blog/2021/04/27/trait-usage/)
#          - 怎么在 struct 中定义枚举类型？怎么比较枚举值？怎么获取枚举的值？
#
#          - 宏和函数有啥区别？宏有啥用？宏有啥缺点？宏相当于更复杂的函数。元编程、可变参数、宏展开。*宏是基于代码再展开成代码，所以更抽象也更难维护，但是复用性极高。*
#          - 声明宏和三种过程宏分别有啥用？
#          - 怎么用宏创建自定义函数？
#
#          - 错误处理应该用 unwrap 还是 expect？
#          - 什么时候使用`panic!`？什么时候应该返回`Result类型`？
#
#      - topic: 其他
#        qs:
#          - rust作者Hoare列出的18条，现在rust不符合他预期的一些feat # [The Rust I Wanted Had No Future : r/rust](https://www.reddit.com/r/rust/comments/1415is1/the_rust_i_wanted_had_no_future/)
#
#  repo:
#    - url: https://github.com/rust-unofficial/awesome-rust
#    - url: https://github.com/mainmatter/100-exercises-to-learn-rust
#      des: rust入门教程
#
#    - url: https://github.com/tokio-rs/tokio
#      des: = nettty, gnet, twisted.
#      topics:
#        - topic: Tokio
#          qs:
#            - rust 怎么线程休眠和开启新线程？
#            - rust 怎么用 chan 传递数据？
#            - 异步 chan 和同步 chan 有啥区别？
#            - 怎么循环创建多线程，来修改共享变量？
#            - 怎么用 mutex 多线程修改共享变量？
#            - future、asyncawawit 关键字怎么用？
#            - tokio 怎么延迟执行（模拟 setTimeout）？
#    - url: https://github.com/rayon-rs/rayon
#
#    - url: https://github.com/rwf2/Rocket
#      topics:
#        - topic: rocket
#          qs:
#            - 使用 rocket 框架需要哪些准备？
#            - 有哪些常用的 rustup 命令？
#            - rocket 怎么用 mysql？
#
#    - url: https://github.com/loco-rs/loco
#      des: 类似ruby的RoR、PHP的laravel、python的Django，内置了一些功能模块和generator（比如CURD） # [使用Loco快速搭建自己的后台系统 - 又耳笔记](https://youerning.top/post/rust/loco-quickstart/)
#
#    - url: https://github.com/tokio-rs/axum
#      des: axum
#
#    - url: https://github.com/askama-rs/askama
#      des: Rust模板引擎askama # [Rust模板引擎askama快速入门引擎 - 又耳笔记](https://youerning.top/post/rust/askama-tutorial/)
#    - url: https://github.com/clap-rs/clap
#      des: clap
#
#    # [Rust HTTP客户端reqwest快速入门教程 - 又耳笔记](https://youerning.top/post/reqwest-tutorial/)
#    # [Rust HTTP客户端reqwest源码阅读 - 又耳笔记](https://youerning.top/post/reqwest-code-reading/)
#    - url: https://github.com/seanmonstar/reqwest
#      des: Rust HTTP客户端
#
#
#
#    - url: https://github.com/rust-lang/rust-clippy
#      des: 相当于 rust 的 linter
#
#    - url: https://github.com/rust-lang/cargo
#      des: rust官方的pkg manager
#
#
#    - url: https://github.com/cordx56/rustowl
#      des: rustowl 是一个用于可视化 Rust 中所有权和生命周期的工具，旨在帮助调试和优化代码。1、可视化变量的所有权移动和生命周期。2、通过悬停变量或函数调用来显示信息，使用不同颜色表示不同状态。3、实现了 LSP 服务器 cargo owlsp，以便于与其他编辑器集成。
#
#    - url: https://github.com/TheWaWaR/simple-http-server
#      des: Rust 的轻量级 HTTP(s) 服务器。该项目是用 Rust 编写的轻量级 HTTP(s) 服务器，可快速提供简单易用的静态文件服务。它拥有开箱即用、跨平台的特点，支持 HTTPS、认证、CORS 配置、文件上传等功能。



#- type: ASM
#  tag: langs
#  repo:
#    - url: https://github.com/capstone-engine/capstone
#      des: Capstone 反汇编框架



#- type: zig
#  tag: langs
#  repo:
#    - url: https://github.com/ziglang/zig
#      des: 不依赖 libc、better C interop、robust 这几个feat。Zig 与其他类似语言的区别，核心的一点就是：简单。没有隐式控制流、没有隐式内存分配、统一的构建工具，用尽可能少的特例（没有宏与元编程），来支持编写复杂程序。
#      topics:
#        - topic: "@comptime"
#          d: https://kristoff.it/blog/what-is-zig-comptime/





#- type: lua
#  tag: langs
#  using:
#    url: https://github.com/lua/lua
#    sub:
#      - url: https://github.com/luarocks/luarocks
#        des: lua package manager
#  https://github.com/nvim-neorocks/lux 比 luarocks更现代的lua包管理工具。luarocks太原始了。这种require来加载的机制跟PHP在没有composer一摸一样


#      - url: https://github.com/LuaJIT/LuaJIT
#        des: luajit 是使用 C 实现的 lua 解释器，因为使用了 jit，所以比默认的 lua 要快很多。luajit 是目前性能最好的脚本语言之一 (lua 本身只有 2w 行代码，很轻量，加之没有历史包袱，很容易添加 jit 特性)。
#    topics:
#      - topic: lua 基本认知
#        qs:
#          - lua, feats? #
#          - Why lua is faster than others?
#          - 和其他主流语言相比，lua 有哪些特点？ # 定位是助攻型语言，来辅助宿主语言解决问题；使用场景是在既需要高性能，有需要灵活性的场景。
#          - lua 的编译原理？
#          - lua 的 coroutine 怎么使用？跟其他语言的有什么区别？
#          - 使用`.`和`:`来调用某个 class 下的方法，有什么区别？ # 使用`:`的方法，第一个参数不需要写 self 作为 class 参数 (想想 python 的方法)
#          - 模块：lua 模块与 js 比较类似，定义模块，实现模块，然后导出模块
#          - 元表 metatable
#          - lua支持多线程吗? # Lua不支持多线程，而是使用非抢占式多线程，或协作式多线程来实现协程特性。
#          - Lua中有哪些基本数据类型？ # Lua中有nil、boolean、number、string、function、thread、table和userdata等基本数据类型。
#          - Lua中的table是如何工作的？ # Table是Lua中唯一的复合数据结构，可以作为数组、字典或集合使用，支持任意类型的键和值。
#
#      - topic: lua 常用操作
#        qs:
#          - Lua中的元表是什么？ # 元表是Lua中的一种机制，可以用来重载运算符，类似于JavaScript的prototype或Python对象的魔法方法。
#          - 如何在Lua中抛出和捕获错误？ # 可以使用error函数来抛出错误，使用pcall或xpcall函数来捕获和处理错误。
#          - Lua中的断言是如何工作的？ # Lua中可以使用assert函数来进行断言，如果条件为false，则抛出错误。
#          - 如何在Lua中调试程序？ # 可以通过设置断点、使用调试库或使用IDE的调试工具来进行调试。
#
#      - topic: luajit
#        qs:
#          - lua 和 luajit 有啥区别？
#          - 有哪些对于 luajit 进行性能优化的方法？
#
#  repo:
#    - url: https://github.com/luau-lang/luau
#
#    - url: https://github.com/EmmyLua/IntelliJ-EmmyLua
#      des: lua plugin for IDEA.





#- type: java
#  tag: langs
#  using:
#    url: https://github.com/openjdk/jdk
#    qs:
#      - 基本类型和包装类型都有哪些？ # java 有 9 种基础类型 (4 个整数型；2 个浮点型；字符串型；布尔型；void)
#      - java 拼接字符串的几种方法？
#      - java 里定义常量的几种方法
#      - java 里的 Enum 是否能被继承？ # enum 类不能被继承
#      - java 字符串转对象？用 gson
#      - 判断两个字符串是否相等？ # java 里不要用==来判断两个字符串是否相等，用==比较两个对象时，比较的是两个对象的内存地址，所以一定是不等的，用 str1.equals(str2) 进行比较
#      - list 的 append 操作？
#      - 二维 HashMap 怎么初始化？
#      - List 怎么初始化？
#      - HashMap 怎么判断 key 是否存在？
#      - hutool 的文件读取？
#      - java 有哪些包管理工具？maven gradle ant
#      - List 怎么去重？
#      - 怎么往 List 里追加数据？
#      - int 和 BigDecimal 互相转换 # `BigDecimal.valueof(int)`int 转 BigDecimal
#      - Object 转 List？
#      - 获取 json 的所有 key？
#      - map 的 forEach 循环？
#      - java 的泛型是什么？ # *泛型的本质是参数化类型，也就是说所操作的数据类型被指定为一个参数* 使用泛型，我们可以写一个泛型方法来对一个对象数组进行排序；然后，调用该泛型方法来对整型数组，浮点型数组，字符串数组进行排序
#      - java 的泛型都有哪些？
#      - java 的泛型 super t 和 extends t 的区别？ # super 向上限定（泛型可以是 E 及其父类）；extends 向下限定（可以是 E 及其子类）
#
#  repo:
#    - url: https://github.com/spring-projects/spring-boot
#      topics:
#        - topic: x
#          qs:
#            - springboot 的参数校验？
#            - "@Valid, Validate 和@Validated 有什么区别？" # 使用@Valid 和@Min 限制入参
#            - 在 request 层，怎么判断数据在表中唯一 unique，或者在表中已经存在 exists 呢？ # 目前只有 jpa/hibernate 才能通过注解实现这个需求`@Table(uniqueConstraints=@UniqueConstraint(columnNames="A,B"))`
#            - spring boot 怎么自定参数验证的返回？ # springboot 用@Valid 验证参数失败，如何自定义返回给前端的 json 啊
#            - 怎么自己实现一个注解？怎么通过自定义注解，进行参数验证？
#            - 怎么验证时间参数？
#            - springboot 怎么实现统一的异常捕获？
#            - springboot 里使用计划任务？ # 直接在 Application 类上使用注解@EnableScheduling 开启定时任务，会自动扫描
#            - 怎么使用 springboot 的拦截器？
#            - 怎么写 springboot 的单元测试？
#            - 怎么在 springboot 里使用 swagger？
#
#    - url: https://github.com/mybatis/mybatis-3
#      topics:
#        - topic: x
#          qs:
#            - mybatis 传入参数为 list, array, map 的情况下，mapper.xml 的写法？（重要）
#            - mapper.xml 的常⻅写法（重要） # foreach, concat, choose(when, otherwise), selectKey, if
#            - mybatis 的 mapper.xml 里 jdbc 类型和 java 的数据类型不一致，导致错误，怎么解决？ # *比较常用的有：java 的 long 对应 jdbc 的 bigint；Mybatis 使用时因 jdbcType 类型大小写书写不规范导致的异常*
#            - mybatis 怎么实现多对多？ # 直接在 mapper 里写原生的多对多语句就可以了
#            - mybatis 中的#{}和${}区别？ # `#{}`可以防止 SQL 注入
#            - mybatis 怎么判断数据是否存在？
#            - mybatis 怎么实现分⻚？
#            - 添加成功后怎么返回主键 id？以及怎么判断是否添加成功？ # 翻了几个帖子比如 MyBatis + MySQL 返回插入成功后的主键 id，都需要自己实现 mapper 来实现这个功能，但是实际上，如果 save 成功，直接从 user 对象里取 id 就可以拿到主键 id
#            - mybatis 的 Tag name expected 的问题 需要对比较运算符进行转义，才能在 mybatis 里使用
#            - mybatis-plus 怎么实现软删除？
#    - url: https://github.com/dromara/hutool
#
#    - url: https://github.com/square/okhttp
#      des: okhttp




#- type: lang
#  tag: langs
#  repo:
#    - url: https://github.com/langserver/langserver.github.io
#      des: LSP (Language Support Protocol)
#    - url: https://github.com/alecthomas/chroma
#      des: 可以理解为golang生态下的prism或者shiki。hugo, gitlab, sourcegraph all use chroma to achieve syntax highlighting. 除了上述应用，chroma还可以用在IDE语法高亮、各种blog的Code Block语法高亮、命令行工具的语法高亮中使用。








#- type: blockchain
#  tag: langs
#  score: -1
#  repo:
#    - url: https://github.com/ethereum/solidity
#      sub:
#        - url: https://github.com/AmazingAng/WTF-Solidity
#
#    - url: https://github.com/ArweaveTeam/arweave
#    - url: https://github.com/ArweaveTeam/arweave-js
#    - url: https://github.com/smartcontractkit/chainlink
#    - url: https://github.com/ipfs/kubo
#      doc: https://docs.ipfs.tech/
#      des: IPFS implementation in Go
#    - url: https://github.com/INFURA/infura
#    - url: https://github.com/wevm/wagmi
#    - url: https://github.com/trufflesuite/truffle
#    - url: https://github.com/NomicFoundation/hardhat
#    - url: https://github.com/ethers-io/ethers.js
#
#    - url: https://github.com/freqtrade/freqtrade
#      des: Freqtrade是一个用Python编写的免费开源加密交易机器人。它被设计为支持所有主要的交换，并通过电报或web控制。它包含回溯测试、绘图和资金管理工具，以及通过机器学习进行的策略优化。
#
#    - url: https://github.com/electric-capital/crypto-ecosystems
#      des: crypto-ecosystems 是一个用于共享开源区块链、Web3、加密货币和去中心化生态系统数据的分类法。1、提供了一个不断更新的生态系统分类，便于跟踪新兴项目。2、使用 TOML 配置文件格式，使得数据既可读又易于机器处理。
#
#    - url: https://github.com/a16z/awesome-farcaster
#    - url: https://github.com/farcasterxyz/protocol
#      doc: https://docs.farcaster.xyz/developers/frames/
#
#    - url: https://github.com/ethereum/go-ethereum
#      des: 以太坊协议的官方 Go 语言实现
#
#    - url: https://github.com/topfreegames/pitaya
#      des: 一个可扩展的游戏服务器框架，支持集群和分布式部署
#
#    - url: https://github.com/assimon/epusdt
#      des: 一个开源的 USDT 支付网关，支持 TRC20 和 ERC20
#
#    - url: https://github.com/sevtin/coinex
#      des: 一个加密货币交易所的开源实现，支持多种数字货币交易
#
#    - url: https://github.com/Chia-Network/go-chia-libs
#      des: 提供与 Chia 区块链交互的 Go 语言库，涵盖钱包和区块链节点的功能。1、提供与 Chia RPC 交互的客户端，通过 HTTP 请求或 Websockets。2、支持钱包相关操作，如发送交易、获取交易记录、铸造NFT等。3、支持节点相关操作，如获取区块链状态、推送交易等。
#
#    - url: https://github.com/XRPLF/rippled
#      des: rippled 是一个去中心化的加密货币区块链守护进程，使用 C++ 实现 XRP Ledger 协议。1、去中心化的交易处理，确保没有单一方可以决定交易成功或失败。2、快速高效的共识算法，每 4 到 5 秒结算一次交易，吞吐量可达每秒 1500 笔。3、有限的 XRP 供应，总量为 1000 亿个，不会再增加。
#
#    - url: https://github.com/solana-labs/solana
#      des: solana
#      sub:
#        - url: https://github.com/anza-xyz/agave
#          des: Agave 是由 Solana Labs 分拆出的开发公司 Anza 构建的验证器客户端，其目标是支持 Solana 网络的多客户端生态系统。Agave 基于 Solana Labs 原有代码库分叉而来，但进行了多项优化和重构，例如重命名依赖库、移除旧版 API 端点等，以明确其独立性。olana 此前依赖单一的验证器客户端（原 Solana Labs 代码库），而 Agave 的推出标志着 Solana 开始拥抱多客户端架构。例如，另一个客户端 Firedancer 也在开发中，但进展缓慢。Agave 目前是 Solana 主网的主要生产客户端，承担了区块生成、交易处理等核心功能。
#
#    - url: https://github.com/slowmist/Blockchain-dark-forest-selfguard-handbook
#      des: 【区块链黑暗森林自救手册】学习到了不少风险防控的东西，在区块链黑暗森林世界里，首先牢记下面这两大安全法则：零信任。简单来说就是保持怀疑，而且是始终保持怀疑。持续验证。你要相信，你就必须有能力去验证你怀疑的点，并把这种能力养成习惯。
#
#  topics:
#    - topic: jd
#      picDir: works/web3
#      qs:
#        - 学学用 solidity 在 ethereum 上开发智能合约
#        - hyperledger fabric
#        - 区块链的协议，运行机制，加密技术，底层实现和共识算法等，参与涉及安全协议和架构
#        - 搭建区块链底层基础设施，实现联盟链，私有链的逻辑，提供应用层的调用
#        - substrate 框架 bitcoin 和 freecash
#
#    - topic: 有哪些区块链的常用术语？
#      qs:
#        - active nomination 活跃提名。
#        - alexander 亚历山大。
#        - attestation 证明
#        - authority
#        - BABE
#        - Block 区块
#        - Block explorer 区块浏览器
#        - BLS
#        - Bonding 绑定
#        - Bridge 转接桥
#        - 拜占庭容错
#        - collator 收集人
#        - consensus 共识
#        - Dapps 去中心化应用
#        - DOT
#        - Duty Roster 值勤表
#        - Epoch
#        - Era。等于一个数量的 session，就重新计算验证人集，并支付奖励的时间段
#        - Equivocation 重复签名
#        - Extrinsic 外部信息
#        - Finality 终结，Finality Gadget 终结工具
#        - Fisherman 钓鱼人
#        - FRAME
#        - Genesis 创世区块
#        - Hard Fork 硬分叉
#        - Hard Spoon 硬汤匙
#        - KSM。kusama 网络代币的缩写，就是 polka 的金丝雀网络，是 polka 的早期网络
#        - nominator 提名人
#
#        - "【***塔勒布论文：《比特币、货币和脆弱性》***】" # [塔勒布论文：《比特币、货币和脆弱性》 - 0xYidaoban / 一刀半](https://www.yidaoban.com/index.php/archives/121/)
#        - DEX 与 CEX  (Decentralized Exchange) (Centralized Exchange)
#        #  DEX（去中心化交易所）：是一种基于区块链技术的加密货币交易平台，允许用户直接在区块链上进行比特币等加密货币的交易，而无需通过中心化的中介机构。DEX 的核心特点是去中心化、透明和安全，用户对自己的资金和交易具有完全的控制权，交易记录公开可查，降低了交易风险。常见的 DEX 有 Uniswap、PancakeSwap 等。
#        #  CEX（中心化交易所）：是传统的加密货币交易平台，由中心化的机构或公司运营和管理。CEX 为用户提供了一个便捷的交易界面，用户可以在平台上进行比特币等加密货币的买卖、充值、提现等操作。CEX 具有较高的交易效率和流动性，但存在一定的安全风险，如黑客攻击、资金被盗等。知名的 CEX 有币安、火币、Coinbase 等。
#        - 仿盘、meme 币
#        - DeFi（去中心化金融）Decentralized Finance：是指利用区块链技术和智能合约构建的去中心化金融生态系统，包括去中心化借贷、交易、稳定币等多种金融应用。DeFi 旨在提供更加开放、透明和高效的金融服务，与比特币的 DEX 交易等有密切的联系，很多 DEX 就是基于 DeFi 概念构建的。
#        - NFT（非同质化代币）Non-Fungible Token：是一种基于区块链技术的数字资产，具有唯一性和不可替代性，常被用于数字艺术、收藏品、游戏道具等领域。一些 Meme 币项目会与 NFT 结合，推出与 Meme 相关的 NFT 产品，增加项目的趣味性和价值。
#        - ICO（首次代币发行）Initial Coin Offering：是一种为加密货币项目筹集资金的方式，项目方通过发行新的代币，向投资者募集比特币、以太坊等主流加密货币。在 Meme 币的发展过程中，一些项目也会通过 ICO 的方式来进行初始的资金募集和市场推广。
#
#    - topic: 以太坊
#      qs:
#        - 以太坊中有哪些树（交易树、收据树、状态树）
#        - 为什么需要收据树（将交易执行过程中的一些特定信息编码为交易收据，方便对交易进行零知识证明、索引和搜索）
#        - 以太坊里的状态是什么，状态树怎么存的状态（账户状态包括 balance、nonce、codeHash、storageRoot，使用 Merkle Patricia Trie 即 MPT 存的账户状态）
#        - 以太坊的合约数据的储存形式（还是 MPT。合约账户中的 storage root，对应账户的 storage trie）比特币里的交易是怎么存的（Merkle Tree，MT）
#        - 介绍 Merkle Tree 的性质、优点，为什么用 Merkle Tree 存（实现 SPV、Merkle Proof）
#        - 为什么以太坊要将 MT 改成 MPT（这个问题比较复杂，建议读者去 B 站北大肖臻老师的区块链课中寻找答案）
#        - 介绍比特币的 UTXO
#        - 比特币查询余额只能从头开始遍历整条链吗，有没有高效方法（创建 UTXOSet 缓存）
#        - 比特币地址是怎么生成的（助记词＜-＞seed→＞私钥-＞公钥-＞PubKeyHash＜-＞address，其中＜- ＞表可双向转换，→表单向转换，最后的 PubKeyHash 转换为 address 的时候用的是 base58 编码，base58 编码的原理即辗转相除法）
#
#    - topic: solana
#      qs:
#        - sol什么时候能永久性解决粉尘攻击，网络中断无法提供服务，吞吐量有限等等问题？
#        - “我开始也是用JS/TS发现各种库太多，各种实现库函数和API频繁更换，典型的就是Solana/Web3.js的1.x和2.0，我果断开始用Python做脚本交互了，就是在Anchor开发时JS/TS代码调用Anchor IDL.json翻译成Python调用，总之我很佩服JS/TS开发者”
#        - 为什么meme都扎堆到solana上？1、交易成本低（solana几乎没有手续费，而eth手续费几十刀） 2、交易速度快，吞吐量大，solana的TPS理论上5w，而eth只有十几的TPS 3、发币门槛低（eth需要开发智能合约，并且承担高昂的gas费） 可以看到sol和eth本身是互补关系
#        - 为何需要分拆 Agave？ # 1、技术去中心化需求。以太坊等区块链通过多客户端实现去中心化（如 Geth、Nethermind），而 Solana 长期依赖单一客户端，存在单点故障风险。Agave 的推出是 Solana 向多客户端生态迈出的关键一步，但当前仍以 Agave 为主，Firedancer 等其他客户端尚未成熟。 2、解决开发瓶颈。原 Solana Labs 代码库因高度优化和硬件依赖，导致扩展困难。Anza 通过分拆 Agave 重构代码，例如引入中央调度器、优化 Syscall 接口等，以提升开发灵活性和网络性能。
#         - 冲土狗、批量撸空投 # 交易所叫做二级市场，链上叫做一级市场，圈内人叫里面的币也叫做土狗币，很多币刚出来都是在一级市场，有叙事且表现良好被交易所看重了才会上交易所。一级市场风险很高，骗子多，涨跌幅也很恐怖，市场情绪火热的时候一天能出好几个千百倍币。一级市场发币自由。发行价格，而的数量，可不可买卖都是由撰写合约者决定的









# [greyli/PythonExercises: 按照难度排列的经典 Python 练习题。](https://github.com/greyli/PythonExercises)
# [jackzhenguo/python-small-examples: 告别枯燥，致力于打造 Python 实用小例子，更多Python良心教程见 https://ai-jupyter.com](https://github.com/jackzhenguo/python-small-examples)




#- type: Python
#  tag: langs
#  using:
#    url: https://github.com/python/cpython
#  repo:
#    - url: https://github.com/astral-sh/uv
#      des: 比 conda 更好用的python包管理工具。主要是他有缓存机制。已经下载过的包，不用在下载了，直接复用。那就相当的快。确实牛掰，就是命令略微绕点。类似于conda和poetry的结合体。
#      topics:
#        - topic: 【技术选型】python包管理工具
#          table:
#            - name: uv
#              缓存复用: true # 全局缓存池
#              环境隔离: "✅（自动创建.venv）"
#              依赖管理: "✅（兼容pyproject.toml/requirements.txt）"
#              性能优势: "⚡️ Rust底层，比pip快10-100倍"
#              适用场景: "现代项目开发、CI/CD、高频依赖安装"
#
#            - name: pip
#              url: https://github.com/pypa/pip
#              缓存复用: false # 仅本地临时缓存
#              环境隔离: "❌（需手动搭配venv）"
#              依赖管理: "✅（基础依赖管理，无锁定机制）"
#              性能优势: "🐢 依赖解析串行处理"
#              适用场景: "简单项目/兼容性优先场景"
#
#            - name: pipx # 现在python引入了新的保护机制（PEP 668），如果python环境被系统/包管理器（如 Homebrew）管理，直接使用 pip 安装包到系统全局环境可能会导致依赖冲突或破坏系统环境。是无法安装cli的。这是我在安装rendercv时遇到的。
#              url: https://github.com/pypa/pipx
#              缓存复用: false
#              环境隔离: "✅（独立CLI环境）"
#              依赖管理: "❌（仅安装可执行包）"
#              性能优势: "⚖️ 中等"
#              适用场景: "安全安装全局CLI工具（如black、poetry）"
#
#            - name: poetry
#              url: https://github.com/python-poetry/poetry
#              缓存复用: false
#              环境隔离: "✅（手动激活）"
#              依赖管理: "✅（语义化版本锁）"
#              性能优势: "🐢 依赖解析较慢"
#              适用场景: "包开发、发布PyPI"
#
#            - name: conda # 应该说，既不是类似pip那样的python包管理工具，也不是brew之类的os包管理工具。而是主打Data Analysis领域的包管理工具，相关工具适配的相对更好。
#              url: https://github.com/conda/conda
#              缓存复用: true # 包缓存
#              环境隔离: "✅（跨语言环境）"
#              依赖管理: "✅（支持非Python库）"
#              性能优势: "🐢 下载安装慢"
#              适用场景: "科学计算、跨语言依赖（如R/CUDA）"
#
#            - name: pipenv
#              url: https://github.com/pypa/pipenv # pipenv = pip + pyenv + virtualenv
#              缓存复用: false
#              环境隔离: "✅（自动激活）"
#              依赖管理: "✅（Pipfile.lock）"
#              性能优势: "🐢 依赖解析慢"
#              适用场景: "小型Web项目"
#
#    - url: https://github.com/pytest-dev/pytest
#      des: 用pytest代替unittest作为python的单测工具
#  record:
#    - 【2025-06-20】移除掉python的几个web框架【django】、【flask】、【fastapi】、【starlette】、【taipy】。用python做web相较之下简百无一用，没有意义。
#    - 【2025-06-24】移除【PyMySQL】、【sqlalchemy】、【jinja】、【Pillow】、【aiohttp（用 aiohttp 代替 request 实现异步 HTTP 请求）】、【uvloop（用 uvloop 代替 asyncio 实现更好的性能）、】
#  topics:
#    - topic: Python 常用语法
#      qs:
#        - magic methods? datatype? `*args` and `**kwargs`? yield?
#        - python @property decorator.
#        - urllib
#        - Python 怎么捕获警告？（注意：不是捕获异常） # [Python 怎么捕获警告？（注意：不是捕获异常）](https://mp.weixin.qq.com/s?__biz=MzI2MzEwNTY3OQ==&mid=2648980367&idx=1&sn=b049f31a044e17246039929d7f6e068c)
#
#
#    - topic: xxx
#      qs:
#        - 1. 高效学习 Python 的方法
#        - 2. 提升 Python 开发效率的工具
#        - 3. 编写爬虫需要掌握的HTTP、HTML基础知识
#        - 4. urlib、requests、Beautiful Soup 库的使用方法知识
#        - 5. Python 内置的数据类型方法的底层原理，并学会自定义类似的方法
#        - 6. 高效的数据结构模块：collections
#        - 7. 变量的底层逻辑：命名空间（作用域和闭包)、变量的引用
#        - 8.  高阶函数、装饰器进阶
#        - 9.  列表推导式、字典推导式
#        - 10. 设计模式：面向对象编程、单例模式、工厂模式
#        - 11. 元类、C3 算法、MRO、魔术方法
#        - 12. 元编程与工厂函数
#        - 13. 异常处理机制的原理和用法
