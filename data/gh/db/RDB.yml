---

# TODO DTCC
#- url: https://github.com/DB-DTCC/DTCC2023
#  doc: https://github.com/DB-DTCC
#  des: DTCC(中国数据库技术大会)的PPT资料，真的很有干货，历年资料都有



# TODO innodb_view
#- url: https://github.com/jeremycole/innodb_ruby
#  des: InnoDB Viewer. 具体来说就是用来学习InnoDB的可视化工具
#- url: https://github.com/zput/innodb_view
#  des: 同上
#- url: https://github.com/alibaba/innodb-java-reader
#  des: 阿里开源的innodb viewer




# TODO 研究一下 myrocks [mysql-5.6/storage/rocksdb at fb-mysql-8.0.32 · facebook/mysql-5.6](https://github.com/facebook/mysql-5.6/tree/fb-mysql-8.0.32/storage/rocksdb) [wisehead/myrocks_notes: myrocks and rocksdb source code analysis（myrocks/rocksdb源码解析）](https://github.com/wisehead/myrocks_notes) [MyRocks：超越 InnoDB 的存储引擎 - 黄金架构师](https://hhwyt.xyz/database/myrocks/)

# 可以看到RDB实际上还是MySQL及其衍生物，比如MariaDB和Percona, 然后就是Oracle和SqlServer
- type: RDB
  tag: db
  score: 5
  using:
    url: https://github.com/mysql/mysql-server
    doc: https://dev.mysql.com/doc/refman/8.0/en/
    rel:
      - url: https://github.com/MariaDB/server
        des: MariaDB
        qs:
          - mariadb的rocksdb 为啥 mariadb要用rocksdb?
          - MariaDB的RocksDB引擎已经支持TTL索引。也就是类似MongoDB和CK那样的数据自动过期机制。

      - url: https://github.com/XiaoMi/soar
        des: soar之类基于SQL指纹实现的sql优化工具，可以对sql进行预优化（也就是搭配slow-log在服务还没上线之前就对sql进行优化）。目前soar只支持mysql。feats包括 索引优化、EXPLAIN解读、sql打分、自动SQL重写、启发式规则建议、执行计划展示、Profiling、。试用了几个，在同类工具里算是比较好用的。但是现在已经用不成了。
        qs:
          - 要求实现一个sql optimizer来给sql打分和美化，怎么实现？ # 具体来说就是用go-sql-driver之类的ORM对EXPLAIN进行解析，并把结果集的字段修改为中文后，输出为自定义格式
          - soar.yaml
          - "@sql指纹 sql fingerprint" # pt-fingerprint(percona fingerprint, using regex), TiDB SQL parser(AST)

      - url: https://github.com/chartdb/chartdb
        des: ChartDB 是一个开源的数据库图表编辑器，允许用户通过单个查询可视化和设计数据库。1、支持多种主流数据库，如 PostgreSQL、MySQL、SQLite 等。2、直观的交互式编辑工具，便于调整复杂结构。


      - url: https://github.com/vitessio/vitess
        doc: https://vitess.io/docs/20.0/reference/features/
        score: 5
        des: 【RDB中间件（分库分表）】vitess能解决所有mysql高可用方面的常见问题，比如什么主从复制、读写分离、分库分表、写库单点问题等等。
        qs:
          - "***vitess 对比 ShardingSphere，或者说 vitess有哪些feats?***" # [对比 Vitess，ShardingSphere 有哪些不同 - ludongguoa - 博客园](https://www.cnblogs.com/ludongguoa/p/15354678.html)

          - Vitess 如何处理读写分离？
          - vitess concepts? Cell, Keyspace, Keyspace Graph, Keyspace ID, Replication Graph, Shard, Tablet, topology-service (TOPO), VSchema, VStream, VTGate, vtctl, vtctld, vtworker # [云原生数据库vitess | cloud-native-devops](https://rocdu.gitbook.io/cloud-native-devops/cloud-native/cloud-native-vitess)

          - How does VTGate, VTTablet works?
          - sharding, Vitess 是如何实现数据库的水平扩展的？支持哪些类型的分片策略？
          - Vitess 中的 Resharding 过程
          - Vitess 如何处理大规模的事务？
          - Vitess 提供哪些机制来确保数据库的高可用性？
          - Vitess 如何支持多租户架构？
          - Vitess 的客户端如何处理连接池和事务？
          - 为什么Vitess推荐每个MySQL服务器250GB？
        rel:
          - url: https://github.com/yoshinorim/mha4mysql-manager
            des: 也就是MHA. MHA 是目前中小公司使用的常见 MySQL 高可用架构，但有些云厂商`不支持虚拟 VIP`，或者`VIP 不是同网段的`，所以无法实现基于`MHA`或`Keepalived 漂移 VIP 形式`的高可用使用场景。于是针对这种情况，衍生出Cousul 服务发现+MHA 这种架构。consul+MHA的缺点是成本略高，也不好定位问题，用`Maxscale`解决

          - url: https://github.com/knex/knex
            doc: https://knexjs.org/
            des: A query builder for PostgreSQL, MySQL, CockroachDB, SQL Server, SQLite3 and Oracle, designed to be flexible, portable, and fun to use.

          - url: https://github.com/liquibase/liquibase
            doc: https://docs.liquibase.com/
            des: liquibase

  repo:
    - url: https://github.com/postgres/postgres
      doc: https://www.postgresql.org/docs/current/
      rel:
        - url: https://github.com/pgRouting/pgrouting
          doc: https://pgrouting.org/
        - url: https://github.com/pgRouting/osm2pgrouting


        - url: https://github.com/timescale/timescaledb
          score: 5
          des: 完整时序数据库（列存压缩+连续聚合），兼容 PG 生态。 【2025-07-01】移除【tgres（纯 PG 时序方案，功能较简单）】
          rel:
            - url: https://github.com/pipelinedb/pipelinedb

        - url: https://github.com/postgrespro/rum
        - url: https://github.com/bitnine-oss/
        - url: https://github.com/HypoPG/hypopg
        - url: https://github.com/knizhnik/imcs
        - url: https://github.com/ossc-db/pg_hint_plan

        - url: https://github.com/zombodb/zombodb
          des: Making Postgres and Elasticsearch work together like it's 2023.
        - url: https://github.com/orioledb/orioledb
          des: Next generation storage engine for pgsql. cloud-native storage engine. 面向 OLTP 设计，Bw-tree 优化写性能，UNDO 降低 IO，兼容原生协议。【2025-07-01】移除【neondatabase/neon】


        - url: https://github.com/plv8/plv8
          des: V8 Engine Javascript Procedural Language add-on for PostgreSQL
        - url: https://github.com/apache/age
          des: 【GraphDB】Cypher 语法支持，集成 PG 事务特性。【2025-07-01】移除【AgensGraph-Extension（已经EOL了）】


        - url: https://github.com/heterodb/pg-strom
          des: Master development repository
        - url: https://github.com/citusdata/pg_cron
          des:
        - url: https://github.com/pgpartman/pg_partman
          des: 自动化时间/ID 分区维护。

        - url: https://github.com/dimitri/pgcopydb
        - url: https://github.com/zalando/patroni
          des: 用来实现pgsql高可用集群的中间件。K8s 友好，强一致性（DCS 如 etcd），自动化故障转移。【2025-07-01】移除【repmgr】，不如 patroni好用。

        - url: https://github.com/pgsty/pigsty
          score: 5
          des: Pretty Useful. 用来构建生产级可用的pgsql服务（类似RDS）。


        - url: https://github.com/CrunchyData/pgmonitor
          des: Used to collect metrics and alerting-resources from crunchy data.
        - url: https://github.com/amutu/zhparser
          des: zhparser = zh parser. pgsql extension for FTS of Chinese. 基于SCWS实现
        - url: https://github.com/Casecommons/pg_search
        - url: https://github.com/dimitri/pgloader
          des: 用来把数据import到supabase时发现的，支持各种sql（比如sqlite、mysql、mssql等）

        - url: https://github.com/citusdata/citus
          score: 5
          des: Distributed PostgreSQL as an extension. pgsql没有内置分片，可以使用citus实现sharding. 成熟度最高（被微软收购集成到 Azure），支持自动分片、分布式 JOIN、多租户架构，大规模 OLTP/HTAP 场景首选。【2025-07-01】移除【pg-sharding/spqr（较新，轻量级无状态查询路由，适合基础分片需求）】、【paradedb（专注搜索分析的分布式方案（替代 Elasticsearch））】。移除【cstore_fdw】citus本身已经内置列式存储了。




        - url: https://github.com/CrunchyData/postgres-operator
          des: pgsql operator, 这种把pgsql放到k8s的operator确实挺鸡肋的
        - url: https://github.com/cloudnative-pg/cloudnative-pg
          des: pgsql in k8s

        - url: https://github.com/jackc/pgx
          des: pgx是一个为Go语言编写的PostgreSQL数据库驱动和工具包。它提供了一个低层次、高性能的接口，并且公开了PostgreSQL特有的功能，如LISTEN/NOTIFY和COPY。此外，它还包括了一个适配器用于标准的database/sql接口。【2025-07-01】移除【kataras/pg】

        - url: https://github.com/mathesar-foundation/mathesar
        - url: https://github.com/graphile/crystal


        - url: https://github.com/supabase/supabase
          doc: https://supabase.com/
          des: 我用来作为在线pgsql使用. 相当于开源版本的 【firebase】, 提供了 pgsql database, auth, storage, functions 功能。但是不支持自定义域名。如果个人开发者的，能省一笔服务器费用。

        - url: https://github.com/frectonz/pglite-fusion
          des: 将 SQLite 数据库嵌入到 PostgreSQL 表中。

        - url: https://github.com/pgvector/pgvector
          des: pgvector 是为 Postgres 提供的开源向量相似性搜索工具。1、支持精确和近似最近邻搜索。2、支持单精度、半精度、二进制和稀疏向量。3、支持 L2 距离、内积、余弦距离、L1 距离、汉明距离和 Jaccard 距离。4、具有 ACID 兼容性，时间点恢复，JOINs 等 Postgres 的所有优秀特性。标准方案，无缝集成向量搜索（ANN/KNN），语法简洁生态完善。

        - url: https://github.com/shayonj/pg_flo
          des: pg_flo is a tool for moving and transforming data between PostgreSQL databases using logical replication. It features real-time data streaming, fast initial loads, powerful transformations, and flexible routing. pg_flo can be used for real-time data replication between PostgreSQL databases, ETL pipelines with data transformation, data re-routing, masking, and filtering, database migration with zero downtime, and event streaming from PostgreSQL.

        - url: https://github.com/electric-sql/electric
          des: Electric 是一个用于将 Postgres 数据同步到本地应用和服务的工具。1、提供 HTTP API 以便从 Postgres 同步数据。2、支持逻辑复制的 Postgres 数据库。3、可以通过客户端库和集成直接使用。

        - url: https://github.com/PostgREST/postgrest
          score: 3
          des: PostgREST 是一个为任何现有的 PostgreSQL 数据库提供完整 RESTful API 的工具。1、提供比手动编写更清晰、更符合标准和更快速的 API。2、支持高性能，能够在 Heroku 免费层上实现每秒 2000 次请求的亚毫秒响应时间。3、采用 JSON Web Tokens 处理身份验证，并将授权委托给数据库中的角色信息，确保安全性的一致性来源。

        - url: https://github.com/pgsty/pig
          des: pgsql extension # [PostgreSQL: ANNOUNCE pig: The Postgres Extension Wizard](https://www.postgresql.org/about/news/announce-pig-the-postgres-extension-wizard-2988/)

        - url: https://github.com/xataio/pgroll
          des: 自动化 schema 变更（无锁、回滚兼容），安全可靠。


        - url: https://github.com/orf/locksmith
          des: Detect SQL migration issues quickly and easily

        - url: https://github.com/opengauss-mirror/openGauss-server
          doc: https://docs.opengauss.org/zh/
          des: OpenGauss
      topics:
        - topic: pgsql 基本认知
          des: "***Compare mysql and pgsql. 从 MVCC, replication, index(bptree), transaction, perf, extensibility, ecosystem, operability, usability方面***"
          tables:
            - name: 《一无是处的mysql》
              url: https://mp.weixin.qq.com/s/__mnrLBN88RPgpET0kq3vg
              table:
                - name: 并发控制算法实现混乱
                  具体表现: |
                    - 不同隔离级别下加锁策略不一致（如SERIALIZABLE加读锁而其他级别不加）
                    - MVCC快照机制与封锁算法混用缺乏清晰边界
                    - 可重复读隔离级别实际实现为快照隔离（SI）
                  后果: |
                    - 不同隔离级别行为不可预测
                    - 开发者难以准确理解数据一致性保障机制
                    - 弱隔离级别未实现预期性能优势

                - name: 死锁检测机制性能低下
                  具体表现: |
                    - 可串行化隔离级别下60%数据异常依赖死锁检测
                    - 全局锁表遍历消耗大量CPU资源
                    - 独立死锁检测线程每秒扫描整个锁表
                  后果: |
                    - SERIALIZABLE级别TPC-C性能仅为RC的40%
                    - 高并发场景下系统性性能骤降
                    - 锁等待超时错误频发（ERROR 1205）

                - name: 隔离级别设计混乱
                  具体表现: |
                    - 可串行化实现与ANSI-SQL标准背离
                    - RC/RU隔离级别行为界限模糊
                    - 间隙锁仅在RR/SER级别生效
                  后果: |
                    - 弱隔离级别下大量"黄色A"数据异常（如Write-read Skew）
                    - 开发者被迫使用SELECT...FOR UPDATE导致性能劣化
                    - 集群资源消耗翻倍（1TPS需求需2倍硬件）

                - name: 快照机制与锁机制配合不当
                  具体表现: |
                    - RC级别每条SELECT创建新快照
                    - 活跃事务列表遍历成本高昂
                    - 长版本链回滚段扫描效率低下
                  后果: |
                    - 读已提交隔离级别性能反低于可重复读
                    - 高并发场景快照创建耗时占事务30%
                    - MVCC内存/存储消耗超预期200%

            - name: 《比较拉胯的pgsql》
              url: https://mp.weixin.qq.com/s?__biz=Mzg2MTA2ODI3NQ==&mid=2247483854&idx=1&sn=fd0bb7eba62e18e819dc516906dbe1a3
              table:
                - name: SSI 算法多余
                  具体表现: |
                    - 为实现可串行化隔离级别引入复杂的 SSI 算法
                    - 需维护 RWConflictPool 有向图
                    - 检测连续 RW 偏序关系
                    - 增加大量代码复杂度
                  后果: |
                    - 性能腰斩（TPC-C 测试中 SERIALIZABLE 性能仅为 RC 的 40%）
                    - 资源消耗大（CPU/内存）

                - name: 死锁检测实现差
                  具体表现: |
                    - 与 SSI 独立维护两套相似的有向图结构（RWConflictPool 和 waitOrders）
                    - 检测逻辑冗余
                    - Greenplum 分布式死锁检测算法低效
                  后果: |
                    - 并发性能下降
                    - 分布式场景（如 Greenplum）TP 事务处理能力几乎不可用

                - name: 隔离级别设计问题
                  具体表现: |
                    - SERIALIZABLE 依赖高开销的 SSI
                    - RC/RU 隔离级别行为混淆
                    - RR 级别无法消除 Write Skew 等异常
                  后果: |
                    - 弱隔离级别未显著提升性能（与理论预期不符）
                    - 用户需主动加锁（如 `SELECT FOR UPDATE`）导致性能劣化

                - name: 并发控制实现冗余
                  具体表现: |
                    - 同时维护 MVCC 快照 + SSI + 2PL 三套机制
                    - 读写操作均触发冲突检测（如 `CheckForSerializableConflictOut`）
                  后果: |
                    - 快照遍历活跃事务列表高开销
                    - 检测逻辑频繁执行，拖累吞吐量
          qs:
            - 那pgsql也支持事务支持，为啥pgsql基于MVTO而不是MV2PL实现MVCC，pgsql使用heap table（类似mysql中memory引擎）而不是HOT呢？
            - pgsql 性能：pgsql的点查QPS和TPS大概多少？相较于mysql怎么样？ # [PostgreSQL 到底有多强？ | Pigsty](https://pigsty.cc/blog/pg/pg-performence/) 点查 QPS 60万+，最高达 200 万。读写 TPS （4写1读）每秒 7 万+，最高达14万。作为一个粗略的规格参考，探探作为一个前部的互联网App，PostgreSQL 全局 TPS 为 40万左右。这意味着十几台这样的新笔记本，或几台顶配服务器（10W内¥）就有潜力支撑起一个大型互联网应用的数据库服务，这对于以前来说是难以想象的。

            - "***为啥说“pgsql能一个打十个？” pgsql用哪些ext能够替代 vector-db, graph-db, tsdb, kvdb, document-db, NewSQL, ES的功能*** " # [为什么PostgreSQL是最成功的数据库？ | Pigsty](https://pigsty.cc/blog/pg/pg-is-best/) “PostgreSQL是一专多长的全栈数据库，天生就是HTAP，超融合数据库，一个打十个。PostgreSQL 是各种关系型数据库中性价比最高的选择：它不仅可以用来做传统的 CRUD OLTP 业务，OLAP 数据分析也是拿手好戏，各种特色功能更是提供了切入多种行业的契机：基于 PostGIS 的地理时空数据处理分析，基于 TimescaleDB 的时序金融物联网数据处理分析，基于存储过程触发器的流式事件处理，基于倒排索引全文检索的搜索引擎，基于 FDW 统一各式外部数据源的访问。单一组件便足以覆盖中小型企业绝大多数的数据库需求：OLTP，OLAP，时序数据库，空间GIS，全文检索，JSON/XML，图数据库，缓存，等等等等，一招鲜，吃遍天。对于海量数据处理，更是可以平滑升级至分布式版本：Citus/GreenPlum/MatrixDB等。可以说，它是真正一专多长的全栈数据库。它可以实现的功能，要比普通的纯OLTP数据库要丰富得多，为CRUD Boy提供了一条转型与深入的进阶道路。从企业用户的角度来看，PostgreSQL在一个很可观的规模内都可以独立扮演多面手的角色，一个组件当多种组件使。而单一数据组件选型可以极大地削减项目额外复杂度，这意味着能节省很多成本。它让十个人才能搞定的事，变成一个人就能搞定的事。”

            - 而说到底，MySQL最大的问题，就是它的生态位越来越狭窄。论严谨的事务处理与数据分析，PostgreSQL甩开它几条街；论糙猛快出原型，NoSQL全家桶又要比MySQL方便太多。论商业发财，上面有Oracle干爹压着；论开源生态，又不断出现兼容MySQL的新生代产品来尝试篡位。MySQL处在了吃老本的危险境地，只是凭籍历史积分存量勉强维持着现状，但时间是否会站在MySQL这一边，我们只能拭目以待了。

            - pgsql比mysql对数据约束更严格，不接受脏数据



        - topic: pgsql arch
          picDir: db/pgsql
          qs:
            - pgsql 的WAL机制是如何保证数据一致性的？


        - topic: HEAP存储引擎，OrioleDB引擎
          qs:
            - heap表的物理结构是怎样的？ # pgsql每个数据库的表数据存储在$PGDATA/base目录下的子目录中，不同数据库之间是完全物理隔离的。表数据实际存在的物理文件可以通过查看文件系统目录看到。每个表有一个唯一的物理文件，这个文件以relfilenode命名，存储在对应的数据库目录下。如果对表执行了TRUNCATE操作，会生成一个新的relfilenode，即一个新的文件，但表的oid保持不变。
            - heap表文件的page是如何组织的？ # heap表文件的单个页面（page）被分为多个heaptuple数据元组。每个page有一个header部分，其中包含了管理元组的字段，如pd_lsn、pd_checksum、pd_flags等。每个heaptuple由HeapTupleData和HeapTupleHeaderData组成，其中HeapTupleHeaderData包含了事务相关的字段，如t_xmin、t_xmax等，用于实现PostgreSQL的事务语义和MVCC（多版本并发控制）。实际的数据存储在data area部分。
            - pgsql的WAL是如何与heap表存储引擎协同工作的？ # WAL是PostgreSQL中的一种日志机制，用于确保数据的一致性和可靠性。在heap表存储引擎中，当执行插入、更新或删除操作时，相关操作会被记录到WAL日志中。这些日志记录会在事务提交时写入到磁盘上的WAL文件中。在发生故障时，WAL日志可以用来恢复数据，确保数据不会因为系统崩溃而丢失。WAL机制与heap表存储引擎的写入操作紧密相关，确保了数据的持久性和一致性。
            - heap表文件的扩展文件有哪些？
            - heap表的写入逻辑是怎样的？ # heap表的写入操作首先会构造一个HeapTuple对象，然后通过一系列的函数调用，将该元组插入到一个可用的page中。这个过程包括初始化元组头、获取可用的page block-number、冲突检测、将元组信息添加到page中、标记page为dirty、写入WAL以及标记relation-cache中的旧tuple所在的buffer失效。值得注意的是，实际的数据落盘是通过checkpointer进程异步完成的，而不是在写入操作的主链路上直接落盘。
            - heap表的读取逻辑是怎样的？
            - 【pgsql的MVCC实现】 # [Internals of MVCC in Postgres: Hidden costs of Updates vs Inserts | by Rohan Reddy Alleti | Mar, 2025 | Medium](https://medium.com/@rohanjnr44/internals-of-mvcc-in-postgres-hidden-costs-of-updates-vs-inserts-381eadd35844)


            - pgsql 是如何处理内核fsync的bug的？
            - pgsql TOAST机制 (The Oversized-Attribute Storage Technique)

            - 聚合函数 count(), sum(), avg(), max(), min()

            - 分组

            - pgsql 窗口函数：是啥？怎么用（ROW_NUMBER(), LAG()/LEAD(), 运行总计, PARTITION BY, FRAME_clause, ）？使用场景？ # 窗口函数是一种能够在一组行（窗口）上进行计算的函数。与普通聚合函数不同，窗口函数不会将多行合并为一行，而是为每一行都保持独立，同时还能获取到相关行的信息。  # 计算排名、计算累计总和、计算移动平均、比较当前行与前后行、计算百分比、计算同组内的相对值
            - 排序重复值：注意 RANK、DENSE_RANK 和 ROW_NUMBER 的区别
            - 我没看懂为啥说“PARTITION BY 不像 GROUP BY 会压缩行，窗口函数保留所有行的详细信息” GROUP BY也是返回分组数据啊，什么是“会压缩行”？ # 我大概理解了，意思是本身DB的好处在于声明式编程。那么 PARTITION BY 其实是一个语义清晰的API。如果我们用GROUP BY或者连查，也能实现相同的操作，但是就相当于自己实现了部分代码，就又变成了过程式编程。带来两个问题：1、语义不够清晰 2、无法使用pgsql本身对PARTITION BY 的优化。 GPT回复说“对，你总结得非常准确！这正是我之前没能很好表达的核心点。这让我想起函数式编程中的一个概念：高阶函数。PARTITION BY 某种程度上就像是一个高阶函数，它接收一个分组规则和一个聚合函数，返回一个新的计算结果。这种抽象让代码更容易理解和维护。”

            - pgsql 存储过程

            - 视图

            - Postgres Blink-tree 的工程实现 # [Postgres Blink-tree 的工程实现 - 黄金架构师](https://hhwyt.xyz/database/postgres-blinktree/)


        - topic: 数据复制和备份
          qs:
            - Replication
            - 不停机备份？ # dump, 文件级备份（PITR(Point-In-Time Recovery)）
            - 怎么操作pgsql故障恢复？ # 备份后，清除WAL，转储并重建数据库

        - topic: lock
          qs:
            - advisory lock 咨询锁

        - topic: FTS
          qs:
            - pgsql的FTS有哪些缺点？ # 性能、缺少feat支持（facet分面）
            - FTS, textsearch, tsquery, tsvector, @@匹配运算符
            - 字组索引 pg_term, pg_bigm,



        - topic: 基本使用
          table:
            - 功能类别: 数据类型
              具体功能: 字符串引号
              mysql语法: 单引号或双引号均可
              pgsql语法: 仅支持单引号（双引号用于标识符）
              关键差异说明: MySQL灵活支持两种引号，而PG严格区分引号用途

            - 具体功能: 时间类型
              关键差异说明: 【ts错位】mysql的ts相当于pgsql的tsz，mysql的datetime相当于pg的ts

            - 功能类别:
              具体功能: 布尔类型
              mysql语法: TINYINT(1)模拟（0=false/非0=true）
              pgsql语法: 原生BOOLEAN（支持TRUE/FALSE/t/f/1/0等）
              关键差异说明: MySQL用整数模拟布尔值，PG有真正的布尔类型

            - 功能类别:
              具体功能: TEXT类型容量
              mysql语法: LONGTEXT最大4GB（基础TEXT仅64KB）
              pgsql语法: 无容量限制
              关键差异说明: MySQL需要选择特定类型突破限制，PG的TEXT无容量限制

            - 功能类别:
              具体功能: 数组支持
              mysql语法: 不支持原生数组（需字符串模拟）
              pgsql语法: 支持原生数组（如INT[]，VARCHAR[]）
              关键差异说明: PG原生支持数组类型，查询更高效

            - 功能类别: 关键操作
              具体功能: 字符串连接
              mysql语法: CONCAT()函数或+（易触发数值运算）
              pgsql语法: "||操作符（标准连接符）"
              关键差异说明: MySQL的+可能误触发数值计算，PG的||是专用连接符

            - 功能类别:
              具体功能: 自增主键
              mysql语法: AUTO_INCREMENT属性
              pgsql语法: SERIAL/BIGSERIAL类型或GENERATED AS IDENTITY
              关键差异说明: MySQL使用属性式，PG基于序列实现

            - 功能类别:
              具体功能: 分页语法
              mysql语法: LIMIT offset, count
              pgsql语法: LIMIT count OFFSET offset
              关键差异说明: MySQL简写格式在PG无效，需注意兼容性

            - 功能类别:
              具体功能: 当前时间函数
              mysql语法: NOW()
              pgsql语法: CURRENT_TIMESTAMP
              关键差异说明: 功能相同但函数名不同，迁移时需修改

            - 功能类别: 查询行为
              具体功能: 大小写敏感
              mysql语法: 默认不区分（依赖collation）
              pgsql语法: 默认区分
              关键差异说明: WHERE name='A'在不同数据库结果可能不同

            - 功能类别:
              具体功能: GROUP BY非聚合列
              mysql语法: 允许不列出（返回任意值）
              pgsql语法: 必须包含在GROUP BY或聚合函数中
              关键差异说明: PG更严格符合SQL标准，避免数据歧义

            - 功能类别:
              具体功能: HAVING引用聚合别名
              mysql语法: 支持（5.7+版本）
              pgsql语法: 不支持（需重复聚合表达式）
              关键差异说明: PG需写完整表达式如HAVING SUM(price)>100

            - 功能类别: 高级特性
              具体功能: RETURNING子句
              mysql语法: 不支持（依赖LAST_INSERT_ID()）
              pgsql语法: 支持INSERT/UPDATE/DELETE...RETURNING
              关键差异说明: PG可单次操作返回修改数据，减少查询次数

            - 功能类别:
              具体功能: 索引类型
              mysql语法: B-Tree/全文/空间（有限）
              pgsql语法: B-Tree/GiST/GIN/BRIN等（支持表达式索引）
              关键差异说明: PG支持更多高级索引类型，特别适合复杂数据

            - 功能类别: 其他差异
              具体功能: 事务中DDL
              mysql语法: 自动提交（隐式提交）
              pgsql语法: 可回滚
              关键差异说明: MySQL的CREATE TABLE在事务中执行后无法撤销

            - 功能类别:
              具体功能: UPSERT操作
              mysql语法: REPLACE INTO或ON DUPLICATE KEY UPDATE
              pgsql语法: INSERT...ON CONFLICT DO UPDATE
              关键差异说明: 两者语法结构完全不同，迁移需重写逻辑
          qs:
            - pgsql 怎么使用 zhparser 进行中文分词? # [浅析 PostgreSQL + zhparser 进行中文搜索的分词与排序优化 · Ruby China](https://ruby-china.org/topics/43468)
            - "***pgsql spec***" # [PostgreSQL 规约（2024版） | Pigsty](https://pigsty.cc/blog/pg/pg-convention/)

            - LIKE 和 ILIKE
            - pgsql 中使用 regex 进行文本匹配
            - pgsql TSVector, TSQuery
            - pgsql 通用反向索引GIN（Generalized Inverted iNdex，GIN），对我们要查询的词素创建索引，GIN和GIST类似，也是索引的API。如果你使用过像Lucene或Sphinx这样的搜索引擎，你会对反向索引（inverted index）比较熟悉。它是一种常见的用于对全文检索进行索引的数据结构。

            - pgsql cube 多维搜索拓展
            - pgsql tablefunc dict_xsyn fuzzystrmatch pg_trgm cube

            - pgsql trx 最佳实践 # [Efficient Database Transactions Using PostgreSQL: Best Practices and Optimization Techniques | by Miftahul Huda | Dec, 2024 | Medium](https://iniakunhuda.medium.com/efficient-database-transactions-using-postgresql-best-practices-and-optimization-techniques-9652d4ce53c0)





        - topic: pgRouting
          qs:
            - pgRouting是什么？ # pgRouting是一款专为PostGIS与PostgreSQL设计的开源插件，它极大地丰富了地理信息系统（GIS）的功能，使其能够支持复杂的路由计算需求。[^7^]
            - pgRouting的主要功能有哪些？ # pgRouting支持多种路径规划算法，包括最短路径、成本最小路径或是考虑多种因素后的最优路径方案。[^7^]
            - 如何定义pgRouting中的网络分析？ # 网络分析是指使用pgRouting进行路径计算和优化的过程，包括但不限于最短路径、旅行商问题（TSP）或车辆路径规划等。[^7^]
            - 如何使用pgRouting进行最短路径查询？ # 可以通过执行SQL查询，使用pgr_dijkstra等函数来获取两点之间的最短路径。[^14^]
            - 如何在pgRouting中处理多个路径规划问题？ # pgRouting支持K-最短路径算法，可以处理多个路径规划问题，如K-Dijkstra算法可以用于一点到多点的最短路径查询。[^7^]
            - 如何在pgRouting中实现动态成本计算？ # 可以通过SQL动态计算成本，其值可以来自多个字段或表，实现动态成本的测算。[^11^]
            - pgRouting在智能交通系统中的应用是什么？ # 在智能交通系统中，pgRouting可以实时分析路况信息，预测交通拥堵趋势，为驾驶员提供最佳行驶建议。[^7^]
            - pgRouting在物流配送领域有哪些应用？ # 在物流配送领域，pgRouting能够根据订单量波动自动调整配送路线，降低运营成本。[^7^]
            - Johnson算法是什么？ # Johnson算法是一种用于计算所有对最短路径的算法，适用于稀疏图和密集图。
            - 如何使用pgRouting中的Floyd-Warshall算法？ # Floyd-Warshall算法是一种动态规划算法，用于在加权图中找到所有顶点对之间的最短路径。
            - A*算法在pgRouting中的应用是什么？ # A*算法是一种启发式搜索算法，用于寻找两点间的最短路径，pgRouting支持该算法的单向和双向版本。
            - 双向Dijkstra算法如何工作？ # 双向Dijkstra算法通过从起点和终点同时进行搜索，以减少计算量，加快最短路径的查找速度。
            - 如何使用pgRouting进行K-最短路径查询？ # K-最短路径算法可以提供多条备选路径，pgRouting支持通过SQL查询实现这一点。
            - pgRouting如何实现旅行商问题（TSP）的求解？ # 旅行商问题是一个经典的优化问题，pgRouting提供了算法来寻找访问所有城市并返回起点的最短路径。
            - 什么是考虑转弯限制的最短路径（TRSP）？ # TRSP算法考虑了路径规划中的转弯限制，适用于城市道路等复杂网络。
            - pgRouting如何实现SQL查询的灵活性？ # pgRouting允许用户通过编写SQL查询来获取两点间的最短路径、成本最小路径或考虑多种因素后的最优路径方案。
            - 如何在pgRouting中添加自定义的路径规划算法？ # 用户可以通过编写自定义的SQL函数或PL/pgSQL代码来扩展pgRouting的功能，实现特定的路径规划算法。
            - pgRouting中的“成本”参数如何动态计算？ # “成本”参数可以通过SQL动态计算，其值可以来自多个字段或表，实现动态成本的测算。

        - topic: temp
          qs:
            - PostgreSQL的GIN索引是如何工作的？ # GIN索引通过将数据存储在树结构中来加速搜索，适用于全文搜索等场景。
            - PostgreSQL的BRIN索引适用于哪些场景？ # BRIN索引适用于大型表，特别是那些数据顺序写入且不经常更新的表。
            - 如何在PostgreSQL中使用EXPLAIN分析查询性能？ # 使用EXPLAIN可以查看查询的执行计划，EXPLAIN ANALYZE可以实际执行查询并显示每个步骤的耗时。
            - PostgreSQL的shared_buffers配置参数有什么作用？ # shared_buffers参数决定了PostgreSQL使用的内存缓存量，默认为128MB。
            - 如何在PostgreSQL中设置工作内存大小？ # 可以使用SET命令在会话中修改work_mem的值，例如`SET work_mem = '64MB';`。
            - PostgreSQL的autovacuum功能有什么作用？
            - PostgreSQL有哪些常用的扩展？ # PostgreSQL有多个扩展，如pg_trgm用于模糊匹配，pg_stat_statements用于监控查询性能等。
            - PostgreSQL如何实现咨询锁（Advisory Locks）？ # 咨询锁是一种轻量级的锁机制，可以用来实现分布式锁或同步数据库内的多个事务。
            - PostgreSQL如何保护数据库免受SQL注入攻击？ # PostgreSQL通过预编译语句和参数化查询来防止SQL注入攻击。

            - GIS 用 GiST 提供的 R 树还是直接使用 R 树更好（mysql 和 PostGIS0.6 之前都直接使用 R 树）？
            - 怎么备份 pgsql 某个数据库的数据？用`pg_basebackup`做物理备份（pg 服务的 data 文件夹），或者用`pg_dump`做逻辑备份（sql 文件）
            - "***How to optimize pgsql?***" # analyze, explain, auto_explain之类的，类似mysql. AUTOVACUUM

            - pgsql 里怎么使用 UUID v7 作为主键 # [Waiting for PostgreSQL 18 – Add UUID version 7 generation function. – select * from depesz;](https://www.depesz.com/2024/12/31/waiting-for-postgresql-18-add-uuid-version-7-generation-function/)
            - Why We Ditched etcd for SQL to Scale RisingWave's Metadata? # [RisingWave Labs Blog - RisingWave: Open-Source Streaming Database](https://risingwave.com/blog/risingwave-replaces-etcd-postgres/)
            - “当然，有前途没前途都是相对其他“国产数据库”来说的，基于开源数据库主干提供专家服务，发行版，扩展，等增量价值才是正路，土法自研是封闭僵化的老路，魔改开源是改旗易帜的邪路。PolarDB for PostgreSQL 总体来说对 PostgreSQL 的魔改程度不大，基本可以复用 PG 生态的扩展，工具与组件，这一点我觉得是非常明智的。”


    - url: https://github.com/duckdb/duckdb
      doc: https://duckdb.org/docs/ # https://dbaplus.cn/news-160-6236-1.html
      des: 跟sqlite一样，也是嵌入式RDB。专注于OLAP，也就是适用于读多写少的场景，而不是像其他RDB一样，同时支持OLAP和OLTP。DuckDB还是个ETL工具，比如说用来把mongodb数据导入mysql。
      sub:
        - url: https://github.com/marcboeker/go-duckdb
          des: 搭配golang使用，也要开启CGO。兼容database/sql，所以不是ORM，而是 raw sql，反正用起来挺烦的。

    # [Collection of insane and fun facts about SQLite - blag](https://avi.im/blag/2024/sqlite-facts/)
    - url: https://github.com/sqlite/sqlite
      sub:
        - url: https://github.com/mattn/go-sqlite3
        - url: https://github.com/tursodatabase/libsql
          des: libSQL 是一个开源的 SQLite 分支，旨在支持更多用例并允许开放贡献。 该项目解决了 SQLite 不接受外部贡献的问题，使得社区可以共同改进和扩展数据库功能。1、嵌入式副本，允许在应用程序内部拥有复制的数据库。2、libSQL 服务器提供远程访问功能，类似于 PostgreSQL 或 MySQL。3、支持多种编程语言，包括 Rust、JavaScript、Python 和 Go 等。
      rel:
        - url: https://github.com/nalgeon/sqlean
          des: 可以为 SQLite 增加更多功能，比如计算样品标准偏差、中位数、百分位数等。与其他数据库管理系统相比，SQLite 的功能很少。SQLite 的作者认为这是一个特点而不是问题，因为 SQLite 有一个扩展机制。现在有很多 SQLite 扩展，但都不完整、不一致，而且散落在互联网上。Sqlean 将它们整合在一起，整齐地打包成域模块，并为 Linux、Windows 和 macOS 提供了文档记录和测试。比如 DB Browser for SQLite 中，借助 sqlean 进行统计分析。
  record:
    - 【2025-06-24】移除【foundationdb】、【tigerbeetle】、【litestream（一个将SQLite数据库流式复制到S3兼容的存储系统的解决方案, 也可以用于备份。相当于某种sqlite的MS复制方案，适用于大型应用（但是很好奇为啥大型应用还要用sqlite，哈哈）。底下来列出了一些备选方案，比如直接用sqlite内置备份命令，再加上s3的sync命令来实现。以及LiteFS 或者 VFS 来实现。感觉意思不大。）】、【redka（基于sqlite实现的redis）】。其实都没啥用。
    - 【2025-06-24】移除【drawdb（用来画ER Diagram的工具。支持导出到5种RDB（mysql, pgsql, sqlite, mariadb, sqlserver），所以归类到RDB下面。感觉没啥用。）】。现在的DB管理工具本身都集成这个功能了，毫无意义。
  topics:
    - topic: 《从SE到DB分类：建立对DB的基本认知》
      isX: true
      des: 把《存储引擎横评》和《数据库分类》进行了整合。“无根之木、无源之水”，反之如果有了这个框架，就可以把所有DB相关的问题都整合进来，从而更加枝繁叶茂、树大根深。
      tables:
        - name: xxx
          table:
            - Type//DS: RDB（B+树）
              元问题: 如何用数学关系（二维表）保证数据的**强一致性**和**完整性**？
              延伸逻辑: 关系代数 → ACID事务 → 索引优化（B+树）→ SQL语言
              解决方案: 基于B+树的聚簇索引实现ACID事务，通过WAL日志和锁机制保障强一致性
              代价: 高并发写入性能（锁机制）、水平扩展难度
              使用场景: OLTP系统、财务交易系统

            - Type//DS: TSDB（TSM树）
              元问题: 如何高效存储和查询**时间维度主导**的连续数据流？
              延伸逻辑: 时间分区 → 数据降采样 → 时间窗口聚合 → 流式处理
              解决方案: 按时间分片存储，使用LSM变种结构批量压缩时序数据
              代价: 随机写入能力、非时序查询效率
              使用场景: IoT监控、实时指标分析

            - Type//DS: KVDB（LSM树）
              元问题: 如何以最低延迟实现**单键（Key）的原子读写**？
              延伸逻辑: 哈希表/跳表 → 内存缓存 → 最终一致性 → CRDT冲突解决
              解决方案: 基于LSM树的追加写机制实现高吞吐KV操作
              代价: 复杂查询（只能通过Key访问）、事务支持
              使用场景: 缓存系统、会话存储

            - Type//DS: Column DB（列存储块）
              元问题: 如何快速对**海量数据的某一列**进行批量分析？
              延伸逻辑: 列存储压缩 → 向量化计算 → OLAP优化 → 数据仓库
              解决方案: 列式分块存储配合向量化执行引擎
              代价: 单行高频更新效率、事务支持
              使用场景: 商业智能、大数据分析

            - Type//DS: Document DB（B树）
              元问题: 如何灵活存储和查询**半结构化嵌套数据**（如JSON/XML）？
              延伸逻辑: 无模式（Schema-less）→ 嵌套文档索引 → MapReduce聚合
              解决方案: B+树索引嵌套文档结构，支持动态字段
              代价: 跨文档事务、多表关联查询效率
              使用场景: 内容管理系统、产品目录

            - Type//DS: Vector DB（HNSW）
              元问题: 如何在高维空间中实现**低延迟的向量相似性搜索**并解决"维度灾难"？
              延伸逻辑: 近似最近邻算法 → 量化降维 → 分布式索引 → GPU加速计算
              解决方案: 分层可导航小世界图实现高效近邻搜索
              代价: 精确性（近似结果） / 事务支持 / 高内存消耗
              使用场景: 图像检索、推荐系统

            - Type//DS: Graph DB（邻接表）
              元问题: 如何高效存储和遍历**节点间复杂关系网络**并支持实时推理？
              延伸逻辑: 邻接表存储 → 图遍历算法优化 → 分布式图分片 → 索引下推
              解决方案: 物理邻接列表实现O(1)关系跳转
              代价: 非图模式查询性能 / 存储冗余 / 水平扩展复杂度
              使用场景: 社交网络、欺诈检测
          record:
            - 【2025-06-15】之前对SE和DB之间的关系理解有误（“能否把一切同类数据库之间，相同类型数据库选型就不同，选型之间的所有区别都归咎于存储引擎呃的区别。这个观点是正确的吗？如果正确的话，那是不是相同类型的db，其区别本质来说就是存储引擎的区别？”），我之前的理解是SE和DB之间是一对多关系（也就是说SE是type，而DB则是其中的item）。但是更好的理解是，SE应该看作是DB技术组合的tag，作为技术组合的其中一项存在（或者也可以姑且认为这些与DB绑定SE之间是一一对应关系）。所以需要修改这个table，之前是“从SE到SE再到DB”，现在这个table只需要保留DS就可以了（删掉了之前该table里对SE的对比，按照这些比较项）。具体的DB拆到gh里各自的type里了，至于SE就像上面所说，就作为各自DB的tag存在。
            - 【2025-06-15】删除了pgsql和mysql的对比（有以下比较项：CPU限制、第三方中间件、主从复制一底层原理、主从复制一安全性、JIT、并行查询、插件功能、check约束、GPU加速sql语句、数据类型、数据备份）


        - name: 【技术选型】RDB
          table:
            - SE: InnoDB
              DB: mysql、mariadb、percona
              SE-feat: B+树索引 + 行级锁 + Redo Log
              索引结构: B+Tree（聚簇索引）
              数据组织方式: 索引组织表 (IOT)
              事务支持: ACID + MVCC
              锁粒度: 行级锁
              写入性能: 中等（B+Tree维护开销）
              写操作机制:  # 通过ACID事务管理写入，使用双写缓冲（doublewrite buffer）确保写入安全性，先写入内存中的缓冲区（buffer pool），然后异步刷新到磁盘。
              读取性能: 高（B+Tree范围查询）
              读操作机制: 使用B+Tree索引进行快速查找，支持MVCC提供一致性读，减少读写冲突
              压缩效率: 页级压缩
              数据持久化: 通过Redo Log和双写缓冲确保数据持久化，支持崩溃恢复

            - SE: PostgreSQL Storage Engine
              DB: pgsql
              SE-feat: 堆表（Heap Table） + MVCC
              索引结构: B+Tree（非聚簇）
              数据组织方式: 堆表 (Heap Table)
              事务支持: ACID + SSI
              锁粒度: 行级锁
              写入性能: 高（堆表追加写）
              读取性能: 高（多版本并发控制）
              压缩效率: TOAST压缩


        - name: 【技术选型】kvdb
          table:
            - name: LevelDB
              lang: C++
              技术组合: LSM-Tree
              事务ACID支持: 单写事务（弱一致性）

            - name: RocksDB
              url: https://github.com/facebook/rocksdb
              doc: https://github.com/facebook/rocksdb/wiki
              lang: C++
              技术组合: LevelDB + 多线程 Compaction + 布隆过滤器（查询优化） + 前缀压缩
              事务ACID支持: 单写事务（ACID）

            - name: BadgerDB
              url: https://github.com/dgraph-io/badger
              lang: golang
              技术组合: 纯 LSM（直接写日志） + 值日志分离存储（减少写放大） + 无 SSTable 层级
              事务ACID支持: ACID事务

            - name: BoltDB
              lang: golang
              技术组合: B+Tree + 单文件存储（内存映射） + ACID事务（MVCC）
              事务ACID支持: ACID事务

            - name: LMDB
              url: https://github.com/LMDB/lmdb
              what: B+Tree + Copy-on-Write（无锁读） + 零拷贝内存映射
              des: 与sqlite类似的嵌入式RDB，但是不如sqlite。以下两点：相同数据的情况下, 比sqlite3的数据库大13%。key大小限制。lmdb的key被限制在1978个字节以内, 这在一些情况下会非常容易超出限制。


            - name: bitcask
              url: https://github.com/basho/bitcask
              lang: Erlang
              技术组合: 日志追加写（顺序IO） + 内存哈希表（快速定位） + 合并文件（Hintfile 压缩）
              事务ACID支持: 无事务支持

            - name: rosedb
              url: https://github.com/rosedblabs/rosedb
              lang: golang
              技术组合: bitcask存储模型 + redis数据类型（String/List/Hash/Set/ZSet） + 内存索引 + 异步持久化
              事务ACID支持: 基础事务支持

            - name: nutsdb
              url: https://github.com/nutsdb/nutsdb
              lang: golang
              技术组合: bitcask存储模型 + redis数据类型（List/Set/SortedSet）
              事务ACID支持: ACID事务

            - name: moss
              url: https://github.com/couchbase/moss
              lang: golang
              技术组合: LSM变体（无WAL设计） + 纯Go实现 + 零拷贝读取 + 并发压缩 + 快照隔离
              事务ACID支持: 快照隔离

            - name: lotusdb
              url: https://github.com/lotusdblabs/lotusdb
              lang: golang
              技术组合: 混合架构（LSM（写优化） + B+Tree（读优化）） + 哈希索引（快速定位）
              事务ACID支持: ACID事务


            - name: go-memdb
              url: https://github.com/hashicorp/go-memdb
              lang: golang
              技术组合: Radix树存储 + MVCC + 二级索引 + Watch API
              事务ACID支持: ACID事务
          record:
            - 【2025-06-10】添加了【技术组合】，并且所以就移除了【Compaction策略】、【kv有序性】、【内存管理】这三个比较项。因为基本上都是LSM Tree实现的，这几项本身都是LSM本身的特点，大同小异。不应该作为该kvdb的比较项。
            - 【2025-06-10】移除了【存储介质优化】这个key。因为现代主流 KVDB 几乎都默认针对 SSD 优化（RocksDB/BadgerDB/TiKV），只有历史遗留系统（如 LevelDB）明确针对 HDD 设计。所以就没有意义了。
            - 【2025-06-10】移除了【分布式设计】这个key，也移除了tikv之类的repo。因为kvdb其实默认单机，tikv、etcd、cockroach 这些也不能算是 kvdb。不能说他们支持存kv，就算是kvdb了。这是一个很严重且普遍的误区。因为这些也只是把 kvdb 作为存储层而已，比如说 etcd就使用了 boltdb 作为存储层，但是你不能说 etcd就是kvdb了，etcd的核心在于保证 分布式一致性（以及 分布式锁、分布式队列之类的）。包括分布式数据库CockroachDB  也是同理。CockroachDB 这些被认为是 NewSQL，其实是 在kvdb(LSM-Tree)上面构筑一个兼容 SQL 常用语句和事务的兼容层，这样既可以享受大规模 LSM-Tree 集群带来的扩展性和高性能，也可以尽量少改动现有应用代码和开发习惯。其变动核心也在于上面的兼容层，以及为了实现分布式，而使用的 raft（从这点来说 tidb和etcd其实架构很相似）。所以这些就不应该被归类到kvdb里。
            - 【2025-06-10】移除了【典型应用场景】这个key。其实这个也是跟我们上面说的【分布式设计】也有关系。我们上面就说了 etcd, tikv, cockroach 这些不是kvdb，就是因为他们本身都是把kvdb作为服务的一部分，也正是因为kvdb都是嵌入式类型，所以也才能作为所谓“单体kvdb”（因为单体kvdb就不是真正kvdb了）的一部分。
          qs:
            - 【bitcask存储模型】 # [源码分析基于 bitcask 的 rosedb KV 存储引擎的设计实现 – 峰云就她了](https://xiaorui.cc/archives/7414)
        #  1. **核心数据结构**：如 `B+Tree`、`SkipList`、`LSM`。
        #  2. **持久化机制**：如 `WAL`、`Checkpoint`、`日志追加写`。
        #  3. **优化策略**：如 `布隆过滤器`、`多线程 Compaction`。
        #  4. **分布式特性**：如 `Raft`、`Gossip 协议`。
        #  5. **特殊设计**：如 `值日志分离`（BadgerDB）、`内存映射`（LMDB）。

        - name: 【技术选型】数仓
          des: 主流开源MPP数据库 (Greenplum, ClickHouse, Apache Doris, Presto/Trino, Apache Kylin) 各自有啥特点
          table:
            - name: ClickHouse
              技术组合: MergeTree引擎(LSM-Tree变种)+向量化执行引擎+数据压缩算法(Delta/LZ4)
              lang: C++
              查询语言: SQL方言(ClickHouse特有)
              适用场景: 实时分析、日志处理、Web分析
              核心优势/缺点: |
                - 优势: 极致查询性能、高压缩率、开源
                - 缺点: 事务支持弱、单点写入性能受限
              索引结构: 主键索引(稀疏索引)+跳数索引
              事务ACID支持: 无(仅追加写入)
              锁机制（锁粒度）: 表级锁
              查询性能/机制: ⭐⭐⭐⭐⭐(列式存储+向量化执行)
              写入性能/机制: 高吞吐批量导入
              压缩效率: 高(Delta/LZ4算法)
              持久化机制: LSM-Tree结构

            - name: Cassandra
              url: https://github.com/apache/cassandra
              技术组合: LSM-Tree+Gossip协议(集群状态同步)+可调一致性(QUORUM读写)
              lang: java
              查询语言: CQL(类SQL)
              适用场景: 时序数据、物联网
              核心优势/缺点: |
                - 优势: 高扩展性、跨数据中心容灾
                - 缺点: 复杂查询性能差、不支持JOIN
              索引结构: SSTable+布隆过滤器
              事务ACID支持: 无(最终一致性)
              锁机制（锁粒度）: 行级锁
              查询性能/机制: ⭐⭐⭐(最终一致性)
              写入性能/机制: 高并发写入(10万+/秒)
              压缩效率: 中(Snappy压缩)
              持久化机制: CommitLog+SSTable

            - name: ScyllaDB
              url: https://github.com/scylladb/scylladb
              技术组合: LSM-Tree+分布式分区(一致性哈希)+异步副本同步+多核优化(Seastar框架)
              lang: cpp
              查询语言: CQL(兼容Cassandra)
              适用场景: 低延迟高性能场景
              核心优势/缺点: |
                - 优势: Cassandra兼容性+10倍性能提升
                - 缺点: 社区较小
              索引结构: 全局二级索引(GSI)
              事务ACID支持: 无
              锁机制（锁粒度）: 无锁架构
              查询性能/机制: ⭐⭐⭐⭐(<1ms P99延迟)
              写入性能/机制: 百万级QPS
              压缩效率: 高(Zstd压缩)
              持久化机制: MemTable+SSTable

            - name: HBase
              url: https://github.com/apache/hbase
              技术组合: LSM-Tree+ZooKeeper协调+HDFS分布式存储
              lang: java
              查询语言: Java API/SQL层(Phoenix)
              适用场景: 大规模数据存储
              核心优势/缺点: |
                - 优势: 强一致性、Hadoop无缝集成
                - 缺点: 分析能力弱
              索引结构: 行键+布隆过滤器
              事务ACID支持: 有限
              锁机制（锁粒度）: 行锁
              查询性能/机制: ⭐⭐(依赖RowKey设计)
              写入性能/机制: 支持随机读写
              压缩效率: 中(GZIP压缩)
              持久化机制: HDFS+WAL

            - name: Vertica
              技术组合: MPP架构+列式存储+投影优化(物化视图)
              lang: C/C++
              查询语言: SQL
              适用场景: 企业级数据仓库
              核心优势/缺点: |
                - 优势: ACID事务支持、混合行列优化
                - 缺点: 商业化成本高
              索引结构: B树索引
              事务ACID支持: 完整ACID
              锁机制（锁粒度）: 行级锁/MVCC
              查询性能/机制: ⭐⭐⭐⭐(复杂查询优化)
              写入性能/机制: 中等
              压缩效率: 中(Zlib/QuickLZ)
              持久化机制: 预写日志

            #- SE: Vertica Storage Engine
            #  DB: vertica
            #  SE-feat: 列压缩 + 投影存储
            #  索引结构: 投影存储
            #  数据组织方式: 列分组投影
            #  事务支持: 有限事务
            #  锁粒度: 行级锁
            #  写入性能: 低（实时压缩开销）
            #  读取性能: 极高（内存列运算）
            #  压缩效率: 自适应编码压缩





            - name: Druid
              技术组合: 分布式Segment存储+列式存储+倒排索引+ZooKeeper协调
              lang: java
              查询语言: SQL/原生JSON
              适用场景: 实时监控、广告业务
              核心优势/缺点: |
                - 优势: 时间序列优化、预聚合
                - 缺点: 运维成本高
              索引结构: 位图索引+倒排索引
              事务ACID支持: 无(仅追加)
              锁机制（锁粒度）: 分区锁
              查询性能/机制: ⭐⭐⭐⭐(实时)
              写入性能/机制: 批量追加为主
              压缩效率: 高(LZ4/Zstd)
              持久化机制: 深度存储+实时节点

            - name: Doris
              技术组合: MPP架构+列式存储+向量化执行+物化视图
              lang: C++/Java
              查询语言: SQL(MySQL协议)
              适用场景: 交互式BI分析
              核心优势/缺点: |
                - 优势: 高并发支持、云原生友好
                - 缺点: 复杂查询优化待提升
              索引结构: 前缀索引+布隆过滤器+Z-order索引
              事务ACID支持: 有限
              锁机制（锁粒度）: 分区锁
              查询性能/机制: ⭐⭐⭐⭐(MPP+向量化)
              写入性能/机制: 高(流式导入)
              压缩效率: 高(LZ4/Zstd)
              持久化机制: Segment文件+多副本

        - name: 【技术选型】tsdb
          table:
            - name: InfluxDB
              技术组合: TSM Tree存储引擎+时间序列优化
              lang: Go
              查询语言: InfluxQL/Flux
              适用场景: DevOps监控、中小规模时序数据
              核心优势/缺点: |
                - 优势: 生态成熟，支持连续查询
                - 缺点: 集群版闭源
              索引结构: 时间范围分区+倒排索引
              事务ACID支持: 单机ACID/集群最终一致
              锁机制（锁粒度）: 分区级锁
              查询性能/机制: 中等(依赖时间范围扫描)
              写入性能/机制: 100k-500k points/s(批量写入优化)
              压缩效率: 中等(5-10x)
              持久化机制: WAL预写日志+TSM文件存储

            - name: TDengine
              url: https://github.com/taosdata/TDengine
              doc: https://docs.tdengine.com/
              技术组合: 自研列式存储+分布式架构
              lang: C/C++
              查询语言: 扩展SQL
              适用场景: IoT高频写入、工业传感器
              核心优势/缺点: |
                - 优势: 千万级点/秒写入、强一致性
                - 缺点: 复杂查询较弱
              索引结构: 时序数据块索引
              事务ACID支持: 强一致(分布式ACID)
              锁机制（锁粒度）: 表级锁
              查询性能/机制: 高(简单查询微秒级)
              写入性能/机制: 1M+ points/s(列式压缩写入)
              压缩效率: 极高(10-20x)
              持久化机制: 分布式WAL+列式存储文件

            - name: VictoriaMetrics
              url: https://github.com/VictoriaMetrics/VictoriaMetrics
              doc: https://docs.victoriametrics.com/
              技术组合: 改进Prometheus TSDB+内存优化
              lang: Go
              查询语言: MetricsQL
              适用场景: Prometheus长期存储
              核心优势/缺点: |
                - 优势: 高查询吞吐量
                - 缺点: 集群版闭源
              索引结构: Metric名称倒排索引
              事务ACID支持: 最终一致
              锁机制（锁粒度）: 进程级锁
              查询性能/机制: 高(并行查询)
              写入性能/机制: 300k-700k points/s(内存合并写入)
              压缩效率: 高(10-15x)
              持久化机制: 本地快照+合并存储

            - name: QuestDB
              url: https://github.com/questdb/questdb
              doc: https://questdb.io/docs/
              技术组合: 列存+向量化执行+SIMD指令
              lang: Java/C++
              查询语言: PostgreSQL SQL
              适用场景: 金融实时分析
              核心优势/缺点: |
                - 优势: OLAP查询优化
                - 缺点: 集群功能未成熟
              索引结构: 分区索引+位图索引
              事务ACID支持: 强一致
              锁机制（锁粒度）: 行级锁
              查询性能/机制: 极高(向量化引擎)
              写入性能/机制: 800k+ points/s(并行写入)
              压缩效率: 中等(5-8x)
              持久化机制: 内存表持久化+WAL

            - name: TimescaleDB
              技术组合: PostgreSQL扩展+时序优化
              lang: C
              查询语言: 完整SQL
              适用场景: 混合工作负载、SQL优先场景
              核心优势/缺点: |
                - 优势: ACID强一致
                - 缺点: 写入性能中等
              索引结构: B树索引+时间分区索引
              事务ACID支持: ACID强一致
              锁机制（锁粒度）: 行级锁/MVCC
              查询性能/机制: 高(复杂查询优化)
              写入性能/机制: 50k-200k points/s
              压缩效率: 中等(4-7x)
              持久化机制: PostgreSQL WAL+ZFS压缩

            - name: GreptimeDB
              url: https://github.com/GreptimeTeam/greptimedb
              技术组合: 云原生架构+Apache DataFusion
              lang: Rust
              查询语言: SQL+PromQL
              适用场景: 云原生可观测性
              核心优势/缺点: |
                - 优势: 多数据类型支持
                - 缺点: 新项目成熟度待验证
              索引结构: 倒排索引+位图索引
              事务ACID支持: 可配置一致性
              锁机制（锁粒度）: 分区级锁
              查询性能/机制: 高(混合查询优化)
              写入性能/机制: 500k+ points/s
              压缩效率: 高(10x+)
              持久化机制: 对象存储分层存储

            - name: CnosDB
              url: https://github.com/cnosdb/cnosdb
              技术组合: TSM改进引擎+分布式架构
              lang: Rust
              查询语言: SQL+PromQL+InfluxQL
              适用场景: 高吞吐工业物联网
              核心优势/缺点: |
                - 优势: 云边协同部署
                - 缺点: 社区生态初建
              索引结构: TSI倒排索引
              事务ACID支持: 可调一致性
              锁机制（锁粒度）: 分区级锁
              查询性能/机制: 高(向量化引擎)
              写入性能/机制: 500k-1M points/s
              压缩效率: 高(8-12x)
              持久化机制: WAL+列式压缩存储

            - name: openGemini
              url: https://github.com/openGemini/openGemini
              doc: https://docs.opengemini.org/
              技术组合: 分布式TSM架构+多级存储
              lang: Go
              查询语言: GeminiQL(SQL-like)
              适用场景: 超大规模监控
              核心优势/缺点: |
                - 优势: 华为电信级验证
                - 缺点: 生态起步阶段
              索引结构: 时间分区索引+倒排索引
              事务ACID支持: 最终一致
              锁机制（锁粒度）: 分片级锁
              查询性能/机制: 高(集群并行查询)
              写入性能/机制: 1M+ points/s
              压缩效率: 极高(10-15x)
              持久化机制: 分布式WAL+多副本存储


      qs:
        #  关于存储引擎可以
        #
        #  再看数据存储领域，有两个“极端”发展方向：
        #
        #  - 1、加快读：通过 index 1（日 + 树、二份查找树等方式），提高查询速度，但是写入数据时要维护索引，因此会降低写入效率。
        #  - 2、加快写：纯日志型，数据以 append 追加的方式顺序写入，不加索引，使得写入速度非常高 (理论上可接近 内存随机写 速度），但是缺乏索引支持，因此查询性能低。
        #
        #  基于这两个极端，又衍生出来了了类最具代表性的底层索引结构：
        #
        #  - 1、哈希索引：通过哈希西数将 key 映射成数据的存储地址，适用于等值查询等简单场景，对于比较查询、范围查询等复杂场景无能为力。
        #  - 2、B/B+ Tree 索引：最常见的索引类型，重点考虑的是读性能，它是很多传统关系型数据库，比如 MysQL、Oracle 的底层结构。
        #  - 3、LSM Tree 索引：数据以 Append 方式追加写入日志文件，优化了写但是又没显著降低读性能，众多 NosQL 存储系统比如 Big Table, HBase, Cassandra, RocksDB 的底层结构。

        #- [#115 理论结合实践详解 B+ 树存储引擎（InnoDB、BoltDB、BuntDB） - YouTube](https://www.youtube.com/watch?v=9XtACKzFIRc) 这个视频相当有干货，从存储引擎为什么使用 bptree 说起，到各种 tree 的比较，再到写操作时 bptree 会执行哪些操作，再到这几种使用 bptree 的存储引擎分别是怎么保证 ACID 的，再到 innodb 和 boltdb 使用 bptree 的具体细节。


        - "***【异步持久化】所有DB类型都支持异步刷盘，能否分别说说各种DB各自是怎么实现该机制的？***"


    #- name: 关系型数据库 (RDBMS)
    #  代表产品: MySQL (InnoDB), PostgreSQL
    #  异步持久化实现方式: |-
    #    ✅ Buffer Pool：缓存数据页
    #    ✅ Log Buffer：Redo Log缓冲
    #    ✅ 后台线程：异步刷脏页/日志
    #  关键设计特点: |-
    #    通过 innodb_flush_log_at_trx_commit 控制持久化强度（0/1/2）
    #    支持 synchronous_commit=off 开启异步提交（延迟刷WAL）
    #
    #- name: 内存型数据库
    #  代表产品: Redis
    #  异步持久化实现方式: |-
    #    ✅ RDB：fork()子进程生成快照
    #    ✅ AOF：缓冲写命令 + 后台刷盘（everysec/no）
    #  关键设计特点: |-
    #    子进程完全隔离磁盘I/O
    #    混合持久化（RDB+AOF）优化恢复速度
    #
    #- name: 文档数据库
    #  代表产品: MongoDB (WiredTiger)
    #  异步持久化实现方式: |-
    #    ✅ Cache：内存缓存数据/索引
    #    ✅ Journal：写日志缓冲（默认60ms刷盘）
    #    ✅ Checkpoint
    #  关键设计特点: |-
    #    Journal刷盘可配置为 writeConcern: {j: false}（异步）
    #
    #- name: 列式数据库
    #  代表产品: Cassandra
    #  异步持久化实现方式: |-
    #    ✅ MemTable：内存表结构
    #    ✅ CommitLog：顺序写日志
    #    ✅ SSTable：后台刷入磁盘
    #  关键设计特点: |-
    #    写入先写CommitLog
    #    MemTable满后异步刷为SSTable
    #
    #- name: 时序数据库
    #  代表产品: InfluxDB
    #  异步持久化实现方式: |-
    #    ✅ WAL：预写日志缓冲
    #    ✅ Cache：内存批缓存
    #    ✅ TSM Engine：后台压缩写入磁盘
    #  关键设计特点: |-
    #    数据先写入WAL和Cache
    #    后台按批次/时间异步持久化
    #
    #- name: 图数据库
    #  代表产品: Neo4j
    #  异步持久化实现方式: |-
    #    ✅ PageCache：依赖OS缓存
    #    ✅ 事务日志：延迟批量提交
    #  关键设计特点: |-
    #    默认异步刷盘
    #    需显式配置 dbms.tx_log.rotation.retention_policy=true 保证安全
    #
    #- name: 消息队列
    #  代表产品: Kafka
    #  异步持久化实现方式: |-
    #    ✅ PageCache：依赖OS内存缓存
    #    ✅ 异步刷盘：flush.ms 控制延迟
    #    ✅ 副本同步：ISR机制
    #  关键设计特点: |-
    #    生产者发送即返回
    #    Broker异步持久化到CommitLog
    #
    #- name: 分布式数据库
    #  代表产品: TiDB
    #  异步持久化实现方式: |-
    #    ✅ Raft Log：多副本同步日志
    #    ✅ 内存事务缓冲：批量处理
    #    ✅ 异步写SST
    #  关键设计特点: |-
    #    Raft日志保证分布式一致性
    #    数据最终异步写入存储引擎（RocksDB）

    #     - Type: OLTP
    #      元问题: 如何在高并发下保证**短事务的原子性和实时响应**？
    #      延伸逻辑: 行存储 → 锁机制 → WAL日志 → 主从复制
    #
    #    - Type: OLAP
    #      元问题: 如何快速对**海量历史数据做多维聚合**？
    #      延伸逻辑: 列存储 → 星型/雪花模型 → 预计算Cube → 分布式查询


    - topic: 基本认知（MySQL架构、优化器）
      picDir: db/mysql
      table:
        - name: MySQL 5.5 (2010)
          存储引擎: |
            - InnoDB 成默认引擎
            - 线程池优化
            - WAL 日志压缩
          查询处理: |
            - 分区表支持
            - 事件调度器
          复制机制: |
            - 半同步复制
            - 心跳检测
            - 中继日志自动恢复
          数据类型与扩展: |
            - GIS 空间数据类型
            - 部分 UTF-8 支持

        - name: MySQL 5.6 (2013)
          存储引擎: |
            - InnoDB 性能重构
            - 减少锁竞争
            - 表空间热迁移
          查询处理: |
            - 查询优化器改进
            - 减少全表扫描
          复制机制: |
            - GTID 全局事务标识，用来取代传统的`binlog文件偏移量复制`方式
            - 多线程复制
            - Binlog 崩溃安全
          数据类型与扩展: |
            - 全文索引优化
            - MEMORY 引擎增强

        - name: MySQL 5.7 (2015)
          存储引擎: |
            - InnoDB 崩溃恢复增强
            - 在线 DDL 优化
            - 临时表独立存储
          查询处理: |
            - 支持JSON数据类型，以及相关操作
            - 虚拟列 (Virtual Columns)
            - GIS 空间扩展
          复制机制: |
            - 组提交优化
            - 基于 WRITESET 的并行复制
          数据类型与扩展: |
            - JSON 函数扩展
            - 生成列（Generated Columns）

        # 为什么 MySQL8 移除了`查询缓存`？很简单，使用查询缓存利大于弊，MySQL 团队打算把查询缓存做到客户端，也就是类似`redis6客户端缓存`的功能
        # 增加了“隐藏索引”，索引被设置为隐藏后，优化器会忽略该索引，便于性能调试
        # 查询优化工具`，我们常用的`explain工具`给出的结果，不是实际执行的情况，所以不太准确；而`explain analyze`会实际执行，以测量出查询计划中各个关键点的实际指标，例如耗时、条数，最后详细的打印出来
        - name: MySQL 8.0 (2018)
          存储引擎: |
            - 火山模型执行器
            - 原子 DDL 操作
            - 事务型数据字典
            - 移除 MyISAM 系统表
          查询处理: |
            - 移除了“查询缓存机制”
            - 窗口函数
            - CTE（公共表表达式）
            - 哈希连接优化
          复制机制: |
            - 多源复制聚合
            - 二进制日志压缩
            - 延迟副本控制
          数据类型与扩展: |
            - 默认 UTF8MB4 字符集
            - 降序索引
            - 隐藏索引

        - name: MySQL 9.0 (2025)
          存储引擎: |
            - 向量存储引擎（实验）
            - 高维数据索引优化
          查询处理: |
            - 增强向量计算
            - 近似最近邻搜索
          复制机制: |
            - 行复制全面替代语句复制
            - 分布式事务增强
          数据类型与扩展: |
            - 向量数据类型（实验）
            - AI 场景原生支持
      qs:
        - "***为啥说mysql是纯垃圾?***"
        - "***MySQL的 cpoe(s)***" # cpoe(s) 最核心的是optimizer, executor和storage. 其他的什么connector和parser都没什么意思。查询优化器的核心是各种查询优化方法。
        - 聊聊 executor和存储引擎之间的交互?
        - "***那我们来继续聊聊optimizer的细节，如果我们把mysql比做图书馆的话，ICP, MRR, BKA, BNL 以及 index dive 这些分别可以类比成什么?***"
        - index dive 范围查找优化 # [MySQL :: MySQL 8.4 Reference Manual :: 10.2.1.2 Range Optimization](https://dev.mysql.com/doc/refman/8.4/en/range-optimization.html)
        - ICP索引条件下推(Index Condition Pushdown) 用索引筛选的 where 条件在存储引擎一侧进行筛选，而不是将所有 index access 的结果都放在 server 端进行 where 筛选
        - MRR 多范围读取(Multi-Range Read) 优化器将随机 io 转化为顺序 io，以降低查询过程中 io 开销的一种手段。它通过在一个请求中读取多个连续范围的数据，以减少读取磁盘的次数。MRR通常用于处理顺序扫描（Sequential Scan）或范围查询（Range Query）等需要从磁盘读取大量连续数据的场景。通过减少磁盘IO次数，MRR可以显著提高读取操作的性能。
        - BKA，Batched Key Access，在表连接过程中为了提升 join 性能而使用的 join buffer，其作用是在读取被 join 表的记录时，使用顺序 io(BKA 被使用的标识是执行计划的 extra 信息中会有 BKA 信息)
        - BNL
        - BKA和BNL是不是都是在join操作时才会使用的优化技术？

        - ICP 的原理？有什么好处？什么情况下会下推到存储引擎去处理？ # [MySQL：好好的索引，为什么要下推？](https://mp.weixin.qq.com/s?__biz=MzAwNTc3OTE5Mg==&mid=2657444539&idx=1&sn=f2211d7ed33b073ba91f1ccaa50c8e93)
        - 给我举例说明mysql使用ICP之后的查询流程（相较于没有使用ICP）有什么优化？ # 说白了，有一个  WHERE first_name = 'John' AND last_name LIKE 'Do%'; 的sql，如果没有开启ICP，在InnoDB只查 first_name = 'John'，然后回表到mysql server的executor处理。如果开启ICP，last_name LIKE 'Do%' 也会下推到InnoDB，这样就不需要回表操作了，效率更高（减少了磁盘 IO 和网络传输，提高了查询效率。特别是对于那些返回记录较多但满足查询条件的记录较少的查询）。通过explain可以看到，未使用ICP时，Extra列显示Using where，表示在Server层进行过滤。而使用ICP时，Extra列显示Using index condition，表示在存储引擎层进行了条件过滤。InnoDB默认开启ICP，但是只有在添加索引时会用到ICP。

        - 使用 ICP 的注意事项？
        #- ICP 只能用于二级索引，不能用于主索引。
        #- 不是全部 where 都能用 ICP 筛选，如果 where 条件的字段不在索引中，当然还是要读取整条记录做筛选，在这种情况下，仍然要到 server 端做 where 筛选。
        #- ICP 的加速效果取决于在存储引擎内通过 ICP 筛选掉的数据的比例。
        - 啥是 Index Dive?
        - Optimizer 在哪些情况下会跳过 Index Dive?
        - MySQL8 Optimizer提供的 索引跳跃扫描机制(Index Skip Scan) 有啥用? # MySQL Optimizer提供的机制，不需要设置索引来开启

        - "***Volcano Model(Iterator Executor)***"

    - topic: mysql 有哪些 sql优化工具?
      url: https://dev.mysql.com/doc/refman/8.0/en/performance-schema.html
      qs:
        - 查询计划的解释是由优化器提供的，而执行详细信息的收集是由执行器提供的。 # 这点在mysql和postgres里是一样的。*也就是说几个mysql常用的优化命令，optimizer-trace是optimizer提供的，explain analyze 是 executor提供的，show status 则是一个数据汇总的命令，*
        - "***How to optimize MySQL?***" # [这15个SQL优化口诀，工作面试都够用了](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247495903&idx=1&sn=9ab0f1a49aedbce6a32aa4e7a6794d28)

        - 【explain-analyzer】返回的 cost, actual, time, rows, loops 分别是啥? cost 多少说明优化 # mysql8 提供的优化工具，他会做出查询计划，并提供【估算结果cost】（mysql就是根据cost选择执行计划的），并且会【实际执行】，以测量出查询计划中各个关键点的实际指标actual，比如耗时time、条数rows、循环次数loops。cost是估算值，actual是实际执行，需要保证cost < 100，否则就需要优化。

        - optimize 语句
        - explain 只展示what(选择了哪个查询计划)，但是没有展示why(为什么选择该查询计划，以及替代计划) # [数据库内核月报](http://mysql.taobao.org/monthly/2019/11/03/)


    - topic: "***InnoDB***"
      qs:
        - "***【InnoDB架构】***"

        - RU和SER很好理解，RC和RR不好理解，尝试举例说明这两种设置

        - "***【Innodb锁机制】聊聊innodb锁机制? InnoDB允许行锁和表锁共存（也就是“多粒度锁机制”）有啥意义?***" # 行锁分为共享锁和排他锁，但是 InnoDB 还有两种内部使用的意向锁，这两种意向锁都是表锁。表锁、页锁、行锁、字段锁，当然是锁粒度更小的，开销更小、加锁更快，这个是毫无疑问的。
        - 【InnoDB的7种类型的锁】（共享排他锁（shared and exclusive lock）、意向锁（intention lock）、记录锁（record lock）、间隙锁（gap lock）、临键锁（next-key lock）、插入意向锁（insert intention lock）、自增锁（auto-inc lock））
        - 我说下我的理解哈，shared lock和 exclusive lock就是mutex和rwmutex，所有语言里都有类似的锁机制，不多说。intention lock其实就是IsLocked，用来判断是否加锁，进而决定下一步操作是sleep还是spin什么的，也很常用。下面的gap lock, next-key lock 都是MVCC相关的锁，可以理解。自增锁auto-inc lock是用来保证自增字段唯一性的，防止并发插入时，某个自增字段的数据重复。我不太理解record lock和insert intention lock有啥用。mutex(exclusive lock)不是已经能够在写操作时的行级锁定了吗？为什么还需要record lock呢？还有不是已经有intention lock了吗？为什么还需要insert intention lock？

        - 怎么理解插入意向锁? 到底是I锁还是gap锁?
        - "***innoDB 间隙锁是什么？innoDB 间隙锁有哪些缺点？***" # InnoDB 的锁定是通过在指向数据记录的第一个索引键之前和最后一个索引键之后的空域空间标记锁定信息实现的。这种锁定方式被称为 "NEXT-KEY locking"（间隙锁）。锁定一个范围之后，即使某些不存在的键值也会被无辜锁定，造成锁定的时候无法插入键值锁定内的任何数据。

        - 为什么mysql间隙锁中非唯一索引的next-key lock不退化成行锁？ # [为什么mysql间隙锁中非唯一索引的next-key lock不退化成行锁？ - SegmentFault 思否](https://segmentfault.com/q/1010000043986278) “间隙锁（Gap-Lock）：范围查询或者是等值查询，且记录不存在，当记录不存在，临键锁退化成GAP锁，且gap锁之间不冲突，” “精确等值检索，Next-Key Locks就退化为记录锁，不会加gap锁” “next-key lock 在一些场景下会退化成记录锁或间隙锁。那到底是什么场景呢？总结一句，在能使用记录锁或者间隙锁就能避免幻读现象的场景下， next-key lock 就会退化成记录锁或间隙锁。”

        - "***【FTWRL】 (Forced Transactional Write-Ahead Logging)***" # [FTWRL](http://mysql.taobao.org/monthly/2017/08/04/) 用来阻塞所有写操作，来保证备份时的数据一致性。FTWRL用来确保即使mysql crash, 已经提交的事务也不会丢失，因为事务日志已经被安全地写入到磁盘。这对于需要高数据一致性和可靠性的应用场景非常重要。

        - "***【redolog】"  # 为了获得更好的读写性能，innoDB 将数据缓存到内存 (`innoDB Buffer Pool`)，对磁盘数据的修改也会落后于内存，如果进程崩溃就会导致内存数据丢失，所以 innoDB 就维护了 redolog，*内存数据丢失后，innoDB 会在重启时，通过重放 REDO，恢复数据*


        - "*避免每次读操作都进行磁盘 IO*，具体来说，缓冲池缓存了大量数据页，让 CPU 读取和写入数据时，直接和缓冲区交互，不需要操作磁盘，从而避免磁盘拖慢数据库性能的问题" # *缓存表数据与索引数据*，把磁盘上的数据加载到缓冲池，避免每次访问都进行磁盘 IO，起到加速访问的作用。


    #  - 插入缓冲 (insert buffer)：，*加速插入操作*，插入缓冲用于非聚簇索引的插入和更新操作，先判断插入的非聚簇索引是否在缓存池中，如果在则直接插入，否则插入到`插入缓存对象`中。再以一定的频率进行插入缓冲和辅助索引叶子节点的 merge 操作，将多次插入合并到一个操作中，提高对非聚簇索引的插入性能
    #  - 二次写 (double write)：由两部分组成，一部分是内存中的`double write buffer`，大小为 2MB，另一部分是物理磁盘上共享表空间连续的 128 个页，大小也为 2MB。在对缓冲池的脏页刷新时，并不直接写磁盘，而是通过 memcpy 函数将脏页先复制到内存中的该区域，之后通过`double write buffer`再分两次，每次 1MB 顺序地写入共享空间的物理磁盘上，然后马上调用 fsync 函数，同步磁盘，避免 OS 缓冲写带来的问题
    #  - 自适应哈希索引 (adaptive hash index)：*自动在内存中创建 hash 索引来加速读操作*，innoDB 会根据访问的频率和模式，为热点页建立哈希索引，来提高查询效率。索引通过缓存池的 B+ 树页构造而来，因此建立速度很快，innoDB 存储引擎会监控对表上各个索引页的查询，如果观察到建立哈希索引可以带来速度上的提升，则建立哈希索引，所以叫做`自适应哈希索引`
    #  - 缓存池：为了提高数据库的性能，引入缓存池的概念，通过参数可以设置缓存池的大小和实例个数，缓存池可以存储一下内容：索引页、数据页、undo 页、插入缓冲、自适应哈希索引、innoDB 存储的锁信息和数据字典信息 (data dict)




    # [MVCC 的实现：活跃事务列表和时间戳 - 黄金架构师](https://hhwyt.xyz/database/mvcc1/)
    # [MemGraph 背后论文《基于内存和 MVCC 的高速可串行化》详细解析（一） | 木鸟杂记](https://www.qtmuniao.com/2024/02/06/memory-mvcc-serial/)
    - topic: "***InnoDB 事务（锁、隔离、MVCC）***"
      qs:
        - 什么是 CC？有哪些单版本的 CC 方法？基于这些单版本 CC 方法，MVCC 有哪些不同的实现? # CC 由数据库的调度器负责，事务本身感知不到可能导致数据一致性的冲突事务，调度器会 delay 或者 abort，如果 delay 就延迟到合法时机，如果 abort 就直接回滚。本质上是一个取舍问题，乐观锁不维护锁，吞吐很高，但是相应回滚也会比较多，而回滚比延迟的成本要高很多，所以在冲突较少和 validation 开销小的情况下，使用 OCC。LBCC 的方案则相反。

        - "***用乐观程度（也就是从OCC到PCC）为轴，我们有三种实现MVCC的方法（MVOCC, MVTO, MV2PL），能否分别介绍各自Delay和Abort的时机（tip: 想想tx）?***" # [浅析数据库并发控制机制 | CatKang的博客](https://catkang.github.io/2018/09/19/concurrency-control.html)

        - "***MySQL 2PL***" # 2PL就是通过组合使用innodb的S锁和X锁来在保证事务隔离的情况下，提高并发性能

        - "***InnoDB是怎么保证事务ACID的？***"
        #- `一致性`，*一致性是最基本属性，其他三种都是为了实现一致性而存在的*
        #- `隔离性`，用事务的`隔离级别`保证事务的隔离性，为了保证并发场景下的一致性，引入隔离性，不同事务之间互不影响
        #- `原子性`，用 undolog 保证事务执行失败后，直接回滚
        #- `持久性`，用 redolog 保证事务提交后，对数据的修改是永久性的，即使系统故障也不会丢失

        - 数据库事务隔离发展历史 # [数据库事务隔离发展历史 | CatKang的博客](https://catkang.github.io/2018/08/31/isolation-level.html)

        - 【MySQL对MVCC的实现】ReadView 和 undolog 的数据结构? MVCC = 版本链+ReadView(undolog) 通过版本链的 trx_id 和 ReadView 的高低水位比较后，决定使用哪个版本。ReadView 就是快照，用来做可见性判断。其实可以理解为粒度更细的“一锁二判三更新” # ReadView 的结构 (m_ids, min_trx_id 低水位, max_trx_id 高水位, creator_trx_id)。undolog 版本链的结构 (trx_id 事务 id, roll_pointer 回滚指针)。 # MVCC 就是*读请求直接访问对应版本的数据，从而避免读写事务和只读事务互相阻塞*，同一个数据有多个版本，*最大的好处是读写不冲突，只有同时写操作冲突，可以很大程度上提升性能*
        #  1、当版本链中的trx_id等于creator_trx_id时，说明这个版本是当前事务自己生成的，这个版本的数据对当前事务自然是可见的。
        #  2、但还有其他情况数据也是可见的：当版本链中的事务ID（trx_id）小于ReadView中的creator _trx_id时，说明该版本是在当前事务开始之前就已经提交的事务生成的，这个版本的数据对当前事务是可见的。
        #  3、只有当版本链中的trx_id大于creator _trx_id且小于等于low_limit_id（活跃事务ID列表中的最大值+1），或者trx_id大于low_limit_id时，这个版本的数据对当前事务是不可见的。

        - "***【事务的四种隔离级别是什么】RU、RC、RR、SR 分别是什么？RC有幻读，为了解决幻读所以用的RR，RR 和 RC （在锁、复制、一致性读、MVCC机制方面）的区别？各自会解决和导致什么问题?）?***" # RC 和 RR 的区别在于 ReadView 快照生成时机不同，导致可见性不同。RC 在每次读取数据前都生成一个 ReadView。RR 在第一次读取数据时生成一个 ReadView

        - "***【RR的实现机制】“隔离就是类似linux kernel的可见性，通过可见性来解决事务在并发请求（包括读写操作）时的数据一致性问题。比如什么RU、RC、RR、SR，随着隔离级别的加强，分别解决了脏写、脏读、不可重复读和幻读的问题。隔离级别越来越高，并发性就越差。” 既然上面说SR才能解决幻读问题，那RR又是怎么（通过间隙锁+MVCC）解决幻读问题的？（常见问题）***"
        #  - `RU`读操作不加锁，可能会`脏读`(解决了`脏写`问题)
        #  - `RC`只对记录加记录锁，而不会在记录之间加间隙锁，所以允许新的记录插入到被锁定记录的附近。所以在多次读操作时，会发生`不可重复读`(解决了`脏读`问题)
        #  - `RR`多次读取同一范围的数据会返回第一次查询的快照，不会返回不同的数据行，但是可能会发生`幻读`(解决了`不可重复读`问题)
        #  - `SR`InnoDB 隐式地将全部查询语句加上共享锁，解决了`幻读`问题，但是性能很差

        - 为什么 InnoDB 把 RR 作为默认的隔离级别呢? MySQL 是怎么实现 RR 的? # Lost Update 和 Constraint Violation 都是 Fuzzy Read 的变种

        - "***不同隔离级别，可能导致哪些问题? 脏读、不可重复读、幻读分别是什么?***"
        #- `脏读`，t1 在修改之前提交，t2 读取，t1 回滚，t2 读取了从未提交的数据，*读未提交时，读事务直接读取主记录，无论更新事务是否完成*
        #- `不可重复读`t1 读取，t2 修改该数据并提交，t1 重新读取，数据已被修改，数据不同
        #- `幻读`数据不同*每次都能读到最新数据*

        #  脏读，是指一个事务中访问到了另外一个事务未提交的数据。例如事务 T1 中修改的数据项在尚未提交的情况下被其他事务（T2）读取到，如果 T1 进行回滚操作，则 T2 刚刚读取到的数据实际并不存在。
        #  不可重复读，是指一个事务读取同一条记录 2 次，得到的结果不一致。例如事务 T1 第一次读取数据，接下来 T2 对其中的数据进行了更新或者删除，并且 Commit 成功。这时候 T1 再次读取这些数据，那么会得到 T2 修改后的数据，发现数据已经变更，这样 T1 在一个事务中的两次读取，返回的结果集会不一致。
        #  幻读，是指一个事务读取 2 次，得到的记录条数不一致。例如事务 T1 查询获得一个结果集，T2 插入新的数据，T2 Commit 成功后，T1 再次执行同样的查询，此时得到的结果集记录数不同。

        #- `脏读`一个事务可以读取另一个尚未提交事务的修改数据 (一个事务内修改了数据，另一个事务读取并使用了这个数据)
        #- `不可重复读`在同一个事务中，同一个查询在 T1 时间读取某一行，在 T2 时间重新读取这一行时候，这一行的数据已经发生修改，可能被更新了，也可能被删除了 (一个事务内连续读了两次数据，中间另一个事务修改了这个数据，导致第一个事务前后两次读的数据不一致)
        #- `幻读`在同一事务中，同一查询多次进行时候，由于其他 insert 的事务提交，导致每次返回不同的结果集 (一个事务内修改了涉及全表的数据，另一个事务往这个表里面插入了新的数据，第一个事务出现幻读)
        #- `更新丢失`一个事务内变更了数据，另一个事务修改了这个数据，最后前一个事务 commit 导致另一个事务的变更丢失

        # [再探MYSQL可重复度的“幻读”现象](https://mp.weixin.qq.com/s/yXaVPvio0SDkbJo7-K-upQ)
        # [转转-mysql奇怪的幻读问题_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1PP1WYtEBQ/)
        - 【幻读】


        - 并发事务之间的相互影响?
        - MySQL 遇到脏读怎么办?
        - "***【MVCC 机制的原理和实现】MVCC 是什么? ReadView 是什么? 高水位、低水位?***"

        - MySQL 的 RR 无法避免 PMP / G-Single 异常，Hermitage 将其实际等级定为 单调原子视图/MAV # [MySQL的正确性为何如此拉垮？ - 知乎](https://zhuanlan.zhihu.com/p/675251957)
        - MySQL 事务中的加锁和解锁时机？ # 对记录进行更新操作，或者`select for update`(X 锁)、`lock in share mode`(S 锁) 时，会对记录进行加锁，锁的种类很多，不赘述。在一个事务中，只有在`commit`或者`rollback`时，才会解锁。

        - “lost update 和 constraint violation 都是 fuzzy read 的变种” 这个理解是错的



    - topic: Index（索引原理、添加索引规范、explain）
      qs:
        - "***怎么理解“聚簇索引(clustered) 和非聚簇索引(non-clustered)的区别就是，叶子节点是不是存有行数据”，分别是什么?***" # 聚簇索引是一种物理存储方式，它决定了数据在磁盘上的物理排序顺序。聚簇索引的目的是将相关的行物理上存储在一起，以提高范围查询的性能。聚簇索引是以主键 ID 为节点生成的，非子节点都是 id，叶子节点（最下面一层）会相互连接变成双向链表，并且叶子节点会存储对应的行记录。而非聚簇索引的节点是基于索引列，找到了对应叶子节点的索引值后，会根据叶子节点存储的行记录们指针们，回到聚簇索引去查找行记录（这个过程也叫做回表）。

        - "***explain命令结果集里的extra (index, temporary, filesort)和type (const, range, index, ALL, ref, fulltext) 分别有哪几种?***" # [MySQL EXPLAIN结果集分析 - 附带大量案例](https://mp.weixin.qq.com/s/p5UKuh1yY3P4zrOzVBmY1w)

        - 索引覆盖 (Index Covering) 是啥？ # 索引覆盖就是直接查索引就可以获得数据，不需要再查数据表。
        - mysql的各种存储引擎分别使用了什么算法实现索引？ # InnoDB使用B+树，myisam使用R树，memory使用hash。这里感慨一下：这就是老许说的“本质是数据结构和算法”

        - 左选小写修（最左前缀索引、选择性、小字段、写操作频率、修改索引） # 最主要的索引创建原则其实就是最左前缀和选择性。除此之外就是一些tips，比如什么查询频率、写操作频率（更新非常频繁的字段不适合创建索引）、小字段（对于大的文本字段甚至超长字段，不要建索引）、反向开闭（用修改代替新增）之类的
        - 在什么情况下会使用最左前缀?
        - 最左前缀的本质是啥? # “最左前缀”实际上就是前缀索引在复合索引场景下的使用，也就是说，复合索引中field顺序也要按照“index创建原则”来排序。最左前缀的本质就是ICP，ICP只在满足"最左前缀"条件时起作用。如果查询条件中包含了索引的非最左前缀列，ICP将无法生效，MySQL会在存储引擎层面进行完整的行过滤，这可能会导致性能下降。总结来说，"最左前缀"原则和ICP的本质是基于索引列的前缀进行索引范围扫描，以减少需要访问的行数，提高查询性能。
        - Index选择性是啥? # 就是字段不重复的比例 `count(distinct col)/count(*)`（不重复的索引值（也称为基数 cardinality) 和数据表的记录总数的比值），区间为`(0,1]`，***识别度越高，扫描相同行数，需要的次数就越少，这是由 B+ 树的性质决定的***
        - 前缀索引、 ***尽量使用前缀来索引，如果索引字段的值很长，最好使用值的前缀来索引*** 例如，TEXT 和 BLOG 类型的字段，进行全文检索会很浪费时间。如果只检索字段的前面的若干个字符，这样可以提高检索速度

        - 聊聊MySQL8新增的隐藏索引、降序索引、函数索引这三种索引? # 降序索引和函数索引没啥意思。隐藏索引则非常实用，在没有隐藏索引之前，只能删除索引。如果发现删错了，又得添加索引，成本很高。mysql8之后，可以先将index设置为hiding，之后optimizer就不会再使用该index了。在上线一段时间后确定该index确实用不到再删除，就可以避免“重复创建index”的成本。

    - topic: mysql 建表常见问题
      qs:
        - "***阿里mysql建表规范 (mysql specs)***" # [阿里的MySQL建表规范，居然只有10条……](https://dbaplus.cn/news-11-6183-1.html)
        #  1、is类型字段（数据类型用 unsigned tinyint，而不是bool）
        #  2、字母和数字（下划线连接）
        #  3、表名禁止使用复数
        #  4、禁止使用关键字 (desc, group, range, match, delayed, ...)
        #  5、索引命名规范 (主键索引 pk_xxx, 唯一索引 uk_xxx, 普通索引 idx_xxx)
        #  6、定长，能用char就用char，用不了才用varchar。如果字符串长度大部分超过5000，使用text，独立出一张表单独存储（避免影响主表其他字段索引效率）。
        #  7、强制字段（id和created_at, updated_at）
        #  8、金额类型（小数类型使用decimal，禁止使用float和double。如果范围超过decimal，可以拆成整数与小数分开存储） # 这点存疑，因为decimal的成本太高了

        - 5种SQL语句：DML(manipulation)/DDL(definition)/DCL(Control)/DQL(Query)/TCL(trx control) 分别是什么？各自包括哪些命令?

        - "***为啥“mysql字段一定要 NOT NULL，(并且设置合理的 default 值)”? 怎么配置禁止NULL?***" # 数据库不应该默认 null（因为负向查询`id!=1`不能命中索引，导致全表扫描）。mysql设置为null会导致count(), distinct(), select() 操作数据丢失，并且导致索引失效。 # [弱智的 MySQL NULL | 董泽润的技术笔记](https://mytechshares.com/2022/04/06/week-mysql-null/) “除了上面使用的困惑，NULL 值过多会影响统计信息，可能影响执行计划。MySQL 很不负责的把对 NULL 值的统计方式交给了用户 innodb_stats_method, 默认值是 nulls_equal”，“unique索引有null值，导致主从复制延迟”

        # [MySQL 给数据表增加一列，一定会锁表吗？](https://dbaplus.cn/news-11-6341-1.html) mysql5.6引入DDL之后就不会锁表了，但是会有行锁定。mysql8引入了Atomic DDL操作，进一步减少行锁定时间和操作开销。
        - "***为什么大佬都喜欢建表的时候加个 ext 字段? 已经有大量数据的表怎么改字段? （为啥MySQL8之后添加字段就不会锁表了？）***" # [高级程序员为什么喜欢建表的时候加个 ext 字段？ - V2EX](https://www.v2ex.com/t/976972) 大表加字段，执行alter操作会直接锁表，所以冗余几个JSON类型的ext字段。当然，mysql8的online ddl优化了加字段的操作，直接操作metadata了，可以秒加字段。但是建议还是建表时冗余几个ext字段。这种字段会很好用，可以直接把整个json发给前端，让他们自己处理。也可以取其中某个字段。


    - topic: 《月经话题：从“到底用varchar还是int”，再谈RDB字段类型最佳实践》
      isX: true
      des: 诸如 “到底用 varchar 还是 int”、“到底用 int 还是 enum”、“”、“到底...” 等问问题，真就是月经问题。随便一个抛出来，都能水个几十楼，而且也无法达成共识，各有各的理，那么我们这里就整理一下诸如此类问题。
      tables:
        - name: dddd
          table:
            - feat: 可读性
              enum: ✅ enum 通过为每个状态值提供有意义的名称，极大地增强了代码的可读性。例如，在一个订单系统中，定义订单状态为 enum OrderStatus { PENDING, PROCESSING, COMPLETED, CANCELED } ，代码中使用 OrderStatus.PENDING 来表示待处理订单状态，从名称就能清晰知道其含义。
              int:  # 单纯从数字本身看，可读性较差。例如，用 0 表示订单待处理， 1 表示订单处理中，代码阅读者很难直接从这些数字理解其代表的业务含义。在大型项目中，随着状态值增多，数字与实际状态的对应关系可能变得模糊，需要频繁查阅文档或代码中的注释来确定含义，增加了理解成本。

            - feat: 类型安全性
              enum: 具有严格的类型检查，编译器能确保只使用 enum 中定义的值。例如，若变量声明为 OrderStatus 类型，就只能赋值为 OrderStatus 枚举中定义的 PENDING 、 PROCESSING等值，避免了意外赋值错误，增强了代码的健壮性。但是
              int:
            #- 优点：本身是基本数据类型，在类型系统中具有基础地位。但在表示状态时，编译器不会对其值进行特定状态的约束。
            #- 缺点：可以随意赋值任何整数值，可能导致程序运行时出现逻辑错误。例如，将订单状态变量赋值为一个未定义的整数值，编译器不会报错，只有在运行时根据业务逻辑判断才可能发现问题。

            - feat: 存储和性能
              enum: 优点：在大多数语言中， enum 底层通常也是用整数存储，与直接使用 int 相比，在存储占用上基本没有额外开销。并且由于其语义明确，在代码逻辑执行中，可提高逻辑的清晰度，一定程度上优化代码的可读性和可维护性，间接提升开发效率。
              int: 优点：作为基本数据类型，在存储和计算上都非常高效，无论是在内存中存储还是进行数值运算，都具有良好的性能表现。在数据库存储方面，直接存储 int 类型简单直接。缺点：如果在代码逻辑中需要对不同状态值进行复杂的语义判断，由于缺乏明确的语义，可能需要编写更多的逻辑代码来区分不同状态，影响开发效率。


            - feat: 扩展性
              enum: d
              #- 优点：添加新状态时，只需在 enum 定义中新增一个枚举值即可，代码中使用该 enum 的地方，语义依然清晰，不需要对其他代码进行大量修改。例如，在订单状态中添加 REFUNDED（退款）状态，只需在 enum OrderStatus 中添加 REFUNDED ，相关代码逻辑可以直接使用新状态值。
              #- 缺点：若项目中对 enum 有较多依赖，修改 enum 定义可能会导致一些依赖它的代码需要重新编译，但这通常是较为局部的影响。
              int: d
            #- 优点：理论上可以使用任何整数来表示新状态，无需对数据类型进行修改。
            #- 缺点：添加新状态时，需要在代码中多处判断状态值的地方添加新的判断逻辑，且如果没有统一的管理，可能导致代码中不同地方对新状态值的处理不一致。例如，在订单状态判断逻辑中，需要在所有涉及订单状态判断的if - else 语句中添加对新状态值的判断。


            - feat: 跨语言和数据交互
              enum: dd
              #- 优点：在同一编程语言的项目内， enum 能很好地定义和使用状态值。但在跨语言交互或数据传输时，不同语言对 enum 的支持和实现方式可能不同，可能需要进行额外的转换和适配。例如，将包含
              #  enum 类型的对象序列化为 JSON 进行网络传输，可能需要特殊处理，以确保接收方能够正确解析。
              #- 缺点：需要更多的工作来确保在不同语言环境下的兼容性和一致性。
              int: ddd
            #- 优点： int 作为基本数据类型，在不同编程语言和数据格式中都有广泛的支持。在跨语言交互和数据传输时，几乎不需要额外的转换工作，兼容性强。例如，将表示状态的整数通过 JSON
            #  传输给其他语言编写的服务，接收方可以直接理解和处理。
            #- 缺点：虽然兼容性好，但在接收方同样可能面临可读性差的问题，需要接收方自身有明确的状态值映射关系。
        - name: zzz
          table:
            - name: 状态类（82开）
              y: ✅bool
              z: int, enum
            - name: 时间类（55开）
              y: datetime
              z: ts
            - name: 金额类（91开）
              y: ✅int
              z: decimal
      qs:
        - 【基本认知】
        #  但是，在讨论此类问题之前，需要一些关于 mysql, pgsql 等 RDB 数据类型的基本认知。需要先就这些基本问题达成共识，才能聊应用层面的事情。否则就是对牛弹琴、南辕北辙了。需要对以下问题有明确认知：
        #  ```yaml
        #  - mysql和pgsql从、、、、各自有哪些数据类型？
        #
        #  - 为啥mysql里大家都普遍设置 varchar(255)?
        #
        #  - 11位手机号，存在varchar和int各自占用多少空间
        #  - int(1)和int(10)真的有区别吗？
        #  - 到底用int还是varchar查找更快？到底应该用哪个？
        #  ```
        #
        #  - [database - Is there a good reason I see VARCHAR(255) used so often (as opposed to another length)? - Stack Overflow](https://stackoverflow.com/questions/1217466/is-there-a-good-reason-i-see-varchar255-used-so-often-as-opposed-to-another-l) #
        #  1、索引长度问题，建立一个字段的普通索引最大长度为 768 个字节=255\*3=767+2。2、varchar 存储长度的字段问题。mysql 的 varchar 可以超过 255，之所以默认 255，是因为，varchar
        #  的第一个字节存储了字符串的长度，一个字节 8 位，可以表示 0~255，也就是说，如果你用 varchar(0, 255)，只要一个字节就能存储长度了。这是个分界线。
        #  - [资深开发竟然不清楚 int(1)和 int(10)的区别](https://juejin.cn/post/6992574502282477605) 按照这个帖子的意思，mysql 的 int(10)这些数字并不能限制字符长度，是这个意思吗？括号里的数字，只有设置了
        #  zerofill，才按照字符长度生效。但是需要只有 int 是这样的，可以看到 mysql8 之后 int, tinyint 已经不需要写数字了。
        #
        #  ---
        #
        #  好了，在对以上问题有了基本认知之后。接下来回到主题，把 RDB 数据类型问题分类讨论
        #
        #  其实“用 xxx 存 zzz 类型的”这类问题很多，但是有争议的就那几个。这里先列出来一些其实没啥争议的（甚至感觉是废话的）。但是这很重要，因为共识的达成并不容易，我们需要先列出一些大家能够达成共识的实践。
        #
        #  ```yaml
        #  - JSON数据 # JSON类型
        #  - 地理位置类数据 #
        #  - 固定长度类 # CHAR类型
        #  - 大文本 # TEXT类
        #  ```
        #
        #  那么除了上面这些
        #
        #  ```yaml
        #  - mysql 手机号码 长度 设置什么比较合适？有哪些需要考虑的因素？varchar(20) （手机号码都是数字吗？都是中国的手机号码吗？会按照手机号等值查询吗？会按照手机号范围查询吗？需要手机号列唯一约束吗？）
        #  - 身份证图片存数据库，应该用什么字段类型？
        #  ```


        - mysql 为什么 varchar 经常定义长度为 255？


        - 【主键字段类型的选择】uuid、自增 id、snowflake 作为主键，各自有什么优缺点？为啥推荐使用ulid之类单向自增的uuid作为主键方案? #
        # - *增大了磁盘 io*，因为 uuid 不是自增的，所以需要更多的索引页去查找合适的位置
        #- *插入耗时变长*，因为 uuid 不是自增的，导致以 B+ 树为基础的索引树会在插入时，索引节点分裂的概率更高
        #- *内存碎片变多*，索引节点分裂，会导致页变得稀疏，最终导致数据有碎片

        - 身份证图片存数据库，应该用什么字段类型？ # 文件存文件系统，数据库存文件系统 id。正反面分 2 条记录存，因为存在一条记录你不能保证用户上传正面就一定有反面，如果只有一面那条就是个脏数据。

        - 【RDB各种数据类型的使用场景】分而治之，我们需要先。可以看到，比较有争议的其实就这么几类：状态类、金额类、时间类。
        - "***【时间类】时间类型用什么字段类型？timestamp, datetime, int 有什么区别? （二者（时间精度、自动更新、默认值）都差不多，真正的问题在于 时区转换、时间范围）***" # int其他两者没啥可比性，最大的好处就是容易比较和排序，缺点就是没有可读性。datetime和ts都是不错的，二者五五开。
        #  下面再说说时间类
        #  首先，mysql 和 pgsql 的时间数据类型本身稍有区别。但是可以简单理解为“mysql 的 datetime 就是 pgsql 的 ts，mysql 的 ts 就是 pgsql 的 tstz”，也就是说 pgsql 是没有
        #  datetime 类型的。下面我们还是以 mysql 为准，对 pgsql 可以自行脑补。
        #  时间类的问题无非就是用 ts 还是 datetime 嘛
        #  这个结论其实也是烂大街的，无非是从几个 feat 进行比较，什么 时区转换、时间精度、自动更新、默认值、时间范围
        #
        #  ```yaml
        #  #  - 时区切换：ts的特性是“”，相应的性能也就不如datetime。
        #  #  - 精度：datetime和ts都支持微秒（ts默认精度为秒，但是mysql5.6之后也支持微秒）
        #  #  - 自动更新：这两个都支持设置 DEFAULT CURRENT_TIMESTAMP 和 ON UPDATE CURRENT_TIMESTAMP 来进行自动初始化以及自动更新。
        #  #  - 默认值：未指定默认值时，ts默认值为当前时间戳，datetime则为NULL
        #  #  - 存储范围和存储空间：datetime比ts的存储范围更广（datetime从1000-01-01到9999-12-31，而ts从1970-2038），相应的ts的存储空间也就更小（datetime占8个字节，而ts只占4个字节）
        #  ```
        #
        #  这是从 docs 里摘出来的，***可以看到，如果说状态类用 enum 还是 int 是二八开，那时间类用 datetime 还是 ts，真就是五五开了。真就是各有道理。***
        #  所以，真正的问题并不在于此
        #  真正的问题在于，***“如果我使用 datetime（可能需要更大的时间范围），那怎么做时区转换？有多大的性能开销和成本？”***
        #  结论：***datetime 和 ts 从时间精度、支持自动更新、性能上来说都没啥太大区别。真正做出选择的点，在于是否需要时区转换和时间范围。***


        - "***【状态类】状态用什么字段类型？用enum还是tinyint还是bool?（比如说如何描述性别数据）? 是不是应该无脑使用enum，为啥？在哪些情况下应该使用int?*** 具有类型安全性（编译时发现类型错误）、提高代码可读性和可维护性（语义清晰、易于修改扩展）、利于代码提示与文档化（IDE支持、代码即文档）、避免魔法字符串（消除硬编码字符串隐患）"
        #  结论：状态类用 bool、int 还是 enum
        #  如果只有两个状态，可以考虑用 bool。换句话说，字段名是否可以写成 is_xxx，如果可以就用 bool，如果不能就用其他两个。
        #  如果有多个状态，推荐使用 enum，而非 int。
        #  ***因为无论如何这个转换层都是需要的，无论用 enum 还是 int，都需要通过映射来转换成另一个。但是 enum 语义清晰、迁移性强、数据完整性，enum 本身还内置了数据合法性校验***
        #  。总的来说就是，从类型安全性（编译时发现类型错误）、提高代码可读性和可维护性（语义清晰、易于修改扩展）、利于代码提示与文档化（IDE 支持、代码即文档）、避免魔法字符串（消除硬编码字符串隐患），都应该使用
        #  enum 而非 int。
        #  但是如果，也可以考虑使用 int。因为。



        - "***【金额类】金额应该用什么字段类型？金额为什么不能用 float 存？ (int, float, decimal)***" # [探讨系统中钱的精度问题](https://mp.weixin.qq.com/s?__biz=MzkyMDAzNjQxMg==&mid=2247484440&idx=1&sn=ed2e6bc81a6b40bf8bd1c2d6cf31d0af) 如果精确到分用int（存分转元），需要更精确就用decimal（占用更多的存储空间，计算效率也不高）
        #  最后再说说金额类，这个应该是这三个里面相对最没有争议的了。
        #  一个基本共识：除非是银行或者金融机构之类对金额的精确度要求极高的场景需要使用 decimal 以外，
        #  ***默认使用 int 存分转元，再加上银行家舍入法基本上能够保证大部分业务的对账是没啥问题的。*** （当然是否使用银行家舍入（乃至于是否四舍五入），也有
        #  argue，比如说有些是直接抹零让利消费者，也有直接砍小数的（比如 5.9199，那就按照 5.91），当然这个是属于产品运营范畴的了，跟技术关系不大）
        #  但是其中也颇有一些有意思的问题可以聊聊

        - 【decimal和int的benchmark】我们都说“decimal占用更多存储空间，计算效率也不高”，那么到底占用多少存储空间？计算效率有多低？性能有多差？





    - topic: 常用mysql sql整理
      table:
        - name: 联表 (UNION/UNION ALL) 非连表
          是否笛卡尔积: false
          执行过程: 直接垂直堆叠结果集（根据是否ALL决定是否去重） # 注意联表只能合并结构相同的数据集

        - name: 内连 (INNER JOIN)
          是否笛卡尔积: true
          执行过程: 生成CP结果集 -> 用ON条件筛选匹配行

        - name: 外连 (OUTER JOIN (left/right join))
          是否笛卡尔积: true
          执行过程: 生成CP结果集 -> 筛选匹配行 -> 保留某侧所有行

        - name: 全连 (FULL JOIN)
          是否笛卡尔积: true
          执行过程: （类似外连接）生成CP结果集 -> 保留两侧所有行，并且未匹配行补NULL

        - name: 交叉连 (CROSS JOIN)
          是否笛卡尔积: true
          执行过程: 直接返回CP结果集（无过滤）
      record:
        - 【2025-06-13】【笛卡尔积CP】勘误一个误解，很多人以为只有inner join有笛卡尔积。但是实际上所有join都有笛卡尔积，只不过通过optimizer层（关键字：索引跳跃扫描、哈希连接、归并连接、向量化处理、BKA、BNL），在实际执行时进行优化，解决了其性能问题。
        #  索引跳跃扫描	通过B+树直接定位匹配行	跳过99%不匹配组合
        #  哈希连接	构建哈希表直接探测匹配	只计算实际匹配对
        #  归并连接	双指针滑动仅比较相邻行	避免全量组合
        #  向量化处理	SIMD指令批量匹配	并行化匹配过程

        - 【2025-06-13】【join性能】联表性能当然最好。除此之外，其实几种join的性能（除了交叉连接之外的内连、外连、全连），性能都相差不大。无论是否有索引，都是1000x+，因为两个10w行的表交叉之后就是 10w x 10w，内存直接炸了
      qs:
        - "***多表联查：有哪些连接查询？为啥最好用“子查询”而不是“联查”？***" # 内连接, 左/右连接, 全连接(有就是有，没有就写 Null，但是 MySQL 不支持，可以同时使用left join和right join), 交叉连接(笛卡尔积，交叉连接返回左表中的所有行，左表中的每一行与右表中的每一行组合，cross join) # 数据量小的时候，join 查询好用，大的时候，子查询好用。另外， join 查询最好不要超过 3 个表的关联，大公司一般是禁止用 join 的。

        - mysql不支持全连接（只能通过同时使用左连和右连实现）


        - 为什么 MySQL 不推荐使用 join？（1、性能 2、跨库join） # [为什么 MySQL 不推荐使用 join？](https://dbaplus.cn/news-11-6375-1.html)

        - 子查询 IN、EXISTS、ANY、ALL

        - "*是否要使用 in?*"
        - 在mysql中实现去重操作时，应该使用distinct还是group by? # https://dbaplus.cn/news-11-6205-1.html
        - 一个经典报错“Invalid use of group function”，怎么fix? 为啥会报这个? # [mysql-Invalid use of group function-聚合函数不能直接使用在where后面-使用exists，外查询与子查询应条件关联 - 依羽杉 - 博客园](https://www.cnblogs.com/shishibuwan/p/12589378.html)

        - select/insert/update for update 引发死锁 # `curd for update`都是当前读，需要给数据加锁，幻读就是针对当前读。完整语句是`select * from table where column = '' for update`

        - insert into select # 使用`insert into tableA select * from tableB`语句时，一定要确保 tableB 后面的 where，order 或者其他条件，都需要对应的索引，来避免出现 tableB 全部记录被锁定的情况

        - 为啥 count(*)、count(1) 和 count(column) 没有区别？在什么场景下有区别？ # [MySQL表太大，count(*)都能 502，怎么优化？](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247496626&idx=1&sn=2dd9b16a788f1c8bf7fc0124188d043b) count(1)和count(*)是完全相同的，使用相同的优化。至于，count(1) 的 1 可以理解为“伪列”，也可以理解为`1=1`这样的 true 的情况，只是一个占位符，这两种情况，在 mysql 优化器的角度出发是完全相同的。至于 count(column) 和 count(*) 的区别，则是 count(column) 的列如果是一个可空列，那么只会统计该列不为空的条数，所以可能会和总条数不同。

        - "*MySQL 分页排序时的数据重复问题？怎么避免该问题（而不是解决）？*" # [MySQL Paging Sorting with Data Duplication Problem (MySQL Priority Queue) - SoByte](https://www.sobyte.net/post/2021-09/mysql-paging-sorting-data-duplicates/) (mysql8 order by priority queue, heapsort) 问题原因：根本原因是*MySQL5.6 的优化器在遇到`order by x limit m,n`时使用`priority queue`进行优化，而`priority queue`使用堆排序这个不稳定的排序算法”。解决方法：直接原因是*分页重复数据是否出现与排序字段数据唯一性有关，与排序字段是否有序无关*。所以*如果需要把某个“重复字段”进行排序，只要在`order by`中结合某个“唯一字段”进行排序即可*，把原本不唯一的排序条件变成组合唯一的排序条件，因此可以解决分页数据重复的问题。 [Strange behavior in mySQL 8 with order by, limit and parentheses - Stack Overflow](https://stackoverflow.com/questions/59202878/strange-behavior-in-mysql-8-with-order-by-limit-and-parentheses) mysql8仍然存在这个问题

        - 用 show status 查看 sql 的执行效率，定位执行效率低的 SQL 语句，有哪些关键字段？

        - "***怎么避免“批量插入操作太多”（把两张钱包表倒数据的场景做成qs，写到那个批量插入qs里），报错 Prepared statement contains too many placeholders?***" # [别犯浑！这才是千万级数据全表 update 的正确姿势](https://dbaplus.cn/news-155-6297-1.html) 分批插入嘛，insert操作最大限制65535

        - 利用MySQL JSON特性优化千万级订单表
        - mysql8新增的Document Store是啥? json数据类型有哪些常用函数? # JSON_EXTRACT(), JSON_REMOVE(), JSON_SET(), JSON_CONTAINS_PATH()
        - "***为啥“不建议使用JSON类型，如果有JSON需求，应该直接使用mongo之类的DocumentDB”?***" # [再批 MySQL JSON | 董泽润的技术笔记](https://mytechshares.com/2022/04/06/mysql-is-shit-again/) “RDB的Table Schema 就是强一致的，约束开发不要乱搞，json 这种弱约束的就是开后门，时间一长 json 字段就成了下水道”，mysql json还有潜在的性能问题。

        - "~~为什么应该使用软删除，而不是硬删除？~~"

        - 【mysql sum性能问题】为什么？如何优化？


        # [不讲虚的！30个业务场景的实用SQL优化策略](https://dbaplus.cn/news-155-6362-1.html)
        - 1.慢查询

        - 2.连接查询性能问题

        - 3.子查询性能问题

        - 4.过度使用通配符%的 LIKE 查询

        - 5.大批量插入或更新

        - 6.频繁的重复查询

        - 7.过度使用分组和聚合函数

        - 8.大量重复数据的查询

        - 9.过度使用 OR 条件的查询

        - 10.大型数据分页查询

        - 11.使用不必要的列

        - 12.频繁更新的表

        - 13.未使用索引的外键约束

        - 14.大型查询的分批处理

        - 15.未使用存储过程的重复逻辑

        - 16.未使用合适的数据类型

        - 17.大量写操作导致的锁竞争

        - 18.频繁使用数据库函数和表达式

        - 19.未使用合适的索引策略

        - 20.大量数据的联合操作

        - 21.数据分布不均匀的索引

        - 22.过度使用子查询

        - 23.未使用批量操作

        - 24.过度使用内存表

        - 25.缺乏定期统计和优化

        - 26.未使用合适的数据库引擎

        - 27.使用强制类型转换

        - 28.未优化的长事务

        - 29.未优化的存储过程

        - 30.未考虑 DB 服务器配置和硬件资源

    - topic: MySQL 运维
      qs:
        - 怎么监控 MySQL 索引的使用率？
        - "*监控 MySQL 需要采集哪些指标？怎么监控 MySQL 的流量？*" #
        #- `主从复制`，比如复制线程运行状态、延迟时间
        #- `查询吞吐量`，比如 Questions 计数器
        #- `慢查询`
        #- `连接数`，比如最大连接数、当前连接数、活跃连接数、累计连接数
        #- `innoDB 缓冲池`，比如缓冲池的总页数、空闲页数、利用率等参数
        - mysql binlog的row, statement, mixed 三种模式各自的优缺点以及适用场景? 为啥mysql binlog默认模式从statement到mysql5.7的row，再到mysql8的mixed，这是基于什么考虑呢? # [MySQL :: MySQL 8.4 Reference Manual :: 7.4.4 The Binary Log](https://dev.mysql.com/doc/refman/8.4/en/binary-log.html) 总结：statement性能最好（相应的，数据一致性较差），而row则相反，mixed则是二者的折中方案。（如果数据一致性是首要考虑的因素，那么Row模式可能是更好的选择。如果性能和存储空间是主要考虑的因素，那么Statement模式可能更合适。Mixed模式则提供了一种折中的方案。）


        - "***mysql 深分页问题：mysql深分页很慢，怎么解决这个问题//怎么优化？***"
        - mysql exists 性能问题

    - topic: 【分布式DB】
      table:
        - 代际: 第一代：分片与主从复制
          时期: 2000年代初期
          核心特点: 手动分片, 主从架构, 弱一致性
          代表系统: Google Bigtable, MySQL分片方案
          局限性: 运维复杂需停机扩容, 无跨分片事务支持
          数据模型: 关系型/列式
          扩展方式: 垂直/水平分片
          一致性模型: 最终一致性

        - 代际: 第二代：NoSQL与自动扩展
          时期: 2010年代初期
          核心特点: 自动分片, 灵活数据模型, 最终一致性优先
          代表系统: Apache Cassandra, Amazon DynamoDB
          局限性: 弱事务支持, 复杂查询能力差
          数据模型: 文档/键值/宽列
          扩展方式: 自动分片
          一致性模型: AP优先（最终一致）

        - 代际: 第三代：NewSQL与分布式事务
          时期: 2010年代中期
          核心特点: 全局一致性, 分布式事务, HTAP雏形
          代表系统: Google Spanner, TiDB
          局限性: 强一致牺牲性能, 复杂事务吞吐限制
          数据模型: 关系型增强
          扩展方式: 共识算法(Raft/Paxos)
          一致性模型: CP优先（强一致）

        - 代际: 第四代：云原生与智能化
          时期: 2020年代至今
          核心特点: 存算分离, HTAP深度整合, AI自治运维
          代表系统: Snowflake, TiDB 7.0+, AWS Aurora
          技术趋势: Serverless架构, 多模融合, 边缘协同
          数据模型: 多模融合(文档/图/时序)
          扩展方式: 弹性扩缩容
          一致性模型: 灵活可配置
        #  分片方式：手动分片 → 自动分片 → 共识算法 → 弹性扩缩容
        #  事务能力：无跨片事务 → 单行事务 → 分布式事务 → HTAP混合负载
        #  架构演进：主从结构 → 去中心化 → 全球一致 → 云原生存算分离
      qs:
        - 为什么会产生分布式DB？
        - DB的演化



    - topic: temp
      qs:
        - DB 故障恢复机制的演化 # [数据库故障恢复机制的前世今生 | CatKang的博客](https://catkang.github.io/2019/01/16/crash-recovery.html)

        - OLTP (Online Transaction Processing，联机事务处理), OLAP (Online Analytical Processing，联机分析处理), HTAP (Hybrid Transactional and Analytical Processing) 分别针对不同的应用场景和需求，从应用场景、数据模型、查询类型、性能要求、数据量、数据更新频率、系统架构说说三者的区别 # OLTP 侧重于事务处理，强调快速响应和数据的一致性；OLAP 侧重于数据分析，强调数据的读取速度和多维分析能力；HTAP 尝试结合OLTP和OLAP的特点，支持在一个系统中同时进行事务处理和数据分析。

        - 湖仓一体是啥? 数据仓库、数据湖 # 数据仓库里的是结构化数据，但是全部数据（structured, semi-structured, non-structured）都可以忘数据湖里扔

        - MySQL 批量操作，一次插入多少行数据效率最高？ # [MySQL 批量操作，一次插入多少行数据效率最高？ - MySQL - dbaplus社群](https://dbaplus.cn/news-11-6309-1.html)

        - soar面试题：有一个关于mysql的场景，我在翻页的时候数据加载非常慢，有哪些可能存在的问题？

        - mysql 手机号码 长度 设置什么比较合适？有哪些需要考虑的因素？varchar(20) （手机号码都是数字吗？都是中国的手机号码吗？会按照手机号等值查询吗？会按照手机号范围查询吗？需要手机号列唯一约束吗？） # [mysql存储手机号为什么不用bigint?-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1888392) # [【Mysql】不要用int类型存储11位手机号 - 小阳睡不醒啊 - 博客园](https://www.cnblogs.com/swordpoems/p/17748078.html)
        - 到底用int还是varchar查找更快？到底应该用哪个？ # [有接入过 OAuth 的人应该都发现了， Google ID、Discord ID、Telegram ID 都是大整数，而且都和注册顺序没有直接关系（TGID 和注册顺序有一定关系，但不绝对）这些数据比大小没有意义，为什么不用字符串来存储？ - V2EX](https://v2ex.com/t/1051030) # [Sharding & IDs at Instagram. With more than 25 photos and 90 likes… | by Instagram Engineering | Instagram Engineering](https://instagram-engineering.com/sharding-ids-at-instagram-1cf5a71e5a5c)
        - 到底应该用多表联查还是子查询？各种说法都有
        - 单行子查询、多行子查询、相关子查询 # [MySQL——子查询用法](https://juejin.cn/post/7068447320261525512)

        - 启动mysql服务时，自动初始化数据库和表
        - mysql批量修改操作：三种都是组合update+where，1、在set里用逗号连接多个值。2、使用 set column=CASE WHEN condition1 THEN value1 WHEN condition2 THEN value2 ELSE ...


    - topic: 《MySQL 是怎样运行的：从根儿上理解 MySQL》
      qs:
        - 27、MySQL的server层和存储引擎层是如何交互的
        - 28、MySQL查询成本和扫描区间
        - 29、听说有一个最左原则
        - 31、专题式讲解 —— MySQL中NULL值引起的小锅
        - 32、MySQL使用索引执行IN子句
        - 33、MySQL的COUNT语句是怎么执行的
        - 40、Innodb到底是怎么加锁的
        - 41、语句加锁分析实例
        - 42、MySQL如何查看事务加锁情况
        - 43、专题式讲解 -MySQL介于普通读和锁定读的加锁方式 semi-consistent read
        - 44、两条一样的INSERT语句竟然引发了死锁？

    - topic: 《MySQL 45讲》
      qs:
        - 一条 SQL 查询语句是如何执行的？
        - 一条 SQL 更新语句是怎么执行的？
        - 事务隔离（为什么你改了我还看不见？）
        - 索引（上下）
        - 全局锁和表锁；（给表加个字段怎么有这么多阻碍？）
        - 行锁；（怎么减少行锁对性能的影响？）
        - 事务到底是隔离的，还是不隔离的？
        - 普通索引和唯一索引，应该怎么选择？
        - MySQL 为什么有时候会选错索引？
        - 怎么给字符串字段加索引？
        - 12、为什么我的MySQL会“抖”一下？

        - order by 是怎么工作的？
        - 如何正确地显示随机消息？
        - 为什么这些 SQL 语句逻辑相同，性能却差异巨大？
        - 为什么我只查一行的语句，也执行这么慢？
        - 幻读是什么？幻读有什么问题？
        - 为什么我只改了一行的语句，锁这么多？
        - MySQL 有哪些“饮鸩止渴”提高性能的方法？
        - MySQL 是怎么保证数据不丢的？
        - MySQL 是怎么保证主备一致的？
        - MySQL 是怎么保证高可用的？
        - 备库为什么会延迟好几个小时？
        - 主库出问题了，从库怎么办？
        - 读写分离有哪些坑？
        - 如何判断一个数据库是不是出问题了？

        - 为什么表数据删掉一半，表文件大小不变？
        - count(*) 这么慢，我该怎么办？
        - 误删数据之后，除了跑路，还能怎么办？
        - 为什么还有 kill 不掉的语句？
        - 我查了这么多数据，会不会把数据库内存打爆？
        - 到底可不可以使用 join？
        - join 语句优化？
        - 为什么临时表可以重名？
        - 什么时候会使用内部临时表？
        - 都说 innoDB 好，那还要不要使用 memory 引擎？
        - 自增主键为什么不是连续的？为什么 MySQL 的自增主键不单调也不连续？ # mysql8 之后通过 redolog 解决了自增主键不单调的问题，但是不连续的问题依然存在。*mysql8 对于自增计数器做了修改，每次计数器的变化都会写入到系统的 redolog，并在每个检查点存储在引擎私有的系统表中；当 mysql 服务器被重启后，可以从`持久化的检查点`和`redolog`中恢复出最新的自增计数器，避免出现不单调的主键*
        - insert 语句的锁为什么这么多？
        - 数据复制：怎么最快地复制一张表？
        - grant 之后要跟着 flush privileges 吗？
        - 要不要使用分区表？
        - 自增 ID 用完了怎么办？






    - topic: 【基本认知】分库分表
      table:
        - xxx: Sharding
          存储依赖: 可跨越DB、物理机器
          数据划分: 常见于时间、范围、面向服务等
          存储方式: 分布式
          拓展性: Scale Out
          可用性: 无单点
          价格: 低廉
          应用场景: 常用于Web2.0网站

        - xxx: 分区
          存储依赖: 可跨越表空间、不同的物理属性，不能跨越DB存储
          数据划分: 范围、hash、列表、混合分区等
          存储方式: 集中式
          拓展性: Scale Up
          可用性: 存在单点
          价格: 适中（DAS）甚至昂贵（SAN）
          应用场景: 多数传统应用
      qs:
        - “分库分表已经完全过时了” # [分库分表，可能真的要退出历史舞台了！](https://dbaplus.cn/news-131-5891-1.html)
        - "***MySQL 中间件有哪些核心需求？ 业务和路由算法完全解耦***"
        #  - 数据库虚拟化
        #  - 数据库对业务透明，业务不需要知道数据库的真实 IP、主从、读写、高可用等
        #  - 支持分库，且数据库的分库对业务透明
        - 请比较 Atlas, DBProxy, mysql-proxy, mysql-fabric, sharding-JDBC
        - 还是没懂mysql分区(Partitioning)和分表(Sharding)有啥区别

        - "***什么是分区？分区有哪些缺点，为什么大部分互联网公司都不使用分区，而更多的选择分库分表来进行水平拆分呢？请简述一下分区和分表的各自优势和缺点？使用分库分表的先后顺序？***" # 应该先分表，再分库*，因为分表后可以解决单表的压力，但是数据库本身的压力没有下降，我们需要分库，真正隔离来优化服务。分区就是，所有数据，逻辑上还在一个表中，但物理上，可以根据一定的规则放在不同的文件中。业务代码无需改动。不使用分区，主要是因为在高并发业务下，有很多问题*，主要是三点：如果 sql 不走分区键，很容易出现全表锁；分区表中使用关联查询，性能极差；使用分库分表，我们可以自己选择业务场景和访问模式，而分区则完全交给 MySQL，无法控制。
        - MySQL 有哪几种分表方法？MySQL 有哪几种路由规则？
        - MySQL 有哪些分表方法？时间（按时间分区，大部分只查询最近的订单数据，那么大部分访问都集中在一个分区，比一个表小多了，数据库也可以更好地加缓存，从而提高性能）

    - topic: mysql replication
      qs:
        - mysql replication 的完整过程? MSR过程中，主从服务器分别使用了哪些线程?
        #            - `写入`主机使用`dump 线程`，把`写操作`写入到 binlog 中
        #            - `复制`从机使用`IO 线程`，将主机的 binlog 复制到自己的`中继日志 relaylog`里
        #            - `重放`从机使用`sql 线程`重放 relaylog，实现主从同步

        - 使用 GTID 和 binlog 偏移量各自有啥优缺点? 有啥区别? # (GTID=server_uuid:tag:transaction_id) 其中`server_uuid`是 MySQL 本身提供的，该集群某台 MySQL 的唯一标识，而`transaction_id`则是事务 id，默认递增，保证在某个集群下 GTID 唯一, GTID(Auto-Positioning). binlog(position-based).
        - mysql 读写分离原理?
        - 怎么使用阿里云RDS自带的读写分离服务? # 开启读写分离服务后，所有的 `主实例`和`只读实例`都会有独立的连接地址，还会分配一个额外的`读写分离地址`，服务端数据库地址直接填写`读写分离地址`，RDS 会根据读写请求进行自动转发。读压力太大的情况下，直接水平拓展读库就可以了。存在的问题是*读库高可用，但是写库还是单点*
        - 什么是主从复制？主从复制解决了什么问题？
        - 主从复制的工作原理？主从复制过程中，主从服务器分别使用了哪些线程？
        - 什么是主从复制的复制过滤？
        - 主从复制过程中，主从服务器分别使用了哪些线程？
        - GTID 是什么？GTID 和 binlog 偏移量的区别？使用 GTID 有哪些好处？
        - 怎么使用 GTID 进行 MySQL 复制？怎么配置？

        - "***主从复制的延迟问题（Replication Lagging）的原因？如何优化（注意不是解决）？***" # (relaylog) (refer to the process of MSR) 主从复制的流程就三步，写入/复制/重放。写操作完成后会实时写入 binlog，没有优化点。所以优化点在于后面两步，复制和重放都可以通过多线程并行。所以核心

        - 有哪些使用读写分离后，主从复制延迟导致的坑？ # 写操作还没同步到读库，就直接去读库里读取相应数据，导致数据不一致
        - 怎么保证主库高可用？主主复制是啥？为啥要用主主复制？和主从复制有啥区别？ # 主主复制的目的就是为了冗余写库* 读写分离 (主从复制) 后无限水平拓展读库，只能保证读库的高可用，但是写库仍然是单点，所以，我们为了保证“写库”的高可用，可以设置两个主库，并且设置双向同步
        - 为了保证 主库高可用，主主复制 和 shadow-master 这两套方案，哪套比较好？ # 这两套方案的优缺点都很明显。双主互备可以水平拓展写库，提高性能，但是需要解决两台主库之间的读写延迟，以及读写冲突的问题。shadow-master 则正好相反（虽是双主，但只有一个主提供服务（读 + 写），另一个主是“shadow-master”，只用来保证高可用，平时不提供服务。master 挂了，shadow-master 顶上（vip 漂移，对业务层透明，不需要人工介入））。
        - 使用主主复制时 ID 冲突，怎么办？

    - topic: mysql分区/分表 (partition)
      url: https://dev.mysql.com/doc/refman/8.4/en/partitioning.html # [The Ultimate Guide to MySQL Partitions](https://www.percona.com/blog/what-is-mysql-partitioning/)
      qs:
        - 能否认为“List 是 Range 的延伸，Key 是 Hash 的延伸”？
        - Range（基于属于一个给定持续区间的列值，把多行分配给分区），优点是简单、容易拓展，缺点是负载不均衡，新表压力较大
        - List（类似于按 range 分区，区别是 list 分区是基于列值，匹配一个离散值集合中的某个值来进行选择），优点是负载均衡，缺点是迁移比较麻烦
        - Hash（基于用户定义的表达式的返回值来进行选择的分区）
        - Key（类似于 hash 分区，区别是在于 Key 分区只支持计算一列或者多列，且 MySQL 服务器提供其自身的哈希函数，必须有一列或者多列包含整数值）
        - 为啥使用range+hash的方案来防止数据倾斜?

    - topic: mysql分片/分库 (sharding)
      qs:
        - partition和sharding二者的本质区别（以及因此而产生的一些表面上的区别）?
        - 如何选择 sharding key? # [MySQL 存储海量数据的最后一招：分库分表 | MRCODE-BOOK](https://zq99299.github.io/note-book/back-end-storage/03/01.html#%E5%A6%82%E4%BD%95%E9%80%89%E6%8B%A9-sharding-key)

    - topic: MySQL分库分表：直接让`分库分表中间件`解决分表问题，业务和路由算法完全解耦，更灵活。其实这三点的目的都是类似的，都是为了降低大表问题，降低负载，是吗？只不过实现的方法不同，分库和分区是逻辑上拆分数据，分表则是物理上拆分数据。实际操作时通常是分表+分库。

    - topic: 分库分表带来的问题
      qs:
        - 怎么解决分库分表带来的读扩散问题? # 读扩散问题是指在进行分库分表后，某个查询操作需要同时查询多个分片（shard）或分表，导致查询性能下降的情况
        - mysql即使使用mycat或者vitess之类的中间件，分库分表仍然有哪些问题无法解决? （mysql分库或者分表之后，各自会在使用时产生哪些问题？）
        #- 事务一致性问题。
        #- 跨节点关联查询 join 问题。
        #- 跨节点分页，排序，函数问题。
        #- 全局主键避重问题。
        #- 数据迁移、扩容问题。
        - 相比于NewSQL呢? NewSQL是怎么解决以上问题的?
        - "***用“存算分离”真的能解决使用Vitness, mycat之类数据库中间件常见的lagging问题吗? 怎么解决的?***"




    - topic: 【技术选型】数据库迁移工具
      isFold: true
      table:
        - name: Spirit
          url: https://github.com/cashapp/spirit
          核心定位: MySQL 8 高性能在线 Schema 变更工具
          支持数据库: 仅 MySQL 8
          核心功能: 无锁在线 DDL 迁移; 兼容 gh-ost 工作模式
          性能特点: 优于 gh-ost（低延迟、高吞吐）; 专为 MySQL 8 优化
          技术栈: Go
          适用场景: MySQL 8 无锁大表变更（如 ALTER TABLE）
          局限性: 仅 MySQL 8; 不支持跨数据库迁移

        - name: Goose
          url: https://github.com/pressly/goose
          核心定位: 多数据库 Schema 迁移管理工具
          支持数据库: postgres, mysql, sqlite, mssql, redshift, tidb, clickhouse, vertica, ydb, duckdb
          核心功能: 基于 SQL/Go 的迁移脚本管理; 版本控制与回滚
          性能特点: 轻量级，依赖少; 无内置增量同步能力
          技术栈: Go
          适用场景: 多数据库环境的结构迁移（如版本迭代）
          局限性: 无数据增量同步能力; 需手动编写迁移脚本

        - name: golang-migrate
          url: https://github.com/golang-migrate/migrate
          核心定位: 通用数据库 Schema/数据迁移工具
          支持数据库: 30+ 种（含 RDB/NoSQL/OLAP）除了各种RDB还包括neo4j, mongodb, clickhouse等
          核心功能: Schema 与数据迁移; 版本控制与回滚; CLI 驱动
          性能特点: 高扩展性，支持分布式; 无内置增量同步
          技术栈: Go
          适用场景: 异构数据库迁移（如 SQL Server → MySQL）
          局限性: 无 GUI 界面（纯 CLI）; 复杂配置需求






# TODO 画个对比几种mysql HA方案的对比的table
#- url: https://github.com/XiaoMi/Gaea
#  des: MySQL HA. Just for ref.
#- url: https://github.com/MyCATApache/Mycat2
#  des: 之前用过MyCAT1.6，但是已经EOL了，MyCAT2目前也EOL了。MyCAT2的feat有支持prom监控、XA事务、内置HAProxy
#  rel:
#    - url: https://github.com/tarople/mysqlrep-cluster
#      des: harpoxy mycat mysql主从集群
#    - url: https://github.com/liuwel/docker-mycat
#      des: docker部署mycat
#  qs:
#    - 首先mycat1.6早已废弃，mycat2.0也没啥人用。但是作为
#    - mycat支持10种数据分片方法，不同分片方法有各自的使用场景
#    #- 枚举分片: sharding-by-intfile 适用于新闻地方站之类的场景，
#    #- 固定分片: rule 1
#    #- 范围约定: auto-sharding-long 适用于超大表分表
#    #- 取模分片: mod-long
#    #- 按天分片: sharding-by-date
#    #- 通配取模: sharding-by-pattern
#    #- ASCII取模: sharding-by-prefixpattern
#    #- 编程指定: sharding-by-substring
#    #- 字符串拆分hash解析: sharding-by-stringhash
#    #- 一致性hash: sharding-by-murmur
#    - 能否通过使用一张新闻表，对几种常用分片方法进行介绍
#    #- news_id(主键): 应该按照范围分片
#    #- news_title(新闻标题)
#    #- news_area(新闻所属地区): 枚举分片
#    #- news_date(新闻入库时间): 按天分片
#    - 按照范围分片的问题在于，如果数据量不够大，会产生浪费，比如我们规定每10000条数据划分一个node，如果我们的数据量只有不到10000条，都只在节点1处理; 节点2是空闲的。在10000到20000这个区间，节点2处理，节点1是空闲的。所以说范围分片的问题在于数据分配不均匀和资源浪费。
#
#    - 取模+范围结合的分片
#
#    - 经典使用场景：使用mycat中间件实现多节点订单表
#
#    - mycat配置说明：schema.xml, server.xml, rule.xml
#    #- schema.xml: 定义逻辑库和逻辑表，数据节点，数据主机等信息
#    #- server.xml: 配置mycat用户
#    #- rule.xml: 配置分片(分库分表)
#
#    #- `checkSQLschema`标识是否检查sql语法中的schema信息，通常设置为true
#    #    - true: mycat发送到数据库的sql为select * from table;
#    #    - false: mycat发送到数据库的sql为select * from TESTDB.table;
#    #- `sqlMaxLimit`限制每一条sql最多访问100条数据
#    #- `dataNode`指定数据节点
#
#    - master_log_pos
#    - 全局表：不经常修改的表，并且数据量较小的表，比如配置表，才会设置为全局表。对全局表修改时，会对所有节点进行同样对修改，保持同步。读操作时，会随机从一个节点读取。
#    - 自增字段的处理的三种方式（本地文件、数据库、本地时间戳）
#    #  为了保证自增字段的全局统一，mycat提供了sequence进行处理; `sequnceHandlerType` = 0/1/2，有以下三种方式:
#    #  - 0: 从`本地文件`中获取最新的自增字段(推荐，性能最好)优点是序列号由本地加载，获取速度快；缺点是，每一次Mycat重启，其序列的当前值都会回到最初配置的值，会导致主键（id）重复。
#    #  - 1: 从`数据库`中获取(性能不如基于文件配置的自增方案)优点是每一次Mycat重启，序列号的当前值都不会回到最初配置的值，弥补了本地文件方式的缺点；缺点是万一存序列相关信息的那张表所在的数据库挂了（MYCAT_SEQUENCE），这就涉及到单点故障问题了。
#    #  - 2: `本地时间戳`(最省事，但是不推荐，因为如果服务之间时间不同步，可能会有问题; 性能也不好;)优点是不存在以上两种方式的缺点；缺点是根据时间戳自动生成的主键数据类型长度过大，会造成存储浪费。
#
#    - 优化：haproxy+mycat+mysql的简单负载均衡
#    - Mycat是不是配置以后，就能完全解决分表分库和读写分离问题？
#    #  Mycat配合数据库本身的复制功能，可以解决读写分离的问题，但是针对分表分库的问题，不是完美的解决。或者说，至今为止，业界没有完美的解决方案。
#    #  分表分库写入能完美解决，但是，不能完美解决主要是联表查询的问题，Mycat支持两个表联表的查询，多余两个表的查询不支持。 其实，很多数据库中间件关于分表分库后查询的问题，都是需要自己实现的，而且节本都不支持联表查询，Mycat已经算做地非常先进了。
#    #  分表分库的后联表查询问题，大家通过合理数据库设计来避免。
#    - 常见问题：mycat服务在主库和从库间起到什么作用? mycat的数据库服务起到什么作用? 为什么会出现主库和从库数据不一致的情况? 从库数据不一致时，mycat数据库查出来的数据也不一致。每次重启mysql后都需要手动对齐二进制日志的pos; 很麻烦，怎么解决?
#    - mysql8之后，mycat无法连接mysql。简单来说就是mycat1.6本身是mysql5.x，使用mysql_native_password认证机制; 而mysql8使用caching_sha2_password机制; 所以两者直接无法连接。 # 具体问题参考 [mycat1.6.5连接后端mysql8.0.11数据库报连接失败 · Issue #1899 · MyCATApache/Mycat-Server](https://github.com/MyCATApache/Mycat-Server/issues/1899) 这里提供更好的解决方案，启动mysql服务时加上参数 `default-authentication-plugin=mysql_native_password`即可; 这样mycat就能连接mysql了;
#
#- url: https://github.com/mariadb-corporation/MaxScale
#  des: 这部分内容是废的，MHA或者Maxscale这些组件实际上主要提供故障转移，但是vitess本身就支持，所以不需要搭配使用。MaxScale是用来替代MHA（之前用 MHA+Consul）实现HA。由于 GTID 实现方式不同，Maxscale 最新版不支持 mysql 的故障自动转移，只支持读写分离功能。
#- url: https://github.com/apache/shardingsphere
#  des: shardingsphere提供了5种分片策略（standard、complex、hint、inline、none、cosid），不同的分片策略可以搭配使用不同的分片算法（共计12种）









# TODO [ShusenWang的个人空间-ShusenWang个人主页-哔哩哔哩视频](https://space.bilibili.com/1369507485/lists/761574?type=season)
#- type: GraphDB
#  tag: db
#  score: 5
#  using:
#    url: https://github.com/neo4j/neo4j
#  repo:
#    - url: https://github.com/apache/incubator-hugegraph
#      doc: https://hugegraph.apache.org/docs/
#      des: HugeGraph
#  topics:
#    - topic: 【技术选型】图数据库
#      table:
#        - name: Neo4j
#          技术组合: 原生图存储(属性图模型)+Cypher执行引擎+ACID事务+B树索引
#          lang: Java/Scala
#          查询语言: Cypher
#          适用场景: 社交网络/推荐系统/知识图谱
#          核心优势/缺点: |
#            - 优势: ACID强一致性，直观属性图模型
#            - 缺点: 超大图性能下降，集群配置复杂
#          索引结构: 邻接表+属性倒排索引
#          数据模型: 属性图（节点-关系-属性）
#          事务ACID支持: 完整ACID
#          锁机制: 节点/关系级锁
#          查询性能: 中小图毫秒级响应
#          写入性能: 单机万级TPS
#          压缩效率: 中等
#          持久化机制: 预写日志+检查点
#
#        - name: Nebula Graph
#          url: https://github.com/vesoft-inc/nebula
#          技术组合: 分布式存储(RocksDB)+nGQL查询引擎+图分区(哈希/范围)
#          lang: C++
#          查询语言: nGQL
#          适用场景: 超大规模图/实时推荐
#          核心优势/缺点: |
#            - 优势: 万亿边级扩展性，高并发读写
#            - 缺点: 小数据性能平庸
#          索引结构: LSMTree+图分区索引
#          数据模型: 有向属性图
#          事务ACID支持: 部分ACID
#          锁机制: 分区级锁
#          查询性能: 分布式并行计算
#          写入性能: 亿级/分钟批量导入
#          压缩效率: 高
#          持久化机制: WAL+SSTable
#
#        - name: JanusGraph
#          url: https://github.com/JanusGraph/janusgraph
#          技术组合: 分布式存储(Cassandra/HBase/ES)+Gremlin查询引擎+OLAP图计算
#          lang: Java
#          查询语言: Gremlin
#          适用场景: 分布式图计算/IoT
#          核心优势/缺点: |
#            - 优势: PB级数据支持，多存储后端
#            - 缺点: 运维复杂
#          索引结构: 邻接表+ES二级索引
#          数据模型: 属性图
#          事务ACID支持: 最终一致性
#          锁机制: 乐观锁(MVCC)
#          查询性能: 依赖后端存储
#          写入性能: 批量导入需调优
#          压缩效率: 依赖存储后端
#          持久化机制: 依赖Cassandra/HBase
#
#        - name: HugeGraph
#          技术组合: TinkerPop框架(Gremlin)+多存储后端(RocksDB/Cassandra)+图算法库
#          lang: Java
#          查询语言: Gremlin
#          适用场景: 安全领域/反欺诈
#          核心优势/缺点: |
#            - 优势: 读写均衡
#            - 缺点: 查询性能低于Nebula
#          索引结构: 多级索引(顶点/边/属性)
#          数据模型: 属性图
#          事务ACID支持: 有限事务
#          锁机制: 顶点级锁
#          查询性能: 多跳查询优化
#          写入性能: 批量写入优化
#          压缩效率: 中等
#          持久化机制: RocksDB/Cassandra
#
#        - name: SpiceDB
#          url: https://github.com/authzed/spicedb
#          技术组合: Zed权限模型+分布式存储(CockroachDB)+关系遍历引擎
#          lang: Go
#          查询语言: gRPC/HTTP-JSON API
#          适用场景: 细粒度权限管理
#          核心优势/缺点: |
#            - 优势: 权限检查微秒级
#            - 缺点: 非通用图计算
#          索引结构: 关系-对象反向索引
#          数据模型: 资源关系图
#          事务ACID支持: 强一致性
#          锁机制: 行级锁
#          查询性能: 权限检查微秒级
#          写入性能: 需全局校验
#          压缩效率: 高
#          持久化机制: 分布式KV存储
#
#        - name: SurrealDB
#          url: https://github.com/surrealdb/surrealdb
#          技术组合: 混合存储(文档+图)+SurQL语言+实时同步引擎+分布式事务
#          lang: Rust
#          查询语言: SurQL(SQL/GraphQL混合)
#          适用场景: 实时Web/无服务器架构
#          核心优势/缺点: |
#            - 优势: 实时流处理，边缘到云部署
#            - 缺点: 图计算能力弱
#          索引结构: 自适应B+树索引
#          数据模型: 文档+图混合
#          事务ACID支持: 跨模型ACID
#          锁机制: 文档级锁
#          查询性能: 简单查询快，复杂遍历中等
#          写入性能: 高并发写入
#          压缩效率: 高
#          持久化机制: 多引擎支持
#      qs:
#        - 图数据库有哪些常见的应用场景?
#    #- 比如百万级用户量的社交业务，平均每个用户有 30 个好友，现在需要查询某个用户的 N 度人脉 (查询 3 度以上关系比传统的关系型数据库或者 kvdb 都要快太多)
#    #- 电商业务里查询购买了某个商品的用户，还购买了哪些商品
#    #- 多级分销业务里，需要统计某个人的 N 级邀请关系




#- Type//DS: Graph DB（邻接表）
#  元问题: 如何高效存储和遍历**节点间复杂关系网络**并支持实时推理？
#  延伸逻辑: 邻接表存储 → 图遍历算法优化 → 分布式图分片 → 索引下推
#  解决方案: 物理邻接列表实现O(1)关系跳转
#  代价: 非图模式查询性能 / 存储冗余 / 水平扩展复杂度
#  使用场景: 社交网络、欺诈检测
#  SE: Neo4j Storage Engine
#  DB: neo4j
#  SE-feat: 原生图存储 + 双向邻接表
#  索引结构: 属性索引（Lucene）
#  数据组织方式: 节点/关系物理邻接存储
#  事务支持: ACID
#  锁粒度: 节点级锁
#  写入性能: 中等（关系维护开销）
#  读取性能: 极高（直接指针跳转）
#  压缩效率: 无压缩
#
#- SE: Nebula Storage Engine
#  DB: nebula graph
#  SE-feat: 分布式邻接表 + 属性索引
#  索引结构: Raft协议分片
#  数据组织方式: 分布式邻接表
#  事务支持: 最终一致性
#  锁粒度: 分片级锁
#  写入性能: 高（并行写入）
#  读取性能: 中等（跨分区查询）
#  压缩效率: 列式压缩






#- Type//DS: Vector DB（HNSW）
#  元问题: 如何在高维空间中实现**低延迟的向量相似性搜索**并解决"维度灾难"？
#  延伸逻辑: 近似最近邻算法 → 量化降维 → 分布式索引 → GPU加速计算
#  解决方案: 分层可导航小世界图实现高效近邻搜索
#  代价: 精确性（近似结果） / 事务支持 / 高内存消耗
#  使用场景: 图像检索、推荐系统
#  SE: FAISS-Based Engine
#  DB: milvus
#  SE-feat: HNSW + 量化索引
#  索引结构: HNSW图
#  数据组织方式: 向量分片 + 量化编码
#  事务支持: 无
#  锁粒度: 分片级锁
#  写入性能: 中等（图结构维护）
#  读取性能: 极高（近邻跳转）
#  压缩效率: 乘积量化压缩
#
#- SE: Pinecone Engine
#  DB: pinecone
#  SE-feat: 专有向量索引算法
#  索引结构: 混合树状结构
#  数据组织方式: 内存分层索引 + 磁盘持久化
#  事务支持: 无
#  锁粒度: 无锁读取
#  写入性能: 高（异步更新）
#  读取性能: 稳定低延迟
#  压缩效率: 标量量化

#- type: VectorDB
#  tag: db
#  score: 5
#  using:
#    url: https://github.com/milvus-io/milvus
#    des: 以图搜图
#    rel:
#      - url: https://github.com/zilliztech/attu
#        des: 【milvus的web管理工具】目前IDEA的DB仍不支持VectorDB，所以需要这个
#  topics:
#    - topic: VectorDB 基本认知
#      table:
#        - name: Milvus
#          url: https://github.com/milvus-io/milvus
#          doc: https://milvus.io/docs
#          技术组合: 分布式架构(计算/存储分离)+微服务架构+FAISS/HNSW集成
#          lang: Go/C++
#          查询语言: Python/Java/Go SDK
#          适用场景: 工业级AI应用、大规模向量数据管理
#          核心优势/缺点: |
#            - 优势: 支持十亿级向量检索，云原生架构
#            - 缺点: 运维复杂度高
#          索引结构: IVF_FLAT/IVF_PQ/HNSW/SCANN
#          事务ACID支持: 部分支持(最终一致性)
#          锁机制（锁粒度）: 分区级锁
#          查询性能/机制: 毫秒级ANN搜索
#          写入性能/机制: 批量导入+流式写入
#          压缩效率: 中等(PQ量化压缩)
#          持久化机制: etcd元数据存储+MinIO/S3对象存储
#
#        - name: FAISS
#          url: https://github.com/facebookresearch/faiss
#          doc: https://faiss.ai
#          技术组合: 单机优化库+多索引结构(IVF/HNSW/LSH)
#          lang: C++/Python
#          查询语言: Python/C++ API
#          适用场景: 高性能计算、学术研究
#          核心优势/缺点: |
#            - 优势: 极致CPU/GPU优化，毫秒级检索
#            - 缺点: 不支持分布式
#          索引结构: IVF/HNSW/LSH/PQ
#          事务ACID支持: 无
#          锁机制（锁粒度）: 进程级锁
#          查询性能/机制: 暴力搜索/近似最近邻
#          写入性能/机制: 批量加载
#          压缩效率: 高(乘积量化)
#          持久化机制: 内存存储+磁盘快照
#
#        - name: Weaviate
#          url: https://github.com/weaviate/weaviate
#          技术组合: 云原生分布式+RAFT共识+图神经网络
#          lang: Go
#          查询语言: GraphQL
#          适用场景: 语义搜索、知识图谱构建
#          核心优势/缺点: |
#            - 优势: 多模态支持，实时混合搜索
#            - 缺点: 集群版闭源
#          索引结构: HNSW+倒排索引
#          事务ACID支持: 最终一致
#          锁机制（锁粒度）: 对象级锁
#          查询性能/机制: 混合搜索(向量+标量)
#          写入性能/机制: 实时更新
#          压缩效率: 中等
#          持久化机制: WAL+分布式存储
#
#        - name: Qdrant
#          url: https://github.com/qdrant/qdrant
#          doc: https://qdrant.tech/documentation
#          技术组合: Rust分布式架构+HNSW算法
#          lang: Rust
#          查询语言: REST/gRPC
#          适用场景: 实时向量搜索、推荐系统
#          核心优势/缺点: |
#            - 优势: 实时更新，可调一致性
#            - 缺点: 生态较新
#          索引结构: HNSW
#          事务ACID支持: 可配置
#          锁机制（锁粒度）: 分片级锁
#          查询性能/机制: 低延迟ANN搜索
#          写入性能/机制: 实时写入
#          压缩效率: 高(标量过滤优化)
#          持久化机制: 内存映射+持久化存储
#
#        - name: Chroma
#          url: https://github.com/chroma-core/chroma
#          doc: https://docs.trychroma.com
#          技术组合: Python可嵌入架构+HNSW/IVF
#          lang: Python
#          查询语言: Python/JavaScript SDK
#          适用场景: 推荐系统、NLP任务
#          核心优势/缺点: |
#            - 优势: 轻量易集成，支持元数据过滤
#            - 缺点: 功能有限
#          索引结构: HNSW/IVF/PQ
#          事务ACID支持: 无
#          锁机制（锁粒度）: 集合级锁
#          查询性能/机制: 实时ANN检索
#          写入性能/机制: 动态索引更新
#          压缩效率: 中等
#          持久化机制: SQLite/ClickHouse
#
#        - name: fast-search
#          url: https://github.com/farouqzaib/fast-search
#          技术组合: Go分布式架构+Raft协议
#          lang: Go
#          查询语言: REST API
#          适用场景: 全文+语义混合搜索
#          核心优势/缺点: |
#            - 优势: 支持多模态检索，容错设计
#            - 缺点: 生态不成熟
#          索引结构: HNSW+倒排索引
#          事务ACID支持: 最终一致
#          锁机制（锁粒度）: 分片级锁
#          查询性能/机制: 混合搜索(全文+向量)
#          写入性能/机制: 实时索引更新
#          压缩效率: 中等
#          持久化机制: WAL+分布式存储
#      qs:
#        - 怎么理解“纯VDB和Vector插件+RDB，都无法满足RAG的需求”？
#        - 两种专用索引结构（HNSW, IVF）分别是啥？
