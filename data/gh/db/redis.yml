---



- type: redis
  tag: db
  score: 5
  using:
    url: https://github.com/redis/redis
    doc: https://redis.io/docs/latest/
  repo:
    - url: https://github.com/orca-zhang/ecache
      score: 5
      des: 【Local-Cache】
      topics:
        - topic: 【技术选型】
          table:
            - name: ecache
              url: https://github.com/orca-zhang/ecache
              高并发支持: true
              核心数据结构: 环形缓冲区+哈希索引
              容量: 固定容量限制
              ttl: 键级别ttl
              统计信息: true
              独立部署: false
              内存优化: 零GC、零内存分配

            - name: bigcache
              url: https://github.com/allegro/bigcache
              高并发支持: true
              核心数据结构: 分片哈希表 + 字节数组
              容量: 无限制
              ttl: 统一ttl
              统计信息: true
              独立部署: true
              内存优化: 连续内存分配，消除指针

            - name: go-cache
              url: https://github.com/patrickmn/go-cache
              高并发支持: true
              核心数据结构: 嵌套map (map[string]Item)
              容量: 无限制
              ttl: 键级别ttl
              统计信息: false
              独立部署: false
              内存优化: 值直接存储非指针结构

            - name: groupcache
              url: https://github.com/golang/groupcache
              高并发支持: false
              核心数据结构: 哈希表 + 双向LRU链表
              容量: LRU大小限制
              ttl: false
              统计信息: true
              独立部署: true
              内存优化: 链表节点整块分配

            - name: golang-lru
              url: https://github.com/hashicorp/golang-lru
              高并发支持: false
              核心数据结构: 哈希表 + 双向LRU链表
              容量: 键大小限制(LRU)
              ttl: false
              统计信息: false
              独立部署: false
              内存优化: 值直接存储在链表节点

            - name: Ristretto
              url: https://github.com/dgraph-io/ristretto
              高并发支持: true
              核心数据结构: 分片存储池 + TinyLFU计数器
              容量: 可设置上限
              ttl: 键级别ttl
              统计信息: true
              独立部署: false
              内存优化: 无锁并发 + 频率矩阵

            - name: gocache
              url: https://github.com/eko/gocache
              高并发支持: true
              核心数据结构: 多后端适配器
              容量: 取决于后端
              ttl: 键级别ttl
              统计信息: true
              独立部署: true
              内存优化: 分层设计解耦存储

            - name: Google Guava
              url: https://github.com/google/guava
              高并发支持: true
              核心数据结构: 基于引用的缓存结构
              容量: 可设置上限
              ttl: 键级别ttl
              统计信息: true
              独立部署: false
              内存优化: Weak/Soft引用管理

            - name: Caffeine
              url: https://github.com/ben-manes/caffeine
              高并发支持: true
              核心数据结构: 分层轮转时钟哈希表
              容量: 可设置上限
              ttl: 键级别ttl
              统计信息: true
              独立部署: false
              内存优化: W-TinyLFU淘汰算法
          qs:
            - "***cache指标：Compare bigcache, cachego, freecache, gcache, gocache, groupcache, lrucache.*** QPS和hit-ratio"
            - 【结论】需要极高吞吐选bigcache；内存敏感场景用freecache；分布式环境考虑groupcache；简单需求直接lrucache。


        - topic: "cache和kvdb有啥区别?"
          qs:
            - cache和kvdb之间的区别可以简单理解为“kvdb的基本需求是持久化，所以是基于硬盘的。而cache则都是基于内存的”，也就是“是否支持持久化”，但是现在cache基本上也都支持持久化，所以也就没什么区分了。cache的持久化基本上也是基于kvdb实现的。

            - 由于Redis本身主要用作内存缓存，不适合做持久化存储，因此目前有如SSDB、ARDB等，还有如京东的JIMDB，它们都支持Redis协议，可以支持Redis客户端直接访问；而这些持久化存储大多数使用了如LevelDB、RocksDB、LMDB持久化引擎来实现数据的持久化存储。



    - des: golang中怎么使用redis实现互斥锁?
      url: https://github.com/go-redsync/redsync
      record:
        - 【2025-06-26】移除【bsm/redislock】。如果是redis集群的话，redsync是唯一解。redislock只能在单机下保证不丢锁，因为其本质上仍是单节点锁。

    - des: 怎么在k8s中部署redis集群?
      url: https://github.com/spotahome/redis-operator # 用来在k8s上部署redis的CRD，可以用来自动化部署redis集群，并支持redis HA

    - des: redis数据迁移工具，用来在不同的redis实例之间迁移、同步、备份数据?
      url: https://github.com/tair-opensource/RedisShake

    - url: https://github.com/HDT3213/delayqueue # (zset+lua) 使用 zset，拿时间戳作为 score，消息内容作为 key 调用 zadd 来生产消息，消费者用 zrangebyscore 指令获取 N 秒之前的数据轮询进行处理。
      des: How to use redis to implement delay-queue?


    - url: https://github.com/redis/go-redis
      doc: https://redis.uptrace.dev/guide/
      score: 5
      sub:
        - url: https://github.com/KromDaniel/rejonson
          des: support store and operates JSON data in redis. provide func like JsonGet(), JsonSet(), JsonDel()
      qs:
        - 【pipeline使用】怎么用 golang 的 go-redis 操作pipeline插入大量数据？怎么查询数据？自动提交和手动提交 # rdb.Pipelined
        - go-redis 怎么开启对Cluster中Slave Node的访问?
        - go-redis 在集群模式下使用pipeline功能?

    - url: https://github.com/RediSearch/RediSearch
      doc: https://redis.io/docs/latest/develop/interact/search-and-query/
      des: 【Redis Modules】怎么通过redis module让redis具有FTS功能? # Redis Modules, Used to support FTS. 如果没有用ES，又把redis作为数据源的话，可以用RediSearch代替ES。RediSearch还支持secondary index, 比如说如果redis中存了大量文章，就可以把文章的标题和内容作为索引的字段，执行搜索操作时，就可以快速定位到包含关键字的文章，而不需要遍历整个数据集。

    - url: https://github.com/RedisBloom/RedisBloom
      des: 【Redis Modules】including Bloom filter, Cuckoo filter, Count-min sketch, Top-K, and t-digest.
  record:
    - 【2025-06-24】移除【DiceDB】、【slatedb（专为云原生场景设计的高性能嵌入式存储引擎，充分利用对象存储的特性，提供高扩展性和稳定性。它以极简的架构、高效的数据访问和强大的数据一致性保障，成为现代分布式系统和云原生应用的理想存储方案。）】
    - 【2025-06-26】移除【rueidis】，golang实现的，所以只能作为golang的redis client使用，跟go-redis一个生态位。但是二者之间区别很明显，Rueidis采用「单连接多路复用 + 无锁环形缓冲」的架构，以及「零拷贝 + 内存池复用」的内存管理，而go-redis则是「连接池 + 阻塞式 I/O」的架构。但是相应的，Rueidis 的开发体验不如 go-redis，可以理解为Rueidis 为性能牺牲了部分开发便利性。比如说，1、Rueidis要求开发者预先知道返回类型（字符串/整数/数组），选错解析方法直接报错。2、Rueidis 没有内置 JSON 工具链，需开发者手动处理序列化。
    - 移除【redisraft】
  topics:
    - topic: 【技术选型】分布式缓存
      picDir: db/redis
      table:
        - name: Redis
          url: https://redis.io
          API兼容性: Redis协议
          性能特点: 高吞吐，单线程架构
          内存效率: 中等
          特殊功能: 丰富数据结构，持久化选项
          适用场景: 多功能缓存/数据库，需要复杂数据结构的场景

        - name: Valkey # FOSS redis
          url: https://github.com/valkey-io/valkey
          API兼容性: Redis协议
          性能特点: 高吞吐量，支持TLS/RDMA
          内存效率: 与Redis相当
          特殊功能: 支持集群模式，内置TLS安全
          适用场景: Redis替代方案，需要保持协议兼容的场景

        - name: Memcached
          url: https://github.com/memcached/memcached
          API兼容性: Memcached协议
          内存管理: 内存分配算法slab allocation
          线程模型: 多线程（主线程监听 + Worker线程执行） # 主线程监听，worker子线程接受请求，执行读写（这个过程中可能存在锁冲突)
          网络模型: 使用Libevent实现IO复用
          高可用: 本身不支持，可以使用magnent实现
          性能特点: 简单高效，专注缓存场景
          内存效率: 中等
          特殊功能: 多线程架构，LRU淘汰
          适用场景: 纯KV缓存需求，简单读写场景

        - name: Dragonfly # 3个feat：与redis和memcache的API完全兼容，无感切换；相比memcache更高的性能；相比redis更高的内存利用率
          url: https://github.com/dragonflydb/dragonfly
          API兼容性: Redis+Memcached双协议
          性能特点: 超高吞吐(25倍于Redis)，低延迟
          内存效率: 比Redis高30%
          特殊功能: 自动快照，Prometheus监控
          适用场景: 高性能缓存，替代Redis/Memcached

        - name: Tair # Redis模块 # 天然兼容redis，通过模块化DT（比如zset, list, hash之类的）拓展其功能 # 直接使用这个模块，就可以实现多条件排序（多维排序） # 直接使用这个模块，就可以实现多条件排序（多维排序） # [让 Redis zset 支持多条件排序一些需求中经常要我们实现一个排行榜，数据量少的话可以使用 RDB 数据库排序，数 - 掘金](https://juejin.cn/post/6844903977234989063) 如果不用TairZset，也可以这么操作
          url: https://github.com/tair-opensource/TairZset
          API兼容性: Redis协议扩展
          性能特点: 依赖Redis基础性能
          内存效率: 依赖Redis基础
          特殊功能: 多维排序，多条件查询
          适用场景: Redis扩展，需要复杂排序的场景
      qs:
        - "***【cache和storage的关系】怎么理解“需要cache，归根到底还是因为storage和业务场景本身不匹配造成的”***" # 我的认知：二者的核心区别还是在于是否落盘，不落盘，就不存在 storage问题，也就不存在查找问题，更不存在LSM、B+Tree之类存储模型的问题。老许的这句话指的是，缓存的本质是“适配器”——当现有存储系统（如数据库）的特性（延迟、并发、一致性、生命周期）无法满足业务场景的需求时，通过缓存来填补两者的鸿沟。


        - 缓存（Cache）和存储（Storage）是什么关系？它也是一种存储中间件么？ # “既是也不是。首先，缓存和一般的存储中间件一样，也在维持着业务状态。从这个角度看，缓存的确是一类存储。但是，缓存允许数据发生丢失，所以缓存通常是单副本的。一个内存缓存的集群挂了一个实例，或者一个外存缓存的集群坏了一块硬盘，单就缓存集群本身而言，就出现数据丢失。”
        # 回到前面的问题，缓存（Cache）和存储（Storage）到底是什么关系？
        #
        #
        #  为什么说它是补丁？
        #
        #  因为如果存储本身非常匹配业务场景的话，它不应该需要缓存在它前面挡一道，内部自己就有缓存。至于把一个复杂的 F(x) 缓存起来，更根本的原因还是存储和业务场景不那么直接匹配所致。
        #
        #  但是实现一个存储很难，所以存储的业务场景匹配性很难做到处处都很好。
        #
        #  出现事务（Transaction），是为了改善存储的业务场景“写操作”的匹配性，把一个复杂操作包装成一个原子操作。
        #
        #  出现缓存（Cache），则是为了改善存储的业务场景“读操作”的匹配性，提升高频读操作的效率。
        #
        #  所以我们说，缓存是一个存储的补丁。
        #
        #  那么为什么我们说这是一个不太完美的补丁呢？
        #
        #  因为上面的 FastF(x) 并没有被包装成一个原子的读操作。从严谨的角度来说，这段代码逻辑是有问题的，它会破坏数据的一致性。
        #
        #  对于一个确定的 x 值，如果 F(x) 永远不变，这就没问题。但如果 F(x) 值会发生变化，会有多个版本的值，那就有可能会出现并发的两个 F(x) 请求得到的结果不同，从而导致缓存中的值和存储中的值不一致。
        #
        #  这种情况后果有可能会比较严重。尤其是如果我们有一些业务逻辑是基于 FastF(x) 得到的值，就有可能会出现逻辑错乱。
        - "***设计缓存架构时需要考量哪些因素（读写方式、KV size、key 的数量、读写峰值、命中率、平均缓存穿透加载时间、缓存可运维性、缓存安全性）***" # [03 设计缓存架构时需要考量哪些因素？](https://learn.lianglianglee.com/%e4%b8%93%e6%a0%8f/300%e5%88%86%e9%92%9f%e5%90%83%e9%80%8f%e5%88%86%e5%b8%83%e5%bc%8f%e7%bc%93%e5%ad%98-%e5%ae%8c/03%20%e8%ae%be%e8%ae%a1%e7%bc%93%e5%ad%98%e6%9e%b6%e6%9e%84%e6%97%b6%e9%9c%80%e8%a6%81%e8%80%83%e9%87%8f%e5%93%aa%e4%ba%9b%e5%9b%a0%e7%b4%a0%ef%bc%9f.md) ***cache技术选型的一些定量分析点：读写方式、过期策略、读写峰值、命中率***

    - topic: "***【七大缓存经典问题】***"
      isX: true
      picDir: db/redis/cache-problems
      tables:
        - name: 【hotkey, bigkey】
          table:
            - name: hotkey
              判断标准: 访问频率：QPS占比超10% (如总QPS 10k，单Key达7k)
              解决方案: 【拆key】hotkey#1~hotkey#N 随机访问分散压力
            - 判断标准: 流量集中度：承载集群30%+流量 (如1MB的Hash每秒大量HGETALL)
              解决方案: 【二级缓存】本地缓存+Redis，减少远程访问压力
            - 判断标准: 性能瓶颈：导致节点CPU/带宽达物理极限（响应时间明显延长）
              解决方案: 【读写分离+副本扩展】hotkey单独集群部署

            - name: bigkey
              判断标准: 集合元素：key中k > 5k（Hash/Set成员>5k）
              解决方案: 【压缩后写入】如gzip压缩
            - 判断标准: 数据大小：String类型>10KB （或者 其他类型>10MB）
              解决方案: 【拆key】如List拆分为key_1~key_n
            - 判断标准: 操作耗时：删除耗时>1秒（持久化RDB/AOF显著变慢）
              解决方案: 【加长expire】设置更长TTL，结合UNLINK异步删除

        - name: 【缓存写策略】
          des: 核心在于“数据可靠性——性能——一致性 不可能三角”，三种方案就是三种tradeoff
          table:
            - name: Write Around
              核心需求: 【写性能】可靠性
              核心思想: 直接写数据库，被动失效缓存
              适用场景: 写多读少场景（后台管理/配置修改）
              读策略搭配: 延迟双删(write-around + cache-aside) 必须搭配 Cache Aside（读请求回填缓存）
              使用禁忌: 高频写后立即读（引发缓存击穿）

            - name: Write Through
              核心需求: 【读性能】数据一致性
              核心思想: 缓存作为数据代理，同步双写缓存和数据库
              适用场景: 强一致性场景（金融交易/库存扣减）
              读策略搭配: 异步重试(write-around + read through) 必须搭配 Read Through（缓存作为唯一入口）
              使用禁忌: 写密集场景（DB成性能瓶颈）

            - name: Write Back
              核心需求: 【读写性能 都很好】但是一致性差
              核心思想: 只写缓存，异步批量更新数据库
              适用场景: 高吞吐写入场景（日志记录/实时统计）
              读策略搭配: 无需特殊读策略（缓存即最新数据）
              使用禁忌: 关键数据存储（如支付订单，可能丢数据）

        - name: 【缓存经典问题】
          des: 注意都是高并发场景下发生的问题
          table:
            - name: 缓存击穿 (breakdown)
              why: 【单点key】key在DB中存在
              解决方案: 请求加锁mutex // singleflight
            - name: 缓存穿透 (penetration)
              why: 【单点key】key在DB中不存在
              解决方案: bloomfilter // 默认值
            - name: 缓存雪崩 (avalanche)
              why: 【大量key】归根到底是 缓存数据大量丢失导致的，分为两种可能：1、缓存集群挂了1个实例 2、没有给随机expire
              解决方案: singleflight加锁+随机expire+服务熔断+淘汰策略用LFU
      qs:
        - 【缓存预热】我们需要在服务启动之前（缓存冷启动）或者服务高峰期之前，做个缓存预热，防止服务被打挂了，所以，根据需求，有哪些需要缓存预热的场景呢? 针对以上列出的缓存预热场景，有哪些常用方法呢?
        # 应用启动时预热、定时任务预热
        # 使用storm实时计算TopK的hotkey，分组存成多个task，再并发执行task（把数据从mysql拉到redis，这里注意做成pipeline） # 缓存预热的核心是获取大key和热key
        #        - (large number cache invalidation) (random expire, LFU, )
        - 【缓存并发竞争问题】
        - 【双写一致性】怎么保证redis和mysql的双写一致性？三种方案（延迟双删、异步重试、CDC）各自的场景？


    - topic: "***redis arch（线程模型、内存模型、网络模型、删除策略、过期策略、RESP）***"
      picDir: db/redis/arch
      qs:
        - "***【线程模型】***" # 其实redis的线程模型没啥好说的，就是主线程+fork子线程
        - redis多线程之后，之前的那些阻塞操作（比如keys, bigkeys, HGETALL之类的各种遍历操作以及DEL之类的），还会阻塞吗？为啥？
        - 为什么 redis 在最初的版本中选择单线程模型？(redis 之前为什么不使用多线程？)(单线程的 redis 为什么这么快？) 为什么 redis 在 4.0 之后引入多线程？聊聊对 redis 多线程的评价？ # 主线程处理业务，子线程执行耗时操作，确保主线程不被阻塞*本质上来说还是单线程模型。redis 的多线程只是用来处理网络数据的读写和协议解析，执行命令还是单线程顺序执行*所以我们不需要去考虑常用命令的并发和线程安全问题。
        - "**redis 多线程的实现机制？**"
        - redis 多线程默认是关闭的，怎么开启多线程呢？ # io-thread-do-reads 和 io-threads
        - redis 开启多线程之后，是否会存在“线程的并发安全问题”？

        - "***【内存模型】Malloc(jemalloc)***"
        - redis virtual memory, THP and swap # redis 有自己的 VM 机制，理论上能存储比物理内存更多的数据。当数据超量时，会引发 swap，把冷数据刷到硬盘上
        - "***【网络模型】当我们输入redis命令时，redis是怎么处理这条命令的(client requests)? reactor model?***" # [聊聊 Redis 是如何进行请求处理 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/674) redis use reactor as event-driven. 具体流程就是，序列化命令、解析并处理命令、处理完成后再序列化成 RESP 格式的响应、最后再通过 RESP 协议返回给客户端。

        - 【服务调优】How to reduce the memory usage of redis? 为什么 redis 建议关闭THP? # THP会导致COW期间复制内存页从 4KB 变成 2MB，导致fork子进程速度变慢、高并发下容易造成内存溢出，所以建议关闭

        - redis的删除策略和过期策略都是针对删除key的策略，那么二者有啥区别？ # 删除策略是when和how（何时删除和怎么删除），淘汰策略是what（删除哪些key）
        - 【删除策略 (Eviction Strategy)】redis的删除策略（定时、定期、惰性）有啥区别？为啥redis使用 惰性+定期的删除策略（redis的lazy-free机制）？
        - 【过期策略 (Expiration Strategy)】redis的几种（2*3+2）淘汰策略分别有哪些？ # (volatile/allkeys * LRU/LFU/random) + no-eviction, volatile-ttl (在设置了过期时间的 key 中，具有更早过期时间的 key 优先移除)
        - 不同内存回收策略（过期键的删除策略），Redis 的内存用完了各自会发生什么？
        - LRU 和 LFU 分别适用于哪些场景？LFU 在哪些场景下比 LRU 更好？ # LFU 算法是根据使用次数来计算的，LRU 是根据使用时间来计算的
        - "具体聊聊 redis 的 近似LRU算法(Approximated LRU) ？和LRU算法有什么区别？" # 采样. 当 Redis 接收到新的写入命令，而内存又不够时，就会触发近似 LRU 算法来强制清理一些 key。具体清理的步骤是，Redis 会对 key 进行采样，通常是取 5 个，然后会把过期的 key 放到我们上面说的“过期池”中，过期池中的 key 是按照空闲时间来排序的，Redis 会优先清理掉空闲时间最长的 key，直到内存小于 maxmemory。redis 通过配置maxmemory-samples，默认为 3，数字越大，cpu 开销越大，越接近“理论 LRU 算法”

        - 【RESP(redis serialization protocol)】众所周知，所有redis命令都是由RESP执行的，包括redis主从复制和 pipeline也都是用了RESP，那RESP在redis命令执行中的具体使用？ # [Redis serialization protocol specification | Docs](https://redis.io/docs/latest/develop/reference/protocol-spec/) 客户端发送命令给 redis 服务器时，redis 就会使用 RESP 协议来处理这些命令）。具体流程就是，序列化命令、解析并处理命令、处理完成后再序列化成 RESP 格式的响应、最后再通过 RESP 协议返回给客户端。
        - RESP 怎么识别5种数据类型 (Simple String, Errors, Intergers, Bulk Strings, Arrays)? # +, -, :, $, *
        - RESP的16种类型（8种数据类型对应的+8种操作对应的（pub/sub、事务协议、））
        #  pub-sub 发布订阅协议，client 可以订阅 channel，持续等待 server 推送消息。
        #  事务协议，事务协议可以用 multi 和 exec 封装一些列指令，来一次性执行。
        #  脚本协议，关键指令是 eval、evalsha 和 script等。
        #  连接协议，主要包括权限控制，切换 DB，关闭连接等。
        #  复制协议，包括 slaveof、role、psync 等。
        #  配置协议，config set/get 等，可以在线修改/获取配置。
        #  调试统计协议，如 slowlog，monitor，info 等。
        #  其他内部命令，如 migrate，dump，restore 等。



    - topic: "***redis datatype（RedisObject、...、pipeline、pub/sub、lua、stream）***"
      qs:
        #            - "***RedisObject 是怎么通过 type(datatype), encoding(ds), ptr 实现redis各种数据类型的?***"

        #            - "【DS本身的实现】几种redis的核心数据结构，比如 SDS、dict(hashtable)、intset、linkedlist、ziplist、skiplist 分别是怎么实现的？以及" # [图解redis五种数据结构底层实现(动图哦)](https://i6448038.github.io/2019/12/01/redis-data-struct/)


        # string
        #            - string不都是用 len, free, buf[] 实现的吗？为啥redis要自己实现SDS作为字符串呢？ # [redis/src/sds.h at unstable · redis/redis](https://github.com/redis/redis/blob/unstable/src/sds.h) 因为redis是基于 c 实现的（而非 cpp），然而c 的字符串确实没有 len 属性。
        #            - redis中字符串的编码（encoding）有raw、embstr、int三种，那各自区别是啥呢？ # [Redis 源码解析 6：五大数据类型之字符串 - 小新是也 - 博客园](https://www.cnblogs.com/chenchuxin/p/14204452.html) 唯一区别是：raw 是分配内存的时候，redisobject 和 sds 各分配一块内存，而 embstr 是 redisobject 和 raw 在一块儿内存中。
        #            - redis 字符串的 []buf 有啥用？为啥还需要 free字段呢？



        # hash
        #            - 使用 hash 类型，有哪些要注意的坑？ # hash 元素太多的话，用 hgetall 会阻塞，应该用 hmget 或者 hscan 代替
        #            - 聊聊 redis 中 HashMap 的实现？（双 table、渐进式 rehash、扩容条件、缩容条件、bgsave、COW 机制）




        # set
        #            - redis是怎么用intset实现set的呢（intset 本身是有序的，但是redis set是无序的）？怎么理解“可以把 redis 的 intset 理解为一种特殊的 list，因为他是无序的”


        #            - 为什么 Redis 选择使用skiplist而不是RBT来实现zset? Why redis using skiplist to implement list, hash and zset, rather than RBT? # 各种操作的复杂度相同，但是 zrange以及 zrank操作，红黑树的效率没有跳表高。按照区间查找数据时，跳表可以做到 O(logn) 的时间复杂度定位区间的起点，然后在原始链表中顺序往后遍历就可以了，非常高效。
        #            - "***redis 中 zset 的实现？redis 的 zset 为什么使用 hashmap 和跳表（而不用红黑树）？（非常高频的面试题）***" # zset使用 HashMap 和skiplist来保证数据的存储和有序，HashMap 里放的是成员到 score 的映射，而跳跃表里存放的是所有的成员，排序依据是 HashMap 里存的 score，使用跳跃表的结构可以获得比较高的查找效率，并且在实现上比较简单


        - "***redis的scan命令基于hashtable实现，scan查找的具体流程? scan 命令查找 key，为什么会有重复？怎么解决？***" # [Redis Scan 原理解析与踩坑 -](https://www.lixueduan.com/posts/redis/redis-scan/) hashmap, rehash, expand/shrink scan 基于 hash 表实现，scan 就是对这个一维数组进行遍历，每次返回的游标值也就是这个数组的索引，渐进式 rehash。*两次 scan 期间可能会有 rehash 发生，如果 rehash 扩容的时候，不会出现重复；如果连续缩容时，可能 th1 有的 th0 也有*
        - redis scan 坑 # [Redis 中 scan 命令踩坑，千万别乱用！！-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1822307)

        - "***redis 中 dict 的实现（Redis的Hashtable是如何扩容的?）？redis 的 HashMap 是怎么扩容的？dict 的 rehash 和渐进式 rehash***" # [redis/src/dict.c at unstable · redis/redis](https://github.com/redis/redis/blob/unstable/src/dict.c#L66) set 的内部实现是一个 value 永远为 null 的 HashMap，实际上就是通过计算 hash 的方式来快速去重，这也是 set 能提供判断一个成员是否在集合内的原因。


        - Pipeline 有什么好处，为什么要用 Pipeline？ # (RESP buffer, ) (batch processing, not atomic (not support transaction)) pipe 模式，就是把 redis 命令写入文件，再转成 redis 协议，再用 pipe 插入。简单来说，就是**把原来的命令，通过文本这种中介，批量打包执行。** 用 pipe 可以将多次 IO 往返的时间压缩成一条，批量处理嘛。

        - pipeline 和 消息队列 都是 redis 的异步操作，有什么区别呢？对象不同，应用请求和redis命令 # 消息队列是用 redis 异步处理应用的请求，而 pipeline 是异步操作 redis 的命令
        - 事实上redis事务是BASE，也不支持回滚。那pipeline和tx有啥区别呢？不是都不支持回滚吗？

        - redis stream，“stream是一种通过radix tree连接的delta-compressed macro nodes”，被认为是“简易版 kafka”，那相较于pub/sub，多了哪些feat? 相较于kafka，又少了哪些feat? # 相较于 pub/sub，还支持消息持久化，即使宕机，消息也不会被丢弃，还借鉴了 kafka 的很多设计，比如 consumer group，组内消费者共享所有信息，但是一条信息只能一个消费可以消费到，相当于一个轻量级 kafka，很实用。redis stream有kafka的消费组的概念，但是没有partition的概念，所以它是个低配版的Kafka。现在看起来Streams像是一个追加模式的，以时间为分数，元素是小型Hash的zset。更省内存，在很多场景下可以代替zset（最常见的就是有ts但是只需要类似“比分”这样简单数据的场景（股票价格、气象数据、MQ的任务分发等、IM场景）） 用stream代替list。

        - 【redis stream】能否列举一些既能用其他数据类型，也能用stream，但是stream更好的使用场景吗？
        - 使用redis stream模拟用户注册后，消息队列发送邮件


        - redis中lua的实现 # [浅析 redis lua 实现 | 董泽润的技术笔记](https://mytechshares.com/2022/10/07/dive-redis-lua/)



    #    - topic: redis trx
    #      qs:
    #        - 怎么理解“redis trx 是BASE事务，而不是ACID事务” # 不同于传统的 RDB，事务操作会对数据进行加锁，redis 事务操作只会在数据被其他客户端抢先修改的情况下，通知执行了 WATCH 命令的客户端，这时事务操作失败，客户端可以选择重试或者中断操作（这种做法称之为乐观锁；）
    #        - 怎么在 redis 中使用 trx? multi, exec, discard, watch/unwatch # BASE(not ACID), OCC, (multi, exec, discard, watch/unwatch)
    #        - redis trx 为啥不支持回滚（为啥即使命令执行失败，也继续执行其他的命令而不是回滚所有命令）? BASE事务都不支持回滚吗？有没有支持回滚操作的BASE事务？如果有，有哪些BASE事务但是支持回滚的？ # 这是redis本身的设计，因为redis本身就不考虑，因为语法或者数据类型错误导致执行失败的这些“垃圾场景”
    #
    #        - 那我如果想实现redis的原子操作，是用redis事务，还是用lua脚本呢？
    #        - 我有一个不太明白的问题哈，众所周知redis的transaction是BASE的。那为啥还要通过引入类似seata或者DTM这种Distributed trx 服务来使用AT和TCC这些弱一致性方案，而不是直接使用redis的transaction呢？既然都是弱一致性的。我们会在一个项目中使用各种数据库，比如postgres, mongo, elasticsearch, influxdb等等各种类型的数据库，分布式事务是怎么保证在这些数据库的数据一致性的呢？按照我的想法，分布式事务难道不应该是数据库服务本身应该提供的吗？比如说我们使用vitess实现mysql高可用，那vitess本身就应该给我们保证分布式事务的数据一致性。不是吗？



    - topic: "***redis用法***"
      qs:
        - "***redis 批量查询，有哪几种方法?*** （比如我们要获取商品的详情，有日销量、月销量、库存数量、评价数量）"
        #比如我们要获取商品的详情，有日销量、月销量、库存数量、评价数量，这些数据都在Redis 缓存中，那么我们是要拿四趟？还是一趟呢？当然是一趟最好呀
        #
        #- MGET (批量查询字符串类型key)
        #- HMGET (批量查询某个hash类型key中的field的value)
        #- pipeline (不限制，但是尽量不要使用pipeline，因为cluster “由于 Redis Cluster 采用的是分片机制，这些键无法保证所有的 key 都在同一区域的哈希槽上。因此，即使使用了 Pipeline，每个命令仍可能在不同的节点上进行处理，导致多个命令的执行不在同一时刻。”)
        #- lua （相比于HMGET只能查询某个hash key，lua就可以批量查询多个hash key）
        #
        #`eval "local rst={}; for i,v in pairs(KEYS) do rst[i]=redis.call('hgetall', v) end;return rst" 2 user:1 user:2`

        - 限流，redis 实现限流有哪些方案？
        - redis 实现分页
        - redis 实现多条件查询
        - 怎么使用 redis 实现流量整形
        - 怎么把 redis 数据刷回 MySQL

        - 【如何删除 redis 里的 key】我们通常结合使用scan+del来删除redis的key，那如果在redis集群中，怎么删除key呢
        - "*MySQL 里有 200W 数据，redis 只存 20W 数据，如何保证 redis 中都是热点数据？*"
        - 假如 Redis 里面有1 亿个 key，其中有10w 个 key 是以某个固定的已知的前缀开头的，如果将它们全部找出来？

        - "***我需要实现订单超时未支付自动关闭，怎么用redis实现延时队列？根据延时队列的核心需求，这些方案有哪些优缺点？***" # redis 的 zset+lua，设置 score 为过期的时间，通过 score 把 member 进行排序。消费端轮询这个 zset 队列，对比 score 和当前时间，进行消费。使用 eval 通过 lua 脚本实现 redis 的原子性操作
        - 如何用Redis统计在线人数? # set


    - topic: Redis运维 (monitor)
      qs:
        - 有哪些可能导致 Redis OOM的操作？ # monitor, setbit
        - 我们通常在mysql组合备份策略，用“全量 + 增量”，还是“全量 + 差量”？这两种组合备份策略有啥区别呢？分别的适用场景？ # 如果对备份时间和存储空间有较高要求，可以选择"全量 + 增量"备份策略。如果对备份恢复能力有较高要求，可以选择"全量 + 差异"备份策略。
        - "***有哪些监控 redis 的指标(redis metrics)？***"
        - 怎么用prom监控和analyze redis的big-keys, hot-keys?
        - 如何分析 Redis 里存了什么？ # 使用 RDB 备份，解析生成的 RDB 文件
        - redis 服务调优可以分为两部分，linux本身优化（在下面），和redis配置调优（写到redis.conf里了），具体各自有哪些方法？
        - 【redis安全相关问题】（1、msf/nmap批量探测redis服务器。2、暴力破解redis密码。3、ssrf漏洞） # [Redis的一些漏洞复现利用 | Pa55w0rd 's Blog](https://www.pa55w0rd.online/redis/)

        # [从一个事故中理解Redis（几乎）所有知识点_redis_阿里技术_InfoQ写作社区](https://xie.infoq.cn/article/625ec23aa816d2be0ad811de3)
        - redis内存被打满了，按理说应该是依旧可用的，为啥我们的会直接timeout呢？



    - topic: redis 主从复制（SYNC、PSYNC）
      why:
      what:
      hti:
        - "***【sync全量复制】sync全量复制的 WRR(写入、复制、重放) 三步的具体过程? redis2.8 之前只使用SYNC 机制，会导致哪些问题？***"
        - 【psync2】
        - "***redis MSR = FYSNC + PSYNC + RESP buffer，能否聊聊其具体流程?***" # (offset, copybuffer, runid). offset 是偏移量。copybuffer 是复制积压缓冲区，每次主节点同步数据推自己的 offset 和 buffer 过来后比对双方数据的 offset，之后决定是否需要同步 buffer 里面的数据。而 runid 是每个 slave 节点启动时发给 master 节点，用于标识之前是否同步过，决定进行全量还是部分复制。

        - "***PSYNC 和 PSYNC2 的主要流程基本一致，都是建立连接、数据同步（sync）和命令传播这三个阶段，能否大概说明这三步各自的具体过程？***" # 1、建立连接：slave保存master的runid和offset。2、数据同步：3、命令传播：同步增量数据+主从之间发送心跳（确认双方在线，slave 节点还会去发送自己的 offset 去获取更新命令）
        - "***PSYNC的offset、copybuffer、runid分别是啥？***" # offset（偏移量）、copybuffer（复制积压缓冲区，每次主节点同步数据推自己的 offset 和 buffer 过来后比对双方数据的 offset，之后决定是否需要同步 buffer 里面的数据）、 runid（每个 slave 节点启动时发给 master 节点，用于标识之前是否同步过，决定进行全量还是部分复制）
        - PSYNC2 机制进行了哪些优化（能否说说主从复制的全流程？能否聊聊主从服务器在命令传播阶段的心跳检测？）？ # PSYNC2相比于PSYNC的主要优化在于处理从节点断线重连和故障切换时的复制问题，减少了在这些情况下的全量复制，提高了复制效率。在PSYNC2中，当从节点晋升为主节点时，它会保留两组复制ID和偏移量，这样可以更容易地与其他从节点进行部分重同步，从而减少了全量复制的需要。
        - "***redis psync 的本质就是全量复制+增量复制，那psync在 master和slave维护的 offset，是不是就是类似mysql的GTID?***"
        - 命令传播 # 命令传播阶段主要有两个点，一个是*同步增量数据*，一个是*主从之间发送心跳*确认双方在线，slave 节点还会去发送自己的 offset 去获取更新命令
        - 【开放题】所有replication都是position-based，那如果我们对比一下mysql和Redis的replication机制，二者有什么相同和不同呢?
      hto:
        - 无盘复制是什么?



    - topic: redis sentinel
      url: https://redis.io/docs/latest/operate/oss_and_stack/management/sentinel/
      why:
      what:
        - "***What's redis-sentinel?***" # HA, collateral tasks(monitor, failover). sentinel 解决高可用问题，master 宕机时故障转移
        - sentinel 有什么优缺点？ # 解决了主从复制不能自动故障转移的问题，继承了主从复制难以动态扩容的缺点
        - sentinel 的主要功能？ # 监控/通知/自动故障转移
        - sentinel 和 keepalived 进行故障转移有什么区别？ # keepalived 基于 VRRP 协议，通过 IP 漂移实现高可用，这种方案不好

      htu:
        - 怎么配置 sentinel？sentinel 集群实现对自身和 redis 主从复制进行监控？
      hti:
        - "***【redis sentinel 工作机制】***" # gossip(to notification leader is dead)+raft(to elect leader(smaller priority > larger offset > smaller runid))
        #- 假设一个 master 复制到 N 个 slave，同时运行了多个 sentinel。
        #- 如果一个 sentinel 检测到了 master 没有响应，那么它会广播一个`SDOWN 消息`(自己主观认为的) 给其他 sentinel。
        #- 当指定数量的 sentinel 都认为 master 宕了，那么这就成为了事实，`ODOWN 消息`(客观真实的) 会被广播。之后，一个新的 master 会被选出来，这一切对于 redis 的客户端都是透明的。

        - sentinel怎么选出新 master，大概流程？根据这些slave机器的哪些指标进行选择？ # 1、首先判断优先级，选择优先级较小的。2、如果优先级相同，选择复制 offset 更大的。slave 和 master 同步之后，offset 会自动增加。3、如果复制下标也相同，就选择 runid 小的。每个 redis 实例都有一个 runid，runid 是启动时设置的随机字符串。

        - sentinel 中 gossip 协议的使用？ # 用 gossip 接收 master 是否下线
        - sentinel 最少要求几个节点？ # 3 个，因为需要`n/2+1`个节点才能进行选举，最小奇数

        - sentinel 有多个，具体谁来执行故障转移？多个 sentinel 会选出一个 leader，具体的选举机制是 raft 算法
        - sentinel 是怎么发现 slave 和其他 sentinel 的？`询问 master`/`pub/sub 机制`发现其他 sentinel


    - topic: redis cluster
      why:
        - 【redis-sharding】啥是 redis-sharding? 为啥 redis-cluster 取代了 redis-sharding 成为标准的cluster方案? # range, hash(id=hash(key)%N). redis-sharding 是在 redis 4.0官方提供redis-cluster之前，业界普遍采取的一种在redis client上进行划分key的方案。
      what:
        - redis-cluster 和 redis-sentinel是啥关系? # sentinel 解决高可用问题，master 宕机时故障转移。cluster 集群嘛，解决拓展性问题，单个 cluster 内存不足时，使用 cluster 存数据。

        - What's redis-cluster? redis-cluster的工作机制? # 集群嘛，解决拓展性问题. meet/ping/pong/fail
        - cluster 有什么优缺点？
      when:
      htu:
      hti:
        - redis-cluster也用的链地址法来解决哈希冲突吗？那为啥还要固定hash-slot数量呢？链地址法相对于开放寻址法的优势不就在于其更适于动态扩容吗，既然固定数量，岂不是用开放寻址法更好？

        - redis-cluster 是怎么通过 hash-slots 实现一致性hash的? slot=CRC16(key)/16384? 为啥是16384个哈希槽? # 1w6是个不多不少，比较trade-off的数量，太多了会导致网络阻塞（如果2^16个哈希槽，心跳包8K个，现在1w6个，心跳包是2k个），太少了会影响gossip传播效率。

        - Hash Slot Resharding and Rebalancing for Redis Cluster.
        - codis, twemproxy 之类的 HA方案已经EOL了，现在主流的redis HA方案怎么做?
        - cluster 集群最少几个节点？(6 个，3 主 3 从，主节点提供读写操作，从节点作为备用节点，只用来故障转移)
        - cluster 节点之间，会发送哪些消息？(meet/ping/pong/fail)
        - cluster 怎么实现数据分区？哈希槽
        - 怎么请求分布式缓存的路由？缓存节点的拓展和收缩
        - 怎么发现和转移故障？主要聊聊节点下线之后怎么恢复？(资格检查/触发选举 (根据`复制偏移量`决定触发的优先级，偏移量越大，节点延迟越低)/发起选举/投票选举)
        - 分布式缓存节点之间的通信？gossip 协议
        - 为啥 cluster 不用 gossip 选主？ # [一万字详解 Redis Cluster Gossip 协议 - 阿里云开发者社区](https://developer.aliyun.com/article/779564#slide-6)

        - Redis 集群方案什么情况下会导致整个集群不可用？
        - Redis 集群的主从复制模型是怎样的？

        - Redis 集群会有写操作丢失吗？为什么？

        - Redis 集群之间是如何复制的？

        - Redis 集群最大节点个数是多少？

        - Redis 集群如何选择数据库？





#- object encoding key # 查看某个 key 具体使用了哪种数据结构
#- object refcount key # 查看某个 key 的共享对象的引用次数
#- redis-cli --bigkeys # 用来查看所有bigkey
#- redis-benchmark # redis 性能测试工具
#- redis-check-aof # 检查 aof 日志的工具
#- redis-check-dump # 检查 rdb 日志的工具
#- CONFIG RESETSTAT
#- INFO
#- migrate # 实际上是 dump+restore+del 三个命令的组合，但是是原子性命令，支持源 redis 和目标 redis 之间直接迁移，比 dump+restore 好用，可以直接替代
#- rename # key重命名会执行 del 删除旧键，如果数据太大，会阻塞 redis
#- SETBIT key offset value # SETBIT；“置位”
#- GETBIT key offset # GETBIT；“取值”
#- BITCOUNT key [start end] # 返回位图中第一个值为 bit 的二进制位的位置；在默认情况下，命令将检测整个位图，但是用户可以通过可选的 start 参数和 end 参数指定要检测的范围
#- BITOP operation destKey key [key...] # BITOP 支持 and，or，not，xor 这四种操作；很常用的一条命令
#- SLOWLOG GET <?N> # 查看最新的N条慢日志，包括命令本身、执行时间和执行时所在的时间戳
#
#- EVAL/EVALSHA # redis lua中 eval 和 evalsha 有什么区别？为什么推荐使用 evalsha？【减少网络带宽消耗】EVALSHA 通过 SHA1 校验和替代脚本传输，显著降低网络负载，尤其适用于高频操作或大脚本场景。一个 1KB 的脚本，执行 1 万次时，EVAL 需传输 10MB 数据，而 EVALSHA 仅需传输 400KB。
#- script load加载，拿到返回的 sha1，再script exists判断脚本是否存在，最后evalsha sha1执行脚本 # 用哪些命令管理 lua 脚本？redis 里使用 lua 脚本的流程？
#- SCRIPT FLUSH
#- SCRIPT KILL
#- SCRIPT DEBUG # 怎么在 redis 里调试 lua 脚本？
#- lua 脚本超时时间？超时后怎么处理？
#
#- redis 自动过期相关命令(TTL, PTTL, PERSIST, PEXPIREAT)，分别有啥用？
