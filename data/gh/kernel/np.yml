---



- type: 网络协议
  tag: kernel
  score: 5
  repo:
    - url: https://github.com/HarshKapadia2/tcp-version-performance-comparison
      des: Performance Comparison of TCP Versions. (Reno, CUBIC, Vegas, BBR). 基于Rlang实现的

    - url: https://github.com/jpillora/chisel
      des: a fast TCP/UDP tunnel, transported over HTTP, secured via SSH

    - url: https://github.com/the-tcpdump-group/tcpdump
      doc: https://www.tcpdump.org/manpages/tcpdump.1.html
      des: 名为tcpdump，但是能抓所有network layer和application layer的数据包，包括但不限于TCP、UDP、ICMP、ARP、IP和Ethernet，还可以用于分析 IPv6 数据包。tcpdump 的强大之处在于其灵活性和丰富的过滤功能，用户可以根据自己的需求编写过滤表达式来捕获特定类型的数据包。例如，用户可以基于源地址、目的地址、端口号、协议类型或其他数据包内容来过滤数据包。 #  [使用 tcpdump 抓包 - Luyu Huang's Blog](https://luyuhuang.tech/2022/12/05/tcpdump.html)

    # [详解 KCP 协议的原理和实现 - Luyu Huang's Blog](https://luyuhuang.tech/2020/12/09/kcp.html)
    - url: https://github.com/skywind3000/kcp
      des: Better KCP

    - url: https://github.com/Shopify/toxiproxy
      des: A TCP proxy to simulate network and system conditions for chaos and resiliency testing

    - url: https://github.com/google/bbr
      des: BBR, 注意需要在v2, v3下找sc.
      doc: https://zhuanlan.zhihu.com/p/383910510
      qs:
        - “BBR 则基于反馈驱动，进行主动窗口调整*，BBR 内置了自主的调速机制，不受 TCP 拥塞控制状态机的控制，BBR 算法是自闭的，周期性查看是否有更多的带宽，如果有就利用它，如果没有，就退回之前的状态。”
        - BBR算法的基本原理是什么？ # 利用估算的带宽bw和延迟RTT直接推测拥塞程度（而不是基于发包的AIMD）从而计算滑动窗口。BBR的目标是最大化利用网络的带宽容量（BDP. Bandwidth-Delay Product），通过调整拥塞窗口（cwnd）和pacing rate来避免网络拥塞。
        - BBR算法如何计算即时带宽？ # 通过测量已应答的数据量（delivered）和这些数据被确认所用的时间（interval_us）来计算即时带宽。具体的计算公式为：bw = delivered / interval_us。
        - BBR算法的状态机有哪些状态？ # STARTUP、DRAIN、PROBE_BW和PROBE_RTT。BBR通过这些状态机来动态调整pacing rate和cwnd
        - BBR算法如何处理丢包和RTT增加？ # BBR算法区分了噪声丢包和拥塞丢包。对于噪声丢包，BBR不会因为丢包而降低即时带宽的计算，因此不会因为非拥塞性的丢包而减少cwnd。对于拥塞丢包，BBR会观察RTT是否持续增加，如果RTT没有达到最小值或更小，那么BBR会认为网络可能真的发生了拥塞，并相应地调整cwnd
        - BBR算法如何与公平队列（FQ）协同工作？ # FQ可以根据BBR设置的pacing rate将数据包平缓地发送到网络中，而不是一次性突发。这种平缓发送的方式有助于减少网络拥塞，提高整体的网络性能。
        - TCP 的 BBR 听起来很牛，你知道它是如何达到这个最优点的吗？
        - 设备缓存会导致延时？ # 假如经过设备的包都不需要进入缓存，那么得到的速度是最快的。进入缓存且等待，等待的时间就是额外的延时。BBR 就是为了避免这些问题：充分利用带宽；降低 buffer 占用率。

        - 降低发送 packet 的速度，为何反而提速了？
        #  标准 TCP 拥塞算法是遇到丢包的数据时快速下降发送速度，因为算法假设丢包都是因为过程设备缓存满了。快速下降后重新慢启动，整个过程对于带宽来说是浪费的。通过 packet 速度—时间的图来看，从积分上看，BBR 充分利用带宽时发送效率才是最高的。可以说 BBR 比标准 TCP 拥塞算法更正确地处理了数据丢包。对于网络上有一定丢包率的公网，BBR 会更加智慧一点。
        #  回顾网络发展过程，带宽的是极大地改进的，而最小延迟会受限与介质传播速度，不会明显减少。BBR 可以说是应运而生。

        - BBR 如何解决延时？
        #  S1：慢启动开始时，以前期的延迟时间为延迟最小值 Tmin。然后监控延迟值是否达到 Tmin 的 n 倍，达到这个阀值后，判断带宽已经消耗尽且使用了一定的缓存，进入排空阶段。
        #  S2：指数降低发送速率，直至延迟不再降低。这个过程的原理同 S1
        #  S3：协议进入稳定运行状态。交替探测带宽和延迟，且大多数时间下都处于带宽探测阶段。


    - url: https://github.com/hanshuaikang/Nping
      des: 多地址并发 Ping 工具。基于 Rust 开发使用 ICMP 协议的 Ping 工具, 支持多地址并发 Ping, 可视化图表展示, 数据实时更新等特性。Rust 的多地址并发 Ping 工具。这是一个用 Rust 开发的可视化 Ping 工具，支持同时对多个目标地址并发 Ping 操作。它提供了分区折线图和表格视图等可视化展示，支持实时动态展示延迟、丢包率等性能指标，同时兼容 IPv4 和 IPv6 网络环境。

    - url: https://github.com/mdlayher/icmpx
      des: ICMP，需要注意的是kernel默认的conn中并不会使用ICMP进行网络诊断（虽然ICMP是IP的附属协议）
      qs:
        - 怎么做一个使用ICMP协议进行压测的工具？ # [mping: 使用新的icmp库实现探测和压测工具](https://colobu.com/2023/09/10/mping-a-multi-targets-high-frequency-pressure-measuring-and-detection-tool/)


    - url: https://github.com/cloudflare/pmtud
      des: PMTUD = Path MTU Discover. MTU是网络层协议，所以当然也是IP协议的附属协议，MTU是用来确保IP数据包不需要在传输过程中被分片，从而提高网络效率和避免分片带来的问题。PMTUD通常依赖于ICMP消息来检测路径上是否存在更小的MTU，并相应地调整数据包大小。

    - url: https://github.com/chronolaw/http_study
      des: 【HTTP协议】
      rel:
        - url: https://github.com/quic-go/quic-go
          doc: https://quic-go.net/docs/
          des: QUIC = Quick UDP Internet Conn
          rel:
            - url: https://github.com/alibaba/xquic
              des: 阿里搞的QUIC
            - url: https://github.com/cloudflare/quiche
              des: cf对QUIC的实现。大多数 QUIC 实现工作在用户空间，支持灵活“插拔”不同的拥塞控制算法，如 Cubic、BBR 和 PCC 等。这让工程师在无需深入内核开发的情况下，能灵活调整可靠传输机制和拥塞控制策略。如 Cloudflare 开发的开源 QUIC 实现 quiche，提供了 setSendAlgorithm 方法，工程师可直接选择合适的拥塞控制算法，无需经过操作系统内核。

    - url: https://github.com/insomniacslk/dhcp
      des: DHCPv6 and DHCPv4 packet library, client and server written in Go
      qs:
        - DHCP(Dynamic Host Configuration Protocol) DHCP 协议是用来自动配置 IP 的，DHCP 协议能给客户推荐 PXE # 有了这个协议，网络管理员就轻松多了。他只需要配置一段共享的 IP 地址。每一台新接入的机器都通过 DHCP 协议，来这个共享的 IP 地址里申请，然后自动配置好就可以了。等人走了，或者用完了，还回去，这样其他的机器也能用。
        - IP 地址的回收和续租：How does DHCP allocate IP on the LAN? (discover, offer) # 如果有多个 DHCP 服务器，这台新机器会收到多个 IP，他会选择最先到达的那个 DHCP offer，并且向网络发送一个广播数据包（其中包括客户端的 MAC 地址，选择的 IP 地址，提供该 IP 的 DHCP 服务器等），通知网络里的所有 DHCP 服务器，已经获得 IP，并退回不需要的 IP。
        - DHCP 是怎么解析的呢？
        #- 新来的机器通过 IP 地址 0.0.0.0 发送一个广播包，目的 IP 地址是 ***************；广播包封装了 UDP，UDP 里封装了 DHCP；新加入的机器通过使用 DHCP 向外广播，我是新来的（boot request），我的 MAC 地址，我还没有 IP，谁能给我一个 IP 地址？
        #- *这个时候 DHCP server 就会通过 MAC 识别机器唯一，给该机器分配一个该局域网下唯一的 IP；这个过程就是 DHCP offer*
        #- DHCP Server 为用户保留给他提供的 IP 地址；*Linux 默认逻辑是，如果这是一个跨网段的调用，就不会直接把包发送到网络上，而是把包发送到网关*
        #- 单播时，DHCP 协议用来在主机启动时，自动获取 IP 地址，默认网关，DNS 服务器 (DNS 解析就是主机把网址信息发送给 DNS 服务器，然后解析完成返给 IP 地址)。如果广播域没有 DHCP 服务器，需要在网关上配置 DHCP，并 relay 到中心机房的 DHCP 服务器。
        #- 组播时，需要 IGMP 告诉网关，想加入哪个组，如 ********* ,网关再向上游路由器发加入 ********* 的请求，路由器之间使用 PIM 协议来通信。如果在主机和网关之间有交换机，需要交换机配置 IGMP snooping 或 cisco CGMP 来窥看 IGMP 消息交互，来决定把 ********* 只转发给加入组的主机，而不是发给所有的主机。*如果是一个跨网段的调用，就不会直接把包发送到网络上，而是把包发送到网关*

        - PXE 是什么？PXE(Pre-boot Execution Environment) 预启动执行环境，用来批量化安装操作系统，具体流程不谈
        - "*解析 PXE 的工作流程？*"


    - url: https://github.com/beevik/ntp
      des: ntp
      qs:
        - 什么是 NTP 协议？网络时间同步协议，用来在分布式时间服务器和客户端之间进行时间同步，是一个跨越广域网和局域网的复杂的时间同步协议，精度为 ms 级。NTP 使用 UDP 报文进行传输，使用 UDP 端口号为 123。
        - NTP 协议的实现原理？
        - NTP 有哪几种工作模式？请简要说明 `客户端/服务器模式`、`对称模式`、`广播模式`、`组播模式`
        - NTP 协议的报文？NTP 协议有两种报文，一种是`时间同步报文`，一种是`控制报文`

    - url: https://github.com/r3labs/sse
      des: 一个用Go语言实现的Server-Sent Events(SSE)客户端和服务器库。

    #- url: https://github.com/eranyanay/1m-go-websockets
    #  des: 一个展示Go语言处理百万级WebSocket连接的示例项目
    - url: https://github.com/gorilla/websocket
      des: Use websocket in golang
      rel:
        - url: https://github.com/websockets/ws
          des: officially nodejs client for ws.
      topics:
        - topic: ws协议 基本认知
          des: 【数据自动刷新】几种方案 ws vs SSE vs 轮询 vs Http/2 server push 从（是否双向通信、实时性（是否有请求延迟）、是否支持自动重连、资源开销、服务器压力、浏览器兼容性、使用是否方便、使用场景）其优缺点"
          table:
            - name: WebSocket
              通信方向: 双向通信
              实时性: 高实时性，无请求延迟
              自动重连: true
              资源开销: 较高（需维持持久连接）
              服务器压力: 较高（需处理大量持久连接）
              浏览器兼容性: 现代浏览器支持良好
              使用场景: 聊天应用/在线游戏/实时交互

            - name: SSE
              通信方向: 单向通信（服务端→客户端）
              实时性: 较好（存在轻微延迟）
              自动重连: true # 支持（内置重连机制）
              资源开销: 较低（基于HTTP协议）
              服务器压力: 中等（需维护持久连接）
              浏览器兼容性: IE不支持，其他主流浏览器支持
              使用场景: 新闻推送/金融行情/实时监控

            - name: HTTP/2 Server Push
              通信方向: 单向推送（服务端→客户端）
              实时性: 较低（不适用动态数据）
              自动重连: false
              资源开销: 低（复用HTTP/2连接）
              服务器压力: 低（利用多路复用特性）
              浏览器兼容性: 依赖HTTP/2支持
              使用场景: 静态资源预加载/页面优化
          qs:
            - ws 协议是什么：为啥说“ws本质上是HTTP的拓展，本质上跟HTTP2类似，都是变成了全双工通信协议”？ # ws 针对客户端和服务器之间双向持续通信而设计，同时兼容 HTTP 协议，ws 使用 HTTP 协议建立连接，连接以后客户端和服务端的双向通信就与 HTTP 无关了
            - ws 的应用场景？ # 在线游戏、在线文档、体育实况、股票基金报价这些不刷新就需要一直更新数据的
            - ws 和 wss 有啥区别？ # wss 是 ws 的加密版本
            - ws 连接过程：握手和数据传输两部分（或者说客户端向服务端发起连接请求，和服务端返回握手应答两部分），一共使用了4个headers，分别是? # (handshake, exchange data. switch protocols, similar with HTTP2.) (Connection, Upgrade)
            # - 通知服务器协议升级`Connection:upgrade`，升级协议的服务器地址`Host:0.0.0.0:9501`
            # - 连接秘钥`Sec-WebSocket-Key`(客户端把 key 传给服务器，服务器处理后再回传给客户端，客户端根据回传的 key 判断是否建立连接)
            # - 升级版本`Sec-WebSocket-Version`
            # - *服务端返回握手应答 `Upgrade:websocket`*，唯一与请求 header 不同的参数，代表已经成功切换协议。。websocket 请求的`响应 header`，http 状态码为 101。

            - 数据传输：数据传输需要客户端，非 websocket 客户端不能与 websocket 服务器通信。浏览器可以直接使用 Chrome/Firefox/高版本 IE/Safari 等浏览器内置了 JS 语言的 WebSocket 客户端。

        - topic: 具体使用
          qs:
            - 如何把 client 保存起来，需要发送时再发送？`core.ClientMap.Store()`
            - 简易封装 client 对象，ping 客户端？封装 ws 的 client 对象，通过 ping 实现 ws 保活

            - 怎么发送 json 数据，并渲染？
            - 封装读数据 chan 的基本套路
            - 定义消息格式，解析客户端消息：
            - 客户端和服务端的双向交互：怎么实现？
            - 简单的断线重连：怎么实现？


    - url: https://github.com/centrifugal/centrifugo
      doc: https://centrifugal.dev/docs/server/configuration
      des: Centrifugo can instantly deliver messages to application online users connected over supported transports (WebSocket, HTTP-streaming, SSE/EventSource, GRPC, SockJS, WebTransport) 之前一直以为 centrifugo 是个ws客户端，但是


    - url: https://github.com/openssl/openssl
      des: 提供了 TLS、DTLS 和 QUIC（目前仅限客户端）等协议的实现。它还包含一个通用的密码库，可独立使用。OpenSSL 是基于 SSLeay 库开发的。需要注意的是kernel的TLS协议是自己实现的，也就是Kernel TLS，它比 OpenSSL 更高效，因为它直接在内核空间中进行加密和解密操作，减少了用户空间和内核空间之间的上下文切换。说白了，应用场景不同。

    - url: https://github.com/XTLS/RealiTLScanner
      des: A TLS server scanner for Reality

    - url: https://github.com/projectdiscovery/tlsx
      des: 【TLS协议】

    - url: https://github.com/mdlayher/arp
      des: 数据链路（ARP）

    - url: https://github.com/hashicorp/mdns
      des: Simple mDNS client/server library in Golang. Used to optimize DNS.
    - url: https://github.com/TencentCloud/httpdns-sdk-android
      des: Tencent Cloud HTTPDNS SDK. HttpDNS通过HTTPS向自己的 DNS 服务器发送域名解析请求，替代了传统的基于 DNS 协议向运营商 Local DNS 发起解析请求的方式，优化了移动互联网服务中域名解析异常带来的问题。直接集成到各种client即可，跟服务端无关。
    - url: https://github.com/TimothyYe/godns
      des: A dynamic DNS client tool that supports AliDNS, Cloudflare, Google Domains, DNSPod, HE.net & DuckDNS & DreamHost, etc, written in Go.
    - url: https://github.com/felixonmars/dnsmasq-china-list
      des: Chinese-specific configuration to improve your favorite DNS server. Best partner for chnroutes.
    - url: https://github.com/pi-hole/pi-hole
    - url: https://github.com/nadoo/glider
      des: glider is a forward proxy with multiple protocols support, and also a dns/dhcp server with ipset management feats(like dnsmasq).
    - url: https://github.com/coredns/coredns
      des: CoreDNS 是 k8s 集群默认的 DNS 服务组件，CoreDNS 主要负责为集群内的 Pod 和 Service 提供 DNS 解析能力。

    - url: https://github.com/jeessy2/ddns-go
      des: 跟DNSMasq或者BIND还不太一样，ddns-go是用来动态更新DNS记录的服务，以便将域名映射到动态分配的IP地址。而DNSMasq则功能更多，它们提供了完整的DNS服务，包括域名解析、DNS缓存、区域传输等功能。它们可以作为本地DNS服务器，提供域名解析和缓存功能，还可以作为权威DNS服务器，管理特定域名的DNS记录。换句话说，ddns-go只是DNSMasq的一部分，只提供了动态更新DNS记录的功能。
    - url: https://github.com/mr-karan/doggo
      des: 非常好用的工具



    - url: https://github.com/geekr-dev/openai-proxy
      des: 核心就是一个比较复杂的 golang 原生 HTTP 请求，比如 HTTP Proxy 的使用、headers 的处理、设置 timeout、流式处理返回数据

    - url: https://github.com/ycd/dstp
      des: 一个命令行工具，用于测试和监控TCP/HTTP连接

    - url: https://github.com/jason5ng32/MyIP
      des: 这个项目是 IP Toolkit，主要功能包括：1、查看本地 IP 地址，并从多个 IPv4 和 IPv6 提供商获取信息。2、提供详细的 IP 地址信息，包括国家、地区、ASN (自治系统号)、地理位置等。3、测试各种网站的可访问性，如 Google、GitHub、YouTube 等。4、检测 WebRTC 连接时使用的 IP 地址。5、显示 DNS 端点数据以评估在使用 VPN 或代理时出现 DNS 泄漏风险。同时支持暗黑模式和极简模式，在不需要 Bing Map API Key 情况下也可以轻松部署到 Vercel 上。此外还提供了查询任意 IP 地址信息的工具，并且支持英文和中文语言。

    - url: https://github.com/monasticacademy/httptap
      des: httptap 是一个用于查看任何 Linux 程序发出的 HTTP/HTTPS 请求的工具。

    - url: https://github.com/pouriyajamshidi/tcping
      des: ping TCP port
    - url: https://github.com/nxtrace/NTrace-core
      des: 【可视化路由跟踪工具】


  topics: # FIXME 把之前在各种repo里的网络协议，都拆出来了。之后就不要再合进去了。应该多想想，怎么缩减现有的这些内容。
    - topic: 网络协议
      picDir: kernel/np/osi
      qs:
        - "***数据包在TCP/IP中流转的大概流程? 数据包怎么产生? 每一层怎么处理?***"
        #- `物理层`。*因为基于物理电路，所以需要一个将数据转化为物理信号的层*
        #- `数据链路层`。*(ARP 协议) 用于 IP 地址与物理地址的对应 (物理地址是数据链路层和物理层使用的地址，IP 地址是网络层和以上各层使用的地址，是一种逻辑地址)*（数据链路层的主协议是 ARP）
        #- `网络层`（组装数据包 + 寻址（路由）+ 尝试发送）。*网络层负责`ip 数据包的产生`以及`ip 数据包在逻辑网络上的路由转发`*（无法直接用 MAC 寻址，需要先使用 IP 地址）。其中，`IP协议`提供的是不可靠的服务，他只是负责尽可能快地将分组从源节点送到目的节点。其他 IP 协议的附属协议做了一下优化，比如`BGP等路由协议族`保证最短路径，`ICMP/IGMP`则保证包在网络上的正常传输 (给传输层以保障，如果错误则直接返回错误)。（*header 验证 (主要功能)，以及`分片`、`重组`和`路由`*）
        #  - 其中，`IP 协议`提供的是不可靠的服务，他只是尽可能快地将分组从源节点送到目的节点。
        #  - 其他`IP 协议的附属协议`做了一下优化。比如`BGP 等路由协议族`保证最短路径。`ICMP/IGMP`则保证包在网络上的正常传输 (给传输层以保障，如果错误则直接返回错误)。
        #- `传输层`（发送数据包）。*网络层已经解决了基本的发送数据包的问题，传输层的目的则是，针对不同的需求，产生了不同的传输层协议*（比如有一些特殊的需求，有的人想要连接快，不介意丢包，有的人想要连接稳定）。
        #- `应用层`。*传输层已经根据各种需求，封装了不同的传输层协议*。应用层将需求细化，根据我们的实际使用封装了各种供我们直接使用的协议。

        - "***TCP/IP协议族内“数网传应”四层的作用分别是什么？***"

        - 可以用“邮局收发邮件来通信”来类比网络中数据包的收发。具体来说，向顺丰下单（第一次请求），顺丰接单（应答)，你向快递小哥联系 (回应应答)，你将消息放进盒子里 (开始封装请求，会话层)，快递小哥封装 一层盒子贴上快递单带回网店 (传输层），到快递点检查是否区域快件（网络层)，将 快件交给运输车 (链路层)，各个快递转运 中心（物理层），快件到达收件市转运中心 (物理层），转运输车（链路层），到达区 域分发（网络层)，网点派送（传输层)，快递员方面签收（会话层），拆开检查（表示层)，收到快递（应用层）。总结一下，下单、揽件、运输、派送、签收，就可以分别看作是应用层、传输层、网络层、链路层、物理层。 # 但是这个类比其实是有问题的，存在以下问题：网络包的收发是在客户端和服务端之间进行的，收发快递的话，则通常只有卖家给买家发快递。并且他这里把快递中心看作是服务端，不太合理。买家和快递之间会频繁地收发吗？所以很不合理。更好的类比是收发邮件，在这个比方里收到邮件是要再回信的，发件人和收件人就类似客户端和服务端，邮件在中间的流转就类似网络包的处理。下单、揽件、运输、派送、签收，就可以分别看作是。IP 就是运输、MAC 就是派送。 # “TCP 确保数据包的可靠传输，IP 可以想象成一个门牌号，HTTP 则是快递包里的具体数据。”

        - "***网络包的生命周期：Linux 系统收包流程***"
        #  1、当外部网络发送数据包到服务器时，首先由网卡 eth0 接收该数据包。
        #  2、网卡通过 DMA（Direct Memory Access，直接内存访问）技术，将数据包直接拷贝到内核中的 RingBuffer（环形缓冲区）等待 CPU 处理。RingBuffer 是一种首尾相接的环形数据结构，它的主要作用是作为缓冲区，缓解网卡接收数据的速度快于 CPU 处理数据的速度问题。
        #  3、数据包成功写入 RingBuffer 后，网卡产生 IRQ（Interrupt Request，硬件中断），通知内核有新的数据包到达。
        #  4、内核收到硬件中断后，立即调用对应的中断处理函数。通常情况下，中断处理函数会简单地标记有新数据到达，并唤醒 ksoftirqd 内核线程来处理软中断（SoftIRQ）。
        #  5、软中断处理过程中。内核调用网卡驱动提前在内核中注册的 NAPI（New API）poll 接口，从 RingBuffer 中提取数据包，并生成 skb（Socket Buffer）数据。skb 是 Linux 内核中用于管理网络数据包的主要结构。它包含了网络包的所有信息，包括头部、数据负载等，并在内核的各个网络协议层之间传递。
        #  6、skb 被传递到内核协议栈中进行处理。这里涉及多个网络层次的处理操作：
        #    1、网络层（L3 Network layer）：根据主机中的路由表，判断数据包路由到哪一个网络接口（Network Interface）。这里的网络接口可能是稍后介绍的虚拟设备，也可能是物理网卡 eth0 接口。
        #    2、传输层（L4 Transport layer）：如解/封数据包，处理网络地址转换（NAT）、连接跟踪（conntrack）等操作。
        #  7、内核协议栈处理完成后，数据包被传递到 socket 接收缓冲区。应用程序随后利用系统调用（如 Socket API）从缓冲区中读取数据。至此，整个收包过程结束。
        #  分析 Linux 系统处理网络数据包的过程，我们可以注意到潜在问题：数据包的处理流程过于冗长。整个处理流程涉及到多个网络层协议栈，如数据链路层、网络层、传输层和应用层。这些网络层之间传递数据需要封包/解包，以及频繁的操作系统上下文切换。

        - (segment, packet, frame, stream) “传输层的数据叫做段 segment，网络层的数据叫做包 packet，数据链路层的数据叫做帧 frame，物理层的数据叫做流 stream”
        #- 以太网数据包的大小是固定的，1500 字节的负载 +22 个字节的头信息=1522 字节
        #- IP 数据包在以太网数据包的负载里面，它也有自己的头信息，最少需要 20 个字节，所以 IP 数据包的负载最多为 1480 字节
        #- TCP 数据包在 IP 数据包里面。除去头信息，它的最大负载是 1460，如果超过最大负载，就需要在发送两个 TCP 数据包

        - 这块感觉不太对，怎么理解“只要是在网络上跑的包，都是完整的。可以有下层没上层，绝对不可能有上层没下层。所以，对 TCP 协议来说，三次握手也好，重试也好，只要想发出去包，就要有 IP 层和 MAC 层，不然是发不出去的。”这句话？我有一个疑问，真的“绝不可能有上层没下层”吗？那客户端发出的网络包不是自顶向下的吗？从应用层的 HTTP 头到 TCP 头到 IP 头，最后到 MAC 头。那这句话不就不对了吗？


    - topic: 数据链路层
      qs:
        - 数据链路的相关技术（共享介质型网络、非共享介质型网络、根据 MAC 地址转发、环路检测技术、VLAN）
        - ethernet-frame 或者称为“以太帧”，就是数据链路层的数据包。可以看到是比较简单的，比较核心的就是 MAC 的源地址和目标地址，以及 payload（数据包的 body，可以看到占据绝大部分内容，通常就是 IP 协议数据包）
        - 数据链路层除了Ethernet，还有哪些方法？ # 无线通信、PPP、Token Ring、FDDI、HDLC 分别是啥？
        - 数据链路层 # 封装解封装、编址（物理地址）、流量控制、链路连接的维护、帧同步、差错校验

        - "***为啥数据链路层也需要流控和差错控制啊？不是传输层主协议 TCP 协议才需要这些机制保证数据传输呢？“从数据链路层一直到应用层，每层都有类似流控和差错控制这样的保证可靠性和完整性的机制。”***" # 总结：1、数据链路层通过ARQ实现流控，通过CRC实现差错控制。2、IP 协议本身支持基本的数据校验功能，但是需要 ICMP 协议和 RSVP 协议保证可靠性。
        - "***“简单来说，数据链路层通过ARQ实现流控，通过CRC实现差错控制”***"
        #  流控：数据链路层通过自动重传请求（ARQ）机制来实现流控。ARQ允许接收方检测到错误帧时，可以请求发送方重新发送该帧，从而控制发送方的发送速率，避免接收方因处理不过来而丢失帧。
        #  差错控制：数据链路层通过循环冗余检查（CRC）等错误检测技术来实现差错控制。CRC是一种常用的错误检测方法，它通过在数据中添加冗余信息来检测数据传输过程中的错误，如果检测到错误，可以利用ARQ机制请求重传。

        - 数据链路层有哪些主要功能（处理比特传输发生的错误、、）？
        - 大家都广播数据包，会不会产生混乱？发送有没有先后规则？（信道划分、轮流发送、随机发送）
        #  - `信道划分`（分多个车道；每个车走一个车道）
        #  - `轮流发送`（单双号限行）
        #  - `随机发送`（先发出去，如果网络拥堵，就放弃，等网络环境好了之后再次发送 (ethernet 以太网就是用的这种方式)）

        - 如果发送的时候出现错误，怎么办？ *网络包里有个 CRC 参数 (循环冗余检测)，专门用来处理这种问题，通过 XOR 异或算法，来计算整个包是否在发送的过程中出现了错误*

        - "*随机访问MAC协议和轮转访问MAC协议 这两种不同的MAC(介质访问控制协议) 分别是啥? 有啥用?*"
        - "为啥有了IP，为啥还要 MAC？或者反过来，有了 MAC，为啥还要 IP？“IP 是地址，有定位功能；MAC 是身份证，无定位功能；CIDR 可以用来判断是不是本地人。” ***“IP 层解决远程定位问题，MAC 是没有远程定位功能的，只能通过本地 ARP 的方式找到。”***"

        - MAC 地址为什么是全局唯一的？ # mac 地址的前 24 位是设备制造商的标识符 OUI，后 24 位是自定义串 NIC。只要每个设备制造商都能保证在同一个命名空间的全部 MAC 地址唯一，就可以保证 MAC 全局唯一
        - 如何修改 MAC 地址？ # 只能通过 ifconfi 命令临时修改 MAC 地址，因为 MAC 地址和硬件是绑定的，一旦重启 OS，这些变更就会被系统撤销，想要让类似的变更永久生效需要在系统重启时执行相应的命令或者修改对应的网卡配置文件

    - topic: ARP
      qs:
        - ARP协议 (Address Resolution Protocol) 是啥? “用于实现从 IP 地址到 MAC 地址的映射，询问目标 IP 对应的 MAC 地址”、“也就是已知 IP 地址，求 MAC 地址的协议。通过解析网络层地址来查看数据链路层地址的网络传输协议*” # 用于实现从 IP 地址到 MAC 地址的映射，询问目标 IP 对应的 MAC 地址。也就是已知 IP 地址，求 MAC 地址的协议。通过解析网络层地址来查看数据链路层地址的网络传输协议。***注意：只有是同一个网段，才会发 ARP 请求，获取 MAC 地址。***
        - ARP 工作原理? （请求应答、广播请求单播回应）
        - ARP 是数据链路层还是网络层？ # 基于功能考虑，ARP 是数据链路层协议，基于分层和包封装来考虑，ARP 是网络层协议。(ICMP 协议同理)。需要注意的是，协议到底所属哪一层，可以从应用/功能来考虑，也可以从层次/包封装来考虑。
        - RARP协议是怎么实现的? # ARP地址解析协议 和 RARP逆地址解析协议 是数据链路层协议，是某些网络接口（如以太网和令牌环网）使用的特殊协议，用来转换 IP 层和链路层使用的地址。


    - topic: TCP 相关
      qs:
        - 【TCP 数据包的大小】（TCP 包包含了源地址、目标地址、序列号、确认号、checksum 等信息，用于确保数据的可靠传输）最大负载 146 个字节
        #- 首先，*数据包的结构，以太网数据包着 IP 数据包，IP 数据包包着 TCP 数据包*
        #- 以太网数据包的大小是固定的，1500 字节的负载 +22 个字节的头信息=1522 字节
        #- IP 数据包在以太网数据包的负载里面，它也有自己的头信息，最少需要 20 个字节，所以 IP 数据包的负载最多为 1480 字节
        #- TCP 数据包在 IP 数据包里面。除去头信息，它的最大负载是 1460，如果超过最大负载，就需要在发送两个 TCP 数据包
        - 【TCP 数据包的编号 (SEQ)】 # 编号的意义在于，丢包后，知道丢失的是哪个包。接收完成后，也可以按照编号还原数据。第一个包的编号是随机的，第二个包则是 +1，比如包 1 编号 100，第二个则为 101。
        - 【TCP 数据包的组装】 # 操作系统会自己实现对 TCP 数据包的组装，但是不会去处理 TCP 包里的数据。组装完成，就移交给应用程序，让应用程序去检查 # TCP segment. 5-tuple in TCP? The tuple (source IP address, source port, destination IP address, destination port, transport protocol). A 5-tuple uniquely identifies a UDP/TCP session.

        - TCP 的连接有这么多的状态，你知道如何在系统中查看某个连接的状态吗？ # 用 netstat 或者 lsof，grep 一下 establish listen close_wait 之类的这些看看
        - How to use nc(Netcat) to read and transfer data from TCP and UDP?

        - TCP中的拆包和粘包是怎么回事 # [怎么理解 TCP 粘包与拆包? - V2EX](https://www.v2ex.com/t/747735) # [分享文章： TCP 粘包？ TCP 警察什么梗 - V2EX](https://v2ex.com/t/876066) TCP是流式协议（压根就没有packet），拆包和粘包都是对HTTP来说的

        - "***client如何实现长连接?*** 还是要搞清楚TCP长连接和HTTP长连接" # [4.15 TCP Keepalive 和 HTTP Keep-Alive 是一个东西吗？ | 小林coding](https://xiaolincoding.com/network/3_tcp/tcp_http_keepalive.html)

        - 【TCP七种定时器】 # [搞懂TCP七种定时器——吊打面试官](https://mp.weixin.qq.com/s/1Yx1cVOMoMU_d-wX8hRoKQ)



    - topic: "***三次握手 & 四次挥手***"
      url: https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&amp;mid=2651439650&amp;idx=5&amp;sn=12764c4477f850c3f5f627545c45ec40
      picDir: kernel/np/tcp
      qs:
        - 【11 states of TCP】
        #- SYN（同步包），SYN 包用来初始化和建立连接。SYN 就是 synchronize sequence numbers 同步序列编号，是 TCP/IP 建立连接时使用的握手信号。在客户端和服务端建立 TCP 连接时，会首先发送 SYN 包。客户端接收到 SYN 包后，会在自己的段内生成一个随机值 X
        #- SYN-ACK（同步 - 确认包），本地的 SYN 消息和较早的 ACK 包。服务端收到 SYN 包后，打开客户端连接，发送一个 SYN-ACK 作为回答。确认号设置为比接收到的序号多一个，即 X+1，服务器为数据包选择的序列号是另一个随机数 Y
        #- ACK（确认包），用来帮助对方确认收到的 SYN 包。Acknowledge character, 确认字符，表示发来的数据已确认接收无误。最后，客户端将 ACK 发送给服务器。序列号被设置为所接收的确认值即 Y+1
        #- FIN（终止包），用于请求关闭连接，表示发送方不再发送数据
        #- 不重要的
        #  - URG：紧急指针有效标志位，当它被置为 1 时，紧急指针才有效
        #  - PSH（推送包）：用于向接收方传递数据，并告知接收方立即将数据交给上层应用程序处理。
        #  - RST（复位包）：用于强制中断连接，通常在发生错误或无法处理连接时使用。

        - 【三次握手】Three-Way handshake process? # (SYN queue, accept queue)

        - TCP半连接队列和全连接队列：TCP 三次握手的时候，Linux 内核会维护两个队列，分别是TCP半连接队列（SYN队列）和全连接队列（accept队列），分别是什么? # 服务端收到客户端发起的 SYN 请求后，内核会把该连接存储到半连接队列，并向客户端响应 SYN+ACK，接着客户端会返回 ACK，服务端收到第三次握手的 ACK 后，*内核会把连接从半连接队列移除，然后创建新的全连接，并将其添加到 accept 队列，等待进程调用 accept 函数时把连接取出来*

        - 【四次挥手】的过程?

        - 为什么建立连接要三次握手，而断开连接要四次？“简单来说，三次握手的第一步建立连接，后两步都是确认。四次挥手一样的，只不过因为 TCP 连接是全双工协议，需要双方互相确认断开连接，所以多了一步（也就是第一步断连，后面三步都是确认）。” # 因为 TCP 连接是全双工的，双方都需要告知对方关闭，告知时都需要经过一个来回。第二次和第三次挥手时，服务端的 ack+fin 不能一起回复，因为第一次挥手时，客户端告知了服务端要关闭连接，但是服务端可能还有数据要发给客户端处理，所以先回复一个 ack 的应答，在服务端主动关闭连接发起 fin 之前，还有剩余的数据包需要发送过。

        # [TIME_WAIT 和 CLOSE_WAIT 状态解析 | liuruijie](https://liuruijie87.github.io/2020/08/26/TIME_WAIT%E5%92%8CCLOSE_WAIT/)
        - "***为啥服务器有大量TIME_WAIT和CLOSE_WAIT状态的TCP连接? 这些conn是怎么产生的? 为什么 TCP 四次挥手，要等待 2MSL 才能关闭? How to resolve?***" # 总结：***如果存在大量的 TIMEWAIT，往往是因为短连接太多，不断的创建连接，然后释放连接，从而导致很多连接在这个状态，可能会导致无法发起新的连接。解决方案是打开 TCP_TW 的两个配置以及 tcp_timestamps 配置。***
        # TIME_WAIT 是主动断开连接的一方会出现的，与TIME_WAIT成对出现的是CLOSE_WAIT，CLOSE_WAIT出现在被动断开连接的一方，client和server都有可能出现。TIME_WAIT会持续2MSL（MSL默认2min，2MSL就是4min，但是可以自定义）之后关闭。所以在高并发场景中，一定会有大量TIME_WAIT状态。可想而知，如果大量出现这种状态，那么大量conn无法真正进入CLOSED状态，就会产生大量overhead，连接数直接被打满，无法创建新连接，导致client连接失败。解决方案也是类似的，最有效的方法当然是直接TCP长连接（而不是现在这种高并发场景导致的大量短连接）。如果无法使用长连接，那就“允许 time_wait 状态的连接可以被复用和减少 time_wait 的时间 (但是可能会存在旧连接数据包乱序和旧连接没有正常关闭的问题)”。
        - 为什么需要 TIME_WAIT？ # 客户端等待两个“最大数据段生命周期 Maximum Segment Lifetime, MSL”的时间之后会进入 CLOSED 状态，TIME_WAIT 一定是发生在主动关闭的一方，主动关闭一方需要等待 2MSL 才会最终关闭。原因：1、防止“被动关闭方”的延迟数据被窃取。2、防止“被动关闭方”没有收到最后的 ACK

        - 为什么会产生大量 TIME_WAIT?
        #- 有大量 tcp 短连接的存在：高并发下建立了许多 tcp 连接，完成业务处理后马上断开了连接，导致有大量处于 time_wait 状态的 tcp 连接
        #- tcp 四次挥手的关闭机制：因为存在"延迟的数据包数据"和"最后一次握手可能会发生丢包"这两种情况，所以有`time_wait`的机制去保证 tcp 连接能够正常关闭

        - 大量TIME_WAIT（可能导致连接数被用尽，无法创建新连接），所以我们需要怎么解决大量TIME_WAIT的问题？ # 1、服务端：允许 time_wait 状态的连接可以被复用和减少 time_wait 的时间 (但是可能会存在旧连接数据包乱序和旧连接没有正常关闭的问题)。2、客户端：尽量建立 TCP 长连接进行复用。

        - 为什么是 2MSL？ # 1、等待 2MSL 时间的核心原因是怕最后一个 ACK 包对方没有收到，那么对方在超时后会重发第三次握手的 FIN 包。2、主动关闭端接到重发的 FIN 包后，可以再发一个 ACK 应答包。
        - 服务端为啥出现大量 CLOSE_WAIT？ # 1、主子进程共享 socket。2、close 不能完全关闭

        - TCP 四次挥手中，客户端的 FIN_WAIT_2 状态如何处理乱序的报文? fin包为什么会是乱序的? kernel怎么解决这个问题的?
        #  场景描述：当四次挥手完成了前两次后会变成 FIN_WAIT_2 状态，服务端会把自身还在处理的数据知会给客户端后才会开始发起三次挥手 FIN。如果这个时候，服务端把剩余的数据发送给客户端的报文因为网络拥堵导致比 fin 包要晚到，这时候 fin 包的 seq 在客户端那边就会体现出是有乱序且有间隔的
        #  内核的解决方案是，会把这些乱序报文放到乱序队列里面，等前面晚到的报文到达后才会一起执行。也就是 tcp 的超时重传，以及滑动窗口的机制去保证 TCP 四次挥手中的可靠性

        - TFO (TCP-fastopen) 是怎么“把三次握手优化成了第一次握手就直接建立连接”? # TFO是TCP的一个可选配置项，就是通过FOC来给曾经建立过连接的conn来减少一次握手。

        - TCP的time-wait状态为什么等待2MSL而不是常规的RTO时间呢？ # 其实没有区别，一般 timewait 都会调小，等那么久很耗资源。理论上的 2MSL 是没问题的，但是工程中的 2MSL （ 2min ）更像是一个相对来说比较保险的经验值。在工程中，我的理解是，就算是 3MSL ， 4MSL 乃至 NMSL 都没法一定保证连接不会复活这件事。

    - topic: "***TCP可靠性（FC、EC、CC、ARQ）***"
      qs:
        - 基本认知：为啥TCP需要保证可靠性? # 从 [RFC 793 - Transmission Control Protocol](https://datatracker.ietf.org/doc/html/rfc793) 可知，从网络 IO 缓冲中读出来的数据必须是无损的、无冗余的、有序的、无间隔的*。但是，网络环境是复杂的，*默认会损坏、乱序、丢包、冗余*，所以会使用一整套手段来防止这些问题发生。所以，我们需要着重关注以下问题：差错控制（乱序问题）、流量控制、拥塞控制、ARQ 机制（丢包问题）、连接维护。
        - 为什么 TCP 协议在弱网环境下有严重的性能问题？ # 底层的数据传输协议在设计时必须要对`带宽的利用率`和`通信延迟`进行权衡和取舍，所以想要解决实际生产中的全部问题是不可能的，TCP 选择了充分利用带宽，为流量而设计，期望在尽可能短的时间内传输更多的数据。在弱网环境下（丢包率高）影响 TCP 性能的三个原因，`拥塞控制算法`是导致 TCP 在弱网环境下有着较差表现的首要原因，`三次握手`和`累计应答`两者的影响依次递减，但是也加剧了 TCP 的性能问题。

        - "***How does TCP ensure reliability? (FC（乱序问题）, EC, CC, ARQ（丢包问题）)***"
        #- EC 差错控制 (乱序问题) (checksum, seq, ack)
        #- FC 流量控制 (wnd, tag flow,)
        #- CC 拥塞控制 (packet-loss based, cwnd, ssthresh, slow start, fast retransmit)
        #- ARQ 机制 (丢包重传) (Fast Retransmit, After 3 DACK) 丢包重传机制和拥塞控制无关，二者都属于为了保证 TCP 可靠性的机制。丢包重传机制通常会在网络拥塞时触发，所以通常会被误认为是拥塞机制的一部分，这种认知是错误的。总结来说，专人专事，拥塞控制是用来判断网络是否拥塞的，而丢包重传是用来确保数据的可靠传输，而不是主动控制网络拥塞。
        #
        #如果拿微信发消息举例，EC就是首先得确保你发送的消息没有错别字、语法错误或者信息不连贯，没有颠三倒四、前言不搭后语吧。差错控制通常通过使用校验和、循环冗余校验（CRC）等技术来实现。
        #
        #- CC则是限制发送方发微信的总量，不让发送方一次发送太多（如果发送太多，就随机丢弃一些信息）。
        #- FC则是来匹配发送方和接收方的处理信息能力（网络好、接收方能力强就多发，否则就少发），所以需要给重要信息打个 tag，优先处理。
        #- 滑动窗口：是一种用于流量控制和拥塞控制的技术，它允许发送方在等待确认之前发送一定数量的数据。在微信聊天中，这可以想象为一个“缓冲区”，里面包含了你已经发送但还未得到确认的消息。如果缓冲区满了，你就需要等待一些消息得到确认后再继续发送。
        #- 拥塞窗口：是拥塞控制中的一个概念，它限制了发送方在任何给定时间可以发送的数据量。在微信聊天的比喻中，这相当于你根据网络状况和对方的接收能力来调整你一次能发送的消息数量。

        - "其中，*乱序问题和流量控制都是通过`滑动窗口`解决的*。滑动窗口就相当于你领导和你的工作备忘录，布置过的工作要有编号，干完了有反馈，活不能派太多，也不能太少。*拥塞控制是通过`拥塞窗口`来解决的。* 拿微信聊天解释上面几种概念"
        #- 差错控制就是你首先得保证说话没有颠三倒四、前言不搭后语吧
        #- 拥塞控制则是限制发送方发微信的总量，不让发送方一次发送太多（如果发送太多，就随机丢弃一些信息）
        #- 流量控制则是来匹配发送方和接收方的处理信息能力（网络好、接收方能力强就多发，否则就少发），所以需要给重要信息打个 tag，优先处理。

        - cwnd (Congestion Window) 和 ssthresh (Slow Start Threshold) 分别是什么? 这两个参数是怎么决定使用哪种限流算法的? Why? # 当 cwnd > ssthresh 使用慢启动算法，如果相反则停用慢启动算法，使用拥塞控制算法
        #  cwnd 代表拥塞窗口（Congestion Window），它是发送方在拥塞控制过程中可以发送的数据量。拥塞窗口的大小决定了发送方可以发送的数据包数量。发送方会根据网络的拥塞情况动态调整拥塞窗口的大小。
        #  ssthresh 代表慢启动门限（Slow Start Threshold），它是拥塞控制算法中的一个阈值。当拥塞窗口小于等于慢启动门限时，TCP 进入慢启动阶段，每经过一个往返时间（RTT），拥塞窗口大小会加倍。但当拥塞窗口大于慢启动门限时，TCP 进入拥塞避免阶段，每经过一个往返时间，拥塞窗口大小只增加一个数据包。
        #  当网络发生拥塞时，TCP 会根据拥塞情况设置新的慢启动门限值，同时将拥塞窗口大小重置为一个较小的值，进入拥塞避免阶段。这是为了减少发送方的发送速率，避免进一步加剧网络拥塞。

        #- 当 cwnd > ssthresh 使用慢启动算法
        #- 当 cwnd < ssthresh 使用拥塞控制算法，停用慢启动算法
        #- 当 cwnd = ssthresh 这两个算法都可以

        - What's Relay ACK (延迟应答), Cumulative ACK (累积应答), Duplicate ACK, Delayed ACK(延迟应答)?
        #- 确认应答就是 ACK 机制，不多说
        #- 延迟应答，延迟应答就是接收端收到数据之后，稍微等一会再应答，*发送端发好几次数据，接收端只需要一次来 ACK，这样可以降低网络拥塞的概率，提高数据的传输效率*
        #- 捎带应答，捎带应答就是接收端在给发送端发送数据的时候，捎带着向发送端发去 ACK，应答的内容是接收端已经收到发送端发送的数据

        - 拥塞控制 params # (RTT, RT0(Retransmit Timeout), cwnd)
        - "***概述并且对比一下不同TCP versions的实现和性能 (Reno, Cubic, BBR, bbr2, iperf3, vegas, Westwood, BIC)***" # 如果拿用迅雷来举例，Reno/CUBIC就是勾选“下载优先”，而Vegas则是“上网优先”，那BBR就是“智能模式”。BBR算法相比Reno和CUBIC这种基于AIMD快速达到bandwidth最大流量的算法（但是这个对其他TCP连接则不利），更智能地利用网络带宽，通过实时监测网络延迟和动态调整发送速率来减少拥塞，而不是仅依赖丢包信号来调整，从而提高网络传输效率和公平性。

        - 传统拥塞控制：在 BBR 出现之前（比如Reno 和 CUBIC 是基于事件驱动的，以丢包作为探测依据的拥塞算法），拥塞控制分为四个部分：慢启动、拥塞避免、快速重传、快速恢复 各自具体流程？以及传统拥塞控制的问题？ # 如果瓶颈路由器的缓存特别大，那么这种将会导致严重的性能问题，*TCP 链路上长时间 RTT 变大，但吞吐量维持不变
        #- 以较低的发送速率开始，以每 RTT 两倍的速度来增加发送速率，直到到达瓶颈，这个阶段称为“慢启动”
        #- 到达瓶颈后，开始降速，线性提高发送速率，直到发生丢包，这个阶段就是“拥塞避免”
        #- 丢包后，`线增积减`开始大幅降速，针对丢包使用`快速重传算法`重新发送，同时也使用`快速恢复算法`把发送速率尽量平滑地拉上来

        - TCP默认使用哪种Concurrency Control算法呢? # 我们所说的TCP Version实际上指的是Congestion Control机制，TCP本身没有默认的拥塞控制算法，我们可以在kernel中自定义。
        - AIMD 线增积减 # 线性增长（经过 1 个 RTT，拥塞窗口大小会加 1）、积性减少（当发送方发送的数据包丢包时，拥塞控制阈值会减半）
        - TCP 的流量控制和拥塞控制有什么区别？
        #- `拥塞控制`congestion control
        #  - 针对问题：针对于*全局网络的控制*
        #  - 解决方案：*只控制一个网络中的整体流量速率*，具体做法是`随机丢弃流量`，比如说如果被丢弃的是 TCP 流量，那么被丢弃的 packet 所对应的 TCP session 流量就会下来，这样就会让总体的流量降下来
        #- `流量控制`flow control
        #  - 针对问题：针对的是*发送方和接收方速度不匹配的问题*，比如经典的`fast sender and slow receiver问题`（接收方缓存大小与发送速率不匹配），提供一种速度匹配服务遏制发送速率使接收方应用程序的读取速率与之相适应。
        #  - 解决方案：*给不同的流量分类，打上不同的优先级，优先级从高到低；优先保障重要流量的传输，不管其他流量丢不丢包*

    - topic: SWS (Silly Window Syndrome)
      qs:
        - 要谈 nagle，首先要谈 SWS，SWS 是指发送方发送数据包的效率降低，带宽利用率下降。但是 SWS 并不是正常场景下 TCP 会产生的问题，可以认为是“弱网场景”（网络延迟高、接收方窗口小、发送方数据太多）下才会产生的问题，我们通常使用以下方法来解决这个问题：nagle和延迟确认（Delayed Acknowledgment）机制（就是延迟应答）。

        - What's SWS? 为什么会SWS? # 弱网环境这个经典场景所产生的现象（接收方的接收能力变差，导致窗口大小缩小，使得发送方发送的数据包中有效载荷非常小，大部分是TCP报文头部，从而造成带宽的浪费）
        #  当发送方发送的数据包大小小于接收方窗口的大小时，就会出现 Silly Window Syndrome（SWS）问题。这种情况下，发送方的数据包可能会被接收方的窗口大小所限制，从而导致发送方的发送效率低下。
        #  具体来说，当发送方发送一个较小的数据包时，接收方会确认收到这个数据包，并通知发送方可以发送下一个数据包。但是，由于接收方窗口的大小限制，发送方在得到确认之前不会发送下一个数据包。这样就导致了发送方发送数据的速率被限制，无法充分利用可用的带宽。
        #  举个例子来说明：假设接收方的窗口大小为 1000 字节，发送方发送了一个只有 100 字节的数据包。接收方收到了这个数据包，并发送确认给发送方。但由于接收方窗口大小为 1000 字节，发送方在接收到确认之前不会发送下一个数据包，导致发送方的发送速率受到限制。
        #  SWS 问题会导致网络带宽的浪费和传输延迟的增加。为了解决这个问题，一种常见的方法是使用延迟确认（Delayed Acknowledgment）机制，即接收方在一定时间内等待多个数据包后才发送确认，从而减少确认的数量，提高发送效率。
        #  总结一下，SWS 问题是由于发送方的数据包大小小于接收方窗口大小所导致的发送效率低下的问题。通过使用延迟确认等机制，可以减少 SWS 问题的发生，提高网络的性能和利用率。

        - How to resolve? (nagle, D-ACK) # 可以通过接收方和发送方的策略来避免SWS。接收方可以在窗口大小小于某个阈值时关闭窗口，直到有足够的缓冲区空间再打开。发送方可以使用Nagle算法，延迟发送小数据包，直到满足特定条件，如已收到先前数据的确认或有足够的数据积累。
        - Nagle算法是什么，如何工作？ “Nagle属于流量控制” # Nagle算法是一种发送方策略，用于减少小数据包的发送。它通过延迟发送小数据包，直到满足两个条件之一：已发送的数据都已经收到确认应答，或者积累的数据大小达到或超过最大段长度（MSS）。这样可以减少发送的总数据包数量，提高网络效率。Nagle 是 TCP 的一个配置项，用来做“小包发送的优化”，具体来说，就是让发送方累计数据，一次发送。*还是拿微信聊天举例，就是让发送方别老发“hhhh”、“然后呢？”之类的这些零碎话，或者把一整段文字一句一条发出来。让他把多条文字整合成段，再发出来。* 这样就会导致接收方出现延迟。
        - 为啥说“是否开启Nagle是个取舍问题”? # 这是个取舍问题，如果默认开启 Nagle 就会导致延迟，如果禁用 Nagle 又可能导致 SWS。因此，大部分场景下都应该开启 Nagle，如果是视频通话/网游等低延迟场景，则应该关闭 Nagle。
        - 怎么给TCP配置Nagle?

    - topic: UDP
      picDir: kernel/np/udp
      qs:
        - What's UDP? How does it works? # listening port, conn
        - "***从 header struct, features, pros and cons, usage scenarios 对比一下TCP和UDP?***" # TCP和UDP的区别，归根到底是假设场景不同。从RFC 793 可知，从网络 IO 缓冲中读出来的数据必须是无损的、无冗余的、有序的、无间隔的。但是，网络环境是复杂的，默认会损坏、乱序、丢包、冗余，所以会使用一整套手段来防止这些问题发生。
        - TCP 和 UDP 区别：对比了一下 UDP 和 TCP 的 header segment，为啥TCP使用offset，而不是UDP那样的length? # 结论：TCP的offset就相当于UDP的length。TCP 的头部（header）确实没有专门的字段用于表示段（segment）的长度。这是因为 TCP 使用另一个字段来表示数据的长度，即 TCP 头部中的"数据偏移"（Data Offset）字段。TCP 头部的数据偏移字段指示了 TCP 头部的长度，以 4 字节为单位。由于 TCP 头部的长度是固定的（通常为 20 个字节），因此可以通过减去 20 来计算出数据部分的长度。这个设计决策简化了 TCP 协议的实现，并且在数据的传输过程中不需要传输额外的长度信息。当接收方收到 TCP 段时，它可以根据接收到的 TCP 头部中的数据偏移字段计算出数据部分的长度，从而正确地处理和重组数据。总之，TCP 通过使用固定的头部长度和数据偏移字段来管理数据的长度，而不需要额外的段长度字段。这样可以减少头部的开销，并提高协议的效率和可靠性。

        #- *UDP 是无连接协议，TCP 是面向连接的协议*（两个对等端内部网之间直接建立逻辑连接）（我们都说 TCP 是面向连接的传输协议，但是网络传输都是没有连接的，包括 TCP 也是一样。TCP 所谓的“连接”，其实就是通讯双方维护的一个“连接状态”，让它看上去像是有连接一样。所以，TCP 的状态转移是非常重要的。）
        #    - TCP 是面向连接的，虽然网络的不安全不稳定特性决定了多少次握手都不能保证连接的可靠性。但是 TCP 的三次握手能够最低限度上保证连接的可靠性。
        #    - UDP 不是面向连接的，UDP 传送数据前并不与对方建立连接，对接收到的数据也不发送确认信号。发送端没有收到通知，当然也不用重发，所以说 UDP 是无连接的，不可靠的一种数据传输协议。
        #- *TCP 比 UDP 安全*TCP 通过跟踪数据的传送，并确认和跟踪序号来确保它成功到达接收方。（TCP 为了实现网络通信的可靠性，使用了复杂的拥塞控制算法，建立了繁琐的握手过程以及重传策略。由于 TCP 内置在系统协议栈中，极难对其进行改进）
        #- *相应的，UDP 比 TCP 传输速度更快，实时性更高，网络带宽需求更小，功耗更低*（虽然 TCP 协议中植入了各种安全保障功能，但是在实际执行的过程中会占用大量的系统开销，无疑使速度受到严重的影响。反观 UDP 由于排除了信息可靠传递机制，将安全和排序等功能移交给上层应用来完成，极大降低了执行时间，使速度得到了保证。）
        #- TCP 保证数据包的发送一定到达
        #- TCP 保证数据包的完整性
        #- TCP 是面向字节流的
        #- TCP 有拥塞控制机制

        - QUIC/KCP/ENET 这几种UDP优化方案分别是啥？各自有啥区别？ # [KCP协议：从TCP到UDP家族QUIC/KCP/ENET-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1964393)

    - topic: KCP
      qs:
        - KCP 协议是什么？ # KCP 的设计目标是为了解决在网络拥堵的情况下 TCP 传输速度慢的问题，TCP 保证数据准确交付，UDP 保证数据快速到达，KCP 则是两种协议的一个折中，*KCP 基于 UDP 的 ARQ 协议实现，在尽可能快的情况下保证可靠性* KCP 没有规定下层传输协议，但通常使用 UDP 来实现，至于原因，非常有必要说明，如果不清楚，就不能够真正地了解 KCP。另外，KCP 协议可以以非侵入的方式集成到大部分现有网络传输方案中，因为 KCP 只是算法实现。

        - KCP 协议有哪些特点？
        #- `RTO不翻倍`，TCP 超时 RTO 更新直接 x2，而 KCP 开启快速模式只 x1.5，很厉害
        #- `选择性重传`，只传输丢失的数据包，TCP 会全部重传丢包之后的全部数据包
        #- `快速重传`，不会等到超时
        #- `非延迟ACK`，TCP 会延迟发送 ACK，KCP 可以设置是否延迟
        #- `KCP协议，除了单独的ACK包外，所有包都有UNA信息`，ARQ 模型响应有两种，UNA（此编号前所有包已收到，如 TCP）和 ACK（该编号包已收到），光用 UNA 将导致全部重传，光用 ACK 则丢失成本太高，以往协议都是二选其一
        #- `非退让流控`，发送窗口可以只取决于发送缓存大小和接收端剩余接收缓存大小，KCP 正常模式同 TCP 一样使用公平退让法则，即发送窗口大小由：`发送缓存大小`、`接收端剩余接收缓存大小`、`丢包退让`及`慢启动`这四要素决定。但传送及时性要求很高的小数据时，可选择通过配置跳过后两步，仅用前两项来控制发送频率。

        - How does it works? KCP报文结构？“KCP 的 ARQ 机制与 TCP 类似，只是部分策略不同，学习 KCP 也有利于我们理解 TCP”
        - KCP 传输数据的流程？能否聊聊 KCP 协议的源码细节？(这里只写大概，具体流程需要看源码) # KCP 为了实现选择性重传（ARQ），会维护一个接收窗口（滑动窗口）。如果收到有序数据会将其放到接收队列，以待应用层消费。如果存在包丢失，会判断。超过设置的次数，会让其选择重传对应的包。其实就是通过一个 rcv_nxt（接收窗口当前偏移）来判断当前需要接受的数据包。如果收到的包在窗口范围，但是不是 rcv_nxt。先保存，等包连续之后才会将连续的数据包放入到接受队列供应用层消费。同样网络不好的情况，KCP 也会实现拥塞控制，限制发送端的包。

        - Compare KCP and TCP? 两者最核心的区别是什么? # (FC, CC, EC) (UDP+ARQ, RT0, )

    - topic: ARQ丢包重传机制
      qs:
        - ARQ协议(Automatic Repeat-reQuest)是啥？ # ARQ是数据链路层的一种错误纠正协议，它通过使用确认和超时两个机制，在不可靠的网络上实现可靠的信息传输。
        - 丢包重传机制通常会在网络拥塞时触发，所以通常会被误认为是拥塞机制的一部分，这种认知是错误的。总结来说，*专人专事，拥塞控制是用来判断网络是否拥塞的，而丢包重传则是已经丢包之后才触发的*
        - How does ARQ (Automatic Repeat-reQuest) works? ARQ 的实现机制? # ARQ协议是数据链路层的一种错误纠正协议，它通过使用确认和超时两个机制，在不可靠的网络上实现可靠的信息传输。实现机制：错误侦查、正面确认、超时重传、负面确认续传
        - ARQ协议分为哪几种? (Stop-and-Wait ARQ, Go-Back-N ARQ, Selective Repeat ARQ(SACK)) # 停等式、选择性重传、回退
        - ARQ 的实现机制（错误侦查、正面确认、超时重传、负面确认续传）？



    - topic: IP协议基本认知（IP数据包、IP版本、单播组播广播、）
      picDir: kernel/np/IP/IP
      qs:
        - IP 协议是什么？解决了什么问题？IP 地址是什么？
        #- IP 协议是网络层协议，这里提纲挈领地提两个观点：
        #- *IP 协议是网络层协议的主协议，网络层其他协议都是为了拓展和保障 IP 协议存在的*
        #- *IP 不是面向连接的，从设计时，就被认为是不可靠的，是尽力而为的协议*，所以同一个连接的每个 IP 包都可以自由选择路径，到每一个路由器，都自己去找下一跳，丢了就丢了，是*靠上一层 TCP 的重发来保证可靠性*
        #- *IP 协议解决了多个局域网的通信问题。IP 地址就是 IP 协议定义的一套地址规则*

        - 我总结一下 IP 协议的核心知识点（我总觉得自己对这块知识的掌握比较一般）
        #- IP 地址，比如什么 CIDR、子网掩码、广播地址、组播地址、环回接口 loopback
        #- IP 包
        #- 路由协议族，RIP、OSPF、BGP
        #- ICMP 协议
        #- 除此之外还有 ARP 协议、DHCP 协议、PXE

        - 分组交换：IP 协议采用分组交换的方式，在发送数据时将数据包分成多个较小的数据段，然后通过网络独立传输，再在目的地重新组装。
        - “IP协议真正解决的是多个LAN的通信问题。IP地址就是IP协议定义的一套地址规则。”
        - “但是尝试解决可靠性问题，并不意味着他本身就是可靠的”。这句话太精辟了。
        - 比如说“IP不是面向连接的，从设计时，就被认为是不可靠的，是尽力而为的协议，所以同一个连接的每个 IP 包都可以自由选择路径，到每一个路由器，都自己去找下一跳，丢了就丢了，是靠上一层 TCP 的重发来保证可靠性”
        - 如果最后一跳的时候，IP 改变了怎么办？ # 对于 IP 层来讲，当包到达最后一跳的时候，原来的 IP 不存在了。比如网线拔掉了，或者服务器直接宕机了，则 ARP 就找不到了，所以这个包就会发送失败了。对于 IP 层的工作就结束了。但是 IP 层之上还有 TCP 层，TCP 会重试的，包还是会重新发送，但是如果服务器没有启动起来，超过一定的次数，最终放弃。

        - 网络号、IP 地址、子网掩码和广播地址的先后关系是什么？
        #  当在一个数据中心或者一个办公室规划一个网络的时候，首先是网络管理员规划网段，一般是根据将来要容纳的机器数量来规划，一旦定了，以后就不好变了。
        #  假如你在一个小公司里，总共就没几台机器，对于私有地址，一般选择 ***********/24 就可以了。
        #  这个时候先有的是网络号。192.168.0 就是网络号。有了网络号，子网掩码同时也就有了，就是前面都是网络号的是 1，其他的是 0，广播地址也有了，除了网络号之外都是 1。
        #  当规划完网络的时候，一般这个网络里面的第一个、第二个地址被默认网关 DHCP 服务器占用，你自己创建的机器，只要和其他的不冲突就可以了，当然你也可以让 DHCP 服务自动配置。
        #  规划网络原来都是网络管理员的事情。有了公有云之后，一般有个概念虚拟网络（VPC），鼠标一点就能创建一个网络，网络完全软件化了，任何人都可以做网络规划。

        - 从功能上来分类地址类型。基本上可以分为三类（网络地址、广播地址、主机地址） # 就好像上面说的，全 0 和全 1 都是特殊的。全 0 是网络地址。全 1 是广播地址。
        #  网络地址 - 它是系统中一组设备或一组 IP 地址的标识符。网络地址有点像我们的邮政编码，而没有与之关联的街道地址。邮政编码代表一个地理区域。我们的网络地址代表 IP 地址范围。网络地址有时称为网络前缀，或简称为前缀。
        #  广播地址 是第二种地址。广播地址是网络上所有设备的标识符。举个例子来说，在美国的话，纸质的邮件还是非常普及的。所以每家都会经常收到纸质的广告，比如说某某超市商品促销了之类的。这个就是广播，你会发现住在某一个区域的住户都会收到相同的广告促销。这个就有点像是广播地址。如果我有一个小公司想要发类似的广告，只需要去邮局然后说，我想把这个广告发给邮政编码 xxxxx 的住户，然后邮局就会帮我去投放了。广播地址的目的是可以一次将消息发送到网络上所有设备的地址。
        #  主机地址 是第三种。主机地址是确认在网络中的独一的设备。比如一台电脑，打印机或者是一个路由器。如果我们有一台设备比如说电脑想有一个 IP 地址。那么该计算机必须具有主机地址，并且不能为其分配网络地址或广播地址。前两种地址主要用于描述我们的网络，但是主机地址才是我们需要显示的应用到我们的设备上。

        - 单播、组播和广播的意义和原理是什么？
        #  在《TCP/IP 详解》这本书里面，有两章讲了广播、多播以及 IGMP。广播和组播分为两个层面，其中 MAC 层有广播和组播对应的地址，IP 层也有自己的广播地址和组播地址。
        #  广播相对比较简单，MAC 层的广播为 ff:ff:ff:ff:ff:ff，IP 层指向子网的广播地址为主机号为全 1 且有特定子网号的地址。
        #  组播复杂一些，MAC 层中，当地址中最高字节的最低位设置为 1 时，表示该地址是一个组播地址，用十六进制可表示为 01:00:00:00:00:00。IP 层中，组播地址为 D 类 IP 地址，当 IP 地址为组播地址的时候，有一个算法可以计算出对应的 MAC 层地址。
        #  多播进程将目的 IP 地址指明为多播地址，设备驱动程序将它转换为相应的以太网地址，然后把数据发送出去。这些接收进程必须通知它们的 IP 层，它们想接收的发给定多播地址的数据报，并且设备驱动程序必须能够接收这些多播帧。这个过程就是“加入一个多播组”。
        #  当多播跨越路由器的时候，需要通过 IGMP 协议告诉多播路由器，多播数据包应该如何转发。

        - STP 协议能够很好地解决环路问题，但是也有它的缺点，你能举几个例子吗？
        - "***IPv4和IPv6的header有啥区别***"
        - 具体聊聊IP header每个字段的具体作用?
        - IP addr, Network addr, Broadcast addr

    - topic: 路由选择协议 (Dynamic Routing)
      qs:
        - 路由选择协议是啥？根据 AS(自治系统)，分为哪几种？IGP和EGP有啥区别？本质是graph的负权边和负权环问题
        #- 内部网关协议（IGP）：即在一个 AS 内部使用的路由选择协议，而这与互联网中其他 AS 选用什么路由协议无关。比如：OSPF。
        #- 外部网关协议（EGP）：若源主机和目的主机不再同一个 AS 中，就需要使用一种协议将路由选择信息传递到另一个 AS 中，这就是 EGP。比如：BGP。

        - What about 'dynamic routing protocols'?
        - OSPF、BGP、RIP 和 graph
        - 其实按照我的理解，OSPF 和 BGP 的本质区别在于能否解决负权边问题是吗？OSPF 使用 Dijkstra 算法，BGP 使用 Bellman-Ford 算法，这两个算法的核心区别就是是否能够解决负权边和负权环问题。AS 就是这里的负权边嘛。还是拿收发邮件举例来说明，静态路由就是您自己在小区（局域网）里溜达问题着玩，但是除了小区，就要用导航（网关）了，网关会（根据 OSPF、BGP、RIP 等路由协议）自动更新路由表，自动规划出最合理的路由。
        #- RIP 协议是一种动态路由选择协议。路由器定时与相邻路由器交换路由表，根据对方发送的路由表更新自身的路由表，从而动态地更新整个 AS 内的所有路由器的路由表
        #- OSPF 是基于链路状态计算路由的。OSPF 属于 IGP 协议，是链路状态路由协议，一般运行在 AS 自治系统内部，采用 SPF 算法保证了在 AS 内部不会产生环路；由于 OSPF 协议是每台路由器自己计算出来的，所以过滤路由非常麻烦

        #  RIP（Routing Information Protocol）是一种距离向量路由协议，主要用于小型网络中。它使用跳数（hop count）作为衡量路径优劣的指标，并通过将路由表信息广播到相邻路由器来实现路由的学习和更新。RIP 协议对网络的规模和拓扑变化的适应性较差，因此在大型网络中使用较少。相比之下，OSPF（Open Shortest Path First）和 BGP（Border Gateway Protocol）是链路状态路由协议，具有更高的灵活性和可扩展性。OSPF 协议使用链路状态数据库和 Dijkstra 算法来计算最短路径，适用于中等规模的企业网络。BGP 协议是一个可实现自治系统之间路由选择的协议，主要用于互联网的外部路由选择。
        #  总的来说，RIP 协议适用于小型网络，使用跳数作为路径选择的度量标准；OSPF 协议适用于中等规模的企业网络，使用链路状态数据库和最短路径算法；BGP 协议适用于互联网的外部路由选择。

        - 那在网络层发送数据包时，会自动选择 OSPF 或者 BGP 吗？
        - 为啥我们可以把 AS 自治系统（Autonomous System）直接理解为负权环（Negative Weight Cycle）呢？
        #  将自治系统（AS，Autonomous System）理解为负权环（Negative Weight Cycle）是一种简化的比喻，用于描述 AS 路径选择中的一种情况。
        #  在路由协议中，AS 是互联网中的一个独立管理的网络运营域，它可以包含多个网络设备和路由器。AS 之间通过边界路由器（Border Router）相互连接，并通过路由协议来交换路由信息。
        #  负权环是一个图论概念，指的是在有向加权图中存在一条环路，其路径上的权重之和为负值。在这种情况下，如果一个数据包在该环路上进行无限循环，其累积的权重会不断减小，形成负权环。
        #  在 AS 路径选择中，路由协议（如 BGP）使用了一种叫做"最短路径优先"（Shortest Path First）的算法来选择最佳路径。这个算法会根据路径的权重或者距离来选择最短路径。然而，当存在负权环时，算法可能无法收敛，因为它会不断选择累积权重为负的环路，导致无限循环。
        #  将 AS 自治系统理解为负权环是为了形象地描述这种情况。当 AS 之间存在某种问题或配置错误时，可能会导致路由协议选择了一个负权环，从而导致数据包在网络中无限循环，无法正确路由。
        #  需要注意的是，实际的 AS 路径选择是复杂而动态的，涉及到多个因素和策略。尽管负权环是一种可能的情况，但它并不是 AS 路径选择中的唯一问题。

        - RIP 协议
        - OSPF 协议（开放式最短路径优先）是啥？防环路机制？
        - BGP：多线 BGP 机房是怎么回事儿？ # BGP 主要用于互联网 AS 自治系统之间的互联，BGP 的最主要功能在于控制路由的传播和选择最好的路由。各大运营商都具有 AS 号，全国各大网络运营商多数都是通过 BGP 协议与自身的 AS 来实现多线互联的。使用此方案来实现多线路互联，IDC 需要在 CNNIC（中国互联网信息中心）或 APNIC（亚太网络信息中心）申请自己的 IP 地址段和 AS 号，然后通过 BGP 协议将此段 IP 地址广播到其它的网络运营商的网络中。使用 BGP 协议互联后，网络运营商的所有骨干路由设备将会判断到 IDC 机房 IP 段的最佳路由，以保证不同网络运营商用户的高速访问。

        - 路由协议要在路由器之间交换信息，这些信息的交换还需要走路由吗？不是死锁了吗？
        #  OSPF 是直接基于 IP 协议发送的，而且 OSPF 的包都是发给邻居的，也即只有一跳，不会中间经过路由设备。BGP 是基于 TCP 协议的，在 BGP peer 之间交换信息。
        #  RIP 是 UDP 协议，OSPF 直接发 IP 包。而 BGP 使用 TCP 协议，路由器之间会建立 TCP 连接，每 60s 发送一次 keep-alive 消息。

        - Compare BGP and OSPF? # (环路问题, negative weight edges) (Dijkstra) (BGP sponsor exchange msg, )
        - What's NAT (Network Address Translation)? # 用来改我们的源 IP 地址。例如我们网络上的内部 IP 地址，并且在出 Internet 时将其替换为自己的公共 IP 地址。也就是用来吧把内网IP改成公网IP。

    - topic: ICMP（查询报文、差错报文）
      picDir: kernel/np/ip/ICMP
      qs:
        - ICMP协议是啥？两种ICMP报文（查询报文（用来查询是否有问题，包括 request 请求和 reply 回复两种）、差错报文（用来通知出错原因，包括目标不可达、原点抑制、重定向、超时几种错误类型））
        # ICMP 报文是封装在 IP 包里面的。因为传输指令的时候，肯定需要源地址和目标地址。它本身非常简单。因为作为侦查兵，要轻装上阵，不能携带大量的包袱。
        #- *ICMP 是控制报文协议，是 IP 协议的附属协议*，ICMP 报文是封装在 IP 包里的，因为传输指令的时候，肯定需要源地址和目标地址，ICMP 只携带很少的数据
        #- 数据包在异常复杂的网络环境中传输时，常常会遇到各种问题，当遇到问题时，传输失败，但是一定要返回“错误信息”，这样才能根据实际情况，调整传输策略
        #- IP 用 ICMP 来与其他主机或路由器交换`错误报文`和其他重要信息，虽然 ICMP 主要用于 IP，但是其他程序也可以访问 ICMP。
        #- ICMP 的功能：*确认 IP 包是否成功送达目标地址，报告发送过程中 IP 包被废弃的原因*，在 IP 通信中，如果某个 IP 包没有到达目标地址，将由 ICMP 负责通知具体原因

        - ICMP 两种报文（查询报文和差错报文），其实可以理解为主动和被动两种获得消息的方法。

        - 差错报文的四种类型（unreachable 目标不可达、redirect msg 路由重定向（也就是下次发给下一个路由器）、source quench 源站抑制（就是让源站放慢发送速度）、time exceeded 超时（就是超过网络包的生存时间还是没到））分别是啥?
        - 我举几个 ICMP 差错报文的例子：终点不可达为 3，源抑制为 4，超时为 11，重定向为 5。这些都是什么意思呢？我给你具体解释一下。

        - "***traceroute 的原理（为啥 traceroute 故意不设置分片，从而确定路径的 MTU?）? traceroute 的时候，都发生了什么?***"
        #  (ttl=1/2/.../n, invalid UDP port(unreachable), MTU?) 设置TTL递增的（非法端口号的）UDP包，直到返回“端口不可达”错误的ICMP报文。之所以这么搞，是因为UDP是无连接的，它允许我们即使在没有建立连接的情况下也能发送数据包并接收错误响应，从而探测网络环境。如果不报错就无法真正探测网络环境。类比一下就是给佛祖送《道德经》，肯定会被打回来，这下就知道target机器的IP了。如果送《金刚经》，人家直接遁入空门了。
        #- `使用 TTL 追踪路由器 IP`traceroute 的第一个作用就是故意设置特殊的 ttl，来追踪来往目的地沿途经过的路由器，具体来说，*traceroute 会发送 N 个 (ttl 从 1 到 N 的)UDP 包，直到到达目标主机，这样就拿到了路径上所有路由器的 IP*。另外，怎么知道 UDP 有没有到达目标主机呢？traceroute 会选择一个`非法值`作为`UDP 端口号`，UDP 包到达后，目标主机就会返回一个`目标不可达`的`差错报文`
        #- `路径 MTU 发现`traceroute 还有一个作用就是故意设置不分片，*从而确定路径的 MTU*。

        - 为啥 traceroute 使用 UDP 协议呢？相当于（traceroute 使用）UDP 就能够保证最小可用性是吧，如果 traceroute 用 TCP 就太重了

        - traceroute 是不是无法获取精确的延迟数据? Why? 怎么解决该问题? # 因为traceroute主要设计用来追踪数据包的路由路径，而不是精确测量延迟。

        - "***ping 的原理? (ECHO REQUEST, ECHO REPLY) flag和seq，具体就是三部分***"
        #- *构建数据包并发送*，机器 A*构建一个`ICMP 回送请求报文`+`IP 头`+`MAC 头`的数据包，并发送出去*，其中 IP 头包含了`源 IP 地址`和`目标 IP 地址`，MAC 头包含了`源 MAC 地址`和`目标 MAC 地址`(通过 ARP 协议获得)
        #- *对比 MAC 头并发送应答包*，机器 B 收到数据包后，对比其中的`目标 MAC 地址`是否与本机相同，相同则接受，不同则丢弃，如果接受到的话，会构建一个`ICMP 回送响应报文`，再发送给机器 A
        #- *接收应答包*，在规定时间内，机器 A 如果收到 ICMP 的应答包，则说明机器 B 可达，否则不可达

        - ICMP 差错报文是谁发送的呢？ # ICMP 包是由内核返回的，在内核中，有一个函数用于发送 ICMP 的包。关键字 icmp_send

        - 当发送的报文出问题的时候，会发送一个 ICMP 的差错报文来报告错误，但是如果 ICMP 的差错报文也出问题了呢（其实就是一些边界条件）？
        #  我总结了一下，不会导致产生 ICMP 差错报文的有：
        #- ICMP 差错报文（ICMP 查询报文可能会产生 ICMP 差错报文）；
        #- 目的地址是广播地址或多播地址的 IP 数据报；
        #- 作为链路层广播的数据报；
        #- 不是 IP 分片的第一片；
        #- 源地址不是单个主机的数据报。这就是说，源地址不能为零地址、环回地址、广播地址或多播地址。

        - How does MTR works? # MTR 相当于 traceroute + ping，traceroute 无法获取精确的延迟数据，所以我们通常都会像 MTR 这样组合使用，在 traceroute 的每个节点持续进行 ping 操作，来获取更精确的延迟数据。


    - topic: structure of HTTP? (header, body)
      qs:
        - 为啥说“HTTP 协议的核心部分是传输的报文内容”？ # HTTP 协议在规范文档里详细定义了报文的格式，规定了组成部分，解析规则，还有处理策略，所以可以在 TCP/IP 层之上实现更灵活丰富的功能，例如连接控制，缓存管理、数据编码、内容协商等等。HTTP 相关的知识点，无非是状态码、动词、header，其实就是 `HTTP Message`，也就是 HTTP 报文。再就是基于这些的 HTTP 优化方法（比如 HTTP 缓存、压缩等）。

        - 报文里有请求头（起始行 start line）、响应头（头部 header）、空行和 body，响应头里有动词和状态码。

        - URL structure (protocol, domain, port, path, query parameters, TLD)
        - http 状态码、header字段、动词
        - HTTP动词：标准 HTTP 动词就 8 种，除此之外还有一些拓展方法（这些扩展方法实际上是一些第三方服务提供的，比如webdav就提供了MKCOL、MOVE等动词（本质上还是API）） # [HTTP Messages - HTTP | MDN](https://developer.mozilla.org/en-US/docs/Web/HTTP/Messages)
        - http get跟head

    - topic: HTTP Header Fields
      url: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers
      qs:
        - "***HTTP headers type? 分别有啥用? 这4种type (通用 general, 请求 request, 响应 response, 实体 entity) 下各自有哪些字段?***" # [HTTP头字段 - 维基百科，自由的百科全书](https://zh.wikipedia.org/zh-cn/HTTP%E5%A4%B4%E5%AD%97%E6%AE%B5)
        # RFC2616定义了HTTP/1.1的47个fields，之后还有RFC4229等添加其他fields，现在归IANA管理
        #- 通用 header（general header fields, 请求报文和响应报文都会使用的 header）
        #- 请求 header（request header fields, 从客户端向服务器端发送请求报文时使用的 header；补充了请求的附加内容，客户端信息，响应内容相关优先级等信息）
        #- 响应 header（response header fields, 从服务器端向客户端返回响应报文时使用的 header；补充了响应的附加内容，也会要求客户端附加额外的内容信息）
        #- 实体 header（entity header fields, 针对请求报文和响应报文的实体部分使用的 header；补充了资源内容更新时间等与实体有关的信息）

        - Keep-Alive 有啥用? # 允许多个 HTTP 请求和响应复用同一个 TCP 连接

        - 如何避免 Keep-Alive 带来的问题?
        - 一个 tcp 连接能发几个 http 请求? http能不能一次连接多次请求，不等后端返回? # 总结：也就是说这个问题是由HTTP决定的，因为TCP默认长连接，HTTP和TCP之间永远都是连接状态的，所以我们只要保证HTTP是长连接的，整个链路就打通了。
    # 如果是HTTP 1.0 版本协议，一般情况下，不支持长连接，因此在每次请求发送完毕之后，TCP连接即会断开，因此一个TCP发送一个HTTP请求，但是有一种情况可以将一条TCP连接保持在活跃状态，那就是通过Connection和Keep-Alive首部，在请求头带上 Connection: Keep-Alive ，并且可以通过Keep-Alive 通用首部中指定的，用逗号分隔的选项调节keep-alive的行为，如果客户端和服务端都支持，那么其实也可以发送多条，不过此方式也有限制，可以关注《HTTP 权威指南》4.5.5 节对于Keep-Alive连接的限制和规则；
    # 而如果是HTTP 1.1 版本协议，支持了长连接，因此只要TCP连接不断开，便可以一直发送HTTP请求，持续不断，没有上限；
    # 同样，如果是HTTP 2.0 版本协议，支持多用复用，一个TCP连接是可以并发多个HTTP请求的，同样也是支持长连接，因此只要不断开TCP的连接，HTTP请求数也是可以没有上限地持续发送，

    - topic: http状态码
      url: https://github.com/for-GET/http-decision-diagram
      qs:
        - "***common used status code? 列举 100、200、300、400、500 的常用状态码***"
        - 100 status code（10x 是 HTTP/1.1 新增的，用来表示服务器收到请求，需要请求者继续执行操作（收到请求了，你丫继续操作））
        #  - 100 继续。客户端应继续其请求。为了让服务器检查请求的 header，客户端必须在发送请求实体之前，发送"Expect:100-continue"的 header 参数，并接收到"100-continue"; 使用这个 100 状态码的场景是，对大文件上传/下载等耗时操作的优化，比如客户端有个大文件需要上传，但是客户端不知道服务端是否愿意接收这个文件，这种情况下，就应该先发送"Expect:100-contine"参数，如果服务端愿意接收就返回"100-continue"，否则就返回"417 状态码 (Expectation Failed)"
        #  - *101 切换协议*。服务器根据客户端的请求切换协议。只能切换到更高级的协议，例如，切换到 HTTP 的新版本协议。

        - 200 status code
        #- 200 请求成功。一般用于 GET 与 POST 请求
        #- *201 已创建，成功请求并创建了新的资源*
        #- 202 已接受。已经接受请求，但未处理完成
        #- *203 非授权信息。请求成功，但返回的 meta 信息不在原始的服务器，而是一个副本*
        #- 204 无内容。服务器成功处理，但未返回内容。*增删改之类的操作，处理成功；只返回 bool 值，不返回内容*
        #- 205 重置内容。服务器处理成功，用户终端（例如：浏览器）应重置文档视图。可通过此返回码清除浏览器的表单域。
        #- *206 部分内容。服务器成功处理了部分 GET 请求*

        - 300 status code（重定向，需要进一步的操作以完成请求（收到请求了，去别的地方处理吧））
        #- *301 永久重定向*，请求的网页已永久移动到新位置；/*308 永久重定向*
        #- *302 临时重定向*，服务器目前从不同位置的网页响应请求，但是请求者应继续使用原有位置来响应以后的请求/*307 临时重定向*。使用 GET 请求重定向。
        #- 303 查看其它地址。与 301 类似。使用 GET 和 POST 请求查看
        #- *304 未修改*。所请求的资源未修改，服务器返回此状态码时，不会返回任何资源。客户端通常会缓存访问过的资源，通过提供一个头信息指出客户端希望只返回在指定日期之后修改的资源
        #- 305 使用代理。所请求的资源必须通过代理访问
        #- 306 已经被废弃的 HTTP 状态码
        - 400 status code（客户端错误 请求包含语法错误或无法完成请求（你丫自己操作错了，会不会玩？））
        #- 400 客户端请求的语法错误，服务器无法理解
        #- *401 未授权；用户 token 验证错误；*
        #- *402 参数错误*
        #- *403 是 token 通过，但是 method not allowed*
        #- 404 服务器无法根据客户端的请求找到资源（网页）。通过此代码，网站设计人员可设置"您所请求的资源无法找到"
        #  的个性页面
        #- 405 客户端请求中的方法被禁止
        #- 406 服务器无法根据客户端请求的内容特性完成请求
        #- 407 请求要求代理的身份认证，与 401 类似，但请求者应当使用代理进行授权
        #- *408(request timeout) 服务器等待客户端发送的请求时间过长，超时*
        #- 409 服务器完成客户端的 PUT 请求是可能返回此代码，服务器处理请求时发生了冲突
        #- 410 客户端请求的资源已经不存在。410 不同于 404，如果资源以前有现在被永久删除了可使用 410 代码，网站设计人员可通过 301 代码指定资源的新位置
        #- 411 服务器无法处理客户端发送的不带 Content-Length 的请求信息
        #- 412 客户端请求信息的先决条件错误
        #- 413 由于请求的实体过大，服务器无法处理，因此拒绝请求。为防止客户端的连续请求，服务器可能会关闭连接。如果只是服务器暂时无法处理，则会包含一个 Retry-After 的响应信息
        #- 414 请求的 URI 过长（URI 通常为网址），服务器无法处理
        #- 415 服务器无法处理请求附带的媒体格式
        #- 416 客户端请求的范围无效
        #- 417 服务器无法满足 Expect 的请求头信息
        - 500 status code（服务器错误，服务器在处理请求的过程中发生了错误（开发和运维还没死快抬上来））返回 500 状态的先后顺序？ (502，503，504 有什么区别？) (3241x)
        #- 500 服务器内部错误，无法完成请求
        #- *501 服务器不支持请求的功能，无法完成请求*
        #- *502 无效的错误网关*
        #- *503 由于超载或系统维护，服务器暂时的无法处理客户端的请求。延时的长度可包含在服务器的 Retry-After 头信息中*
        #- *504(gateway timeout) 网关超时*
        #- *505 HTTP 的版本不受支持*
        #- 508 处理请求时死循环
        #- 522(connection timeout)，是 CloudFlare 自己定义的一个 code

        - HTTP 状态码 304 的具体原理? What exactly is the status code 304? (How does http cache-control works in HTTP and browser?)
        #- *有缓存，且缓存已过期时*，将 header 里的`If-Modified-Since`，`If-None-Match`这两个参数与服务器进行对比
        #- 如果相同，则获取本地的缓存信息，返回 304
        #- 如果不同，则服务器返回新的资源，返回 200。并将`Last-Modified`，`ETag`重置。
        #- *ETag 的优先级高于 Last-Modified*，其中，Etag 是对资源的`INode`，`size`和`最后修改时间 MTime`进行 Hash 后得到的，是对于资源的唯一标识。`Last-Modified`是资源被修改的最后时间

        - “实际上就是通过 Cache-Control 来控制 HTTP 缓存” 能否具体聊聊？协商缓存、强制缓存。用超市里的食品过期来进行类比。
        #  水果上贴着标签“private, max-age=5”。这就是说水果不能放进冷柜，必须直接给顾客，保鲜期 5 天，过期了还得去超市重新进货。
        #  冻鱼上贴着标签“public, max-age=5, s-maxage=10”。这个的意思就是可以在冰柜里存 10 天，但顾客那里只能存 5 天，过期了可以来便利店取，只要在 10 天之内就不必再找超市。
        #  排骨上贴着标签“max-age=30, proxy-revalidate, no-transform”。因为缓存默认是 public 的，那么它在便利店和顾客的冰箱里就都可以存 30 天，过期后便利店必须去超市进新货，而且不能擅自把“大排”改成“小排”。
        #  关于缓存的生存时间，多了两个新属性“max-stale”和“min-fresh”。
        #  “max-stale”的意思是如果代理上的缓存过期了也可以接受，但不能过期太多，超过 x 秒也会不要。“min-fresh”的意思是缓存必须有效，而且必须在 x 秒后依然有效。
        #  比如，草莓上贴着标签“max-age=5”，现在已经在冰柜里存了 7 天。如果有请求“max-stale=2”，意思是过期两天也能接受，所以刚好能卖出去。
        #  但要是“min-fresh=1”，这是绝对不允许过期的，就不会买走。这时如果有另外一个菠萝是“max-age=10”，那么“7+1<10”，在一天之后还是新鲜的，所以就能卖出去。

        - "***HTTP 状态码 301/308、303/307 的区别？redirect(permanent/temporary), qs(get/post), (browser caching) “其实所谓的永久和临时，本质上来说是浏览器缓存的问题”***"
        # 永久重定向，301 和 308(308 是 301 的补充) 二者的区别在于是否允许浏览器将原本为 POST 的请求重定向到 GET 请求上。301允许，308不允许。
        # 临时重定向的 3 个：302,303,307(303 和 307 都是 HTTP1.1 对于 302 的细化，目前 302 状态没用)
        # 301 会缓存该 url 到浏览器，有效期通常为 100d，请求时，会直接从缓存中取出该短链映射的 url，返回时在 header 中添加参数`Location: url`指定原链接。而 302 还是会跳转到原 url，所以被称为“临时重定向”。

        #临时重定向的 3 个：302,303,307(303 和 307 都是 HTTP1.1 对于 302 的细化，目前 302 状态没用)
        #
        #- 303：*用来把 post 请求变为 get 请求*。对于 POST 请求，它表示请求已被处理，客户端可以接着使用 get 方法去请求 location 里的 URI
        #- 307：*只能使用 post 请求*。对于 POST 请求，它表示请求还没有被处理，客户端应该向 location 里的 URI 重新发起 POST 请求
        #- *其实所谓的`永久`和`临时`，本质上来说是`浏览器缓存`的问题*。301 会缓存该 url 到浏览器，有效期通常为 100d，请求时，会直接从缓存中取出该短链映射的 url，返回时在 header 中添加参数`Location: url`指定原链接。而 302 还是会跳转到原 url，所以被称为“临时重定向”。

        - HTTP 代理和缓存代理

    - topic: HTTP2
      picDir: kernel/np/http
      table:
        - name: HTTP/1.1
          运输工具: 单辆TCP卡车
          多个请求如何运输: 串行 (依次排队发/收) 或 弱流水线
          处理慢/丢包请求的影响: 严重阻塞! 阻塞后面所有请求
          HOL阻塞类型: 应用层HOL
          类比: 一辆卡车，包裹严格按顺序放

        - name: HTTP/2
          运输工具: 单辆改进TCP卡车
          多个请求如何运输: 内部多条虚拟传送带(流)，独立标记帧
          处理慢/丢包请求的影响: |
            应用层解决! 不互相卡
            但底层阻塞! 卡车整体丢包停运会影响所有请求
          HOL阻塞类型: 应用层解决 传输层(TCP)HOL残留
          类比: 一辆卡车，内部多传送带+标签帧

        - name: HTTP/3
          运输工具: QUIC无人机小队
          多个请求如何运输: 每个流近似独立无人机，高度自治
          处理慢/丢包请求的影响: 几乎无影响! 只影响单个流自身
          HOL阻塞类型: 应用层+传输层均解决
          类比: 灵活无人机队，独立处理订单
      qs:
        - HTTP2建立连接的具体流程（类似ws协议，也是先从http1.x upgrade到http2之后再建立连接）?
        - “HTTP 2.0 成功解决了 HTTP 1.1 的队首阻塞问题，同时，也不需要通过 HTTP 1.x 的 pipeline 机制用多条 TCP 连接来实现并行请求与响应；减少了 TCP 连接数对服务器性能的影响，同时将页面的多个数据 css、js、jpg 等通过一个数据链接进行传输，能够加快页面组件的传输速度。”
        #HTTP2 主要解决了 HTTP1.1 的问题：通过“压缩头部”解决了发送消息包含了太多不必要的头部信息（带宽消耗大）
        #- `header 压缩`，让一个 HTTP 请求放到一个 TCP 数据包里，而不是分成多个，提高性能
        #- `预测资源请求`
        #- `缓冲区溢出`
        #- `pipeline`和`队头阻塞`

        - 大幅度提升了 HTTP 的传输效率（消除了应用层的队头阻塞，拥有header compression、二进制帧(plain text->binary)、多路复用、流量控制、服务器推送）

        - "***How to optimize HTTP? long conn, TFO, Domain Sharding(browser limit domain conn)*** “在整个 HTTP 系统里有三个可优化的环节，分别是服务器、客户端和传输链路。但因为我们是无法完全控制客户端的，所以实际上的优化工作通常是在服务器端。这里又可以细分为后端和前端，后端是指网站的后台服务，而前端就是 HTML、CSS、图片等展现在客户端的代码和数据。”、“我把这方面的 HTTP 性能优化概括为三个关键词：开源、节流、缓存”"
        #  开源
        #  Nginx 或者 OpenResty 自身也有很多配置参数可以用来进一步调优，举几个例子，比如说禁用负载均衡锁、增大连接池，绑定 CPU 等等，相关的资料有很多。
        #  特别要说的是，对于 HTTP 协议一定要启用长连接。在[第 39 讲]里你也看到了，TCP 和 SSL 建立新连接的成本是非常高的，有可能会占到客户端总延迟的一半以上。长连接虽然不能优化连接握手，但可以把成本“均摊”到多次请求里，这样只有第一次请求会有延迟，之后的请求就不会有连接延迟，总体的延迟也就降低了。
        #  另外，在现代操作系统上都已经支持 TCP 的新特性“TCP Fast Open”（Win10、iOS9、Linux 4.1），它的效果类似 TLS 的“False Start”，可以在初次握手的时候就传输数据，也就是 0-RTT，所以我们应该尽可能在操作系统和 Nginx 里开启这个特性，减少外网和内网里的握手延迟。

        #  节流
        #
        #  压缩之外，“节流”还有两个优化点，就是域名和重定向。
        #  DNS 解析域名会耗费不少的时间，如果网站拥有多个域名，那么域名解析获取 IP 地址就是一个不小的成本，所以应当适当“收缩”域名，限制在两三个左右，减少解析完整域名所需的时间，让客户端尽快从系统缓存里获取解析结果。
        #  重定向引发的客户端延迟也很高，它不仅增加了一次请求往返，还有可能导致新域名的 DNS 解析，是 HTTP 前端性能优化的“大忌”。除非必要，应当尽量不使用重定向，或者使用 Web 服务器的“内部重定向”。

        - 为啥HTTP2之后应该用Domain Sharding来代替 resource merging?

        # [基于 nginx quic 分支体验 http3 | 董泽润的技术笔记](https://mytechshares.com/2022/04/06/nginx-blog-quic/)
        # [Head Of Line Blocking 困扰两个月的线上问题 | 董泽润的技术笔记](https://mytechshares.com/2022/04/06/hol-prd-issue/)
        - HTTP2的HOL问题：tcp restranmit 引起的 HOL(head of line) blocking

        - 我们都知道HTTP2最大的feats就是解决了HTTP1.1的HOL问题，但是"为什么 HTTP1.1 有队头阻塞问题，HTTP2 究竟怎么解决的很多人都不清楚"。其实"队头阻塞"是一个专有名词，不仅仅这里有，交换器等其他都有这个问题，引起这个问题的根本原因是使用了队列这种数据结构。对于同一个 tcp 连接，所有的 http1.0 请求放入队列中，只有前一个请求的响应收到了，然后才能发送下一个请求，这个阻塞主要发生在客户端。这就好像我们在等红绿灯，即使旁边绿灯亮了，你的这个车道是红灯，你还是不能走，还是要等着。对于同一个 tcp 连接，http1.1 允许一次发送多个 http1.1 请求，也就是说，不必等前一个应收到，就可以发送下一个请求，这样就解决了 http1.0 的客户端的队头阻塞。但是，http1.1 规定，服务器端的响应的发送要根据请求被接收的顺序排队，也就是说，先接收到的请求的响应也要先发送。这样造成的问题是，如果最先收到的请求的处理时间长的话，响应生成也慢，就会阻塞已经生成了的响应的发送。也会造成队头阻塞。可见，http1.1 的队首阻塞发生在服务器端。






    - topic: QUIC (HTTP3)
      picDir: kernel/np/http/quic
      qs:
        - “QUIC 协议通过基于 UDP 自定义的类似 TCP 的连接、重试、多路复用、流量控制”
        - "QUIC的特点：1、支持连接迁移 2、低时延连接 3、可插拔拥塞控制 4、降低对丢包的敏感度"

        - HTTP3 feats? Why high perf? （为什么需要在HTTP/3中使用QUIC协议？HTTP/3是如何与QUIC协议相结合的？） # not seq(not block), UDP(QUIC)-based, CUBIC->BBR, 0-RTT()
        - QUIC协议解决了哪些HTTP/2中存在的问题（HTTP2 和 HTTP3 的区别？）？有哪些性能提升？ # HOL Blocking(队头阻塞head-of-line blocking)
        #- `把“拥塞控制算法”从 CUBIC 算法变为了 BBR 算法`，在数据传输量大且数据包丢失频繁的情况下，性能提升很大
        #- `HTTP3 基于 UDP 协议`，所以性能肯定比基于 TCP 的 HTTP 和 HTTP2 提升了不少。*基于 TCP 协议的 HTTP2 最大的问题就在于，TCP 连接中的任何中断都会阻塞所有数据流；而 HTTP3 基于 UDP，所以即使数据包丢失，也只会中断一个数据流，而不是所有数据流*
        #- `另外 HTTP3 支持 0-RTT`；也就是说，建立连接时通过`消除与服务器的 TLS 确认`，从而让后续连接的启动速度更快；比起完全使用`TLC 协商`的方式，客户端可以更快地请求数据，网站可以更早开始加载

        - QPACK. QPACK 通过更高效的头部压缩技术，减少了网络传输中的冗余数据量。这种压缩机制不仅提升了数据传输的效率，还能缓解前面提到的“队头阻塞”。 # [2.8 QUIC 设计原理与实践 | 深入高可用系统原理与设计](https://www.thebyte.com.cn/http/quic.html)


    - topic: SSH
      qs:
        - SSH 是什么？安全外壳协议，SSH 为建立在应用层和传输层基础上的安全协议，专为远程登录和其他网络服务提供安全性的协议。因为传统的网络传送协议，比如 FTP，POP，Telnet 本质上都是不安全的，他们在网络上用明文传送数据，用户账户和用户口令，很容易受到“中间人攻击”
        - SSH 的组成？传输层协议 + 用户认证协议 + 连接协议
        - SSH 的工作过程（对称加密和非对称加密）？

    # [How DNS works. What is DNS? Learn how step by step.](https://howdns.works/zh-hans/)  漫画讲解DNS工作原理
    - topic: DNS
      qs:
        - What's DNS? (hostname2ip) What are the cons of DNS?
        - DNS解析过程? router是直接解析还是转发DNS?
        - DNS zone


    # [码了 2000 多行代码就是为了讲清楚 TLS 握手流程](https://mp.weixin.qq.com/s?__biz=MzAwNTc3OTE5Mg==&mid=2657443920&idx=1&sn=57fe3c9dc3a0f4ef3de2ca00dd8927cc)
    - topic: TLS基本认知
      picDir: kernel/np/TLS
      qs:
        - SSL 协议和 TLS 协议都是“在传输层对网络连接进行加密”，那二者有什么区别? #  SSL是1994年Netscape设计的，TLS是IETF 在 SSL3.0 的基础上设计的协议（1999年），之后两个协议就是一码事了
        - TLS 协议由什么组成 # TSL 协议分为`TLS记录协议`和`TLS握手协议`，*位于底层的 TLS 记录协议，建立在 TCP 之上，负责进行加密 (以及数据封装、压缩等基本功能)，位于上层的 TLS 握手协议负载加密以外的其他操作*(用于在实际的数据传输开始前，通讯双方进行身份认证、协商加密算法、交换加密密钥等)
        - 【握手流程】TLS 握手流程（TLS handshake? Compares to TCP handshake process?）(RSA+AES hybrid encryption) “TLS 握手可以与 TCP 握手对比来看，其实就是中间多了 RSA+AES 混合加密的流程”，直接查看我总结的混合加密流程就可以了
        - 怎么优化“TLS 握手时间”？
        - 这个图里的两个是不是，TLS1.2就是 DH+RSA算法，而TLS1.3之后之所以握手时延缩短至 1 RTT，就是因为去掉了RSA算法，直接使用ECDH。这个理解对吗？ 关于 TLS1.3的理解有问题（1、RSA 未被完全移除。2、ECDH 不是提速的主因 → 流程重构（客户端主动发送密钥参数） 才是 1-RTT 的关键。）。 准确说法是：“TLS 1.3 通过合并握手步骤（客户端首次发送密钥参数）+ 强制前向安全算法（如 ECDHE）+ 移除冗余交互，实现 1-RTT 握手。RSA 静态加密被废除，但签名仍可用，而 ECDH 是算法升级的组成部分，非唯一提速原因。”




    - topic: HTTPS证书
      qs:
        - 验证 HTTPS 证书的过程是什么样的?
        - "***HTTPS 证书的吊销机制?***" # 用`OCSP协议`(在线证书状态协议，TLS 协议的拓展协议) 来判断还没过期的证书是否被吊销了
        - HTTPS 的证书过期是谁来判断？
        - 证书的合法性又是谁检查的呢？
        - 什么时候触发？
        - 影响性能吗？
        - 如何吊销证书？
        - HTTPS 的请求是客户端（浏览器）发起的，他是如何知道证书被吊销的？
        - 验证 HTTPS 证书的过程是什么样的？

        - HTTPS证书的OCSP stapling机制（OSCP Stapling 解决了 OSCP 访问慢、用户隐私泄露的问题）：server定期从 CA 获取其证书状态的 OCSP 响应，并且缓存在本地，在 TLS 握手时将这个响应提供给客户端，从而避免了客户端（比如浏览器）直接向 CA 查询证书状态的需要。这样的话，是否存在可靠性问题呢，比如说证书已经过期了，但是我们server直接伪造证书有效期呢 # OCSP 信息是由CA 的私钥签名的，我们server伪造不了

    - topic: https如何做中间人攻击
