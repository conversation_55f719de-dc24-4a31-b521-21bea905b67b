---



# TODO 学习一下tf的基本用法，就可以删掉了
#- url: https://github.com/antonputra/tutorials
#  des: 就是antonputra视频对应的代码，也就是通过tf实现各种 kafka vs rabbitMQ, pgsql vs mysql 之类的对比


# TODO
#- url: https://github.com/muesli/crunchy
#  des: Finds common flaws in passwords. 比如说很多密码生成工具，用来判断密码强弱，就可以用这个pkg. 比 wagslane/go-password-validator 的代码更简洁和规范


#- url: https://github.com/Doragd/Algorithm-Practice-in-Industry
#  des: “搜推广”相关blog推荐，可以看看 # TODO

# TODO
#  url: https://github.com/Admol/SystemDesign
#  doc: https://learning-guide.gitbook.io/system-design-interview


- type: 通用模块/架构设计
  tag: works
  score: 5
  using:
    url: https://github.com/ByteByteGoHq/system-design-101
    doc: https://bytebytego.com/guides/
    des: 就是 bytebytego 的那个项目，很多做的很好的架构图，非常实用
  repo:

    # TODO 看下怎么在golang里实现，在实现API之前先实现API文档并且推上去（直接作为类似APIfox那样的使用）
    - url: https://github.com/swaggo/swag
      score: 5
      record:
        - 【2025-06-25】移除【go-swagger】、【RapiDoc】、【Redocly/redoc】。都是swagger日志相关的repo，很麻烦，不如简单一点，不要去考虑什么UI之类的，不要庸人自扰。就用最经典的「代码+文档一体」的方案，也就是swaggo提供的。
        - 【2025-06-26】移除【kin-openapi】他这个是用来validate以及操作 OpenAPI文档本身的。跟swaggo无关。

    - url: https://github.com/mswjs/msw
      score: 5
      des: 【API Mock服务】 # [Source - Generate request handlers from HAR files, OpenAPI documents, and other sources.](https://source.mswjs.io/docs/integrations/open-api/)
      record:
        - BE和FE中确实需要有一个主导者，但是无论谁主导都需要通过API文档做mock，来提供一个基础共识，之后才能基于这个API设计进行讨论。而非某一方主导，则另一方必须要全盘接受。API设计需要不断探索和磨合，而不是由哪一方就直接定了的。
        - 【2025-07-15】移除【stoplightio/prism（搭配swagger使用，自动根据swagger.json生成Mock服务）】、【hoverfly（一个轻量级的服务虚拟化和API模拟工具。【2025-06-24】可以理解为 Mock Service，但是Hoverfly 并不是要取代前端 Mock Service，而是解决前端 Mock Service 无法覆盖的那些场景（主要是网络层行为、服务间调用、非前端应用的测试），特别是在后端开发、集成测试、E2E测试、可靠性测试和复杂的微服务环境中，Hoverfly 提供了一种更强大、更接近真实网络行为的“靠谱”的模拟方案。）】。后者就不提了，服务虚拟化而非mock（基本原理是类似gor那种流量复制）。不使用后者就一个问题“如果在swagger.json里没有声明中文的情况下，这两个工具是否仍然可以要求mock返回中文？”，得到（自己也验证后）的答案是prism不支持该操作，而msw的msw-auto-mock则内置了faker.js直接设置locale就可以获得中文mock数据。
        - 【2025-07-15】再拿swagger+msw跟postman/apifox之类的工具做个比较，功能上应该说相差不大，后者更易用，毫无上手门槛。但是apifox最大的问题在于很难维护，我做过一个1300多个API的项目，我进入项目的时候整个项目已经跑了6年了，实际上这1300个API里只有不到400个还在用的，但是基于某些原因，之前的也都保留，并且虽然分version了，但是整个规划很差。就导致很多问题。实际上这个是swagger相对于apifox的优势，也就是API文档更新实时。而msw实际上只是补足了swagger没有mock服务的缺陷。所以虽然可能apifox这样的工具更易用，但是我个人倾向于把apifox作为个人使用的工具，也就是把json直接导入来操作，而不是在apifox上维护文档。



    - url: https://github.com/spf13/cobra
      score: 5
      des: 【cli框架】As Trivy said "Trivy has a lot of capabilities and needs a configuration file now. spf13/cobra has a better integration of configuration files. ... We should move to spf13/cobra.". Support feats like sub-cmd, flags, hooks(PreRun, PostRun), integrate with viper
      repo:
        - url: https://github.com/jedib0t/go-pretty
          score: 4
          des: 我用这个代替了【tablewriter】，这个repo同样支持各种table自定义样式（Without Border / Footer / Bulk Append, Custom Separator, cells merging、无边框）、支持自定义table颜色、支持多种数据源（CSV）、支持输出多种格式（Markdown）。

        - url: https://github.com/charmbracelet/huh
          score: 4
          des: 目前golang生态下搭配cobra实现prompt cli的最佳选择

        - url: https://github.com/charmbracelet/gum
          score: 4
          des: gum并不能作为mod集成到golang应用，只是一个用来增强shell交互性和用户体验的cli工具。直接基于bubbletea+lipgloss实现。之前一直觉得挺鸡肋的，看到 [用gum给IRedis增加数据源选择能力](https://chedan.io/gum-iredis) 这个帖子之后发现，还是挺有用的。说白了就是给本身不是交互式cli提供交互能力。
      record:
        - 【2025-06-24】移除【bubbletea】以及【srss】、【snips.sh】这两个基于 bubbletea 实现的repo。我一直不喜欢TUI应用
        - 【2025-06-24】移除【tablewriter】、【tview】、【lipgloss】，其中第一个可以被【go-pretty】替代，后两个都可以被【pterm】完美上位替代。
        - 【2025-06-24】移除【pterm】。已经确认了，组合使用【huh】+【go-pretty】是更好的选择，huh的交互比pterm更好，go-pretty相比 pterm 又支持更多组件。
        - 【2025-07-14】移除之前写的“【技术选型】cli”，所以移除掉【urfave/cli】、【alecthomas/kong】这两个都更适合构建小型cli工具  # [What is the essential difference between urfave/cli и spf13/cobra? : r/golang](https://www.reddit.com/r/golang/comments/5sdvoh/what_is_the_essential_difference_between/)
      qs:
        - "***我在实现一个响应式cli工具（比如小说爬虫），怎么尽量提高其易用性？（实现命令行工具的通用优化）***" # 首先，任何cli工具都推荐实现为响应式cli（prompt cli），易用性直接拉满。另外，参数校验、交互式引导、进度指示、日志记录、配置文件支持（允许通过配置文件直接执行cli，减少用户重复操作）、颜色和格式。


        - topic: cobra使用
          qs:
            - "***【hook执行顺序】所有hook按以下顺序运行 hook(PostRun, PreRun)***"

            # [LeslieLeung/reaper: REpository ArchivER(REAPER) is a tool to archive repositories from any Git servers.](https://github.com/LeslieLeung/reaper)
            # [Mrucznik/wonsz: The best of viper & cobra combined.](https://github.com/Mrucznik/wonsz)
            # [carolynvs/stingoftheviper: Share configuration between spf13/cobra and spf13/viper](https://github.com/carolynvs/stingoftheviper)
            - "***【cobra搭配viper使用】也就是“把config.yml作为默认flag并解析后，就可以直接使用config.yml中的所有key”，这样就不需要bind太多flag了***" # 在 cobra 中搭配使用 viper。使用场景为有大量参数，总不能一个一个用 flag 绑定吧。这种场景下，直接使用 flag 绑定 config.yml，然后用 viper 解析 config 就可以了。
            - 【flag】有哪几种flag? PersistentFlags() 和 LocalFlags() 有啥区别? 怎么用 PersistentFlags?

            - "***【Args 参数验证】怎么使用cobra内置验证规则来进行“Args参数验证”？***"

            - 怎么在所有命令的入口判断 env 是否存在，并且在所有子命令中都可以直接调用？ # 给rootCmd加个checkEnv()的PersistPreRun。其实直接放到root.go 的init里就可以了。不需要上面这种做法。

            - 怎么把一个子命令作为另一个子命令的子命令?
            - 什么情况下使用 RunE，而不是 Run 呢?

            - 【搭配交互式cli使用】cobra 怎么搭配 pterm(或者 promptui)等interactive cli 使用？ # [divrhino/studybuddy: Build an interactive CLI application with Go, Cobra and promptui. Video tutorial available on the Div Rhino YouTube channel.](https://github.com/divrhino/studybuddy)



    - url: https://github.com/vbenjs/vue-vben-admin
      score: 5
      doc: https://jacobhsu.github.io/vue-vben-admin-docs/
      des: vben嘛，还是不错的。之前有个gf-vben的后台就是基于这个实现的。
      repo:
        - url: https://github.com/flipped-aurora/gin-vue-admin
          doc: https://www.gin-vue-admin.com/guide/introduce/project.html
          des: 【后台项目脚手架】gva 仅供参考。优点：不是 laravel-admin 这类项目，是正经企业级后台项目，所以功能更强大
        #- url: https://github.com/xinliangnote/go-gin-api
        #- url: https://github.com/GoAdminGroup/go-admin
        #  des: 类似`laravel-admin`，但是可配置性还差得远，比如左侧栏的默认展开等等；后台体验比较差，完全不符合预期。优点是支持各种 web server 的 adapter, 并且支持命令行直接生成项目；支持各种数据库 driver, 比如小项目可以直接用 sqlite。
        #- url: https://github.com/go-admin-team/go-admin
        #- url: https://github.com/bufanyun/hotgo
        #  des: 企业级应用的gf实现的后台项目
        #- url: https://github.com/tiger1103/gfast
        #- url: https://github.com/suyuan32/simple-admin-core
        #  des: gozero+Vben5 实现的，提供了完整的用户管理、权限管理、角色管理、菜单管理、日志管理、配置管理等功能，支持多语言等特性，适用于小型或大型企业快速搭建分布式后台管理系统。

        - url: https://github.com/casbin/casbin
          doc: https://editor.casbin.org/
          des: access controls
          topics:
            - topic: RBAC
              table:
                - name: Permify # 也是类似casbin那样的Auth服务，但是permify更类似Google Zanzibar，更适用于复杂业务系统，提供更细粒度的权限。但是也与RBAC, ReBAC, ABAC兼容。与casbin的使用场景不同。
                  url: https://github.com/Permify/permify
                  项目类型: 独立授权服务
                  核心模型: ReBAC (Google Zanzibar) + RBAC/ABAC
                  策略定义: 声明式 DSL
                  存储支持: PostgreSQL, Spanner, etc (分布式存储)
                  多租户支持: 原生支持
                  性能特点: 水平扩展 + 低延迟 (微秒级)
                  测试工具: 场景测试 + 策略覆盖分析
                  审计日志: 支持
                  认证集成: 纯授权
                  社区生态: Discord 社区 (活跃)
                  核心优势: |
                    - 关系型权限
                    - 分布式一致性
                    - 审计友好
                  适用场景: 复杂业务系统 (社交图/资源关系)

                - name: Casdoor # casdoor和casbin的maintainer差不多，也是casbin社区项目统一使用的鉴权平台。Casdoor 是一个基于 OAuth 2.0 / OIDC 的 UI 优先集中认证 / 单点登录 (SSO) 平台，简单点说，就是 Casdoor 可以帮你解决 用户管理 的难题，你无需开发用户登录注册等与用户鉴权相关的一系列功能，只需几个步骤，简单配置，与你的主应用配合，便可完全托管你的用户模块，简单省心，功能强大。
                  url: https://github.com/casdoor/casdoor
                  项目类型: IAM/SSO 平台
                  核心模型: RBAC/OAuth2/OIDC
                  策略定义: UI 配置 + JSON
                  存储支持: MySQL, PG, SQLite
                  多租户支持: 通过应用隔离
                  性能特点: 中等负载
                  测试工具: 无
                  审计日志: 未明确
                  认证集成: 完整认证流 (SSO/OIDC)
                  社区生态: Casbin 生态 (统一)
                  核心优势: |
                    - 开箱即用 SSO
                    - 可视化用户管理
                    - 多协议支持
                  适用场景: 统一用户认证管理

                - name: Casbin
                  url: https://github.com/casbin/casbin
                  doc: https://editor.casbin.org/
                  项目类型: 授权库
                  核心模型: 多模型 (ACL/RBAC/ABAC)
                  策略定义: PERM 元模型 (策略/效果/请求/匹配器)
                  存储支持: 文件/DB/内存
                  多租户支持: 域/租户模型
                  性能特点: 高效匹配 (纳秒级)
                  测试工具: 在线编辑器测试
                  审计日志: 无
                  认证集成: 需配合 Casdoor
                  社区生态: 多语言支持 (曾有社区管理争议)
                  核心优势: |
                    - 模型灵活
                    - 多语言支持
                    - 轻量级集成
                  适用场景: 嵌入式权限控制

                - name: Ladon
                  url: https://github.com/ory/ladon
                  项目类型: 策略 SDK
                  核心模型: 策略模型 (类似 AWS IAM)
                  策略定义: JSON 策略 + 条件函数
                  存储支持: 内存/CockroachDB
                  多租户支持: 需自定义实现
                  性能特点: 正则匹配性能受限
                  测试工具: 无
                  审计日志: 支持 (AuditLogger)
                  认证集成: 纯授权
                  社区生态: ORY 维护 (较小)
                  核心优势: |
                    - 策略条件丰富
                    - 类 IAM 语义
                    - 资源属性控制
                  适用场景: 微服务/IoT 策略管理
              qs:
                - "*有哪些权限控制方案？（ACL 权限控制（基于访问控制列表的）、RBAC 权限控制（基于角色的权限控制）、Auth 权限控制、DAC 自主访问控制、MAC 强制访问控制、ABAC 基于属性的访问控制模型（阿里云，AWS））*"
                - RBAC 有哪些变种（RBAC96 模型 有哪些）？
                #            - RBAC0 模型；（最基础的 5 张表的 RBAC）
                #            - RBAC1 模型；引入了`角色继承关系`，即角色具有上下级的关系，角色间的继承关系可分为`一般继承关系`和`受限继承关系`，一个角色可以分配 n 个子角色；
                #            - RBAC2 模型：添加了`责任分离关系`，对角色的约束控制。互斥角色、基数约束、先决条件角色。
                #            - RBAC3 模型；`最全面的 RBAC 模型，基于 RBAC0，将 RBAC1 和 RBAC2 做了整合；`
                #            - 用户组模型；（这个有点奇怪；把用户组和角色挂起关系，管理员直接给用户组分配角色，用户加入用户组自动拥有该用户组下所有角色对应的所有权限；）
                #              - 适用于用户和角色非常多，而且很多用户有相同属性的情况，在用户和角色中间使用用户组来收束角色，举个例子：`n 个用户，平均每个用户 x 个角色，那么 RBAC0 就需要分配 nx 次，如果有 k 个用户组的话就是 k*(n+x) 次`
                #              - 分为`有上下级关系的用户组`和`普通用户组`
                #              - 基于`有上下级关系的用户组模型`拓展出的适用于大型 ERP 系统的`组织/职位/用户组模型`

            - topic: casbin
              qs:
                - storage? Model Storage, Policy Storage,
                - Multi-tenancy? RBAC with Domains
                - casbin使用 # [casbin权限模型推演 - 又耳笔记](https://youerning.top/post/casbin/)

        - url: https://github.com/qax-os/excelize # excelize库实现excel导入导出封装（二），基于map、多个sheet、多级表头、树形结构表头导出，横向、纵向合并单元格导出
          doc: https://xuri.me/excelize/zh-hans/
          des: 【excel处理】
          record:
            - 【2025-06-24】移除了【xlsx】、【go-excel】、【tabtoy】
      record:
        - 【2025-06-24】把之前repo里react相关admin全部删掉了（之前废了很多功夫加上的，现在又要花时间再删掉，真TM）。确实用不到，这个道理很简单，国内admin普遍用vue，相应的这些admin本身内置的很多功能，也更符合国内公司的需求，就可以省很多功夫。
        - 【2025-06-24】删掉了【vue3-antdv-admin】，不伦不类的，vue生态有ele、NaiveUI之类很好的admin，没必要用react生态的antd
        - 【2025-06-28】之前想对比一下几个后台admin选型，那天没弄完，今天一想都是瞎搞，vben就挺好用的，还需要考虑其他什么选型吗？移除掉【vue-pure-admin】、【vue-element-admin】这两个都是很早以前的repo了，因此star比较多，但是无论是功能还是UI审美都真不行。移除掉【soybean-admin（跟vben属于一类，都是比较现代的admin，移动端适配不错，路由用的自研的ElegantRouter，类似nextjs，很麻烦，但是个人觉得不如vben）】。这里记录一下admin继续选型的几个比较维度：【前端框架+build工具+UI库】、【路由方案、数据交互、状态管理】就这几个。以后看到其他admin，也可以从这几个维度进行比较。
      topics:
        - topic: vben
          qs:
            - column width 百分比和固定像素 两种有啥区别？百分比天然适配移动端，是不是更通用？
            - CustomRender()和 直接在`<template></template>` 这两种渲染table的方法有啥区别。我个人感觉用CustomRender()更清晰，复用性更强。为啥更推荐使用 CustomRender # customRender 复用性更好是吧，如果几个table的字段有大量可以复用的，我们完全可以定义一个base，然后直接concat到其他ColumnRef里。二者的优缺点是，template 可读性更好，但是复杂逻辑渲染时，template这种模板语法不如 customRender，并且性能也不如customRender.
            - react（或者nextjs）有没有类似 createWebHashHistory 和 createWebHistory 这样的路由模式？ # BrowserRouter和HashRouter 与vue的以上两种路由模式严格对应
            - 【table背景色】 # [vxe-table 给单元格添加自定义颜色状态，单元格加自定义样式 - 可不简单 - 博客园](https://www.cnblogs.com/qaz666/p/18793417)


        - topic: 后台除了核心业务之外，有哪些通用模块？
          qs:
            - 后台用户系统 RBAC
            - 后台列表内容导入/导出
            - 基础功能：上传下载（媒体库功能）

            - 社区内容审核：比如图片/文字的审核处理，用户的审核处理。举报相关的审核处理
            - 数据：用户/流程的流转情况，及时反馈产品
            - 营销：包括自身的活动的管理，还有第三方广告商/渠道等营销管理
            - 资金板块：社区也是要盈利的，所以最重要的资金板块，比如资金流水/订单/打赏/提现等

        - topic: 后台导入导出功能：有 2000 万条数据，按照条件进行搜索，导出 excel，接口调用时间 30s 内，怎么设计？
          url: https://www.51cto.com/article/745619.html
          des: |
            经典的“异步任务 + 拆分重组”场景。两种方案，MQ或者RDB直接导出。

            - excelize等主流excel库都支持“多sheet导出”（而excel单sheet数据104w（2^20），是天然的并发场景），所以只需要做一个job表（jobName, task_status(0/1/-1)），并发跑所有sheet，然后做个对失败任务的retry
            - MQ方案和RDB类似，同样把所有sheet任务下发（也就是并发），让RDB执行导出即可。因为MQ自带了retry机制，所以也就不需要job表来标识进度了。

            另外有一些需要注意的细节：

            - 上传到OSS：因为以上两种方案都是异步导出，所以没法直接将excel文件，同步返回给用户，所以直接传到OSS
            - 通过ws推送通知：这点可有可无，属于优化。也可以让用户主动刷新页面，获取excel导出结果。
            - 如果单sheet仍然太大：就做sheet内数据的拆分重组（类似大文件分片下载）
        - topic: 批量导入功能，还可以如何优化？
          qs:
            - 导入前，设计 excel 模板（字段是否必填、选择项字段的填写、字段的长度和格式、锁定表头字段、友好的输入引导、易辨识的文件名称、模板文件的版本）
            - 导入中，读取写入（写入前校验、忽略问题数据、处理重复导入、导入进度反馈、异步处理）
            - 导入后，反馈结果（核对数量、处理失败记录）


    - url: https://github.com/yeqown/go-qrcode
      score: 1
      record:
        - 【2025-06-26】移除【barcode】、【skip2/go-qrcode】。之前走了误区，竟然会以为生成qrcode的场景，性能很重要。写了“性能是 barcode 的三倍左右，但是生成的图片白边很宽，不好看，自己取舍吧。”。qrcode技术选型最重要的毫无疑问是功能，相较之下【yeqown/go-qrcode】这个repo支持完全自定义（颜色、单元格形状（圆形/矩形）、图标嵌入、边框控制、半色调）。 # 二维码通常是各种营销场景，方便用户使用。url 过长导致二维码像素点过密，怎么解决？把长链转短链，也就是所谓的动态二维码。


    - url: https://github.com/casbin/govaluate
      des: 【Rules-Engine】 # [Go：用规则引擎让你一天上线十个需求](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651451957&idx=2&sn=1c8892b83cc5641e5e90856c9eb5bf5f)
      score: 1
      topics:
        - topic: 【技术选型】规则引擎 # TODO
          table:
            - url: https://github.com/Knetic/govaluate
              des: 类似 Drools或者Activiti那样的规则引擎，但是17年之后就EOL了。有很多针对性优化的repo可供挑选。
            - url: https://github.com/nwkl-home/govaluate


    - url: https://github.com/dcloudio/uni-app
      doc: https://uniapp.dcloud.net.cn/
      score: 1
      des: 【移动端跨平台】做独立开发者，或者做全栈开发必会的框架。真正的“多端合一”，一套vuejs实现的代码可以打包成ios、android还有各种小程序。并且还有一个还算不错的生态，什么uni-cdn, uni-id, uni-pay, uni-push之类的。
      topics:
        - topic: 【技术选型】移动端跨平台
          table:

            # 后两种方案，react-native 以及 uniapp 这种就是用 js 来调用各种原生移动端语言的 API 的，可想而知，一定会存在适配问题，并且会很慢（因为走了两层，并且无论是 js 还是 swift, OC, java, kotlin 性能都不算很好）。
            - name: uniapp
              原理: 基于Vue.js的MVVM架构，通过WebView渲染+原生组件混合方案，使用条件编译实现多端代码复用
              性能: 中等（WebView渲染存在性能瓶颈），H5端表现较弱
              学习成本: 低（前端开发者友好）
              跨端能力: 优秀（可打包iOS/Android/Web/小程序）
              生态支持: 丰富的插件市场（DCloud生态）
              开发效率: 高（代码复用率高）
              原生能力: 依赖插件扩展
              动画能力: 复杂动画实现困难
              平台适配: 需要条件编译处理差异
              性能上限: 较低（受WebView限制）
              是否值得学: true # 微信小程序生态下的最优解，适合快速业务迭代
              评价: 国内小厂的性价比之选

            - name: react-native
              原理: JavaScriptCore引擎驱动，通过Bridge与原生组件通信，使用Flexbox布局映射原生控件
              性能: 中等偏上（JIT编译+原生组件），复杂交互存在卡顿
              学习成本: 中等（需React基础+原生概念） # 需理解React生态与原生交互机制
              跨端能力: 中等（iOS/Android优先，Web需额外配置）
              生态支持: 丰富的第三方库支持
              开发效率: 高（热重载提升效率） # 但受限于Bridge调试
              原生能力: 通过Native Modules扩展
              动画能力: 支持原生驱动动画 # 但复杂动画需原生开发
              平台适配: 较高（Android兼容性问题突出）
              性能上限: 较高（但受Bridge限制）
              是否值得学: true # 大厂主流选择，长期维护有保障，但建议等待新架构稳定
              评价: 大厂复杂应用首选方案

            # flutter 则使用 dart 实现，dart 可以理解为某种 js 方言，flutter 则是吸取了以上教训，直接自己（通过 chrome 的 render engine, skia）实现各种移动端的 UI，那么这种方案的问题也同样可想而知，生态问题！如果使用 flutter 就不能使用。
            - name: flutter
              原理: 自建渲染引擎（Skia），通过Dart虚拟机直接与原生通信，使用Widget树构建跨平台UI
              性能: 接近原生（AOT编译+直接渲染），流畅度最佳
              学习成本: 较高（需学习Dart和声明式UI）
              跨端能力: 优秀（iOS/Android/Web/桌面端） # 但Web支持差
              生态支持: 一般（Dart库较少）
              开发效率: 高（优秀的热重载支持）
              原生能力: 通过MethodChannel扩展
              动画能力: 强大（基于Skia引擎）
              平台适配: 深度定制成本高
              性能上限: 高（接近原生性能）
              是否值得学: false # 注定失败，因为Dart凉了，而Dart和flutter强绑定了
              评价: 技术优秀但生态受限
          qs:
            - dart 和 ts 都是对 js 类型支持的解决方案，那么为啥 dart 在和 ts 的竞争中失败了？


    - url: https://github.com/tauri-apps/tauri
      score: 1
      topics:
        - topic: 【技术选型】GUI
          table:
            - name: Wails
              url: https://github.com/wailsapp/wails
              lang: Go
              跨平台支持: Windows, macOS, Linux
              性能: 高
              是否易用: 中等
              社区支持: 中
              渲染引擎: Web-based (webview)
              APP大小: 小
              web迁移: 高
              内存占用: 低
              适用场景: 跨平台桌面应用，使用Go后端和Web前端
              是否推荐使用: 是，适合Go开发者
              简评: 使用Go和Web技术构建轻量级桌面应用，单二进制文件

            - name: Fyne
              url: https://github.com/fyne-io/fyne
              lang: Go
              跨平台支持: Windows, macOS, Linux, iOS, Android
              性能: 高
              是否易用: 中等
              社区支持: 中
              渲染引擎: 原生 (OpenGL)
              APP大小: 小到中等
              web迁移: 低
              内存占用: 低到中等
              适用场景: 单代码库跨平台应用，特别适合Go开发者
              是否推荐使用: 是，适合Go开发者
              简评: 基于Go的GUI工具包，注重简洁和跨平台支持

            - name: CEF (Chromium Embedded Framework)
              url: https://github.com/chromiumembedded/cef
              lang: C++ (with bindings)
              跨平台支持: Windows, macOS, Linux
              性能: 高
              是否易用: 困难
              社区支持: 中
              渲染引擎: Chromium
              APP大小: 大
              web迁移: 高
              内存占用: 高
              适用场景: 需要嵌入浏览器或使用Web技术构建UI的应用
              是否推荐使用: 是，适合特定场景如浏览器嵌入
              简评: 嵌入Chromium功能强大，但资源占用较高

            - name: Tauri
              lang: Rust, JavaScript/TypeScript
              跨平台支持: Windows, macOS, Linux
              性能: 高
              是否易用: 中等
              社区支持: 中
              渲染引擎: Webview (WRY)
              APP大小: 小
              web迁移: 高
              内存占用: 低
              适用场景: 轻量级跨平台桌面应用，使用Web前端
              是否推荐使用: 是，特别适合性能敏感应用
              简评: Electron的轻量级替代，使用Rust提供更好性能和安全性

            - name: Zig-webui
              url: https://github.com/webui-dev/zig-webui
              lang: Zig, HTML5
              跨平台支持: Windows, macOS, Linux (依赖浏览器)
              性能: 中
              是否易用: 中等
              社区支持: 小
              渲染引擎: 浏览器引擎
              APP大小: 小 (不含浏览器)
              web迁移: 高
              内存占用: 中
              适用场景: 实验性，使用浏览器作为GUI，Zig后端
              是否推荐使用: 不推荐，因社区小且实验性质
              简评: 使用浏览器作为GUI，Zig后端，但受浏览器限制 # 基于browser实现GUI，不是独立APP，而是嵌入在browser内的。既然如此，肯定会有疑问“如果嵌入browser的话，直接做成web不就行了吗？为啥还要用GUI组件？明明各种web端的UI库比GUI多太多了”，实际上，可以把zig-webui理解为“如果electron能复用browser就太好了”的一种实现，相当于复用browser作为client

            - name: Pake
              url: https://github.com/tw93/Pake
              lang: Rust
              跨平台支持: Windows, macOS, Linux
              性能: 高
              是否易用: 中等
              社区支持: 小
              渲染引擎: Web-based
              APP大小: 小
              web迁移: 高
              内存占用: 低
              适用场景: 使用Rust构建轻量级桌面应用
              是否推荐使用: 是，适合Rust特定场景
              简评: 类似Tauri的Rust工具，构建轻量级桌面应用

            - name: PakePlus # PakePlus 是一个基于 Tauri2 和 Github 的打包工具，无需在本地安装复杂的 rust 和 node 依赖环境，只需要一个 Github Token 就可以将任意网站/Vue/React 等项目的 dist 静态文件打包为跨平台桌面软件，体积小巧(小于 5M)并且性能还高，而且 PakePlus 本身也才仅仅 8M 左右小大。
              url: https://github.com/Sjj1024/PakePlus
              lang: Rust (via Tauri)
              跨平台支持: Windows, macOS, Linux
              性能: 高
              是否易用: 高
              社区支持: 小
              渲染引擎: Web-based (same as Tauri)
              APP大小: 小
              web迁移: 高
              内存占用: 低
              适用场景: 快速将Web项目打包为桌面应用，无需本地依赖
              是否推荐使用: 是，适合快速开发和打包Web应用
              简评: 使用Tauri简化Web应用打包为桌面应用

            - name: Slint
              url: https://github.com/slint-ui/slint
              lang: Rust, C++, JavaScript, Python
              跨平台支持: Windows, macOS, Linux, embedded systems
              性能: 高
              是否易用: 中等
              社区支持: 中
              渲染引擎: 原生 (OpenGL, etc.)
              APP大小: 小
              web迁移: 中
              内存占用: 低
              适用场景: 嵌入式和桌面原生GUI，注重性能和低资源占用
              是否推荐使用: 是，特别适合嵌入式和性能关键应用
              简评: 声明式GUI工具包，支持多语言，适合高性能原生UI

            - name: Qt
              lang: C++ (with bindings)
              跨平台支持: Windows, macOS, Linux, Android, iOS, embedded systems
              性能: 高
              是否易用: 中等
              社区支持: 大
              渲染引擎: 原生 (with Qt Quick)
              APP大小: 中等到大
              web迁移: 中
              内存占用: 中
              适用场景: 复杂跨平台应用，需原生性能
              是否推荐使用: 是，适合需要原生性能和跨平台支持的应用
              简评: 成熟框架，工具和库丰富，适合复杂应用

            - name: GTK
              lang: C (with bindings, e.g., gtk-rs)
              跨平台支持: Linux, Windows, macOS (with limitations)
              性能: 高
              是否易用: 中等
              社区支持: 大
              渲染引擎: 原生
              APP大小: 小到中等
              web迁移: 中
              内存占用: 中
              适用场景: Linux桌面应用，跨平台需额外努力
              是否推荐使用: 是，特别适合Linux或需原生外观的应用
              简评: 成熟工具包，特别适合Linux原生应用

            - name: MAUI (.NET Multi-platform App UI)
              lang: C#
              跨平台支持: Windows, macOS, Linux, Android, iOS
              性能: 高
              是否易用: 中等
              社区支持: 大
              渲染引擎: 原生 (platform-specific)
              APP大小: 中
              web迁移: 中
              内存占用: 中
              适用场景: 使用.NET的跨平台应用，特别适合移动和桌面
              是否推荐使用: 是，适合.NET开发者多平台开发
              简评: 微软的原生跨平台应用框架，基于.NET

            - name: Flutter Desktop
              lang: Dart
              跨平台支持: Windows, macOS, Linux
              性能: 高
              是否易用: 高
              社区支持: 大
              渲染引擎: 原生 (Skia/Impeller)
              APP大小: 中
              web迁移: 中
              内存占用: 中
              适用场景: 单代码库跨平台桌面应用，适合Flutter移动开发者
              是否推荐使用: 是，适合跨移动和桌面一致UI或Dart开发者
              简评: 将Flutter能力扩展到桌面，支持跨平台一致UI

            - name: Electron
              lang: JavaScript
              跨平台支持: Windows, macOS, Linux
              性能: 中
              是否易用: 高
              社区支持: 大
              渲染引擎: Chromium
              APP大小: 大
              web迁移: 高
              内存占用: 高
              适用场景: 使用Web技术构建跨平台桌面应用
              是否推荐使用: 是，适合快速开发但性能可能受限
              简评: 使用Web技术构建跨平台桌面应用，资源占用较高

    - url: https://github.com/PaddlePaddle/PaddleOCR
      des: PaddleOCR还真就是。支持多种 OCR 相关前沿算法。提供产业级特色模型 PP-OCR、PP-Structure 和 PP-ChatOCR。打通数据生产、模型训练、压缩和预测部署全流程。
      topics:
        - topic: 【技术选型】OCR
          table:
            - name: EasyOCR
              url: https://github.com/JaidedAI/EasyOCR # https://github.com/otiai10/gosseract
              开发团队: JaidedAI
              核心特点: 多语言支持、轻量级集成
              支持语言: 80+（含中日韩等非拉丁语系）
              识别精度: 较高（复杂背景表现较好）
              处理速度: 中等
              特色功能: 简单API、多语言自动检测
              部署复杂度: 低（Python一键安装）
              使用场景: 多语言快速识别、轻量级应用
              预训练模型: 提供通用模型
              扩展性: 有限
              授权模式: 开源免费
              技术驱动架构: CNN+LSTM+CTC
              适用场景: 多语言混合文档/移动端轻量级应用:cite[1]:cite[5]

            - name: Tesseract
              url: https://github.com/tesseract-ocr/tesseract
              开发团队: Google（原HP开发）
              核心特点: 老牌OCR引擎、高稳定性
              支持语言: 100+（需单独下载语言包）
              识别精度: 中等（标准文档效果佳）
              处理速度: 较快（CPU优化好）
              特色功能: 命令行工具、LSTM引擎
              部署复杂度: 中（需配置语言包）
              使用场景: 传统文档OCR、历史项目兼容
              预训练模型: 提供基础模型
              扩展性: 中等（可训练自定义模型）
              授权模式: 开源免费 (Apache 2.0)
              技术驱动架构: LSTM+规则引擎
              适用场景: 英文古籍数字化/扫描文档批处理:cite[2]:cite[6]

            - name: PaddleOCR
              开发团队: 百度PaddlePaddle
              核心特点: 产业级全流程解决方案、前沿算法集成
              支持语言: 多语言（中文识别最强）
              识别精度: 高（产业级优化模型）
              处理速度: 极快（支持模型压缩/GPU加速）
              特色功能: 三大产业模型（PP-OCR/PP-Structure/PP-ChatOCR）
              部署复杂度: 中高（支持全流程定制）
              使用场景: 工业级应用（移动端/服务器端部署）
              预训练模型: 丰富预训练模型（包括超轻量版）
              扩展性: 全流程支持（训练→压缩→部署）
              授权模式: 开源免费（支持商业部署）
              技术驱动架构: CNN+Transformer
              适用场景: 金融票据识别/中文场景工业部署:cite[3]:cite[7]

            - name: Surya
              url: https://github.com/VikParuchuri/surya
              开发团队: VikParuchuri
              核心特点: 多语言文档OCR与布局分析
              支持语言: 50+（专注文档结构）
              识别精度: 高（文档结构理解强项）
              处理速度: 中等（依赖GPU加速）
              特色功能: 文档分栏/表格/数学公式解析
              部署复杂度: 中（Python库+依赖项）
              使用场景: 学术论文/技术文档解析
              预训练模型: 提供文档专用模型
              扩展性: 中等（支持自定义训练）
              授权模式: 开源免费（MIT许可）
              技术驱动架构: Transformer+图神经网络
              适用场景: 科研论文解析/复杂版式文档处理


    - url: https://github.com/huichen/wukong # [使用wukong全文搜索引擎 - Tony Bai](https://tonybai.com/2016/12/06/an-intro-to-wukong-fulltext-search-engine/)
      score: 1
      topics:
        - topic: 【技术选型】分词工具
          table:
            - name: sego
              url: https://github.com/huichen/sego
              支持语言: 中文
              开发语言: Go
              分词算法: 双数组Trie树
              性能: 4  # 高性能
              内存消耗: 4  # 低
              词典支持: 3  # 内置+自定义
              维护状态: 4  # 活跃
              社区规模: 3  # 中等
              依赖项: 4  # 少
              特色功能: 3  # 搜索引擎集成

            - name: HanLP
              url: https://github.com/hankcs/HanLP
              支持语言: 多语言
              开发语言: Java/Python
              分词算法: 感知机+CRF
              性能: 4  # 高
              内存消耗: 2  # 较高
              词典支持: 5  # 丰富预训练模型
              维护状态: 5  # 非常活跃
              社区规模: 4  # 大型
              依赖项: 2  # 较多
              特色功能: 5  # NLP全流程支持

            - name: ik-rs
              url: https://github.com/blueshen/ik-rs
              支持语言: 中文
              开发语言: Rust
              分词算法: 词典匹配
              性能: 5  # 极高
              内存消耗: 5  # 很低
              词典支持: 3  # 可扩展词典
              维护状态: 2  # 一般
              社区规模: 2  # 小型
              依赖项: 5  # 极少
              特色功能: 4  # 内存安全优先

            - name: vibrato
              url: https://github.com/daac-tools/vibrato
              支持语言: 日语
              开发语言: Rust
              分词算法: Viterbi算法
              性能: 5  # 极快
              内存消耗: 4  # 低
              词典支持: 3  # 专用词典
              维护状态: 4  # 活跃
              社区规模: 3  # 专项
              依赖项: 4  # 少
              特色功能: 4  # 日语形态分析

            - name: bleve
              url: https://github.com/blevesearch/bleve
              支持语言: 多语言
              开发语言: Go
              分词算法: 插件式
              性能: 3  # 中等
              内存消耗: 3  # 中等
              词典支持: 2  # 需配置
              维护状态: 3  # 维护中
              社区规模: 3  # 中型
              依赖项: 3  # 中等
              特色功能: 3  # 全文搜索引擎

            - name: wukong
              支持语言: 中文
              开发语言: Go
              分词算法: 内置分词器
              性能: 4  # 高
              内存消耗: 3  # 较低
              词典支持: 2  # 基础词典
              维护状态: 1  # 停滞
              社区规模: 2  # 小型
              依赖项: 4  # 少
              特色功能: 3  # 全文搜索系统

            - name: ansj
              支持语言: 中文
              开发语言: Java
              分词算法: NLP算法
              性能: 4  # 高
              内存消耗: 3  # 中等
              词典支持: 3  # 自定义词典
              维护状态: 1  # 停滞
              社区规模: 2  # 小型
              特色功能: 3  # 专名识别

            - name: jieba
              支持语言: 中文
              开发语言: Python
              分词算法: 前缀词典+HMM
              性能: 3  # 中等(400KB/s)
              内存消耗: 4  # 低
              词典支持: 3  # 自定义词典
              维护状态: 4  # 活跃
              社区规模: 5  # 超大型
              特色功能: 4  # 搜索引擎模式

            - name: Thulac
              支持语言: 中文
              开发语言: C++/Python
              分词算法: 大规模语料训练
              性能: 5  # 极快(1.3MB/s)
              内存消耗: 3  # 中等
              词典支持: 2  # 内置词典
              维护状态: 3  # 维护中
              社区规模: 3  # 中型
              特色功能: 4  # 词性标注

            - name: pkuseg
              支持语言: 中文
              开发语言: Python
              分词算法: 多领域模型
              性能: 3.5  # 较高
              内存消耗: 3  # 中等
              词典支持: 4  # 领域定制模型
              维护状态: 4  # 活跃
              社区规模: 3  # 中型
              特色功能: 4  # 细分领域支持

            - name: FoolNLTK
              支持语言: 中文
              开发语言: Python
              分词算法: BiLSTM
              性能: 2.5  # 较低
              内存消耗: 1  # 高
              词典支持: 3  # 自定义词典
              维护状态: 1  # 停滞
              社区规模: 2  # 小型
              特色功能: 3  # 实体识别

            - name: transformer-word-segmenter
              支持语言: 中文
              开发语言: Python
              分词算法: Transformer+CRF
              性能: 3  # 中等
              内存消耗: 2  # 较高
              词典支持: 4  # 预训练模型
              维护状态: 4  # 活跃
              社区规模: 3  # 专项
              特色功能: 4  # 实体识别

            - name: MicroTokenizer
              支持语言: 多语言
              开发语言: Python
              分词算法: 多算法融合
              性能: 3  # 可配置
              内存消耗: 4  # 低
              词典支持: 4  # 高度自定义
              维护状态: 3  # 维护中
              社区规模: 2  # 小型
              特色功能: 4  # 算法可视化


    - url: https://github.com/xbpk3t/mask-party
      score: 5
      des: 【社交项目】
      repo:
        - url: https://github.com/superseriousbusiness/gotosocial
          des: ActivityPub社交网络服务器

        - url: https://github.com/nz-m/SocialEcho
          des: 基于MERN stack实现的。SocialEcho 是一个社交网络平台，具备自动内容审核和基于上下文的身份验证系统。 该项目解决了社交媒体中内容管理和用户安全性的问题。1、自动化内容审核系统，利用多种自然语言处理 API 进行过滤。2、基于上下文的身份验证，提高用户账户安全性。3、用户角色分明，包括管理员、版主和普通用户。4、提供常见社交功能，如创建个人资料、发布帖子、评论及点赞等。5、设备管理功能，允许用户直接在平台上管理登录设备。

      topics:
        - topic: 社交应用 基本认知
          qs:
            - 有哪几种社交 app？移动社交根据用户分为熟人社交、社区社交、陌生社交、内容社交及其他社交。市场容量也大概如此。 系统匹配（二半）、基于话题（即刻）、LBS（面具公园，把选择权完全交给用户）、翻牌子+LBS（陌陌、探探）、随机匹配（soul）

            - "***按照模块和功能拆分一下“陌生人社交APP”？用户模块、feed模块、IM模块、LBS模块、用户匹配模块***"
            - 社交应用中有哪些高并发场景（电商促销活动、社交应用、在线直播、支付场景、游戏场景、大规模数据处理）
            # 电商促销活动：比如“双11”、“618”等大型促销活动，由于用户涌入，服务器需要同时处理大量的请求。
            # 社交应用：社交应用中，用户同时在线数量较多，同时需要进行消息推送、在线聊天等操作，会对服务器造成较大的负载压力。
            # 在线直播：在线直播的场景下，服务器需要同时处理大量的视频流，并且需要实时推送给大量的用户，这也是高并发场景的一种。
            # 支付场景：在支付场景下，由于大量的用户同时进行支付操作，需要服务器在极短时间内进行响应和处理，这也是高并发场景的一种。
            # 游戏场景：游戏中玩家同时在线，进行实时游戏交互，需要服务器在短时间内响应并处理玩家请求，这也是高并发场景的一种。
            # 大规模数据处理：比如搜索引擎、数据挖掘等应用，需要处理大量的数据并返回查询结果，也需要应对高并发的请求压力。

            # [社交APP的痛点及九大流派解析:微信陌陌，咱还约吗? | 人人都是产品经理](https://www.woshipm.com/pmd/154277.html)
            - "社交应用的几大痛点（用户粘性、交换wx之后转熟人社交、男女比例平衡、广场、用户匹配），分别怎么解决？" # 增加玩法、转兴趣社区

            - 社交是关系链+信息传递+互动的结合，而陌生人社交则与熟人社交相对，是弱关系链或者无关系链用户之间从匹配、筛选、社交破冰到关系建立的行为链条

            - 社交应用的付费点（、、、） # [细数全球9大交友App的那些付费点](https://mp.weixin.qq.com/s/R4ruynN-WTYUVIAxv8I5gg)

            - “在线闪聊”怎么实现？ # [即构科技：泛娱乐新玩法“闪聊”](https://www.sohu.com/a/397555348_637822)

            - 怎么优化“翻牌子”社交应用（比如陌陌、探探）的匹配算法? 用户匹配时的核心指标? APAD # [基于双向匹配的陌生人社交策略及算法思考](https://mp.weixin.qq.com/s/zqGKAMMwnGJxqMYZnIkGQg)
            #- Activeness ( 新账户数量、使用时长、每日访问次数、每次匹配完成后的对话频率 )
            #- Pickiness (右滑率)
            #- Attractiveness (被右滑率、信息被点击率、信息展示时长 )
            #- Distance (距离)
            #
            #其中attractiveness  和pickiness基本可以用户推荐的当下可预估，做起来比较简单，activeness就需要考虑宏观匹配、调控及长期规划了
            #
            #由于反馈及时程度往往也较大地影响了这个用户的留存，所以在给用户匹配其他可能Ta喜欢的人时，我们需要在匹配计算公式中加入对方未来活跃(活跃时间间隔预估)并回复的概率，并作为因子影响匹配成功率。比如对实时在线的用户给予优先权（因子分数高），让反馈速度提升。并且我们在展示层引入供需关系，并通过冷热预估、投放策略（贪心、概率、bandit等）来进行控制。
            #
            #细节点上，我们需要在整流程中注意popularity ( 受欢迎度/热度 )，distance ( 距离 )，recency ( 活跃度 )，fatigue ( 疲劳度 )，match ( 匹配度 ) 等；通过引入用户静态标签在召回、排序、干预阶段进行策略学习 ( 比如男生会学习到颜值的重要性，女生模型会学习到学历、职业等标签的重要性，有些喜欢多滑动多看漂亮小姐姐，有些希望快速匹配近距离的异性 )。分地区、分时段、多模型的精细化预估；分布式、多线程、并行计算满足海量数据的实时性要求；在供需失衡之前，实施调控干预。通过数据和算法回顾过去、监控现在、预测未来，利用网络状态的分析及推演 ( 看清网络，为优化提供支撑 ) 指导业务。

            - 怎么优化“内容社交”应用（比如soul）的匹配算法? # [社交中的用户价值、状态评估及算法匹配模型 | 人人都是产品经理](https://www.woshipm.com/it/1840695.html)
            #每个用户都是外在+内在的一个人物画像：外在=外形价值+经济价值，内在=性格+爱好。匹配倾向占比：设z1=外形价值,z2=经济价值,z3=性格,z4=爱好； z1+z2+z3+z4=1。
            #
            #用户发布内容时，判断其价值交换倾向，外在还是内在，为其做对应推荐。即找准用户此刻需求，基于历史积累的价值数据匹配。
            #
            #举例：
            #
            #找投资伙伴：倾向经济价值，z1=0.9，为其推荐经济价值匹配度高的人
            #找短期玩伴：倾向外形价值z2=0.8，为其推荐外形价值匹配度高的人
            #找人聊天：倾向性格 z3=0.9，为其推荐性格匹配度高的人
            #找人一起运动：倾向爱好 z4= 0.8，为其推荐爱好匹配度高的人
            #当用户没有明显需求倾向时，默认值 z1=0.3，z2=0.3, z3=0.2, z4=0.2；
            #再结合其历史匹配信息，结合协同过滤等推荐算法，为其做推荐。

            - 基于CLA算法的跨社交平台用户身份匹配.pdf
            - 基于强社交图的多约束信任图模式增量匹配算法.pdf

            - 大盘指标（DAU、）、社交行为指标（匹配率（单向匹配率、双向匹配率）、APAD、反馈率、在线率）、标签性指标（颜值分（上传照片直接打分）、标签（通过标签和用户生活圈层的相似程度来为每个用户建立一套算法作为推荐参考））
            - 先分类再匹配，对用户分层-建模-双向推荐匹配度最好的用户。通过将A对B和B对A的喜欢概率进行预估，并相乘计算匹配成功率；需要在匹配计算公式中加入对方未来活跃(活跃时间间隔预估)并回复的概率，并作为因子影响匹配成功率。比如对实时在线的用户给予优先权（因子分数高）让反馈速度提升。
            - 根据已有的用户画像，进行召回 和 排序。召回：指从全量信息集合中触发尽可能多的正确结果并将结果返回给“排序”。从【高质量用户/用户画像兴趣标签/同城同省、距离/活跃度、热度/被喜欢程度/互相喜欢、互相交流、匹配度预测】等维度进行召回。排序：召回的内容进行打分排序，选出得分最高推荐给用户，对【高颜值用户/实名认证用户/高回复率用户/对高付费率用户/附近的用户/vip用户/在线用户】进行提权，对【匹配数量上限/用户在线状态（正在聊天、正在视频电话）对低质量用户/脚本机器人/黑产行为】进行降权。召回与排序策略中，需要考虑用户冷启动问题、实时性问题、个性化、效率以及用户隐私等问题

            - 以下都是可通过算法模型预测进行控制的模块：颜值分(美的程度)、颜值匹配分(夫妻相)、右滑率、匹配率、对话开启率；未来N分钟内的上线可能性(匹配到聊天的gap)、未来一天曝光量/匹配量(控量)、未来1、7、15天留存概率(比如对低用户释放权益)；展现序列学习(序列生成器完成序列最优)；权益敏感模型(比如给付费模块动态给折扣)；EE模块，上述模型的仿真与生态控制。模型可分先后顺序，陌生人社交在tinder的滑块类模式下，一定是先颜值后，所以先是看顺眼(颜值)、聊得来(可匹配并交流)、可接触(距离)；以国内陌陌、soul、探探刚好分别侧重这三个方向的其中一个，并且整体匹配逻辑上也更突出其中的因子，本质都是为了匹配，但是第一步迈的不同也带来了用户面的差异，所以探探与Soul的用户重合度小于30%。 # [陌生人社交算法拆解-补充 - 知乎](https://zhuanlan.zhihu.com/p/262777442)

        - topic: 实现
          qs:
            - 内容风控：社交应用这种典型的UGC应用，尤其需要应对敏感词过滤问题，那么在实际项目中怎么处理呢？我打算用阿里云内容审核等第三方API来解决这个问题地方，算了一下每个月api费用要6w。但是我想节省成本。所以我是否可以自己实现一个敏感词过滤，以处理普通场景，后面再加一个阿里云内容审核API，处理未被过滤掉的。这个方案可行吗？还有其他什么更好的方案吗？也就是说自建作为粗筛，但是这样也只能过滤掉有明显问题的，只占总量的很小比例，可能还不到1%。剩下的99%内容仍然要走第三方API。这个方案是明显有问题的。我的思路是，自建可以给内容打分，直接筛掉30分一下的（明显违规内容），放行60分以上的。只让第三方API走中间这部分呢内容就可以了。这个方案怎么样？技术上的挑战有哪些？实现成本？我的思路是，自建可以给内容打分，直接筛掉30分一下的（明显违规内容），放行60分以上的。只让第三方API走中间这部分呢内容就可以了。这个方案怎么样？技术上的挑战有哪些？实现成本？我的意思是我想在保证敏感词模块有效性的前提下，尽可能降低成本。除了打分机制以外，还有什么方案吗？

        - topic: 社交应用产品方面的一些思考 # [一年饮冰，难凉热血：一份普通的陌生人社交产品开发笔记-36氪](https://36kr.com/p/2273879650422407)

        - topic: soul 产品拆解 # [竞品分析：拆解Soul匹配筛选模块产品设计 | 人人都是产品经理](https://www.woshipm.com/evaluating/4777588.html)
          qs:
            - Soul围绕着不看脸推出多种匹配方式（灵魂匹配、引力签、语音匹配、视频匹配、恋爱铃、群聊派对），更能满足用户在不同场景下的社交需求。
            - 匹配成功率（破冰率和消息对数）、深度关系
            - “因为 Soul 的用户可以基于动态前置筛选自己不感兴趣的人，进而不会随意创建会话。”
            - “广场沉淀用户发布的生活碎片，Profile聚合生活碎片形成画像。两个人匹配后通过 Profile 画像判断是否符合自己的择偶观，避免无效会话。”
            - 我想在一个社交应用中区分冷热用户，怎么简易高效地实现？为啥我推荐结合使用RFM和LRU解决呢（RFM针对高价值用户，LRU针对最近活跃用户）？另外，也可以把 RFM和TF-IDF结合使用来筛选高价值用户。
            - soul这个APP目前存在哪些问题？
            #  内容质量问题：有用户反映Soul上存在低质量内容生产者，很多用户不发布内容，或者发布大量文字的、负面的内容，导致他们在软件中的“吸引力”下降。
            #  匹配机制问题：Soul的推荐机制被认为不够精准，考察维度不够全面，反馈机制也不够完善，用户难以屏蔽不感兴趣的话题和人。
            #  社交场景单一：用户之间缺乏了解，没有更有趣味性的活动来破冰，容易陷入尬聊，且社交场景较为单一，导致双方聊得更多的话题局限于两性关系。
            #  隐私泄露问题：Soul在帮助用户更好匹配的同时，也在暴露着用户的隐私，如位置信息、职业信息等。
            #  虚假信息与灰产问题：这也是社交软件的通病，Soul上存在假照和灰产比例，需要投入大量精力解决。
            #  用户流向熟人社交：陌生人社交经常遇到的问题是，匹配成功后，用户流向熟人社交，软件被卸载，这对Soul来说是一个产品痛点。
            #  商业模式问题：Soul的商业模式被认为简单，增长空间有限，对高付费用户依赖重，且推广费用高昂，未来似乎也不太可能下降。

        - topic: zzz
          des: |
            ```markdown
            社交产品核心功能有哪些?如果是发现类的陌生人社交，例如探探，就是用户滑动卡片和聊天操作。我们要留意的是新用户的滑动操作的人均次数，看看用户的使用是否顺畅，在哪一步流失最严重，这个我们在后面的用户行为漏斗再详细说一下。还有其他的社交产品，例如我们的语音直播房间，我们会观察用户进房，上麦和停留时长的数据指标，看看用户是否满意核心功能，可以初步验证产品是否成立。

            核心功能触达数据，前面也有说到，每个产品的核心功能是不一样的，而且产品的阶段目标不一样也不一样。拿我们产品来说，产品中期的时候，我们中期的目标是营收和用户活跃，所以产生营收和用户活跃的场景都是我们关心的，房间是产生收入最重要的场景（送礼），所以我们关心用户进房间的次数和时长，还有上麦的次数和时长等。这些都是我们中期关心的数据；

            用户关系数据包括哪些呢？形成关系的数据有：匹配请求、匹配成功率、匹配资料查看次数等，用户关系维持数据有：聊天人均次数、聊天次数分布、用户关系沉默比例等。
            ```

        - topic: 【约吧项目】遇到了哪些问题？
          qs:
            - 如无必要，不要分表。社交项目里把用户表和用户详情表拆开了，结果在实现过程中发现两张表大部分字段是重复的，但是却需要维护两份相同数据，维护有问题还会有数据不一致的情况，太恶心了。总之一句话，如果不是业务必须要拆表，那么都应该尽量维护在一张表里
            - 之前是面具公园一样后置付费的逻辑，现在变成前置付费之后，user 表就很奇怪，会有很多未完善资料的用户；本身后置付费的时候，就会有用户没填资料，现在变成前置之后，可能只有 1/10 的用户填写资料。
            - 不应该在发送短信之后，就直接在 user 表生成用户 # 如果是前置付费，一定是付费完成后，才写到 user 表。如果是后置付费，也应该在填写资料之后，才生成用户（否则就会有很多空用户）
            - 用户如果拒绝定位，该用户的 geo 字段就为 null，这个时候，我们不仅要考虑到当前用户的 geo 是否为 null，也要考虑到目标用户的 geo 是否为 null，否则会发生很严重的问题

    - url: https://github.com/xbpk3t/dc
      score: 5
      des: 【贷超项目】
      topics:
        - topic: 贷超项目
          picDir: works/互金
          tables:
            - name: 《互金项目贷后运营（还款、复贷、逾期催收）》
              url: https://www.sohu.com/a/716423634_114819
              table:
                - name: 还款
                  业务特征: |
                    - 主动还款（用户手动操作）
                    - 代扣还款（自动划扣）
                  关键指标: |
                    - 正常还款金额
                    - 主动还款成功率
                    - 代扣成功率
                    - 总还款成功率（关联利润）
                  行业普遍做法: |
                    - 短信/电话还款提醒为主
                    - 运营干预空间有限（因征信约束力强）
                  核心运营策略: |
                    - 产品运营为核心
                    - 配置稳定性保障（避免代扣分账错误导致逾期）
                    - 延期还款SOP（客服协助协商减免罚息）
                    - 反催收灰产防范策略

                - name: 复贷
                  业务特征: |
                    - 结清复贷（全部还清再借）
                    - 未结清复贷（额度未用尽续借）
                  关键指标: |
                    - 复贷率（结清/未结清）
                    - 复贷金额
                    - 复贷成功率（类借款通过率）
                  行业普遍做法: |
                    - 基础分层（按结清状态/RFM模型）
                    - 自动化触达（策略验证后系统执行）
                    - 价值模型驱动（多维度评分→定制权益→通道优选）
                  核心运营策略: |
                    - 分层经营为核心
                    - 风控额度分层（效果最佳）
                    - RFM模型（低频场景效果差）
                    - 权益策略：
                        - 免息券＞还款券（转化高1%）
                        - 提额推送（优质用户动支率翻倍）
                        - 权益门槛（防羊毛党）
                    - 慎用多卡多资方（避免多头借贷恶化）

                - name: 逾期催收
                  业务特征: |
                    - 用户逾期→资方催收
                    - 降低坏账保障利润
                  关键指标: |
                    - 逾期金额（M0-M6+分段）
                    - 入催率/出催率
                    - 迁移率/滚动率
                  行业普遍做法: |
                    - 自建催收团队（主流）
                    - 资产证券化（大银行打包不良资产出售）
                    - 催收平台监控（通话时长/行为数据优化）
                  核心运营策略: |
                    - 催收系统埋点监控
                    - 重点关注M1-M3关键回收期
                    - 策略性放弃＞M6难挽回案件
          qs:
            - "***【项目模块拆解】用户模块、推广渠道模块***"
            - 一个统计相关的问题，典型的 `同期群分析` 问题，举个例子，昨天渠道 a 做了 10 万个量，今天没做量，但是大部分用户都是今天付费的，应该算今天的量，还是昨天的量？ # 从正常流程来说，应该是算今天的，但是从统计角度出发，应该算昨天的。*还需要注意的一个关键问题是，系统的推广应该是以“次”为单位的*（*通常做法是，一次推广就下发一批推广链接*，像贷超系统里的 `推广渠道功能 `做法的问题在于，只生成一次推广链接，之后除非下架，否则一直续用，这样无法做到精确统计*每次推广每个渠道*的转化（按照现有的做法，如果想要统计某次推广的转化，只能先取出` 当天注册用户`，再查出后续逻辑里这批用户的关键操作的转化，实现起来比较复杂，也不合理））

            - 支付时，重复支付的问题。造成问题的原因是，用户连续点击，生成多条订单，比如订单 a 和订单 b，用户支付掉订单 a，订单 b 会因为支付超时自动关闭；但是查看支付状态时，直接查看 latest 的一条，就是订单 b，这样的话，就会看到是“未支付”状态，导致错误

            - |
              【互联网消费信贷常见模式】
              小额现金贷（直接向借款人提供资金，金额较小（一般五万以内），实时放款）
              常规信用贷（直接向借款人提供资金，金额较大（3-30万），非实时（有24h以内的风控））
              场景消费贷（向服务提供方划转，实时放款）

            - 【互金系统监控系统】（业务汇总类监控、贷款流程监控、定时跑批监控、外部接口监控）
            #业务汇总类监控
            #1） 对当天的进件量进行监控。 例子：当天进件量超过50000件。
            #2） 进件审批场景的监控：如审批的数量、失败数进行监控；例子：当天审批失败的笔数及客户数超过30笔；当天累计审批通过1000笔。
            #3） 实时客户访问数量进行监控；例子：当前客户访问量超过10000；当前客户访问量比上周同比增加80%；
            #4） 对累计放款金额的监控：例子：当天累计放款金额超过一千万；
            #贷款流程监控
            #1） 放款、还款流程监控   例子：信用小贷产品未放款或放款失败，订单号：xxx1000；
            #2） 审批环节监控   例子：信用小贷产品自动审批出错，错误码：10010；
            #3） 风控环节监控   例子：风控规则组出错，错误码：10010；
            #4） 关键业务规则监控   例子：出现贷款额度大于1千万的合同；出现贷款利率超过30%的借据；
            #定时跑批监控
            #1） 跑批节点进行监控  例子：日终批量补充批节点出错，错误码：10010；
            #2） 跑批时效进行监控  例子：日终批量补充批节处理时间超过1小时；
            #外部接口监控
            #1） 调用外部接口超时监控  例子：人行征信接口连续20秒内相应时间大于10秒；
            #2） 调用外部接口出错监控  例子：公积金查询接口连续出错10次；

            - 【互金系统风控模块】准入规则、反欺诈模型、风险等级划分、贷后检测、模型优化与验证
            #风控模型应该是从两个角度去考虑，第一个角度是资产端风控策略，第二个角度是资金端风控策略。考虑主要出发点应该是从贷前、袋中、贷后三个方向去考虑，结合传统业务的风控模型和互联用户的行为数据。针对资金，资产进行风险等级划分，防欺诈系统、袋中的舆情监控、贷后的权重叠加。
            #1）. 准入规则：对不同客户制定不同的贷款门槛，比如根据注册年限和消费次数等设置一个基本的准入门槛，对于后期可以分层次分批次的制定不同的风控策略。
            #2）. 反欺诈模型：从申请反欺诈、行为反欺诈、设备反欺诈等多维度制定反欺诈规则，确保及时侦测和处置可疑警告，维护黑名单数据库及时性、准确性、有效性，熟悉了解贷前、贷中、贷后业务全流程对反欺诈功能的需求。
            #白名单: 可以通过建立数据模型已经数据挖掘，机器学习相关算法进行优质用户的挖掘。
            #黑名单: 黑名单企业可以针对那些逾期、破产企业(法人作为黑名单)、通过手机号码、imei作为用户判断标识，调用第三放征信公司去进行鉴别。
            #3）. 评分卡：根据风险策略设置相应的权重，指定出完整的评分模型，并依据评分结果指定出审批策略、授信策略等。
            #4）. 风险等级划分：将不同的客群进行细分，采用决策树或规则组的方式对不同的客群制定不同的策略和规则，实行精细化审批。
            #5）. 贷后检测：对信贷客户进行日常贷后监测，及时发现风险信号，对于触发风险预警的客户采取一定的措施，如电话核实、提前收回贷款等。
            #6）. 模型优化与验证：跟踪、监测、维护及优化风控策略，确保风控策略的效能及其提升。




    - url: https://github.com/xbpk3t/longzhu-api
      des: 【直播项目】
      score: 5
      rel:
        - url: https://github.com/iyear/pure-live-core
          des: “没有礼物、粉丝团、弹窗，只有直播、弹幕”的直播项目，golang + vue
        - url: https://github.com/hr3lxphr6j/bililive-go
          des: 这个东西是各种平台视频切片或者录制组需要的工具，看起来还不错。用builder模式把所有的直播间创建和配置所有live对象。核心就是lives.go。单体应用，但是也加了prom+grafana的metrics监控，因为这个不是用来监控服务的，而是用prom来监控直播间的。
          rel:
            - url: https://github.com/auqhjjqdo/LiveRecorder
            - url: https://github.com/limitcool/bilistream
            - url: https://github.com/withsalt/BilibiliLiveTools
            - url: https://github.com/timerring/bilive
              des: 自动监听并录制B站直播和弹幕（含付费留言、礼物等），根据分辨率转换弹幕、语音识别字幕并渲染进视频，根据弹幕密度切分精彩片段并通过视频理解大模型生成有趣的标题，自动投稿视频和切片至B站，兼容无GPU版本，兼容超低配置服务器与主机。
      topics:
        - topic: 直播项目
          picDir: works/live
          qs:
            - "***直播项目的服务端，应该有哪些模块构成？核心模块呢？***" # 用户发弹幕、送礼物，主播打PK # 媒体模块，还有信令控制，登录、鉴权、权限管理、状态管理等等，各种应用服务，消息推送，聊天，礼物系统，支付系统，运营支持系统，统计系统等。
            - 设计一个直播系统、包含送礼、长连接、推送、实效性等 (在线画架构图)？
            - 播端（推流端）和看端（拉流端）：“推流端的采集，前处理，编码，和拉流端的接码，渲染”、“中间用server做传输，接收推流端的数据，再分发到拉流端”
            - sc怎么实现？
            - 我有一个需求，给用户发优惠券，但是多笔发送，比如说1000块的优惠券，分成5笔，每张优惠券的有效期是5天，怎么实现？ # 直接在MQ中产生5个延迟任务，到点执行就可以了，通过uid和orderid标注这5笔子订单都属于同一笔。现在longzhu里是
            - "***众所周知，视频网站和直播网站的成本大头都是CDN和带宽成本，那怎么针对性降低这项成本呢？***"

        - topic: x
          des: |
            ```markdown
            所有的互动形式我们都需要统一的进行抽象管理，可以用一句话概括:“在指定的房间指定的时间段，启用一个或多个互动活动/玩法”
            至于某个具体的互动玩法，该玩法需要哪些素材，需要哪些触发媒介，除了通道部分可以走订阅方式，其他都需要定制开发
            比如，弹幕互动中的蓄力类的，除了通用的弹幕通道之后，哪些词，在多少时间窗口内命中多少次，然后触发什么业务逻辑。
            或连击送礼蓄力类，除了送礼通道是通用部分之外，哪些礼物，在多少时间窗口内送出多少个，金额满足多少都属于业务逻辑需要捕获和处理的地方。
            整个互动是需要打通看端和播端。
            看端特效和播端特效是有明显区别的，看端多数是在直播间里可以交互的特效元素，而播端特效最终是在流里体现的。
            ```

        - topic: 直播房间服务基于CQRS的架构演进实践
          url: https://www.51cto.com/article/768163.html

        - topic: 龙珠直播
          qs:
            - 我有个问题哈，一个经典场景，直播平台的房间封禁，我需要在该操作中异步执行直播间断流、封禁房间、把该房间从列表页移除等等操作，但是如果异步的话，我就无法保证所有操作都能成功了，并且也无法使用事务来进行操作，因为以上三个操作分别涉及到阿里云直播的pkg、DB操作和redis操作，有什么好的解决方案呢？ # 推荐同步操作
            - 我们现在有一张对于余额的各种操作的日志表，需要加一张状态表。按照我的想法， 如果想要保证数据一致性，其实直接把这部分写操作，放到该日志表的hook里，就可以了，这种设计有什么缺点吗？





    - url: https://github.com/skr-shop/manuals
      des: 【电商项目】
      score: 5
      rel:
        - url: https://github.com/crmeb/CRMEB
          des: 功能最贴近企业级电商应用的开源项目，有PHP和java两套代码。
        - url: https://github.com/ChangSZ/mall-go
        - url: https://github.com/cexll/mall-go
        - url: https://github.com/newbee-ltd/newbee-mall-api-go


        - url: https://github.com/feihua/zero-admin
          des: gozero实现的电商项目，表设计可以借鉴

        - url: https://github.com/zhongzhh8/SecKill-System
          des: 秒杀模块，基本原理和实现我很了解，需要实现时仅供参考。
          topics:
            - topic: 秒杀模块 基本认知
              qs:
                - 业务对于秒杀的期望 # 1、每个用户都能参与，但是有且仅有一次机会。2、参与的用户都是真实有效的。
                - "***怎么保证每个用户只能参加一次（怎么实现一个账号只能发送一次请求？）？怎么保证秒杀过程中，用户的真实和有效？怎么做秒杀业务的请求合法性校验？***"
                #- `接口限流`，对请求频率过快的用户进行限制，频率请求过快或者请求头异常的请求，返回图形验证码到前端（建议不要使用简单的数字验证码），触发人工解锁，确保背后是一个自然人
                #- `分析用户图像`，根据用户的活跃度，等级，资料的齐全程度；历史购买商品，对用户进行评分，达到特定分数的用户才能参与秒杀
                #- `制定业务门槛`，比如说，要参与秒杀，对于一个电商平台，该用户至少有 1000 元的历史消费额，对于一个理财平台，该用户必须要有订单在投

                - 怎么设计秒杀表（秒杀库存表、秒杀记录表）？
                # - 秒杀库存表 (商品库存 id，商品名称，商品库存数，秒杀开始时间，秒杀结束时间，记录创建时间)
                # - 秒杀记录表 (商品库存 id，手机号码，秒杀状态 1 秒杀失败 2 秒杀成功，秒杀时间)

                - 如何实现“秒杀按钮//api”在活动开始之前不生效？通过“活动开始前通过更新 js 文件再暴露秒杀接口”，有什么要注意的吗？
                # - 秒杀的参数，是否开始 flag 和秒杀接口的随机参数
                # - 秒杀开始时生成一个新 js 文件，主动刷新用户浏览器并展示
                # - *通过使用随机版本号比如`xxx.js？t=987856`，保证 js 文件不被浏览器、CDN、反代服务器缓存*

                - 秒杀单件商品和多件商品有什么区别？
                - 秒杀商品涉及到哪些业务？秒杀商品就引入了商品、订单、支付、库存的锁定和释放、支付的时效性等问题
                - 用静态页面还是页面缓存？当然*用页面缓存*，通常来说，不怎么变化的页面做真静态；经常变化的页面做缓存，不然的话，一变化就要重新生成，浪费时间

                - 服务端具体有哪些流程？要处理哪些东西？两个高并发事务，用户抢优惠券、查询优惠券，这部分通过 redis 实现
                - 服务端怎么实现秒杀系统？乐观锁、悲观锁、队列怎么选择？使用 redis 乐观锁实现秒杀的好处？不使用悲观锁，因为秒杀是典型的读多写少的场景，使用乐观锁，锁开销要小很多。使用队列，队列的开销也比乐观锁更高。

                - 乐观锁的原理？cas 的原理？如果用乐观锁，还是超卖了，可能是什么原因？如何避免Check-Then-Act问题？
                - 使用排他锁，超卖了，为什么？
                - 使用队列实现秒杀，有什么要注意的地方？ # 使用队列可以防止超卖，但是吞吐量没有乐观锁好，还需要使用新组件
                - 要注意哪些东西？ # 1、注意要通过对象缓存 + 页面缓存 + 页面静态化 + 前后端分离，加速秒杀商品的展示。2、使用布隆过滤器，防止缓存穿透问题。3、给 redis 缓存过期时间附加一个随机值，防止缓存雪崩
                - 这里只实现秒杀券业务，不实现秒杀商品，因为秒杀商品就引入了商品、订单、支付、库存的锁定和释放、支付的时效性等问题。这里只聚焦秒杀系统最关键的高并发场景。另外，秒杀券跟其他业务是隔离的，如果以后需要，也可以直接复用。 当然，这样的话，也无法直面很多秒杀商品的真正需求，对该业务缺少理解

        - url: https://github.com/huanghanzhilian/c-shopping

        - url: https://github.com/PingPlusPlus/pingpp-go
          des: ping++ 聚合支付

        - url: https://github.com/kongyuebin1/dongfeng-pay
          des: 聚合支付【项目】
          topics:
            - topic: 聚合支付平台
              qs:
                - 聚合支付平台是什么？几种聚合支付平台（技术集成类、机构转接类、机构直清类、资金二清类）分别是啥？ # 起到支付渠道和我们平台上商户之间的桥梁作用，上游是支付宝、微信、银联、银企直联等，下游是各种商家
                # - 技术集成类的，像 ping++这样的，只做技术整合，不动资金，多次签约，支付渠道单独签约
                # - 是机构转接类，比如天工收银，和银行合作，资金银行托管，只做信息的二清
                # - 机构直清类，一般是金融企业或三方支付机构，如民生银行、恒丰银行等，一次签约，做信息和资金的清算
                # - 资金二清类的，属于要重点监管的，就是以大商户的模式接入，然后再给小商户清算

                - "***“资金二清类平台”是被重点监管的，怎么规避风险？***" # [支付 x 聚合 x 分账 - 回流平台“二清”风险规避之路 · Ruby China](https://ruby-china.org/topics/42576)
                - "***一个聚合支付平台包含哪几个子系统（支付系统：支付网关、主动对账、退款网关、基本支付/退款/转账能力、支付记录/明细、相关的监控运维系统）（财务系统：账务清算、对账系统、账户体系、风控系统、现金流量管理系统）？***"

                - 支付相关概念（聚合支付、幂等性、支付通道、终态、异步、风控、对账、虚拟账户、支付网关、支付要素、数据设计）

                - 聚合支付的清分体系设计 # [聚合支付的清分体系设计-移动支付网](https://m.mpaypass.com.cn/news/202402/02165358.html)

        - url: https://github.com/robGoods/sams
          des: 自动下单
          rel:
            - url: https://github.com/adyzng/go-jd
            - url: https://github.com/zc2638/ddshop

        - url: https://github.com/medusajs/medusa
          des: 一个开源的电商建站平台，可基于各种UI组件与API，用模块拼装的方式，快速搭建个人电商平台。

        - url: https://github.com/zongzibinbin/MallChat
          des: MallChat 是一个既能购物又能聊天的电商系统，以互联网企业级开发规范要求实现。它包括购物车、订单、支付、推荐、搜索等电商必备功能，并持续更新中。基于 SpringBoot + Mybatis + redis + Caffeine + Nginx + websocket 实现。 该项目具有以下特点和优势：1、支持微信扫描登录。2、提供丰富的消息类型和实用小轮子（如 AOP 日志，分布式锁注解）。


        - url: https://github.com/medusajs/medusa
          des: Medusa 是一个电子商务平台，具有内置的定制框架，允许您在不重新发明核心商务逻辑的情况下构建自定义商务应用程序。以下是一些具体的使用场景：1、自定义电商网站：使用 Medusa 构建和定制您的电商网站，满足特定业务需求。2、集成第三方服务：通过 Medusa 的集成模块，轻松连接支付、物流等第三方服务。3、多渠道销售：利用 Medusa 支持多渠道销售，包括线上商店、实体店和社交媒体等。4、扩展和定制功能：使用 Medusa 提供的框架和模块，开发和集成特定功能，如促销活动、会员管理等。
      record:
        - 【2025-06-24】移除【stripe】相关
      topics:
        - topic: 商品模块
          qs:
            - "***怎么设计 sku 系统的表***（主表（商品表、sku 表（库存表）、sku 属性表）、拓展表（分类表、商品表、商品和属性的关联表、商品搜索表、商品和属性的筛选表（用 sql 全文检索实现筛选）））" # 商品表对 sku 表一对多，sku 表对 sku 属性表一对多

            - 有哪些场景需要库存回滚
            #- （用户未支付）用户下单后后悔了
            #- （用户支付后取消）用户下单&支付后后悔了
            #- （风控取消）风控识别到异常行为，强制取消订单
            #- （耦合系统故障）比如提交订单时提单系统 T1 同时会调用积分扣减系统 X1、库存扣减系统 X2、优惠券系统 X3，假如 X1,X2 成功后，调用 X3 失败，需要回滚用户积分与商家库存。

            - 库存扣减有哪些方案？库存扣多了，怎么办？怎么保证不多扣？
            #- 用“设置库存”替代“扣减库存”，以保证幂等性
            #- 使用 CAS 乐观锁，在“设置库存”时加上原始库存的比对，避免数据不一致
            - 库存更新怎么做的呢？分布式锁

        - topic: 购物车cart
          qs:
            - cart table schema?
            - 未登录用户添加商品到购物车，登录后，怎么合并购物车？ # 未登录下加入购物车很简单，具体操作如下，服务端下发 key，客户端把 key 存本地，添加购物车时，服务端创建一个结构为`shopcart:notlogin:{key}`的 hash，值为`goodsId:{附加数据}`，再次添加时直接找到对应 key，往里面添加新数据就可以了。等到用户登录后合并购物车，具体操作如下，登录后异步任务，把上面的`shopcart:notlogin:{key}`的数据插入到数据库，和登录状态下的购物车数据合并，再 rewrite 到`shopcart:login:{userId}`里

        - topic: 订单模块
          qs:
            - "***订单模块的表设计：订单模块都有哪些表？***（主表（购物车表、订单表、订单商品详情表）、拓展表（订单发票表、订单物流表、订单退货表、收货地址表、订单提成表、订单业务审核流程表））"
            - 订单模块中订单状态和支付状态的关系？
            #- 支付状态：未支付，已支付
            #- 物流状态：未发货，已发货，已签收
            #- 订单状态：等待付款，等待发货，确认收货，交易完成，交易关闭
            #- 售后状态：申请售后，退款完成

            - 订单号、交易号、流水号之间的关系：trx_id（交易号） 和 trx_code（支付成功后，这笔交易的 code）有什么区别？为啥需要trx_id和trx_code的一对多关系？
            #  是否每次支付请求都生成一个新的 transaction_code（不重复）来解决重复支付问题，如果真的是重复提交支付请求并重复支付了，那么解决退款问题呢，具体退哪一笔？
            #  重复支付后具体退哪一笔？如果用户支付了两笔，你肯定会收到两个异步通知，那么最后收到的异步通知视为重复支付，对其进行退款。可以看这张表 pay_repeat_transaction。
            #  这样如果用户重复多次申请支付，是不是导致 transaction_code 膨胀？关于膨胀的问题，可以对 code 设置一个生成次数，比如：2^6。因为一般超过这么多次还在发起请求要么是恶作剧，要么你支付服务出了故障。

            #- 订单号是指客户下订单时系统生成的唯一编号
            #- 交易号是指客户完成支付后系统生成的唯一编号
            #- 流水号是指客户完成支付后第三方支付平台生成的唯一编号（订单号和流水号，通常是一对一的，也要看是否。如果一个订单需要多次支付，或者对不同业务方支付，或者存在退款的情况，就是一对多；如果多笔订单，需要汇总结算支付，就是多对一。）

            - 是否每次支付请求都生成一个新的 transaction_code（不重复）来解决重复支付问题，如果真的是重复提交支付请求并重复支付了，那么解决退款问题呢，具体退哪一笔？
            - 重复支付后具体退哪一笔？如果用户支付了两笔，你肯定会收到两个异步通知，那么最后收到的异步通知视为重复支付，对其进行退款。这样如果用户重复多次申请支付，是不是导致transaction_code 膨胀？

            - 订单号生成：“既需要控制订单号长度，又要保证高并发下订单号唯一”，订单号的生成规则（时间戳（14位，精确到秒）+ 8位随机数串）？有哪些方案？ # 可以*对用户 id 用 FNV 哈希算法取值（注意一定要使用完整用户id，因为取用户 id 前 n 位的碰撞概率很高），并填充或者对该值进行位运算来保证统一长度。该方案天然支持分库分表下根据订单号查询、根据用户 ID 查询、根据商户号查询这三类核心查询，因为用户 id 是固定的，`hash(last6(orderId))%16`取模后的值也是相同的 (`因子分表法`)，某个用户的所有订单都会分在同一个表，很容易查询

            - 订单拆分 (子订单)
            - 订单拆分的原因？拆单分为“物理拆单”和“逻辑拆单”两种。实际上，用户结算实际上是“合并付款”，用户付款结束后，我们把订单按照店铺拆单。这样退款的时候，实际上退的是某个店铺的子订单
            - 订单拆分的原则是什么？不改变原始订单数据
            - 怎么拆分订单？
            #- 如果是 B2C 如京东，*根据 sku 拆单，生成多条能关联到父订单的子订单*
            #- 如果是 B2B2C 如淘宝，通常拆分两次，先根据商家拆单，再根据上加下 sku 拆单
            - 是否需要“订单合并”？
            - 订单转移和订单拆分分别是什么？有什么关系？

            - 订单超时自动取消的7种方案

        - topic: 退货退款
          qs:
            - 用户下单后退款，但是积分已经被使用掉了，怎么办？ # *用户只有在确认收货后，积分才能被使用。用户下单后，积分冻结状态，只展示，但是无法使用*。这样就不存在下单后退款，积分却已经被使用的情况了
            - 用户退款成功后，获得的积分肯定是要退还的。但是如果目前该用户的总积分少于退还积分，怎么办？ # 已抵扣的话，则差值直接从退款金额中抵扣
            - "*积分兑换成功后，对积分兑换发起退货，怎么处理？比如积分兑换 -110 积分，退货后直接退还 110 积分，还是分别退还到原消费记录？*" # 应该退还到原消费记录，否则的话，就可以通过兑换再退货的方法，把即将到期的积分重新激活来薅羊毛。*积分应该遵循先进先出的原则，每笔积分的获得都是有时间记录的，不是简单的累加*
            - 什么情况下需要审核才能退款，什么情况下可以自动退款？
            - 订单涉及到多商品 + 优惠券 + 积分，怎么退款？通常有三种方案
            #- `不支持单品退货，只能退整单`。金额退还，赠送积分扣掉，优惠券扣掉
            #- `退单品，退还该商品按比例分摊后的实际支付金额`
            #- `退单品，直接退还该商品的售价`，重新计算优惠券分摊，从用户账户除了扣掉赠送积分，还要扣掉分摊后对应优惠券金额对应的积分数

        - topic: 营销活动
          qs:
            - 电商应用中有哪些常见的营销活动形式（满减、优惠券、抽奖、买一送一、换购（满 a 元，再加 b 元以换购价获得换购商品）、购物券、折扣、红包、金币、跨店满减）？
            - 多种营销活动（比如满减、购物券、折扣、红包、金币、跨店满减）叠加计算扣减，如何实现？
            - 怎么设计“充值促销活动”？“充值促销活动配置表”和“充促日志表”的表设计？充值触发策略（首充、当日首充、累计金额赠送）和赠送策略（送积分、运营币） # 解决方案：使用责任链模式来衔接普通充值和充值赠送策略业务，使用策略模式来判定赠送，用户充值完后异步判断是否满足促销条件，可以使用mq来解偶相关异步操作，插入充值促销获得记录，插入对应赠送的流水表(比如赠送快乐币)。业务系统接收到支付系统成功后，通过责任链来衔接普通充值和充值促销活动，当执行到促销业务逻辑时，通过获取在此之间运行的充值促销活动来判断执行充值的逻辑有哪些，执行逻辑中会有其他赠送逻辑，依旧使用策略模式去判断调用。

        - topic: 优惠券
          qs:
            - 优惠券的作用？（拉新留存促活、用户分类精准运营、降低用户对于价格的敏感度）
            - 如何保证优惠券不超发？
            - 如何生成唯一的优惠券？
            - 如何应对大量的发券请求？
            - 什么是折上折？怎么防止折上折？ # 折上折就是同时满足折扣和满减，并且有优惠券，那么同时享受三种折扣，这样就有薅羊毛的机会了
            - 优惠券发放（活动统一发放、用户操作触发（活动页面发放、订单满额发放、邀请好友发放、新用户注册发放））
            - 优惠券使用规则
            - 优惠券金额处理
            - 优惠券状态（待使用、占用中、已使用、已失效）
            - 优惠券使用（叠加、金额判断、自动使用）
            - 怎么优化优惠券模块的代码？ # 用策略 + 工厂优化优惠券模块代码，优惠券模块用通过工厂模式去实例化不同的策略类，把策略类的方法统一调用来实现。我们通常把设计模式的粒度控制到模块级别。
            - "**优惠券模块的表设计怎么做？**"

        - topic: 跨境电商
          qs:
            - "***跨境电商的6种模式：跨境电商行业的演化（ebay/亚麻 寄售、shopify独立站、、temu）（跨境电商零售模式、跨境电商B2B、B2C模式、C2C、独立站、跨境电商海外仓模式、跨境电商保税模式、市场采购模式）***" # [What is Temu? The cheap online site changing the way we shop, explained - Vox](https://www.vox.com/money/23992696/temu-discount-wish-amazon-shein-chinese-online-shopping)

            - 我想做反向跨境电商，怎么解决在地仓储问题呢？除了自建物流以外，怎么减少仓储成本？
            - 效率工具，仓储，报关清关通关
            - 清关/报关/截关/结关/通关 分别都是啥意思？出口时的先后顺序是？
            - 单证放行/已放行/已结关 有什么不同？





    #- url: https://github.com/ccbikai/Sink
    #  des: Sink 是一个简单/快速/安全的链接缩短器，100% 在 Cloudflare 上运行。 该项目主要解决的核心问题是将长链接压缩为最小长度，并提供了以下功能和优势：1、分析统计。2、serverless 3、可自定义 Slug（利用人工智能生成 Slug）4、链接过期时间设置
    #- url: https://github.com/icowan/shorter
    #  des: go-kit + antd 实现的短链服务
    #- url: https://github.com/missops/go-shortlink
    #- url: https://github.com/liheng666/shortUrl
    #- url: https://github.com/YOURLS/YOURLS
    #  des: PHP实现的短链接服务
    - url: https://github.com/changkun/redir
      des: redir的发号器直接用的uuid
      topics:
        - topic: "***短链接服务***" # TODO
          url:
          why:
            - 为什么需要短链接服务（为什么要用短链，而不是长链接？）？ # 因为字符长度限制、统计、安全、统一管理，所以需要短链接服务。总之都是为了“外链可控”。主流社交媒体平台如weibo, twitter，包括CMS、电商网站都会内置短链接模块。
            # - 在限制内容长度的平台如短信、微博，可以有效控制
            # - url 转二维码，最好使用短链，因为长链生成的二维码更密集，不利于传播
            # - 有些平台无法识别长链，只截取部分 url，会产生问题
            # - 可以统一管理所有 url，三方链接存在于多个网页/代码中，一旦变更，就需要多处更改
          what:
            - "***短链接服务的核心需求?***"
            - 短链接服务的封底估算：我的短链接服务需要每天生成1亿个URL，帮我估算 TPS、QPS、存储成本（假设要运行10年）
          hti:
            - "***用户点击生成“短链”后的整个流程?***"
            - su能统计什么呢?
            - su冲突怎么办? # ~~需要先用redis BF判断lu是否不存在（每次成功生成su后，把lu存在BF里）~~ 不需要BF，无论redis存string还是hash，都直接查找lu即可


          hto:
            - 坏链检测(link-check) # 是否可以参考ip池，对坏链打分，优先check低分link
            - 内容审查，查杀恶意URL（发号时检测简单，生成后被恶意篡改怎么查杀？）
            - 统计
            - 短链接服务高可用（分库分表、读写分离、引入缓存、防攻击）
            - 短链和长链的映射关系? 一对一还是一对多? 为什么? # 一对多，同一个长链接，因为不同的参数，所以生成不同的短链接。可以通过这些参数做数据分析，比如用户相关参数如`生成 url 的用户名`、`当前的 ua`、`之前的 referer`等参数
            - 细节：为什么要用302临时重定向（而不是301）? # 因为301走浏览器缓存，就无法统计，并且无论是坏链还是恶意URL都无法控制
            # *一定要用 302 临时重定向，主要是基于`浏览器缓存问题`，以及由他产生的（坏链、统计和可控问题）三个问题*
            # - `坏链`，长链坏链了，我们修改之后，301 跳转还是会跳到坏链
            # - `短链可控问题`，如果部分长链的内容涉嫌违规，使用 301 跳转无法实现
            # - `统计问题`，使用 301 跳转，直接走缓存，服务端无法统计该 url 的真实使用次数

            - "***发号器算法：短链接服务的`发号器`怎么实现？生成 url 的算法有哪些？怎么选择？***（随便什么算法都行（md5, uuid, snowflake, 洗牌算法, MurmurHash（应该使用murmur64，而非murmur32（100W大概会有121个冲突））, ...），base62处理后截断前8个字符生成su）"
            # - `类 uuid`（插入 db 时可能会频繁导致页分裂和不饱和的节点，导致数据库插入性能降低，影响插入性能）
            # - `snowflake`（snowflake 依赖于系统时钟的一致性，如果某台机器的系统时钟回拨，有可能造成 ID 冲突，或者 ID 乱序）
            # - *`MurmurHash 算法`+`转进制`*，MurmurHash 的 32bit 的最大值是 43 亿，10 进制转 62 进制之后，6 位数的 62 进制大概 568 亿
            # - *不希望反推出全局 ID，使用洗牌算法*，打乱算出的值，比如十进制的 201314 就可以转换为 Qn0。然后再使用洗牌算法，可以返回 n0Q、Q0n....其中之一。但是会有一定几率冲突，多洗几次就行
            # - 希望反推出全局 ID；那就在得到 Qn0 这个数字后，将其转换为二进制数。然后在固定位，第五位，第十位...(等等) 插入一个随机值即可。至于如何反推也很简单，你拿到短链接 key 后，将固定位的数字去除，再转换为十进制即可。

            - 存储（短链作为分库分表的分片key，redis hash(`HSET key lu su`), local cache?）

            - 问题1：[2024-11-14] 短链接服务直接用tikv之类的分布式kvdb是不是更好？
            - 问题2：一对多怎么存呢？在RDB或者kvdb里


    - url: https://github.com/xbpk3t/lottery
      topics:
        - topic: 抽奖系统怎么实现？
          qs:
            - 6种抽奖活动? （年会抽奖、彩票、刮刮乐、双色球、微信摇一摇、支付宝集福卡、抢红包、抽奖大转盘）
            - 关键点：奖品类型，数量有限，中奖概率，发奖规则
            - 并发安全性问题：互斥锁，队列，CAS递减（atomic）
            - 优化：通过三列减少单个集合的大小
            - 抽奖规则应该包含哪几个参数? 共设置几等奖项、以及每个等级的抽奖次数、对应的奖池、中奖概率

    # [GO从0到1实战微服务版抢红包系统-章节目录](https://coding.imooc.com/class/chapter/345.html#Anchor)
    # [3小时极简春节抢红包之Go的实战-慕课网](https://www.imooc.com/learn/1101)
    - url: https://github.com/yiplee/packet-demo
      topics:
        - topic: "***“抢红包”业务要注意哪些核心问题？技术难点？（设计一个抢红包的系统架构、如何保证每个人抢到，如何抗住流量？）***"
          qs:
            - 拆包算法：红包里的金额怎么实现尽量均匀？ (0.01~balance/num*2, cas)
            #- 随机，额度在 `0.01` 到 `剩余平均值*2` 之间（二倍均值）
            #- 比如发 100 块钱，10 个红包，平均 10 块钱一个，发出来的红包的额度在 0.01-20 之间波动，当前面 3 个红包总共被领了 40 块钱时，剩下 60 块钱，总共 7 个红包，那么这 7 个红包的额度在：0.01～（60/7*2）=17.14 之间

            - 为啥应该实时计算（每抢到一个红包，就 cas 剩余金额和红包个数），而非先拆后发？ # 红包只需要存红包总金额和分数，每个用户抢到的金额动态分配，抢到后记录该用户抢到的金额。需要解决高并发下“超卖”问题，每发一个红包，都相当于一次秒杀活动。

            - 存数据：红包应该用 redis 的哪种数据类型存？为什么？用 list
            - 红包系统有哪些子系统？ # 红包系统的核心系统可能有 用户校验(布隆过滤器)、红包分发、异步持久化、系统降级、异步到账 这几个子系统
            - 抢红包先后顺序 对 抢到最大金额 有影响吗？`抢红包人数`和`红包总金额` 还有哪些参数对此实验会有影响？最好可视化
            # - 100 块钱的红包，每组抢红包人数从 5 到 100 分为 5、10、20、50、100 共 5 组，每组都发 1000 次，redis 的 key 按照 `re:p:{xxx}:{seq}` 设计，把每组数据进行拟合，纵轴手气最佳的概率，横轴抢红包人数
            # - 总金额从 100 到 100w 按 5 个梯度，分为 5 组，抢红包人数均为 10 人，每组都发 1000 次，redis 的 key 按照 `re:money:{xxx}:{seq}` 设计，把每组数据进行拟合，纵轴手气最佳概率，横轴红包总金额


    - url: https://github.com/echoface/be_indexer
      des: boolean indexing 广告定向索引实现


    #- url: https://github.com/mlogclub/bbs-go
    #  des: 类似V2ex的技术社区
    #- url: https://github.com/kingwrcy/discussion
    #  des: 极简论坛
    #- url: https://github.com/kingwrcy/moments
    #  des: 极简朋友圈
    - url: https://github.com/rocboss/paopao-ce
      des: pretty fast, a "twitter like" community built on gin+zinc+vue+ts. FRO.
      topics:
        - topic: 能否大概描述一下 twitter/weibo 有哪些核心模块?

        - topic: "***feed流***"
          qs:
            - 搜推广（聚合feed，比如twitter或者weibo的feed流）怎么实现？ # mixer, ranker, scorer. 所有feed流实现都类似，只是召回比例不同
            - 搜推广的召回系统 怎么做？ # [B站搜推大规模召回系统工程实践](https://mp.weixin.qq.com/s/8lDjVqKNGsi235RkUEldJg)

            - redis 里的 feed 数据，怎么分页？
            - feed流 多条件查询（redis 实现多条件查询）？

            - feed 的 timeline 怎么推？推模式和拉模式分别怎么实现？“冷热用户，推拉结合”的“热推冷拉”怎么实现？ # 其实feed流的pull模式也没啥问题吧，主要是感觉这种给每个用户都弄一个mq作为notification的push模式属实没必要。所有用户的post不都在redis里吗？直接用pull模式读该用户的所有following，直接在redis里拉取这些following的post作为feed不就可以了吗？既不需要像push模式一样弄mq，也能避免pull模式本身的数据库压力。这不就是个很基本的操作吗？还是说这个就是所谓的“推拉结合”？
            #  feed流的推模式就是给所有用户都维持一个notification的mq，当你关注的用户发推之后，就往这个mq里塞一条消息。直接读这个mq就ok了。至于拉模式就比较符合直觉了，直接rdb连表查某个用户的所有关注用户，并以此拉取这些用户的所有post。
            #  所以优缺点也是很明显了，推模式需要给所有用户都维持mq，数据量非常大，并且写操作也很难处理（新关注无所谓，通常不会把新关注用户的post加到mq里），但是如果取关某个用户，就需要再在mq里把该用户的所有post移除掉。而pull模式所有操作都是real-time的，所以性能很差，对数据库的压力很大。但是几乎没有复杂度，不需要维持mq，也不需要关心写操作。
            #  换个角度来说，也可以认为是基于rdb实现，还是基于mq实现。推模式就是把本该rdb实现的，让mq来实现了。虽然性能提升了，但是多了个组件，所以复杂度也会提升。是个很典型的优化方法。
            #  至于数据量太大的问题，也可以只在mq里存msgid，直接从redis的
            #  最后，pull就是读扩散，push就是写扩散。有必要这么啰哩啰嗦吗？不就是push和pull吗？

            - feed流 推送怎么实现（微博推送消息如何实现）？ # 在 redis 维护一个当前在线用户的 set，发一条信息的时候就看看关注自己的人那些在线的，然后把信息主动推送到他们的队列中去
            - 推送量很大，资源隔离问题
            - 聚合feed流（类似twitter主页feed流，包括动态、好友动态、活动、广告、图片、视频、直播之类的），优先级展示（权重）
            - twitter现在的“topic下的feed流”，怎么实现






        - topic: "***转评赞***"
          qs:
            - 怎么理解 “转评赞其实都可以看作是消息”？
            - “点赞表”的表设计? # mysql 两张表；每篇文章点赞数表；点赞详情表
            - 为啥点赞数据的存储，redis用zset而不是hash? 各自的实现有啥区别? # zset 的实现更简单直接，hash 的实现如果用一张大 hash 表来存所有用户的点赞数据（比如`userid:wbid -> "111:222"`，需要查某个用户或者某篇微博的点赞数据时，直接切开查询 userid 或者 wbid），这种设计如果数据量大的话，这张表就太大了。所以不合理。zset 可以重复点赞没错，但是用 zscore 判断是否已经点赞就可以了，我不是很理解 hash 的做法。

            #  如果某个post的点赞列表key是 `post:<postId>:likes`
            #
            #  # 点赞操作（ts作为score，userId作为member）
            #  zadd [key]  <ts> <userId>
            #  # 获取点赞列表，返回所有userId
            #  zrange [key] 0 -1
            #  # 获取点赞数（用zcard即可，不需要再额外搞个key来存点赞数了）
            #  zcard [key]
            #  # 获取排名前N的点赞用户
            #  zrevrange [key] <low> <high>
            #  # 取消点赞
            #  zrem [key] <userId>
            #
            #  weibo:detail:{wbId} # hash 微博详情、文章详情、活动详情等
            #  weibo:tag:{tagId} # set，key是后者`article_cate:{cateId}`，member 是微博 id，比如 `weibo-detail:{wbId}` 微博标签、文章分类
            #  weibo:send:time # zset（score 是时间戳，member 是微博 id） 全部微博的发布时间
            #  weibo:thumbs:wbid:{wbId} # # zset，member 存所有用户 id 就可以了，可以根据时间轴获取该微博的点赞记录。某条微博的所有点赞用户、投票用户等。通过 ZCARD 即可获取该微博的点赞数，不需要额外搞一个 key 来存“点赞数”
            #  weibo:thumbs:userid:{userId} # zset，这样就能根据时间轴拿到该用户所有点赞过的微博。某个用户所有点赞、投票过的微博、文章
            #  weibo:unthumbs:userid:{userId} # zset 如果取消赞，就 ZREM 移除上面两个 key，添加到这个 key 里

            - 点赞数据持久化怎么实现？有哪些问题？分别有哪些优化方案？通过定时任务，把 redis 数据刷回数据库

            # [Golang 进阶 6-评论系统架构设计 - 掘金](https://juejin.cn/post/6907187734696165389)
            # [B 站评论系统架构设计 - 哔哩哔哩](https://www.bilibili.com/read/cv20346888)
            - 如何设计一个海量的评论系统？（算是比较常见的场景题吧）
            - "***多级评论（楼中楼）：表设计怎么搞？有哪些字段？怎么实现多级评论？***" # [comment-table-for-mysql](

            - 抖音 千人千面的评论 怎么实现

            - 转评赞的消息通知 怎么实现

            - "***内容型产品，怎么做风控？内容型产品怎么解决帖子更新和审核之间的矛盾？原帖已过审，但是二次修改触发风控，怎么处理？***" # 我的思考是，二次修改和发布post没有区别，都是写操作，都需要走内容风控流程，也都有“待发布状态”（只有用户自己能看到）。但是weibo、twitter等主流社媒平台都是在【产品层面】限制“二次修改操作”（需要开通vip）。另外，一些相关KW：关键词过滤、限流、白名单和黑名单。也就是风控部分的核心是减少审核工作量。审核，冷人分离，热点账户优先审核。
            #  您的思考是合理的，二次修改和初次发布在本质上都是内容的写操作，确实都应该经过内容风控流程。以下是您提到的几个关键点的分析：
            #  待发布状态：无论是初次发布还是二次修改，内容在经过风控审核之前，可以设置为“待发布状态”，这样只有发布者自己能看到，直到内容审核通过后才对其他用户可见。
            #  产品层面的限制：像微博、Twitter等社交媒体平台可能会在产品层面对二次修改操作进行限制，例如只允许VIP用户进行修改。这种做法可能是基于商业考虑，也可能是为了减少审核工作量。
            #  关键词过滤：无论是初次发布还是二次修改，关键词过滤都是一个重要的风控手段。系统可以设置敏感词库，自动检测并标记可能的违规内容。
            #  限流：对于一些高风险或者频繁触发风控的用户，平台可以实施限流措施，比如延长审核时间、限制发布频率等。
            #  白名单和黑名单：白名单用户可能享有更多的发布权限和更少的审核干预，而黑名单用户则会受到更严格的审核和限制。
            #  VIP特权：提供VIP服务可以作为一种商业策略，通过给予付费用户更多的功能和便利来增加用户粘性和收入。但这种做法需要平衡商业利益和内容安全，确保不会因商业利益而牺牲内容的合规性。
            #  内容风控流程：无论是哪种操作，都应该遵循内容风控流程，确保内容的安全性和合规性。这包括机器审核的初步筛选和人工审核的最终判断。
            #  用户反馈机制：提供用户反馈和申诉机制，对于被错误标记或审核的内容，用户可以申请复审。
            #  透明度：平台应该对审核标准和流程保持透明，让用户明白什么样的内容是可以接受的，什么样的内容是违规的。
            #  动态调整：内容风控策略可能需要根据实际情况和政策变化进行动态调整，以适应不断变化的网络环境和用户需求。
            #  综上所述，您的思考方向是正确的，内容风控应该对所有写操作一视同仁，确保内容的安全性和合规性。同时，平台可以根据自身业务需求和商业策略，适当调整产品功能和用户权限。

    - url: https://github.com/getfider/fider
      doc: https://demo.fider.io/
      des: 用户反馈收集平台。使用paddle实现订阅付费。其实就是个feed项目，非常类似gh的discussion模块。

    - url: https://github.com/owenliang/go-push
      qs:
        - 怎么实现“视频弹幕”功能? 需求：历史弹幕可回放
        #```shell
        ## redis zset 用ts作为score
        ## 这个实现跟“历史记录功能”完全相同，也是二维数据直接外key存JSON，FRO
        #ZADD <直播间ID> <弹幕val> <ts>
        #```
        #
        #```go
        #type Bullet struct {
        #  UserId     int // 发弹幕的用户ID
        #  Content    string // 弹幕内容
        #  OffsetTime int // 相对于视频时长的偏移时间，用于确定弹幕出现的位置
        #  Timestamp  int // 用户真正发表弹幕的时间
        #  Extra      *Extra // 扩展字段，比如弹幕的效果（顶端，底端）、样式（颜色，字体大小）等等
        #}
        #```
        #
        #优化：本地缓存（caffeine之类的）、限流（丢弃多余弹幕，类似bz，限制总数+弹幕优选）

        # [直播弹幕系统设计 | 王帅真](https://blog.qizong007.top/article/live-streaming-bullet-system)
        # [100w人在线的 弹幕 系统，是怎么架构的？ - 技术自由圈 - 博客园](https://www.cnblogs.com/crazymakercircle/p/17061303.html)
        - "***直播弹幕相比于视频弹幕的思路和实现有啥区别 (直播业务需要面对的实际场景)? 直播弹幕有哪些难点? 分别怎么解决?***"
        #视频弹幕就是“历史消息”，直播弹幕就是“实时消息”。直播间弹幕可以看作是“松散的群聊IM”（和群聊IM的唯一区别在于，用户随时进群退群），所以无论是实现、问题、优化方案都很类似。
        #
        #- 直播弹幕需要实时写入（所以一定需要MQ异步刷入，避免redis串行写入导致消息背压，解决“瞬时弹幕”）
        #- 直播弹幕需要实时拉取（相比视频弹幕，视频弹幕拉取一次即可），所以需要 `ZRangeByScore 定时轮询`（秒级，准实时即可）
        #- 直播弹幕不需要offset，视频弹幕才有偏移时间（因果一致性）
        #
        #也正因为直播弹幕需要实时拉取，所以挑战就很大了
        #
        #问题：三高、弱网、带宽压力
        #
        #- 弱网环境：ws? long polling? short polling?
        #- 带宽压力：假如说每3秒促达用户一次，那么每次内容至少需要有15条才能做到视觉无卡顿。15条弹幕+http包头的大小将超过3k，如果PCU为10w用户，那每秒的数据大小约为300MB (10w*3/1000)，按照带宽和下载速度的8倍关系，需要300*8 = 2400Mbps的带宽。带宽压力极大，且很贵。***所以为了降低带宽压力，我们会通过对MQ进行限流（降低消费速度，也就是降低“实时性”，也就是之前每隔3s，现在每隔10s拉取，这个拉取时间通常是根据冷热用户（大主播与否）动态调整的）来减少带宽压力。***
        #- 推拉结合，可以把短连接的拉作为长连接推的降级方案
        #- 使用一级缓存（降低redis压力）：对弹幕读请求，使用local cache缓存最近5s的数据在应用服务的内存中（过期了才回源redis），但是这样的话，本地内存使用量随直播间线性膨胀，本地cache的命中率下降，还可能频繁触发GC。怎么解决？***只针对热门直播间使用本地cache，使用“一致性hash”，控制同一直播间尽可能打到同一台服务器，降低本地cache使用量。***

        - 直播网站的弹幕的弹幕回放功能，有哪些优化点?

    - url: https://github.com/bububa/oceanengine
      des: 字节的巨量引擎SDK
      rel:
        - url: https://github.com/bububa/oppo-omni
          des: oppo omni广告引擎SDK，可以作为“推广渠道”的入手，功能比较简单。可以看下 model/data/QAd.go
      topics:

        # [渠道运营：质量评估模型一文通透 - 传播蛙](https://www.wlcbw.com/6432.html)
        - topic: "***如何判断推广渠道是否被刷假量（渠道反作弊）? 怎么评估来源渠道的用户质量? 推广渠道打分算法?***"
          picDir: works/投流
          qs: # FIXME 整理到
            - CAC 和 CPA 有什么区别？ # CAC 特指获取一个客户所花费的成本。相反，CPA 指的是获取流量所花费的成本，而这个流量并不一定是会成为客户*，CAC 对应已注册用户，CPA 对应的只是买量成本
            - 如何计算 CAC？ # 不同的业务应该使用不同的 CAC 的计算公式，不过大部分情况下直接用`CAC = （营销总费用 + 销售总费用）/获取新客数 `这个公式计算就可以了。具体实现时，只要*需要加上`营销费用`字段，用他除一下` 新用户数` 就可以了*

            #通过新增用户数、使用时长、关键行为、GMV、客单价、CAC、LVT、LVT/CAC、留存率等维度按照权重，综合估算用户质量
            #
            #渠道总得分=数量得分*权重1+行为得分*权重2+商业得分*权重3+成本得分*权重4+质量得分*权重5
            #
            #- 把各渠道的数据放到公式计算可以得到各项得分
            #- 通过“层次分析法”计算获得各项权重，大概是3、4、1.5、0.5、1的比例
            #
            #这样就可以获得渠道总得分了
            #
            #---
            #
            #渠道质量评估 （渠道有效性、渠道留存、渠道ROI，分别对应新增、活跃和收益）
            #
            #
            #我有个想法，其实推广渠道评估和用RFM评估用户商业价值的核心是一样的
            #
            #- 目标：都是为了评估商业价值，只不过前者是根据该渠道导入用户的价值评估该渠道的价值，后者是评估单个用户的商业价值
            #- 数据来源：RFM倾向于用户付费（转化），而推广渠道评估则是从“拉留存促转召”的综合评估
            - 如何管理多个推广渠道的预算和投放时间？
            - 如何确定每个渠道的预算分配比例？


    - url: https://github.com/umami-software/umami
      des: GA的开源替代品 # 流量统计分为基于虚拟主机的访问记录，和基于js代码两种。结合这两个帖子来看，应该说没有哪个更准的说法。两方面原因：cf的统计包含了crawler扫描的数据，而GA之类的则默认被各种ADB工具屏蔽js（这个我也遇到过），所以一个会多，一个会少。这个只做为参考就可以了。 # [Cloudflare 网站监测跟其它监测工具的监测结果差异这么大么？ - V2EX](https://cn.v2ex.com/t/1001803) 流量统计分两种。1、一种是基于虚拟主机的访问记录的，比如 Nginx 访问日志、Cloudflare 这个流量统计。优点：数据完整准确，缺点：默认不区分是否非人（即所有访问流量都算上了）因此统计数据是高于实际数据（真人流量）。2、一种是基于 JS 统计代码的，谷歌、百度、Cloudflare 都有这种。优点：区分真人，缺点：主流 JS 统计代码都被各广告屏蔽扩展屏蔽了。因此统计数据是低于实际数据的（当然你可以通过自建/反代来避免 JS 被屏蔽）。


    - url: https://github.com/mixpanel/mixpanel-js
      des: 埋点/打点(User-Tracking)【模块】用户数据的前端打点数据，这个repo是js的，另外还有iphone和android的repo。Mixpanel是一个用于用户分析和产品优化的平台。它提供了深入的用户行为分析，例如漏斗分析、事件跟踪和更复杂的用户细分。Mixpanel还提供了实时数据和A/B测试等功能。
      topics:
        - topic: 【技术选型】打点方案  # https://juejin.cn/post/7336973329429118985
          url: https://posthog.com/blog/posthog-vs-mixpanel
          des: "***对比 posthog, mixpanel 以及第三方服务如神策、友盟、GrowingIO等***"
          table:
            - name: PostHog
              url: https://github.com/PostHog/posthog
              是否免费: 开源免费（SaaS有免费层）
              价格范围（年）: 自建免费 (SaaS $0起)
              用户行为分析: true
              漏斗图: true
              数据隐私控制: true
              自托管选项: true
              目标用户: 独立开发者/中小企业
              备注: 支持无埋点、Session回放，GDPR合规

            - name: Mixpanel
              是否免费: 免费版有限
              价格范围（年）: $0起（免费版限制），进阶$20k+
              用户行为分析: true
              漏斗图: true
              数据隐私控制: true
              自托管选项: false
              目标用户: 中大型企业
              备注: 深度分析强，定价阶梯陡峭

            - name: 神策数据 # 神策数据 golang SDK. 服务端SDK其实就三步，把用户注册到神策（loginId, mobile, email 参数进行Bind()）、设置用户属性（设置用户年龄、性别等用户属性，从而在留存分析、分布分析等功能中，使用用户属性作为过滤条件进行分析）、用户事件埋点（在社交产品里的用户点评赞等事件，在电商产品的加购、下单、支付等关键事件中加入埋点）。
              url: https://github.com/sensorsdata/sa-sdk-go
              是否免费: false
              价格范围（年）: SaaS版¥5w+，自建¥8w+
              用户行为分析: true
              漏斗图: true
              数据隐私控制: true
              自托管选项: true
              目标用户: 中大型企业
              备注: 私有化部署成本高，支持15亿条数据量

            - name: 友盟+
              是否免费: 基础功能免费
              价格范围（年）: 高级功能定制报价
              用户行为分析: true
              漏斗图: true
              数据隐私控制: false
              自托管选项: false
              目标用户: 中小应用
              备注: 侧重移动端，行为分析功能较基础

            - name: GrowingIO
              是否免费: false
              价格范围（年）: ¥10w+起
              用户行为分析: true
              漏斗图: true
              数据隐私控制: true
              自托管选项: true
              目标用户: 中大型企业
              备注: 无代码埋点，支持全链路分析

            - name: Umami
              是否免费: 开源自托管
              价格范围（年）: 免费
              用户行为分析: false
              漏斗图: false
              数据隐私控制: true
              自托管选项: true
              目标用户: 独立开发者/极简需求
              备注: 1、仅基础PV/UV统计，无深度分析功能。2、自建依赖pgsql/mysql

            - name: Plausible
              url: https://github.com/plausible/analytics
              是否免费: 开源自托管
              价格范围（年）: 免费
              用户行为分析: true # (支持自定义事件跟踪：下载、外链点击、404、电商转化、自定义维度)
              漏斗图: false # 社区版明确说明 不包含 漏斗分析功能
              数据隐私控制: true
              目标用户: 寻求比Umami稍丰富功能、注重隐私的中小企业/团队/开发者
              备注: 1、分析功能相较更丰富 2、自建依赖CK，比较重  # 1、分析功能更丰富：支持转化目标、自定义事件、关键词整合(需GSC)。 2、自托管要求更高：需要管理服务器、维护ClickHouse数据库。

            - name: Google Analytics
              是否免费: 基础免费
              价格范围（年）: 高级版$150k+
              用户行为分析: true
              漏斗图: true
              数据隐私控制: false
              自托管选项: false
              目标用户: 通用型
              备注: 隐私合规风险高，事件分析需配置

            - name: 百度统计
              是否免费: true
              价格范围（年）: 免费
              用户行为分析: false
              漏斗图: false
              数据隐私控制: false
              自托管选项: false
              目标用户: 极简需求
              备注: 仅基础流量统计，无用户路径分析
          qs:
            - 三个经典场景：个人使用、小厂、中大厂，你觉得分别用哪个方案比较好？（个人使用 umami、小厂 PostHog SaaS版、中大厂 神策）

            - 神策没有免费版，Saas版本 事件触发5000w次/月，价格¥5w/年。自建年费大约为8万（15亿条埋点数据）、12万、18万左右
            - 总结：GrowingIO、诸葛IO、神策数据这类提供用户行为分析和漏斗图的，三者的价格都是动辄几万每年，都挺贵的。那有什么给独立开发者或者小体量应用提供的免费saas服务吗？或者低价服务。这类工具的saas服务，基本上都是动辄几万，大部分用户的付费区间在一二十万。可能确实没有什么免费工具可选。这么说的话，是不是直接用 umami 或者 GA之类的就可以了，其实也没必要太纠结。毕竟这种增收工具，肯定是在已经有一定营收规模（10万做营收工具的话，至少年利润要在300万左右了）之后才考虑了。也可以 umami, GA, 百度统计 这些都没有用户行为分析和漏斗图




        - topic: 投放归因
          qs:
            - 概率归因&自归因

            - 自归因和非自归因

            - 首次点击，线性归因，时间衰减归因，u型归因

            - 自归因通常是广告平台自行去对转化路径进行归因逻辑的判断，是一些具有归因功能的大平台才会提供的归因方式，这种归因方式对于广告平台来说也具有比较强势的地位。

            - 而非自归因往往是将其广告数据回传给广告主对接的第三方归因平台，由第三方归因平台进行对应的事件归因。

            - 用户点击广告, 下载(拉新)或者打开APP(拉活)
            - 广告平台发送请求到服务器,把归因因子缓存并设置好归因周期
            - 增加一个延时队列, 重复归因一次(防止拉活导致时序问题归因失败)
            - 归因成功将会在应用的首页精准推送内容(获取用户点击广告的文案推送内容)

        - topic: feeds ads
          qs:
            - 信息流广告，开屏广告，插屏广告
            - 一般情况下插屏广告会出现在用户第一次点击某个功能页时弹出，显示需要提示的具体内容。


        - topic: Adload # 广告加载率 ( Ad Load )，指 Feed 里的广告数量占比，起着平衡用户体验和商业化的作用，Ad load 过高，用户会对产品产生负面印象，不同产品形态的 Ad load 边界会不一样，较为私人的 Feed 流的广告加载率偏低（微信朋友圈 2%），资讯信息流的广告加载率较高（今日头条图文 Feed 为 8%）。

        - topic: xxx
          qs:
            - 广告平台除了 穿山甲，还有哪些？

        # TODO 给 ADX 广告交易平台、DSP 需求方平台、SSP 供应方平台、RTB 实时竞价、DMP数据管理平台。这几个做个类比。并且自己画个图。注意整合现有的几张图。
        - topic: ADX
          picDir: works/Ads
          qs:
            - 怎么在ADX广告在无法获取广告完整信息(比如电商广告的url、图片、类目、品牌、产品词)的情况下，通过少量信息提升用户和广告匹配的精准度？ # [基于沉浸度模型预估的广告动态展示](https://tech.qimao.com/ji-yu-chen-jin-du-mo-xing-yu-gu-de-yan-gao-dong-tai-zhan-shi/)

        # [百度大搜的一些历史经验 | 始终](https://liam.page/2024/01/04/legacy-experience-of-Baidu-search/)


    - url: https://github.com/go-pay/gopay
      des: 支付【模块】微信、支付宝、通联支付、拉卡拉、PayPal、Apple 的Go版本SDK。【极简、易用的聚合支付SDK】
      #rep:
      #  - url: https://github.com/silenceper/wechat
      #  - url: https://github.com/wechatpay-apiv3/wechatpay-go
      #    des: 微信支付
      #  - url: https://github.com/royalrick/weapp
      #    des: 微信小程序
      #  - url: https://github.com/shenghui0779/sdk-go
      qs:
        - "***怎么实现基础的支付功能？支付的流程？支付系统的表设计？***"
        - 【离线支付】怎么实现？关键点和核心问题是啥？
    #  ```markdown
    #  做为曾经实现支付宝条码支付的开发，来说一下支付宝和友商微信实现的方式：其实都是基于 OTP 算法来实现的，简单的说就是：
    #  1，服务端生成一对密钥，并给客户端下发
    #  2，客户端使用字典算法，根据当前时间+用户 ID 生成一个支付串，再通过转换生成 18 位或 19 位数字的格式展示给用户
    #  3，商家条码枪采集用户的条码，上送到服务端
    #  4，服务端用同样的密钥同样的算法生成一个区间的支付串（一般为 1.5-3 分钟的区间），然后用上送的串，去匹配，如果匹配中，则认为可性，匹配不到则认为非法
    #  5，解析出用户 ID，对用户进行扣款
    #  当然上述是一个比较简单的主要流程，真实实现还有很多细节，远比这复杂
    #  另外从风控方面，除了上述的算法保证外，还有额度的控制，比如超过 1500-2000 之后会强制拉用户的收银台确认，如果拉不起来，则不能支付，所以离线场景一般用于小额，商超等场景；此外还会根据用户最近上报的地理位置和商家的地理位置，交易频次等各种风控因素做控制 。
    #
    #  至于双离线，其实原理也比较简单，有空展开讲。更离谱的是，我们以前还做过一个服务端挂的情况下，让用户仍然能够支付成功的。
    #  ```


    - url: https://github.com/gorse-io/gorse
      doc: https://gorse.io/zh/docs/master/
      des: 非常易用且容易接入的“通用推荐系统”。gorse支持多源推荐、AutoML、dashboard等feat。
      #rep:
      #  - url: https://github.com/neo4j-graph-examples/recommendations
      #  - url: https://github.com/tomasonjo/NeoGPT-Recommender
      #  - url: https://github.com/ameyagidh/GoRecommendation-System
      #  - url: https://github.com/benfred/implicit
      #    des: Collaborative Filtering for Implicit Feedback Datasets
      #  - url: https://github.com/maciejkula/spotlight
      #    des: Spotlight是个使用PyTorch构建的深度推荐模型。旨在成为推荐系统快速实践工具和新型推荐模型的原型。
      #  - url: https://github.com/PaddlePaddle/PaddleRec
      #    des: 推荐系统库
      #  - url: https://github.com/alibaba/EasyRec
      #    des: EasyRec是一个阿里巴巴开源的大规模推荐算法框架。实现了用于常见推荐任务的最先进的深度学习模型：候选生成（匹配），评分（排名）和多任务学习。它通过简单的配置和超参数调整（HPO）提高了生成高性能模型的效率。
      #  - url: https://github.com/recommenders-team/recommenders
      #    des: Recommenders
      #  - url: https://github.com/tensorflow/recommenders
      #    des: TensorFlow Recommenders是一个使用TensorFlow构建的推荐系统模型的库。这个项目可用于构建推荐系统的完整工作流程，包括：数据准备、模型制定、培训、评估和部署等环节。它建立在Keras框架之上，具备温和学习曲线，同时也支持灵活地构建复杂的模型。
      #  - url: https://github.com/shenweichen/DeepCTR
      #    des: 深度学习推荐系统
      #
      #  - url: https://github.com/NicolasHug/Surprise
      #    des: Surprise # [Python推荐系统库：Surprise – 标点符](https://www.biaodianfu.com/surprise.html)
      #
      #  - url: https://github.com/pytorch/torchrec
      #    des: TorchRec是一个PyTorch域库，旨在提供大规模推荐系统（RecSys）所需的常见稀疏并行原语。它允许作者使用跨多个GPU来训练模型。1、使用混合数据并行性、模型并行性，轻松创作支持大型高性能多设备/多节点模型的并行性原语。2、TorchRec Sharder可以使用不同的分片策略对嵌入表进行分片，包括数据并行、表式、行式、表式行式、列式、表式列式分片。3、TorchRec规划器可以自动为模型生成优化的分片计划。4、流水线训练与数据加载设备传输（复制到GPU）、设备间通信（input_dist）和计算（向前、向后）重叠，以提高性能。5、RecSys的通用模块。
      #  - url: https://github.com/songgc/TF-recomm
      #    des: TF-recomm
      #  - url: https://github.com/d2l-ai/d2l-zh
      #  - url: https://github.com/RUCAIBox/RecSysDatasets
      #    des: 这是用于训练推荐系统（RS）的公共数据源。所有数据集都可以转换为原子文件， 它是一个统一、全面、高效的推荐系统资源。转换为原子文件后的数据集，可以使用RecBole在这些数据集上测试不同推荐模型的性能。数据集包括：购物、电影、广告、音乐、图书、图片、游戏、网站、食品、新闻、衣服等等。
      #  - url: https://github.com/bytedance/monolith
      #    des: BD开源的推荐系统。字节开源的一个用于大规模推荐模型的深度学习框架，建立在 TensorFlow 上，支持批量/实时训练和部署。1、提供无碰撞嵌入表，确保不同 ID 特征的唯一表示。2、实时训练捕捉最新热点，帮助用户快速发现新兴趣。3、基于 TensorFlow 构建，支持批量和实时训练与服务。
      qs:
        - 基于 gorse 和 基于 neo4j 实现推荐系统有啥区别
        - 基于 neo4j 实现推荐（基于Neo4j图数据库的社交推荐系统研究与实现）
        - 基于 NebulaGraph 实现推荐系统 # [基于图数据库的推荐系统 - siwei.io](https://siwei.io/recommendation-system-with-graphdb/)

      topics:
        - topic: 推荐系统
          qs:
            - "***什么是推荐系统？为了解决哪些问题？***" # 解决马太效应（内容分发能力不够强）推荐系统的发展，从最开始的规则匹配，协同过滤，线性模型，深度学习；逐渐缓解了马太效应，但是没有完全解决
            - 推荐系统包括哪些模块？ # 推荐系统主要包括`召回`、`排序`和后续的`业务机制` (重排序、多样性保证、用户体验保证等等) 这三大模块
            - 推荐系统有哪些评价指标？ # MAP 平均精度均值、NDCG 归一化折损累积增益
            - "***CF(Collaborative Filtering 协同过滤)是什么？有哪些CF算法？CF的原理？***" # 基于协同过滤的推荐脱胎于`“基于对象之间相关性”`这种推荐算法，在此基础上，增加了`“基于人之间相关性”`和`“基于模型”`这两种协同过滤算法。`基于模型`的协同过滤推荐就是基于样本的用户喜好信息，训练一个推荐模型，然后根据实时的用户喜好的信息进行预测推荐。

            - 相似度算法 # jaccard 距离、余弦相似度、中心余弦相似度、奇异值分解降维
            - 推荐系统怎么冷启动？
            - 数据规范化 # (Min-Max, Z-Score, 小数定标规范化)
            - PageRank # 随机访问模型(阻尼因子 d), 下雨
            - edgerank 算法是什么？ # edgerank 是谷歌的 pagerank 的简化版本，只有三个参数，亲密度，权重，时间。根据这三个参数，facebook 会把用户的好友状态进行评分，并把这些状态排列
            - AdaBoost


        - topic: 推荐系统公开课——王树森 # FIXME
          url: https://space.bilibili.com/1369507485/lists/615109
          qs:
            - 概要01：推荐系统的基本概念
            - 概要02：推荐系统的链路
            - 概要03：推荐系统的AB测试
            - 召回01：基于物品的协同过滤（ItemCF）
            - 召回02：Swing 模型
            - 召回03：基于用户的协同过滤（UserCF）
            - 召回04：离散特征处理
            - 召回05：矩阵补充、最近邻查找
            - 召回06：双塔模型——模型结构、训练方法
            - 召回07：双塔模型——正负样本
            - 召回08：双塔模型——线上服务、模型更新
            - 召回09：双塔模型+自监督学习
            - 召回10：Deep Retrieval 召回
            - 召回11：地理位置召回、作者召回、缓存召回
            - 召回12：曝光过滤 & Bloom Filter
            - 排序01：多目标模型
            - 排序02：Multi-gate Mixture-of-Experts (MMoE)
            - 排序03：预估分数融合
            - 排序04：视频播放建模
            - 排序05：排序模型的特征
            - 排序06：粗排模型
            - 特征交叉01：Factorized Machine (FM) 因式分解机
            - 特征交叉02：DCN 深度交叉网络
            - 特征交叉03：LHUC (PPNet)
            - 特征交叉04：SENet 和 Bilinear 交叉
            - 行为序列01：用户历史行为序列建模
            - 行为序列02：DIN模型（注意力机制）
            - 行为序列03：SIM模型（长序列建模）
            - 重排01：物品相似性的度量、提升多样性的方法
            - 重排02：MMR 多样性算法（Maximal Marginal Relevance）

            - 重排03：业务规则约束下的多样性算法
            - 重排04：DPP 多样性算法（上）
            - 重排05：DPP 多样性算法（下）
            - 物品冷启01：优化目标 & 评价指标
            - 物品冷启02：简单的召回通道
            - 物品冷启03：聚类召回
            - 物品冷启04：Look-Alike 召回
            - 物品冷启05：流量调控
            - 物品冷启06：冷启的AB测试
            - 推荐系统涨指标的方法01：概述
            - 推荐系统涨指标的方法02：召回
            - 推荐系统涨指标的方法03：排序模型
            - 推荐系统涨指标的方法04：多样性
            - 推荐系统涨指标的方法05：特殊用户人群
            - 推荐系统涨指标的方法06：交互行为（关注、转发、评论）








    - url: https://github.com/openimsdk/open-im-server
      doc: https://docs.openim.io/guides/introduction
      score: 5
      #sub:
      #  - url: https://github.com/openimsdk/openim-sdk-core
      #rep:
      #  - url: https://github.com/cherish-chat/xxim-server
      #  - url: https://github.com/TangSengDaoDao/TangSengDaoDaoServer
      #  - url: https://github.com/WuKongIM/WuKongIM
      #  - url: https://github.com/taoshihan1991/go-fly
      #  - url: https://github.com/alberliu/gim
      #  - url: https://github.com/Terry-Mao/goim
      #  - url: https://github.com/xinjiayu/openim
      #  - url: https://github.com/sevtin/lark
      #    des: IM项目，采用微服务架构设计，支持集群和水平扩展，能够满足高并发业务需求，并实现了万人群消息的秒达。 # [万人群消息转发机制_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1KKyhYjEsw/)
      topics:
        - topic: IM模块
          qs:
            - IM模块，应该有哪些子模块（账号模块、关系模块、联系人模块、群组模块、消息模块、聊天会话模块）构成？ # [基于go-zero的IM系统实战（架构设计）](https://mp.weixin.qq.com/s?__biz=MzkxMjUyOTM4Mg==&mid=2247483930&idx=1&sn=923bd286f8020b374aaa9c4204c0d478)
            - 主流 IM服务商 SDK（网易云信IM、环信IM、腾讯IM、、融云IM）比价? 比如说我们的APP DAU 20w，每个月需要付费多少？ # 都是根据当月最高DAU计算，但是融云IM是直接根据区间计算（¥16k/月），环信IM则是超量付费（¥850/1w DAU，也就是说同样20w DAU， `2888 + (20w-1w)/1w * 850`，大概¥19k/月），网易云信IM同样超量付费（基础日活1w，超出部分 ¥1000/1w DAU），20w DAU的话总计 23k/月

        - topic: IM实现
          qs:
            - IM的聊天功能怎么实现? # [场景题：类微信IM系统的消息顺序一致性保证_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV14y411i7EG/) # [从0开发IM，单聊群聊在线离线消息以及消息的已读未读功能以下是自己参考过的资料： IM 群聊消息究竟是存 1 份(即扩散 - 掘金](https://juejin.cn/post/7202583557751865401)

            - 如果发生“消息风暴”，怎么确保群聊时（比如微信）的消息顺序性? 请画出架构图 （主要设计在于3点：消息模型、收发机制、存储） # [IM群聊消息如此复杂，如何保证不丢不重？-IM开发/专项技术区 - 即时通讯开发者社区!](http://www.52im.net/thread-753-1-1.html)
            # - 消息模型：肯定是采用房间纬度唯一seq，用于：消息收发、排序、补空。同时辅以客户端本地 clientkey 优化本地表现。
            # - 收发机制：推拉结合，在线用户推，离线用户拉。可以有效降低QPS
            # - 存储方面：剩下的主要就是写扩散 or 读扩散、分表等。

            - 延迟优先还是吞吐优先？吞吐优先，打包消息批量发送，比如每10条消息打包发送，QPS就可以/10了 ACK
            - IM的“已读回执功能”怎么实现? # [IM群聊消息的已读回执功能该怎么实现？-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1198347)

            - 怎么保证 im 中 socket 消息的消息顺序？
            - im消息丢失的问题该怎么处理？ 如果重发的话该怎么设计？ ack应答机制该怎么设计？
            - IM 系统是不是都是基于UDP实现的？比如说 OpenIM 用的是UDP还是TCP? 如果用TCP怎么保证性能? 但是如果用UDP的话，怎么保证消息有序呢？


    - url: https://github.com/xbpk3t/hi
      des: 【用户相关】
      score: 3
      topics:
        - topic: "***怎么用 RFM 模型做用户画像？***"
          des: |
            (recency, frequency, money) *用`RFM 模型`来衡量用户的价值*

            - `最近一次消费 recency`衡量用户的流失，消费时间越接近当前的用户，越容易维系与其的关系。1 年前消费的用户价值肯定不如一个月才消费的用户。
            - `消费频率 frequency`消费频率是用户在限定的期间内购买的次数，最常购买的用户，忠诚度也越高。
            - `消费金额 money`消费金额是营销的黄金指标，二八法则指出，企业 80% 的收入来自 20% 的用户，该指标直接反应用户的对企业利润的贡献。

        - topic: RFE模型 用户活跃度模型

        - topic: "***可拓展的用户表：第三方登录(third-party login) 为啥需要（用户主表users、用户详情表user_extends、第三方表user_oauth (user_id, oauth_type, oauth_id, union_id, credential) ）3张表?***"

        - topic: 账户注销
          qs:
            - 我想实现一个注销功能，告诉我具体实现方案。需求如下：1、注销之后，该用户无法再登录，该用户使用原手机号也能注册新账号。2、伪注销：保证注销操作不影响该用户数据。3、自动撤销（也就是用户提交注销申请后，如果在expire内再次登录，就撤销请求，否则就执行注销操作）。另外，注销功能和黑名单功能是不是有点像？只不过一个主动，一个被动。最终的需求都是要保证用户无法再登录该账号。 # 加一个是否注销的字段 is_deleted，注销后，置空原手机号或者把该数据移到“注销用户表”(用户主表的手机号唯一索引)
            - 注销操作时，需要在用户表中进行哪些操作，对应的 用户注销表 需要哪些字段？ # 主表 is_deleted=true、修改mobile # 用户注销表 user_logoff (user_id, logoff_request_time 注销请求时间, delete_time 虽然冻结时间固定，但是也有可能会修改，所以需要该字段, mobile 该用户手机号 , delete_reason 注销原因)

            - 通常我们会在用户表做mobile的唯一索引，如果需要保证“注销之后，该用户无法再登录，该用户使用原手机号也能注册新账号” 的话，岂不是违反了mobile唯一索引？怎么解决这个问题？ # 很简单，有人说“做个mobile和is_logoff的联合索引”就可以了，但是不要这么搞。最简单的方法就是直接修改已注销用户的mobile，比如直接在

            - 为什么撤销注销操作大部分都是立即注销//自动撤销，而非手动撤销（提交注销申请后，在冻结期内用户可以手动撤销申请）？

        - topic: 怎么实现“多端互踢”？除了“redis 黑名单” # [闲聊-多端登录踢人下线的实现思路_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1QoSMY6EWd/)
          des: |
            所谓多端互踢，就是保证一个用户只有一个有效的 token，让之前的 token 失效掉。所以jwt是无法实现的。

            - 把“当前登录用户”存到 redis 的 zset 里，*userId 作为 score，token 作为 member*。用户登录时，直接 zadd，如果 userId 已存在，则直接修改对应 token。如果不存在，则添加。
            - 用户请求接口时，查看该用户 userId 对应的 token 是否在 zset 里，如果不在，则踢出，*保证每个用户只有一个有效的 token*
          qs:
            - "***如果使用jwt的话，会遇到已经把用户放到黑名单，但是该用户仍然可以用jwt登录的问题（类似场景还有 用户注销、退出登录、修改密码、服务端修改了某个用户具有的权限或者角色、用户的帐户被删除/暂停），其实只有两种方法，一种是jwt存在redis里，另一种是修改secret。但是方案1的问题在于，无论是白名单还是黑名单，这个用来存jwt的key都会变成大key，并且有用户直接请求退出登录api就可以刷爆这个key。并且里面的value还没有expire。也就是说有增无减。这个问题怎么处理？修改secret相比方案1更好，但是如果用户多端登录，按照这个方案，其中一端退出登录后，其他端也会退出。***"
            - 多端互踢：同一时间只允许登录唯一一台设备。例如设备 A 中用户如果已经登录，那么使用设备 B 登录同一账户，设备 A 就无法继续使用了 # [JWT-Auth 黑名单功能 | Laravel China 社区](https://learnku.com/articles/12679/jwt-auth-blacklist-function)

        - topic: 短信验证码功能（短信防刷、）
          qs:
            - 验证码存储一定要保证 key 为手机号，切记不要以其它标识作为 key
            - 一定要设置验证码失效时间，比如五分钟，或者更短
            - 流程限定。将手机短信验证和用户名注册分成两个步骤，用户在注册成功用户名密码后，下一步才进行手机短信验证
            - "***短信被刷：怎么实现 手机号重复发送的时间间隔设置（通常60s）、指定手机号和IP的单日最大发送量、监控某时间段的短信发送总量？***"
            - 怎么实现某个IP或者手机号频繁请求短信验证码之后，自动展示图片验证码？直接使用 geetest或者阿里云的号码认证服务 # 这个应该需要和前端配合实现，频繁请求错误后，往接口返回参数加一个 is_captcha=true 的参数，前端请求另一个有 captcha 为 required 的接口
            - 某段时间的短信发送总量超过阈值后，根据实际业务需求，应该有哪些处理？

        - topic: 怎么统计在线人数
          url: https://cloud.tencent.com/developer/article/2430325

        - topic: 用户签到
          url: https://juejin.cn/post/7150505736907259917 # [底层逻辑给你讲明白，签到打卡这种场景数据库缓存到底怎么用_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV13b4BeQEM9/)

        - topic: LBS【模块】
          qs:
            - "***实现 LBS 功能: 为啥应该用 PostGIS+ES? 怎么用 pgsql 的 SLB 后续优化（具体怎么优化基于 PostgreSQL 的 LBS 系统？）***" # [亿级“附近的人”，打通“特殊服务”通道_小姐姐味道的技术博客_51CTO博客](https://blog.51cto.com/u_14355948/2797848) # 默认使用 pgsql，如果是多条件筛选+gis 的场景，直接选择 mysql 或者 pgsql；如果不需要多条件筛选或者不需要联查等场景，也可以使用 mongo 或者 es # 中期用 pg 的 PostGIS 实现，把 LBS 单独做成子系统，使用 PgRouter/PgMonitor 进行优化。后期用 PostGIS 做数据存储源，用 ES 专门提供搜索。

            - 定位服务：如何确保定位的准确性和响应速度？用户隐私如何保护？
            - 地理围栏：地理围栏的精度如何？如何优化电池使用？
            - 位置搜索：搜索结果的排序逻辑是什么？如何处理搜索结果的多样性？
            - 路径规划：路径规划考虑了哪些因素，如交通状况、步行或驾车？
            - 导航服务：导航服务如何处理实时交通信息？用户如何自定义路线偏好？
            - 位置共享：位置共享的隐私设置有哪些？如何控制共享的频率和范围？
            - 签到功能：签到信息包含哪些内容？如何防止虚假签到？
            - 附近的人或地点推荐：推荐算法如何考虑用户偏好？如何处理推荐内容的更新？
            - 事件发现：事件信息的准确性如何保证？用户如何表达对事件的兴趣？
            - 位置标签：位置标签如何影响内容的发现和排序？如何防止滥用位置标签？
            - 位置数据可视化：如何设计用户界面以提高可读性？如何处理大量数据的可视化？
            - 位置权限管理：用户如何控制哪些应用可以访问他们的位置信息？如何教育用户理解权限的重要性？
            - 位置数据的存储和处理：数据存储遵守哪些法律法规？如何确保数据的安全性？
            - 跨平台位置服务：如何处理不同平台的API差异？如何保证服务的兼容性？

        - topic: geohash 算法
          qs:
            - geohash 算法是什么？ # 将地理经纬度坐标编码为由字母和数字所构成的短字符串
            - geohash 算法的原理？ # 它采用的是二分法不断缩小经度和纬度的区间来进行二进制编码，最后将经纬度分别产生的编码 `奇偶位交叉合并`，再用字母数字表示。GeoHash 算法将二维的经纬度数据映射到一维的整数，这样所有的元素都将在挂载到一 条线上，距离靠近的二维坐标映射到一维后的点之间距离也会很接近。当我们想要计算「附 近的人时」，首先将目标位置映射到这条线上，然后在这个一维的线上获取附近的点就行 了。将地理经纬度坐标编码为由字母和数字所构成的短字符串。它采用的是二分法不断缩小经度和纬度的区间来进行二进制编码，最后将经纬度分别产生的编码`奇偶位交叉合并`，再用字母数字表示。


    - url: https://github.com/markbates/goth
      des: 第三方认证. gh, google, gitlab, apple, twitter, etc.
      rel:
        - url: https://github.com/zitadel/zitadel
        - url: https://github.com/go-oauth2/oauth2
        - url: https://github.com/teamhanko/hanko
          des: Passkey 时代的完整身份验证和用户管理解决方案。Clerk、Auth0 等的开源替代方案。
      topics:
        - topic: OAuth
          url: https://lotabout.me/2020/OAuth-2-workflow/
          qs:
            - OAuth 协议的工作原理？
            - OAuth2 有哪几种授权模式？ # 主流是密码模式，授权码模式和简化模式适用于大型网站，客户端模式很少被使用


    - url: https://github.com/micro-plat/beanpay
      des: 用户钱包（资金账户系统）【用户模块】
      topics:
        - topic: "***用户钱包***"
          qs:
            - 基本认知：什么是资金账户系统?
            - 充值币和运营币：分别是啥？充值币和运营币在不同系统中分别有哪些，请举例? # 1、充值币：顾名思义，由用户充值而得，即使用现实货币兑换成APP或游戏中的充值币，通常表现为钻石、点券等。充值币作为平台内的统一流通货币，用以兑换各种增值服务，也是平台方的最重要收入来源之一。2、运营币：运营币由平台系统产生，名称一般表现为金币，用户可通过完成平台任务体系获得。比如：每日签到、评论互动、分享等。运营币对于用户的效用仅次于充值币，用以购买平台内相对普通的增值服务。同样，运营币对于平台方的效用也仅次于充值币，是平台用以拉新、留存和促活的重要介质。
            # 所有的“运营币”都属于资金账户，比如直播平台和网游中的金币、钻石（在龙珠直播做的龙币、贝壳之类的）。除此之外还有微信的零钱账户、电商平台的推广佣金账户、余额账户等。也就是说都是“虚拟账户”，该账户只存在于应用内。其核心是记账，提现仍然需要借助第三方支付平台或者银行。

            - "***用户钱包怎么设计？表设计？***"

        - topic: 热点账户
          qs:
            - "***高并发场景：热点账户是啥？怎么设计热点账户冲扣（或者说，高并发场景下用户钱包的设计？）？先写流水还是先写状态，哪个更合理？***"
            # 热点账户就是余额频繁变动的账户（往往是多对一，也就是大量用户需要在同时对单一账户做资金转入/转出操作）。比较经典的场景有，主播是直播应用的热点账户。
            # 先写状态表，再写流水表（具体来说就是先更新用户钱包余额，再更新钱包操作表）。需要在日志表中记录，更新前的余额、更新后的余额、操作金额。 [2024/7/22] 看到一篇文章，对热点账户，应该只记录流水不记余额，然后合并入账（也就是先写流水表，再写状态表）
            - 缓存入账有两个问题

        - topic: 积分系统
          qs:
            - 积分系统从产品运营角度出发有啥用？ # 促活的好方法
            - 积分系统在设计时需要考虑哪些因素？ # [如何从0到1设计积分系统？ | 人人都是产品经理](https://www.woshipm.com/pd/3047597.html)
            - 积分怎么产生？
            - 积分怎么消费？
            - 积分是否可以流转？以及积分的价值？
            - 实现： # [一个简单的积分系统该如何设计积分是互联网产品中常见的功能，利用积分系统可以增大用户粘性。积分的设计也需要考虑很多诸如积分 - 掘金](https://juejin.cn/post/7164005111088300040)


  topics:


    - topic: 系统架构设计
      isX: true
      picDir: works/system-design
      qs:
        - # [ashishps1/awesome-low-level-design: Learn Low Level Design (LLD) and prepare for interviews using free resources.](https://github.com/ashishps1/awesome-low-level-design)

          #  [架构设计 the Easy Way | Log4D](https://blog.alswl.com/2023/07/architecture-design-the-easy-way/)
          # [第03章：系统设计面试框架 | 系统设计面试：内幕指南](https://learning-guide.gitbook.io/system-design-interview/xi-tong-she-ji-mian-shi-nei-mu-zhi-nan-di-yi-juan/chapter-03-a-framework-for-system-design-interviews)



    - topic: DDD
      picDir: works/system-design/DDD


    # TODO [分类: DDIA | 木鸟杂记](https://www.qtmuniao.com/categories/%E5%88%86%E5%B8%83%E5%BC%8F%E7%B3%BB%E7%BB%9F/DDIA/)
    # [DDIA-木鸟杂记个人主页-哔哩哔哩视频](https://space.bilibili.com/30933812/lists/240551?type=season)
    - topic: 《设计数据密集型应用 DDIA》 # [设计数据密集型应用](http://ddia.vonng.com/#/)





    # TODO 【2025-06-12】在实际开发中，在设计环节需要思考【拆API】怎么判断API是否应该拆分？有哪些评估因素？默认拆分还是合并？1、【回查API】2、【大力使用参数化设计】这是减少同类API数量的利器 3、【业务动作API + BFF聚合层】4、【监控驱动优化】上线后密切监控API调用量、性能、错误率。高频或问题多的API是拆分的候选者；过于冷门且简单的API可考虑合并。
    # TODO 【2025-07-15】自己研究一下怎么在golang里实现完整的“参数化设计”，也就是 filter, fields, sort, ... 整套
    #- 【API设计】请求设计：1、过滤/排序参数规范（如 `?filter=name:eq:test&sort=-created_at`）1、批量操作支持（批量删除/更新）
    #- 【API设计】响应设计：字段选择支持（`?fields=id,name`） # 我举个例子，比如说FE说需要5个参数，我是应该直接默认只返回这些数据，还是全部返回（只omit敏感数据）让FE自己获取呢？哪种更好，通常来说

    # TODO 错误码设计，自己实践一下
    # [浅谈API错误码设计 - 知乎](https://zhuanlan.zhihu.com/p/14972811454)
    # [云API错误码的设计规则腾讯云云API错误码分为两级。以点号分隔。 第一级错误码统一由API平台提供 ，业务选择合适的错 - 掘金](https://juejin.cn/post/7329514858664919103)
    # [如何设计API返回码（错误码）？ - Ken的杂谈](https://ken.io/note/api-errorcode-or-resultcode-desgin)
    # [聊聊接口的返回数据结构 | 希仁之拥的博客](https://blog.keepchen.com/a/talk-about-api-response-structure.html)

    - topic: 《从API开发生命周期到API最佳实践Checklist》 # [API-Security-Checklist/README-zh.md at master · shieldfy/API-Security-Checklist](https://github.com/shieldfy/API-Security-Checklist/blob/master/README-zh.md)
      isX: true
      picDir: works/api
      what:
        - 【JWT】 # [实践出真知，聊聊 HTTP 鉴权那些事 | 董泽润的技术笔记](https://mytechshares.com/2022/04/06/something-about-http-auth/#2-HMAC) “JWT TOKEN 能防篡改但是不能防重放攻击，所以 exp 要短，同时要有 token 黑名单，还得有限流，哪怕是一小时也能把服务打爆”

      hti:
        - 【防抓包】防抓包的有效性需要分层理解：实际上大部分抓包软件是能够抓这些HTTPS包的，只是修改不了而已。即使使用私有协议也只是成本增加了，使用wireshark仍然可以看到网络包。两者均无法实现绝对防抓包，只能通过增加攻击成本降低风险。
        #  - 抓包和防抓包有啥区别？分别怎么实现需求？
        #  - 怎么防止接口被抓包？有哪些方法？ # SSL-PINNING, HPKP, HSTS, Except-CT
        #  - HPKP 的原理？HPKP 怎么实现防抓包？为啥不建议使用HPKP呢，有哪些问题？ # HPKP 可以避免浏览器访问到伪造证书的 HTTPS 网站；在浏览器访问 HTTPS 网站时，网站会锁定浏览器所有接收的该网站的公钥列表，只有浏览器接受的证书与之前通过 HPKP 的 header 里申明的一致时才能访问该网站。使用抓包软件代理 ssl，必然会导致私钥和原服务器的不同，公钥也不同，验证公钥就可以判断是否被抓包；采用 HPKP 验证只需要把公钥硬编码到 APP 就可以了。
        #
        #  - 怎么绕过 SSL-PINNING？ # 可以通过`justTrustMe`这个 Xposed 模块，来禁止 SSL 证书验证
        #  - SSL-PINNING 有哪些问题？ # SSL-PINNING 证书到期后会导致 APP 拒绝服务；更好的方法是走默认的操作系统 CA 验证，而不是 app 自己实现 CA 验证，因为 app 不能对证书信息进行实时更新，可能会出现很严重的问题
        #
        #  - "*为什么你抓不到 baidu 的数据？（app 能获取数据，但是抓包软件抓不到数据，为什么？）*" # [为什么你抓不到baidu的数据](https://mp.weixin.qq.com/s?__biz=MzUzNTY5MzU2MA==&mid=2247497288&idx=1&sn=1d634021528643c2f71e7cbf4dd7a0f7) 因为 charles 之类的抓包软件只抓 ws 和 http，搞一个私有协议就绕过了。所以，用 lua 写一个 wireshark 的解析私有协议的插件，就能抓到了。因为大部分 app 都不会去搞私有协议这些的，所有抓包软件默认不抓这些协议。具体操作：pre_master_key(, client random, server random) 用tcpdump把加密的key导出（ssl.key），再给wireshark配置好这个ssl.key就可以抓取数据了
      hto:
        - 【架构方面】 # TODO 这个也需要加进去
        #- 第三方集成：
        #  - API 网关配置（路由/限流/熔断）
        #  - 监控告警（Prometheus/Grafana）
        #  - 日志聚合（ELK/Sentry）
        #  - 消息队列集成（异步任务）
        #- 依赖管理：
        #  - 第三方 API 封装（支付/短信）
        #  - 服务降级策略（熔断阈值设置）
      qs:





    - topic: 搜索建议模块
      qs:
        - "***搜索建议模块：怎么实现一个简易的搜索建议模块（怎么直接使用redis或者ES实现）？有哪些要注意的地方？最终方案：使用hbase怎么实现呢？***" # 搜索建议模块儿的核心就是把搜索日志做成TrieTree。RediSearch 本身就支持Prefix-Search，ES则可以使用term query或者phrase query实现类似功能  # [常见分布式应用系统设计图解（四）：输入建议系统 – 四火的唠叨](https://www.raychase.net/6299) 核心是怎么把“搜索日志”按时序、搜索频率process后存入hbase，再用TrieTree处理后放到CDN。另外需要想想怎么更新TrieTree，尽量降低开销？ # [系统设计：实时建议服务-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1910975)
        - “搜索建议都是有时效性的，只不过不同应用的时敏不同而已，比如weibo, twitter, tiktok之类的社交媒体平台时敏就更高，也就是说时间权重更高，而google之类的搜索引擎则 search frequency的权重则高于时效”
        - 如何扩展你的设计来支持多语言？ # 为了支持非英文的查询词，我们在字典树节点中存储Unicode字符。如果你不熟悉Unicode，这里介绍一下它的定义：“一个涵盖世界上所有书写系统的所有字符的编码标准，无论是现代还是古代的书写系统。​”欲了解更多的内容，请访问Unicode的官网。
        - 如果某个国家的高频查询词与其他国家的不一样怎么办？ # 在这种情况下，我们可能要为不同国家构建不同的字典树。为了提升响应速度，我们可以把字典树存储在CDN中。
        - 如何支持趋势性（实时）查询词？ # 假设爆发了一个新闻事件，一个查询词瞬间变得流行起来。我们原先的设计并不能支持这种情况，这是因为：原定每周更新字典树，所以下线的Worker并不会立即更新字典树。

    - topic: 面试经典场景题
      qs:
        - 高并发场景下，如何快速判断一个用户是否访问过我们的 APP？ # [如何快速判断一个用户是否访问过我们的 APP？-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1684115) 布隆过滤器 + 映射表（因为 bitmap 要求顺序 id，所以根据设备号查询映射表拿到流水 ID）

        - "***用Bitmap与AST做一个配置化时长系统***" # [用 Bitmap 与 AST 做一个配置化时长系统](https://mp.weixin.qq.com/s?__biz=MzU3NzEwNjI5OA==&mid=2247484732&idx=1&sn=9ec2035f53e6d77b47499a430d2eebfc) 这个跟贷超的自动封存的统计系统有点类似，但是那个是渠道维度的，这个是用户维度的，需要存的key很多。key的结构: online_info_{userId}_{date}也就是说，该业务每天要存`m`个key，如果保留n天，则是`m * n`个key。10万日活，保存10天，粗估一下，1条bitmap大概20k左右，总共20个G的内存。

        - "***怎么实现用户的每日限额：比如限制用户评论（又或者一天只能领三次福利之类的类似场景），在过去一个小时内只能评论三次？限制评论，每个用户限制只能在帖子下最多回复 3 条，用 redis 怎么实现？使用最少内存的方案？请说出大概思路，以及一些需要注意的细节***" # [Go 中实现用户的每日限额（比如一天只能领三次福利）](https://mp.weixin.qq.com/s?__biz=Mzg2ODU1MTI0OA==&mid=2247484838&idx=1&sn=a495e2c4e8105e1681f947412cb85302) # [go-zero/core/limit/periodlimit.go](https://github.com/zeromicro/go-zero/blob/master/core/limit/periodlimit.go) 思路：很简单，直接构造 limit:<biz>:uid 的incr key。细节：1、做一个PeriodLimit的struct供全局调用，其中field包括 period, quota, limitStore, keyPrefix, align 。2、构造不应该用token bucket, leaking bucket, fixed window counter, sliding window log, sliding window counter 等固定时间窗口限流，因为无法处理临界区请求突发场景，应该用redis lua脚本。

        - "***输入网址后，会发生什么？每一步的优化方案？（服务端优化？）What happens after enter the URL? (5P, browser(render engine, js engine), network protocols, server)***" # [重新思考浏览器输入了 URL 并按下回车之后到底发生了什么——本地 DNS 部分 | Nova Kwok's Awesome Blog](https://nova.moe/rethink-type-url-dns/)

        - 给你 1 亿个 URL，爬取数据会遇到什么问题？从 CPU 磁盘 网络 等方面。这个聊了很多 case
        - 有 10 亿用户，让你设计一个社区架构。包括点赞 发帖 删帖 的积分架构？期间一直在追问设计合不合理。不断的优化
        - 设计一个下单扣减库存的分布式应用，请求超时了怎么办？一直重试超时了怎么办？
        - 设计一个每秒 80 万 qps 的过滤器？过滤器用 redis 实现，宕机期间数据怎么恢复？

        - 设计一个压测工具？要求：1、能够指定 并发数 压测时间。2、压测结束后输出 平均请求耗时 最大请求耗时 QPS 失败率。3、可以使用 context 去控制压测协和的停止，最后在 main 里把所有结果进行汇总。

        - "***“历史记录”功能：一些查看历史记录的场景，比如说社媒和新闻网站的post历史记录、电商平台的历史浏览商品、视频平台的历史记录、IM应用的聊天进度（以标记每个人的群聊进度）、音乐平台的播放记录、搜索引擎的搜索记录、在线文档编辑的文档版本、OTA平台的旅行记录。实现也都是类似的。***" # [架构设计之历史记录设计 | 小夜时雨](https://www.xiaoyeshiyu.com/post/f986.html)
        #  ```shell
        #  zset <uid> <postId> <ts>
        #
        #  # 带进度的历史记录，和正常实现类似，无非是把postId换成外key（这个key的value就是带着userId, 进度等数据的pb序列化后的string）。可以理解为一维数据转二维数据，所以做个序列化数据就可以了。
        #  zset <uid> <history_content:<uuid>> <ts>
        #  ```
        #  关键点：
        #  - 判断之前是否有浏览记录，如果有就删除
        #  - zset最好做个数量限制（zset removerange），或者做个ttl
        #  - 冷热分离（冷数据放到mysql或者hbase都可以，双写或者cron刷盘到DB都可以）
        #  上面都是查询“历史记录列表”，如果点查某个视频历史进度，直接查 `history_content:<uuid>` 即可

    - topic: "***第三方服务***"
      qs:
        # [六个调用第三方接口遇到的大坑-51CTO.COM](https://www.51cto.com/article/766716.html)
        # [第三方服务挂了，如何保证服务不受影响？-腾讯云开发者社区-腾讯云](https://cloud.tencent.com/developer/article/1663722)
        - "***第三方服务挂了，如何保证服务不受影响?***"

        # [在线旅游平台如何确保第三方API高可用 - CNode技术社区](https://cnodejs.org/topic/56efaf0d532839c33a99d03d)
        # [去哪儿机票智能预警系统-雷达系统落地实践 - 掘金](https://juejin.cn/post/7452737406200234025)
        - 第三方服务监控：怎么确保第三方API高可用？

        - 接入400多个第三方API的综合注意事项：我们的服务接入了400多个3rd API，能否直接用prom或者zabbix这种monitor来做API可用性监控？（可以参考监控宝，实现API监控、API降级）
        #配置监视项目:在创建的主机上配置监视项目，包括可用性和性能监视项目。可用性监控可以使用HTTP或ICMP来检测第三方API服务的可用性，性能监控可以配置为检查API响应时间、请求成功率等指标。
        #设置触发器:根据监控项目的阈值设置触发器，在API服务性能异常时触发相应的警报。
        #创建图表和报告:通过在Zabbix中创建图表和报告，您可以直观地查看第三方API服务的性能趋势和历史数据。
        #配置报警通知:在Zabbix中设置报警通知方式，如电子邮件、短信等。，以确保在API服务失败时及时通知相关人员。

        # 除监控外，还需关注安全认证、性能优化、数据处理、错误容错、合规性、文档维护、测试、成本控制、与供应商关系、可扩展性及数据备份恢复等多方面要点。
        #- 安全与认证：妥善管理API密钥，采用合适的身份验证与授权方式，部署网络安全防护措施。
        #- 性能优化：实施请求限流与缓存策略，采用异步处理方式提升性能。
        #- 数据一致性与转换：标准化数据格式，制定合理的数据更新策略。
        #- 错误处理与容错：分类记录错误，建立预警机制，实施容错与降级策略。
        #- 合规性：遵循法律与隐私条款，明确数据归属与使用限制。
        #- 文档与维护：维护详细API文档，确保代码具有良好维护性。
        #- 测试策略：编写单元测试与集成测试用例，隔离测试环境与生产环境。
        #- 成本控制：管理API费用，优化服务器资源配置以控制成本。
        #- SLA与供应商关系：明确SLA并跟踪，建立与供应商的有效沟通渠道。
        #- 可扩展性与灵活性：采用可扩展架构，确保功能可灵活调整。
        #- 数据备份与恢复：制定备份策略并定期进行恢复测试。

        # 监控宝与Prometheus功能对比分析：监控宝有多种监控及展示分析、告警等功能，Prometheus除全球ping功能较难实现外，其余类似功能大多可通过配置等方式达成。

        #  监控宝支持通过Postman、Jmeter脚本一键导入来配置监控任务，暂未查到其支持配置文件直接导入.除了已知的全球ping API及接口延时等功能外，还有以下功能：
        #  请求验证及监控：支持GET、POST、PUT、DELETE、HEAD、OPTION六种请求方式的监控，并且可对JSON、XML、Text、ResponseHeaders、状态码等多种方式进行验证，确保请求结果的正确性.
        #  事务监控：一个事务可包含多步请求，通过对多步请求的事务监控，实现对复杂业务流程的全面监控，便于及时发现业务流程中的问题.
        #  智能展示与分析：监控概览页面可智能展示性能差、故障率高的监控任务，包括任务状态、监控频率、监控类型、异常/恢复信息、平均可用率及平均响应时间等，还可通过世界地图和中国地图，实时展现响应时间，数据可细化到地级市.
        #  数据对比分析：可横向对比站点与站点之间、同一站点不同地区之间、不同监控任务之间的数据，帮助用户更好地提升产品性能.
        #  告警功能：可以根据SLA设置告警阈值，当指标超过设定值时，会通过邮件、短信、App Push、电话语音、URL回调通知等方式第一时间发送告警通知，并且提供分级告警通知，可根据告警事件的不同等级将不同的告警推送给不同的人员.
        #
        #  你说的这些功能其实通过 Prometheus 可以比较简单的实现，对吧？比方说你说的这个告警功能，我给 Prometheus 去加一个 alert manager 的 rules 就可以了。然后具体的你说的这个智能展示分析，这个属于 Prometheus Prometheus 的拿手好戏，我直接去加对应的 metrics 。然后呢数据对比分析本身其实就是一个画图嘛，那这这也是 Prometheus 本身可以实现的。然后其实就是除了一个 global pin 它无法实现之外，其他都是 OK 的，对吧？

        - 对低频API进行降级相关问题
        #- 确定降级策略的标准
        #- 需要明确什么样的API属于低频API。可以根据业务使用频率（如过去一段时间内的调用次数低于某个阈值）、业务重要性（非核心业务相关的API）等来划分。同时，要考虑API提供数据的时效性，对于对时效性要求不高的数据对应的API，可以更积极地进行降级。
        #- 调整监控频率
        #- 当降低API请求频率后，可能也需要相应地调整监控频率。因为如果请求频率过低，原本紧密的监控可能会产生大量无效的告警。但也要确保监控频率足够捕捉到API可能出现的问题，避免出现问题长时间未被发现的情况。
        #- 数据一致性维护
        #- 低频API数据更新可能不及时，在降级过程中要考虑如何维护数据一致性。例如，当业务需要访问低频API的数据时，如何判断数据是否过期，是否需要强制更新等。可以在本地存储一份数据副本，并记录数据的更新时间，根据业务需求和数据的特性来决定何时更新副本。

        - 其他可能涉及的问题
        #- API功能变更管理
        #- 随着业务发展或平台方的更新，API功能可能会发生变更。这可能包括请求参数的变化、响应数据格式的改变、新增或删除某些功能等。需要建立一个机制来及时发现这些变更，比如定期检查API文档更新、与平台的开发者沟通渠道保持畅通等。当发现功能变更时，要评估对现有业务的影响，并及时调整代码。
        #- 资源分配和成本优化的动态调整
        #- 400多个API的资源消耗（如服务器资源、带宽等）可能会随时间变化。除了考虑API请求频率的调整，还需要动态优化资源分配。例如，对于那些请求量大幅下降的API，可以适当减少分配给它们的服务器资源份额。同时，根据不同API的收费模式（如按请求次数收费、按带宽使用收费等），重新评估成本结构，进一步优化成本。
        #- 兼容性和升级问题
        #- 当底层的技术框架（如用于API调用的库、操作系统、数据库等）进行升级时，要确保与这400多个API的兼容性。可能需要在升级前进行充分的测试，包括单元测试、集成测试和灰度发布等。特别是对于那些长期未更新的API，可能存在潜在的兼容性风险，需要重点关注。
        #- 多平台差异协调
        #- 不同平台的API可能处于不同的技术生态和业务环境中。例如，有的API可能更关注数据准确性，有的可能更强调实时性。需要协调这些差异，确保在整个服务中这些API能够协同工作。比如，当一个业务流程涉及多个不同平台的API时，如何平衡不同API的特点，以实现最优的业务效果。



    - topic: 排行榜功能
      qs:
        # [Review代码思考：排行榜同积分按时间排序优化方案 | Lua开发实战 - 知乎](https://zhuanlan.zhihu.com/p/380545260)
        - "***如果有一个排行榜，用 zset，根据积分和时间来排序，积分高的，时间最近的排在前面，怎么实现？（排行榜 zset + weight + factory pattern）***"
        #  经典的“排行榜”场景，网游实时战力排行、直播间打赏排行等
        #
        #  这个排行榜具有以下特点：
        #  - 用户每次请求会返回用户的排名
        #  - 送礼金额越多粉丝排名越靠前
        #  - 相同金额送礼越早越靠前
        #  - 排行榜会随着粉丝送礼变化而不断变化
        #
        #  方案：使用 zset user weight , 但是相同金额时间比较这方面我是考虑把金额和时间进行计算得到一个优先级的值。具体是 weight = money * 10^20 + time
        #
        #  很好想到“直接用zset的score进行排序，积分作为高位，时间作为低位”这个方案，但是想到一些存在的问题，比如说
        #
        #  - 通常我们的需求都是类似 [redis zset 多字段排序](https://blog.51cto.com/u_16213628/7599769) 这个帖子所说的“积登录时间排序，updated_at越大，该用户排名越靠前”，这个是天然符合score排序的。但是如果需求变了，我们需要updated_at越小，排名越靠前怎么实现？
        #  - ts时间戳位数太长了，13位嘛，怎么减少数据量？
        #  三种方案的思路其实大同小异，
        #  - 方案一的核心是通过当前时间和活动截止时间的时间差。然后拿时间差直接拼接积分，作为score
        #  - 方案二是位运算，我不懂懒得看了
        #    通过看这篇文章想到以下：
        #  - 这类需求是确定的，不太存在需求变动的问题。所以问题1不存在。
        #  - ts时间戳位数不是问题，因为本身zset的score作为double，64位，无论score的具体值是多少，都会以64位的double进行存储，所以不存在压缩位数来节省内存的操作。

        - 如果用户量亿级规模的排序，怎么搞？
        #  如果是 MMR匹配的游戏，直接根据 MMR 把用户分 bucket（每隔1000 分一个 bucket），比如说用户 A 是黄金分段，MMR7700分，就在 7000-8000 分这个 bucket 中 zrevrank找到其排名，再 zcard 前面的几个bucket，把排名累加即可得到用户 A 的总排名。
        #
        #  通用的解法是 zset+权重，至于权重在不同业务逻辑下的实现则不同（比如内容类可以参考 [内容影响因子](https://juejin.cn/book/6844733795329900551/section/6844733795380232206) 计算实际权重）

    - topic: xxx
      qs:
        - "***服务抖动//性能毛刺：性能毛刺就是负载突然被打满，服务抖动大概分为周期性抖动，无序抖动和偶发抖动，三者各自可能意味着哪些问题？怎么排查（排查顺序）？***"
        - 先从应用角度检查，是否有慢查询？
        - 抖动时间是否稳定，业务量与连接数是否有突增？
        - 服务器是怎么部署和管理的，网络是否稳定？
        #  周期性的毛刺
        #  - 发送到该服务器的业务量本身有周期性的增大。
        #  - 某个进程/任务周期性占用。如果是周期性监控进程占用 CPU，问问监控系统的开发人员，咨询监控软件时候时间点发起监控、内部发起了什么操作或命令，这些操作是否可以优化。
        #  - 定期的锁导致业务量在锁时期积压，锁释放后冲高
        #
        #  杂乱无章的毛刺
        #  - 发送到该服务器的吞吐量本身有是高低不一的。
        #  - 读取队列的算法有问题，读取不均匀
        #  - OS 调度问题
        #
        #  偶发的毛刺
        #  - 可以采用脚本或代码抓取当 CPU 出现毛刺时的调用栈（CoreDump、tprof、truss 等）
        #  - 也很有可能是某应用程序、系统程序或操作系统的 bug

        - "***怎么做业务数据库的“数据清理”？比如说各种流水表和日志表还好处理，业务表呢？除了冷热分离，还有啥方法？***"
        - "***幂等问题（状态表和流水表的数据一致性）：怎么解决幂等问题？“一锁二判三更新, 记得悲观加自旋” 。但是如果不能做查重判断，也不能做唯一索引，怎么解决幂等问题呢（无锁方案解决幂等问题怎么实现？）？***" # [幂等问题到底怎么解决，底层逻辑没人给你讲_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV11u411V7aL/) 1、先加锁，分布式锁或者。2、然后幂等性判断，可以基于状态机、流水表、唯一性索引等查重。3、更新数据。 retry。version, unique index, 组合唯一索引



#- type: 落地页
#  repo:
#    - url: https://github.com/weijunext/landing-page-boilerplate
#      des: 落地页/推广页制作 # [cruip/tailwind-landing-page-template: Simple Light is a free landing page template built on top of TailwindCSS and fully coded in React / Next.js. Made by](https://github.com/cruip/tailwind-landing-page-template)
#
#    - url: https://github.com/jiweiyeah/nextjs-saas-template
#      des: 一个简单易使用、seo 友好的 next.js 模板，没有复杂的操作和配置，适合快速搭建一个落地页、saas 网站等。










#- type: 远程工作聚合站
#  tag: works
#  repo:
#    - url: https://github.com/xbpk3t/popjob
#      des: 远程工作聚合站
#      topics:
#        - topic: 开发popjob过程中前端相关问题//相关疑惑
#
#        - topic: 开发popjob过程中后端相关问题
#
#        - topic: 独立开发：popjob 相关沉淀下来的相关信息//认知
#          qs:
#            - 做APP还是SaaS? 付费方式怎么做（订阅制、买断、还是用量付费）？ # [Ep 51. 独立开发，做 App 还是做 SaaS？【推广和销售】 - 捕蛇者说 | 小宇宙 - 听播客，上小宇宙](https://www.xiaoyuzhoufm.com/episode/676a5ffad6cded6445832b49)
#            - # [独立开发变现，靠的是认知，而不是产品 - 🍺 IceBeer](https://www.icebeer.top/%E7%8B%AC%E7%AB%8B%E5%BC%80%E5%8F%91%E5%8F%98%E7%8E%B0%EF%BC%8C%E9%9D%A0%E7%9A%84%E6%98%AF%E8%AE%A4%E7%9F%A5%EF%BC%8C%E8%80%8C%E4%B8%8D%E6%98%AF%E4%BA%A7%E5%93%81/)







#- type: 自动发卡
#  tag: works
#  repo:
#    - url: https://github.com/assimon/dujiaoka
#      des: 独角数卡(自动售货系统)，laravel-admin实现的，基本上是现在最好用、最成熟的自动发卡工具了
#      rel:
#        - url: https://github.com/Baiyuetribe/kamiFaka

#- type: feed/newsletter
#  tag: works
#  repo:
#    - url: https://github.com/ccbikai/RSS.Beauty
#      des: 一个基于 XSLT 技术的 RSS 美化工具, 可以将普通的 RSS/Atom 订阅源转换成美观的阅读界面。
#    - url: https://github.com/soulteary/flare
#      des: flare，仅供参考的webstack






# TODO
#- url: https://github.com/zhouyaozhouyao/goframe-admin
#  des: 这个项目搞了casbin，用 kustomize 部署 k8s。internal 文件夹下有 MVC 层，里面用了gf对Transaction的封装，不错



# TODO 需要注意的是，Feature Toggle并非只在FE中适用，在BE中也可以实现。其实就是根据YAML配置开关实现功能切换。怎么在golang中实现？
#- 怎么理解“不要拆分代码仓库，不要拆微服务，使用 `monorepo`+`feature toggle`”？ # [如何不 Review 每一行代码，同时保持代码不被写乱？](https://mp.weixin.qq.com/s?__biz=MzU3NzEwNjI5OA==&mid=2247484737&idx=1&sn=3bafa3d2288af3630d59814ebf73f760)
#
##  Monorepo：所有的代码都在一个仓库里。这样就不存在不同模块的仓库有不同的版本问题。大家都是统一的一个版本。升级线上系统的过程拆分成：部署 + 发布，两个步骤。部署的时候，整个 Monorepo 的代码都部署到目标机器上了，但并不代表发布了。
##  Feature Toggle：特性开关来精确控制哪些逻辑分支被发布出去。这样部署就和发布解耦了。要灰度多少比例，可以精确控制。要一个特性开关的两个版本的逻辑分支共存，也可以实现。
# Monorepo（单一代码仓库）
#- **是什么**：所有项目代码（前端/后端/基础设施）存放在**同一个Git仓库**中。
#- **解决什么问题**：
#  - 避免多仓库导致的版本碎片化（如A模块依赖B模块的v1.2，但B已升级到v2.0）。
#  - 支持跨模块的原子提交（一次提交修改多个关联模块）。
#  - 简化依赖管理和构建工具链。
#
## Feature Toggle（特性开关）
#- **是什么**：通过**配置开关**动态控制代码逻辑分支的启用/禁用。
#- **解决什么问题**：
#  - **解耦部署与发布**：新代码部署后，可通过开关按需逐步启用（如10%用户灰度）。
#  - **替代微服务拆分**：用开关控制功能范围，无需为小功能独立部署服务。
#  - **紧急回滚**：故障时关闭开关即可回退，无需重新部署。






#- type: bot
#  tag: works
#  score: -1
#  repo:
#    - url: https://github.com/ngc660sec/NGCBot
#      des: NGCBot 是一个基于 HOOK 机制的微信机器人，支持安全新闻定时推送、KFC 文案、备案查询等多种功能。 该项目具有以下特点和优势：1、支持多个安全资讯网站的定时推送。2、提供丰富的功能，包括备案查询、手机号归属地查询、WHOIS 信息查询等。3、支持积分功能和自动拉人进群。4、可以检测广告并进行处理，支持自动群发和 AI 回复。5、易于使用，适合小白用户上手。
#    - url: https://github.com/Hanson/vbot
#      des: vbot
#
#    - url: https://github.com/mcpservers/mcp-server-chatsum
#      des: 来自 idoubi 开源的微信机器人和消息总结 MCP Server，基于 Claude 的 MCP 协议，可以实现在 Claude 对话框总结微信聊天记录。
#
#    - url: https://github.com/huhuhang/github-wechat-bot
#
#    - url: https://github.com/indes/flowerss-bot
#      des: 一个支持RSS订阅的Telegram机器人，可以推送RSS更新到Telegram
#
#    - url: https://github.com/python-telegram-bot/python-telegram-bot
#      des: telegram bot
#
#    - url: https://github.com/koishijs/koishi
#      des: 一个跨平台的机器人框架，支持多种聊天平台的消息处理和自动化
#
#    - url: https://github.com/ai16z/eliza
#      des: Eliza 是一个用于 Twitter 和 Discord 的对话代理。1、完整功能的 Discord、Twitter 和 Telegram 连接器。2、支持所有模型（如 Llama、Grok、OpenAI 等）。3、多代理和房间支持。4、可检索的记忆和文档存储。




#- type: PDF
#  tag: works
#  score: -1
#  repo:
#    - url: https://github.com/pdfcpu/pdfcpu
#      des: 用于创建新的 PDF 文档、编辑现有文档、合并 PDF 文件、拆分 PDF 文件、加密和解密 PDF 文件，以及执行其他多种 PDF 相关的操作。
#      rel:
#        - url: https://github.com/bblanchon/pdfium-binaries
#          des: 也是pdf操作库和渲染库，类似pdfcpu吧，但是pdfium更侧重于渲染，而非操作。
#    - url: https://github.com/YOYZHANG/PodCastLM
#      des: 通过处理 PDF 的内容，生成适合音频播客的自然对话，并将其输出为 MP3 文件。
#
#    - url: https://github.com/Hopding/pdf-lib
#      des: 用来操作PDF的js库（我主要用来动态渲染和修改PDF），需要注意的是本身不支持中文，需要自己添加ttf才能使用中文
#      rel:
#        - url: https://github.com/parallax/jsPDF
#
#    - url: https://github.com/johnfercher/maroto
#      des: 用 Go 生成样式美观的 PDF 文件。这一个 Go 语言开发的用于创建 PDF 文件的库，其灵感来源于 Bootstrap 框架。它允许你像使用 Bootstrap 创建网站一样，轻松编写和生成不同样式的 PDF 文件。
#
#
#    - url: https://github.com/Stirling-Tools/Stirling-PDF
#      doc: https://docs.stirlingpdf.com/
#      des: 据说是all-in-one的PDF处理工具，java实现的


#- type: 工作流引擎 # TODO
#  tag: works
#  repo:
#    - url: https://github.com/apache/airflow
#      des: DAG工作流引擎。用于编程式编写、调度和监控工作流的平台，以代码定义工作流，有动态性、扩展性等优势，适用于数据处理等场景，与Luigi、Oozie、Azkaban等同类工具相比，在工作流定义、用户界面、集成能力和社区活跃度上有特点 。
#    - url: https://github.com/spotify/luigi
#      des: 也是一个用于构建复杂批处理工作流的 Python 库，它允许用户将任务定义为有向无环图（DAG），并提供了任务调度、依赖管理和错误处理等功能。与 Airflow 类似，Luigi 强调任务的可重复性和幂等性，适用于处理大规模数据处理工作流。然而，Luigi 的用户界面相对较简单，不像 Airflow 那样提供丰富的可视化和监控功能。
#    - url: https://github.com/azkaban/azkaban
#      des: 是一个由 LinkedIn 开发的开源工作流调度系统，用于管理和执行数据处理工作流。它支持使用简单的文本文件（.job 文件）来定义工作流，提供了可视化的监控界面，便于查看工作流的执行状态。Azkaban 易于上手，适用于简单到中等复杂度的工作流调度场景。然而，Azkaban 的功能相对较少，扩展性不如 Airflow。
#    - url: https://github.com/apache/oozie
#      des: 是一个基于 Hadoop 生态系统的工作流调度工具，主要用于管理和协调 Hadoop 作业的执行。它使用 XML 配置文件来定义工作流，支持多种类型的任务，如 Hive 查询、MapReduce 作业、Pig 脚本等。Oozie 与 Hadoop 紧密集成，适用于在 Hadoop 环境中进行大规模数据处理工作流的调度。但与 Airflow 相比，Oozie 的配置和使用相对复杂，学习曲线较陡。
#
#    - url: https://github.com/aizuda/flowlong
#      doc: https://doc.flowlong.com/docs/preface.html
#      des: 【工作流引擎】飞龙
#
#    - url: https://github.com/dromara/liteflow
#      doc: https://liteflow.cc/
#      des: 【工作流引擎】
#      qs:
#        - 表达式引擎、工作流引擎、规则引擎、
#        - 工作流引擎：工作流引擎是解释执行工作流定义以自动化业务流程的软件系统，具备流程定义、任务调度、流程执行与监控、资源管理等功能
#
#
#    - url: https://github.com/goplus/gop
#      des: 【表达式引擎】类似java的groovy，支持动态编译代码并执行的
#      rep:
#        - url: https://github.com/expr-lang/expr
#          des: Expression language and expression evaluation for Go
#        - url: https://github.com/traefik/yaegi









#- type: Markdown
#  tag: works
#  repo:
#    - url: https://github.com/yuin/goldmark
#      des: Markdown Render. Most leading markdown service uses goldmark as markdown render service, rather than blackfriday.
#      sub:
#        - url: https://github.com/kpym/gm
#          des: goldmark cli tool.
#        - url: https://github.com/abhinav/goldmark-toc
#        - url: https://github.com/yuin/goldmark-highlighting
#      qs:
#        - 为啥更推荐 goldmark，而非blackfriday?
#        - goldmark.WithExtensions()
#
#    - url: https://github.com/JohannesKaufmann/html-to-markdown
#      des: Convert HTML into Markdown
#      rel:
#        - url: https://github.com/mattn/godown
#
#    - url: https://github.com/markdown-it/markdown-it
#      doc: https://markdown-it.github.io/markdown-it/
#      des: 一个快速、可扩展的Markdown解析器，支持插件系统和语法扩展
#      rel:
#        - url: https://github.com/markedjs/marked
#          des: js markdown parser and compiler.
#      topics:
#        - topic: js markdown
#          qs:
#            - 到底用 markdown-it 还是 marked? 选型时考虑哪些因素？
#
#
#    - url: https://github.com/microsoft/markitdown
#      des: 把 microsoft office文档转成md
#
#    - url: https://github.com/yshavit/mdq
#      des: like jq but for Markdown, find specific elements in a md doc
#
#
#    - url: https://github.com/DS4SD/docling
#      des: Easy Scraper是不是就使用这个实现的？支持读取多种流行的文档格式（PDF、DOCX、PPTX、图像、HTML 等）并出为 Markdown 和 JSON。具备先进的 PDF 理解能力，包括页面布局、阅读顺序及表格结构。提供统一且表达丰富 DoclingDocument 表示格式。能够提取元数据，如标题、作者及语言等信息。

#         - topic: "***Markdown使用技巧***"
#          qs:
#            - "*多用无序列表，而不是有序列表*"
#            - 【MD中添加注释】 # `[xxx]: # "xxxx"`
#            - 【MD文件之间跳转】
#            - 【MD文件内跳转】md 文件内跳转需要自定义锚点，比如`<spa* id=""></span>`或者`<a href="*></a>`标签才能跳转（*注意要在“预览模式”才能跳转*）
#
#            - 【Markdown Figcaption】How to display markdown image description as figcaption (below the image) in MDX?
#            #[Captions in Markdown | theSynAck](https://thesynack.com/posts/markdown-captions/)
#            #[github - Using an image caption in Markdown Jekyll - Stack Overflow](https://stackoverflow.com/questions/19331362/using-an-image-caption-in-markdown-jekyll)
#            #[html - How to put two images side by side with captions below - Stack Overflow](https://stackoverflow.com/questions/73668542/how-to-put-two-images-side-by-side-with-captions-below)
#            #[html - Using Markdown, how do I center an image and its caption? - Stack Overflow](https://stackoverflow.com/questions/3912694/using-markdown-how-do-i-center-an-image-and-its-caption)
#            #[Add Captions to Images in Markdown - Definitive Guide](https://www.docstomarkdown.pro/add-image-captions-to-images-in-jekyll-blog-posts-with-markdown/)
#            #*[Adding figures with captions to images in markdown with eleventy](https://www.alpower.com/tutorials/adding-figures-with-captions-to-images-in-markdown-with-eleventy/)*
#            #[rbottomley/markdown-it-figure](https://github.com/rbottomley/markdown-it-figure)
#            #[Antonio-Laguna/markdown-it-image-figures: Render images occurring by itself in a paragraph as a figure with support for figcaptions](https://github.com/Antonio-Laguna/markdown-it-image-figures)
#            #[Creating captions for images? · facebook/docusaurus · Discussion #4633](https://github.com/facebook/docusaurus/discussions/4633)
#            #
#            #给 docusaurus 添加 figcaption 支持
#            #
#            #docusaurus markdown image description as figcaption
#            #[Feedback | Docusaurus](https://docusaurus.io/feature-requests/p/image-captions)
#            #[josestg/rehype-figure: rehype plugins to support figure and caption](https://github.com/josestg/rehype-figure)
#            #[Docusaurus 3: how to migrate rehype plugins | johnnyreilly](https://johnnyreilly.com/docusaurus-3-how-to-migrate-rehype-plugins)
#            #
#            #---
#            #
#            #可以看到，目前大部分都还是通过 components 实现 figcaption（包括 rabbitmq 的官方文档），
#            #
#            #而不是通过 remark 或者 rehype 的方式直接加载
#            #
#            #[rehypejs/rehype-unwrap-images: rehype plugin to remove the wrapping paragraph (`<p>`) for images (`<img>`)](https://github.com/rehypejs/rehype-unwrap-images)





#- type: rss
#  tag: works
#  repo:
#    - url: https://github.com/mmcdole/gofeed
#      des: How to parse rss?
#      rel:
#        - url: https://github.com/kurtmckee/feedparser
#          des: python rss parser
#
#    - url: https://github.com/gorilla/feeds
#      des: How to generate rss?
#    - url: https://github.com/extractus/feed-extractor
#      des: How to parse rss in js?
#    - url: https://github.com/reorx/rss-filter
#
#    - url: https://github.com/yllhwa/RSSWorker
#      des: 基于Cloudflare Worker的rss订阅生成器，我用来订阅bz的rss
#
#    - url: https://github.com/RSSNext/Follow
#      des: 锐评一下Follow，首先声明，相较于rss，我现在更喜欢newsletter。也只用自己实现的rss2newsletter服务，定时把所有feed汇总到。这是我经历了近几年对feed流管理的折腾后，摸索出来最适合自己的方案。其实就是 ttrss+briefcake+web3嘛，既然不支持self-hosted，那后面肯定有付费功能。另外，follow的响应太慢了，
#
#    - url: https://github.com/CaoMeiYouRen/rss-impact-server
#      des: support filter contents, and custom notification using hooks.
#
#    - url: https://github.com/bramus/web-dev-rss
#
#    - url: https://github.com/pietheinstrengholt/rssmonster
#
#    - url: https://github.com/HenryQW/Awesome-TTRSS
#      doc: https://ttrss.henry.wang/
#
#    - url: https://github.com/miniflux/v2
#      rel:
#        - url: https://github.com/reminiflux/reminiflux
#          des: Alternative web frontend for miniflux. 相当于miniflux的ttrss UI，但是不支持Three-Column展示，可用性就比较差 "Support for three-column layout".
#        - url: https://github.com/electh/nextflux
#          des: react+NextUI重新实现了miniflux的样式，三栏式布局，比reminiflux更好看更实用
#
#    - url: https://github.com/leafac/kill-the-newsletter
#      doc: https://kill-the-newsletter.com/
#      des: 把newsletter转rss的工具 # [Kill the Newsletter: Convert email newsletters into Atom feeds | Hacker News](https://news.ycombinator.com/item?id=41312259)
#
#    - url: https://github.com/pentacent/keila
#      des: newsletter
#    - url: https://github.com/spatie/laravel-newsletter
#      des: manage newsletter in laravel
#    - url: https://github.com/zudochkin/awesome-newsletters
#      des:
#
#    - url: https://github.com/pictuga/morss
#      des: 用来获取rss feeds fulltext
#
#    - url: https://github.com/easychen/ai-rss
#      des: AI时代的RSS生成器。方糖搞的新产品。
#
#
#    - url: https://github.com/ccbikai/RSS.Beauty
#      doc: https://rss.beauty/
#      des: 说白了就是把rss格式转成用户更友好的页面。类似工具很多，但是这个是其中比较好用的。
#
#    - url: https://github.com/Necoro/feed2imap-go
#      des: 一个将RSS/Atom订阅源同步到IMAP邮箱的工具，Go语言实现版本



#- type: mail # TODO 太多了，需要重新整理
#  tag: works
#  repo:
#    - url: https://github.com/knadh/listmonk
#      des: 自建mailing list and newsletter manager，golang+pgsql实现。后台UI确实不错。
#      doc: https://listmonk.app/docs/
#
#    - url: https://github.com/mailhog/MailHog
#      des: 邮件服务器，全平台支持，还带个 web 页面
#    - url: https://github.com/stalwartlabs/mail-server
#      des: 开源邮件服务器解决方案，支持 JMAP、IMAP4、POP3 和 SMTP，并具有多种现代功能。它使用 Rust 编写，设计为安全、快速、强大且可扩展。
#    - url: https://github.com/resend/react-email
#      doc: https://react.email/
#      des: 为啥我之前就没发现这个呢？众所周知，gmail之类的邮箱服务会过滤掉很多js和css代码。那用这个服务就可以解决这个问题。确保HTML符合邮件客户端的要求，避免使用受限制的JS和CSS。并且本身提供了一些常用的 Mail Template可以直接使用。但是需要注意的是，如果想处理动态数据，最佳方案仍然是API获取数据。如果想获得类似 golang template 的方案，大概只能golang转JSON，react直接渲染JSON数据。“假如你的网站需要做发送邮件的功能，包括构建模板，可以试试 React Email 这个开源库，功能还挺丰富，使用也简单，类似于这种经常收到国外邮件的展示效果，也更现代一些。”。【2025-03-13】这个react-email相较于mjml的优势与resend, Nodemailer, SendGrid, Postmark, AWS SES, Plunk, Scaleway 等邮件提供商的支持，mjml没有提供该功能。但是其劣势在于样式组件太少，只提供了几种最普通的component，而mjml则提供了更多的组件。这就导致不够灵活，并且其优势相较于劣势太过于不值一提了，我需要哪个mail provider，自己去接API就可以了，不需要一个专门用来渲染MAIL的库，给我提供这些。
#      sub:
#        - url: https://github.com/resend/resend-go # [Send emails with Next.js · Resend](https://resend.com/nextjs)
#          doc: https://resend.com/emails
#        - url: https://github.com/resend/resend-node
#      rel:
#        - url: https://github.com/sofn-xyz/mailing
#          des: 不如 react-email 好用
#      topics:
#        - topic: resend
#          qs:
#            - 关于渲染MAIL，需要思考的首要问题是，
#            - nextjs来实现resend template? # [resend/resend-nextjs-app-router-example: This example shows how to use Resend with Next.js (App Router)](https://github.com/resend/resend-nextjs-app-router-example) # [Sending Emails with Next.js and RESEND — The Cool Developer’s Guide | by Imam Dahir Dan-Azumi | Medium](https://medium.com/@eimaam/sending-emails-with-next-js-and-resend-the-cool-developers-guide-fa94975e2741)
#
#    - url: https://github.com/Jinnrry/PMail
#    - url: https://github.com/getmeli/meli
#    - url: https://github.com/mjl-/mox
#    - url: https://github.com/cypht-org/cypht
#
#    - url: https://github.com/pimalaya/himalaya
#    - url: https://github.com/frappe/frappe
#    - url: https://github.com/Mailu/Mailu
#    - url: https://github.com/foxcpp/maddy
#    - url: https://github.com/nikoksr/notify
#    - url: https://github.com/nylas/nylas-mail
#    - url: https://github.com/novuhq/novu
#    - url: https://github.com/psarna/edgemail
#
#
#    - url: https://github.com/roundcube/roundcubemail
#      doc: https://roundcube.net/
#      des: 支持docker安装。开源 Webmail 服务
#
#    - url: https://github.com/ThomasHabets/cmdg
#      des:
#
#    - url: https://github.com/modoboa/modoboa
#      des: modoboa
#
#    - url: https://github.com/maildev/maildev
#      des: 类似mailhog那样的开发时使用的本地mail receiver，但是现在来说不都是直接用resend这种服务了吗？感觉之前这些已经完全没啥用了
#
#    - url: https://github.com/mail-in-a-box/mailinabox
#      doc: https://mailinabox.email/
#
#
#    - url: https://github.com/mjmlio/mjml
#      doc: https://documentation.mjml.io/
#      des: MJML. 众所周知各家邮箱服务商为了避免被eject等网络安全问题，所以很多HTML标签，以及整个style都会被过滤掉（只能写行内样式）。在这种情况下，就可以通过 mjml 来解决这个问题，并且还提供响应式邮件内容的解决方案。
#      sub:
#        - url: https://github.com/Boostport/mjml-go
#          des: 非常实用的库，我的rss2newsletter就是用这个实现的。搜索“Compile MJML to HTML directly in your Go applications!”看到的。
#
#
#    - url: https://github.com/beilunyang/moemail
#      doc: https://moemail.app/ # https://mail.tm/zh/ 这个更好用，但是没开源。特点：极简操作，打开即用随机邮箱，关闭页面前邮箱地址不变，支持多语言和API调用（需自研解析）。亮点：无广告、无追踪代码，适合追求「零干扰」的极客用户。
#      des: 临时邮箱服务（developed by NextJs + Cloudflare）




#- type: RPA
#  tag: works
#  repo:
#    - url: https://github.com/aisingapore/TagUI
#      des: 【特点和优势】：是一个跨平台的，支持命令行和可视化的 RPA 解决方案，用于自动执行键盘、鼠标操作，被认为是最佳远程 RPA 开源平台，是远程 RPA 工作的理想开源项目，能够在不停机的情况下更新，并且是一个基于云的应用程序，可以从世界任何角落进行管理，最多可支持 15 个用户，支持从各种终端设备管理多个会话，支持以类似自然语言的语法编写脚本，提升了无代码体验，还为 Microsoft Word、Excel 的 RPA 自动化产品，为 Microsoft Power Automate 提供了附加组件。【适用场景】：适用于需要进行远程自动化操作、多终端设备管理以及对自然语言脚本编写有需求的用户和场景，可用于网页数据抓取、表单填写、报告生成、系统集成、测试自动化、文件处理、邮件处理等多种重复性任务的自动化。
#
#    - url: https://github.com/robotframework/robotframework
#    - url: https://github.com/saucepleez/taskt
#      des: 【特点和优势】：以前称为 sharpRPA，是一个免费的、开源的、有趣的流程自动化软件，提供了易用的操作界面，无需编码或使用最少的命令即可实现自动化机器人的创建，其屏幕记录器具有多种记录功能，可以记录各种用户活动，从而根据记录信息创建机器人脚本，还包含一个带有拖放机制的可视化脚本生成器，在 “所见即所得” 设计器中设计机器人模型，并提供数十个命令，如自定义代码执行、图像识别和 OCR 等。【适用场景】：适合非技术人员或对编程不太熟悉的用户快速创建简单的自动化流程，常用于处理一些相对简单、规则明确的重复性任务，如数据录入、文件管理、简单的网页操作等。
#    - url: https://github.com/A9T9/RPA
#    - url: https://github.com/open-rpa/openrpa
#      des: 【特点和优势】：具备可拖拉可视化界面，非常成熟且易用，可以用于任何规模大小的企业，拥有非常活跃的社区，并且积极在为项目做贡献，软件更新非常频繁，使用 C# 和 JavaScript 技术开发，提供的功能包括远程管理、状态支持、分析仪表板、调度和规划功能等，能够与各种云提供商集成。【适用场景】：可用于各种规模企业的业务流程自动化，尤其适合需要进行复杂流程设计、管理和监控的企业，如财务、人力资源、客户服务等部门的自动化流程处理，也可与其他系统集成实现更复杂的自动化场景。
#
#    - url: https://github.com/robocorp/rpaframework
#
#    - url: https://github.com/wechaty/wechaty
#      des: Conversational RPA SDK for Chatbot Makers.








#- type: sdk
#  tag: works
#  score: -1
#  repo:
#    - url: https://github.com/fastwego/wxwork
#      des: 企业微信 SDK 的 Go 语言实现
#
#    - url: https://github.com/fastwego/dingding
#      des: 钉钉开放平台 SDK 的 Go 语言实现
#
#    - url: https://github.com/aws/aws-sdk-go
#      des: AWS官方的Go语言SDK，用于访问AWS服务
#      sub:
#        - url: https://github.com/aws/aws-sdk-go-v2
#          des: v2
#    - url: https://github.com/Azure/azure-sdk-for-go
