---


# TODO math相关需要大量整块时间学习，暂不处理
# TODO [马同学](https://www.matongxue.com/) 《马同学线性代数》、《马同学微积分》、《马同学概率统计》


# TODO [AIDajiangtang/Probability-and-Statistics: 微信公众号：人工智能大讲堂，专注人工智能底层数学原理与应用，专栏包括线性代数，概率统计，机器学习，深度学习](https://github.com/AIDajiangtang/Probability-and-Statistics)


#- url: https://github.com/jaraco/wolframalpha
#  doc: https://www.wolframalpha.com/
#- url: https://github.com/pim-book/programmers-introduction-to-mathematics
#  des: This repository contains the code examples from the book "A Programmer's Introduction to Mathematics" and is ideal for programmers looking to deepen their understanding of math. It covers topics like number theory, algebra, and geometry, with a focus on applying these concepts in programming
#- url: https://github.com/TalalAlrawajfeh/mathematics-roadmap
#  des: If you are seeking a structured and comprehensive guide to learning mathematics, this repository is an excellent starting point. It offers a roadmap for mastering various mathematical concepts, covering topics from basic arithmetic to advanced fields such as calculus, linear algebra, and differential equations. Additionally, it includes an image that visually represents a roadmap, indicating where to start and which topics to cover first.
#- url: https://github.com/ManimCommunity/manim
#  des: Visualizing mathematical concepts can significantly enhance understanding, and that's where Manim excels. This Python framework enables users to create stunning mathematical animations. It allows for the programmatic creation of precise animations, as demonstrated in the videos by 3Blue1Brown. Whether you are a teacher producing educational content or a student trying to visualize complex equations, Manim is a powerful tool for bringing math to life.
#
#- url: https://github.com/CamDavidsonPilon/Probabilistic-Programming-and-Bayesian-Methods-for-Hackers
#  des: Bayesian statistics can be intimidating, but this repository makes it accessible. It introduces Bayesian methods and probabilistic programming with an emphasis on computation and understanding. Written in Python, the repository includes practical examples that are perfect for anyone looking to learn Bayesian statistics through hands-on experience. It also features Jupyter notebooks that provide both code and explanations.
#- url: https://github.com/Experience-Monks/math-as-code
#  des: For programmers, mathematical notation can sometimes feel like a foreign language. This repository bridges the gap by providing a cheat sheet for translating mathematical notation into code (Javascript and Python). It’s an invaluable resource for anyone working on algorithms, machine learning, or any math-heavy programming tasks.
#- url: https://github.com/mml-book/mml-book.github.io
#  des: This repository is the companion to the book "Mathematics for Machine Learning", which is a must-read for anyone diving into machine learning. It covers the mathematical foundations of ML, including linear algebra, calculus, and probability. The repository includes resources, exercises, and code examples to help you solidify your understanding.
#- url: https://github.com/rossant/awesome-math
#  des: If you are searching for a comprehensive collection of top-notch math resources, Awesome Math is the perfect repository. It features a curated list of books, YouTube videos, tools, learning platforms, courses, blogs, and resources spanning various branches of mathematics. Whether you are diving into pure or applied math, this repository has something valuable for everyone.
#- url: https://github.com/ossu/math
#  des: The Open Source Society University (OSSU) provides a free, self-directed education in mathematics through its repository. It offers a structured curriculum that includes links to free online courses, textbooks, and exercises. If you are seeking a college-level math education at no cost, this is the ideal resource for you. By following the curriculum, you will learn from instructors at renowned institutions such as Harvard, MIT, and Stanford. This resource is maintained by a community, ensuring that you receive updated materials and guidance.
#- url: https://github.com/dair-ai/Mathematics-for-ML
#  des: This repository is a collection of resources specifically designed to help you learn the mathematics needed for machine learning. It includes books, papers, tutorials, videos, and articles on topics like linear algebra, calculus, and probability. If you are a data science beginner, this is a must-have resource.
#- url: https://github.com/jonkrohn/ML-foundations
#  des: This repository focuses on the foundational concepts of machine learning, covering topics such as linear algebra, calculus, statistics, and computer science. It is designed for individuals who want to establish a strong mathematical groundwork for machine learning. The repository includes code examples and practical applications associated with Jon Krohn's Machine Learning Foundations curriculum.
#- url: https://github.com/chensongpoixs/cartificial_intelligence
#  doc: https://chensongpoixs.github.io/cartificial_intelligence/#/
#  des: AI算法基本功、AI算法进阶技能和OpenCV模块分析


- type: Math
  tag: algo
  score: 0
  using:
    url: https://github.com/OI-wiki/OI-wiki
    doc: https://oi-wiki.org/
    des: OI比赛手册
  topics:
    - topic: 函数 # ***[熬了几个通宵，终于把初中到大学的数学知识梳理完了（学习算法必备数学知识） - 全栈深入 - 博客园](https://www.cnblogs.com/janas/p/14897873.html)***
      qs:
        - 一次函数/反比例函数/二次函数
        - 函数与方程关系
        - 应用场景：利润计算、行程问题、图像分析
        - 函数图像变换：平移, 对称, 伸缩

        - 六大基本函数：常数函数、幂函数、指数函数、对数函数、三角函数、反三角函数
        - 反函数
        - 复合函数
        - 函数的性质（有界性、单调性、奇偶性、周期性）
        - 导数应用：切线斜率, 极值判断, 最优化
        - 分段函数与绝对值函数
        - 二次函数与顶点式
        - 函数的零点与方程根的关系


    - topic: 代数
      qs:
        - 整式运算（因式分解, 分式化简, 多项式运算）
        -
        - 多项式运算与因式分解
        - 方程与方程组（一元一次/二次、二元一次、分式方程）
        - 不等式组与区间表示法

        - 数列（等差/等比数列通项, 求和公式, 递推公式）
        - 复数运算（几何意义, 多项式定理）
        - 数学归纳法
        - 数学归纳法应用
        - 排列组合与二项式定理
        - 复数概念与运算
        - 根式与有理数指数幂


    - topic: 推理
      qs:
        - 命题逻辑（充分条件、必要条件、逆否命题）
        - 直接证明与反证法
        - 数学归纳法应用
        - 存在性命题与构造性证明
        - 抽屉原理与极端原理


    - topic: 平面几何
      qs:
        - 基本图形性质（三角形、四边形、圆）
        - 三角形定理（全等(SSS/SAS/ASA), 相似(AA), 勾股定理）
        - 圆性质（切线判定, 弧长计算, 圆周角定理）
        - 四边形性质（平行四边形, 梯形, 菱形）

        - 圆锥曲线（椭圆/双曲线/抛物线的标准方程）
        - 坐标系转换（参数方程, 极坐标）

        - 全等三角形与相似三角形
        - 勾股定理与三角函数解三角形
        - 平面向量运算与坐标表示
        - 几何最值问题与轨迹方程

        - 解析几何（直线方程、圆方程、圆锥曲线）
        - 平面几何变换（对称、旋转、平移）
        - 几何最值问题
        - 托勒密定理与梅涅劳斯定理


    - topic: 立体几何
      qs:
        - 空间几何体（棱柱、棱锥、圆柱、圆锥、球）
        - 三视图与展开图
        - 空间中的平行与垂直关系
        - 空间向量与坐标运算
        - 空间角计算（线线角、线面角、二面角）
        - 空间向量应用（线面角计算, 二面角分析）
        - 空间坐标系中的几何问题
        - 空间几何证明方法
        - 截面问题与体积最值

        - 体积与表面积公式
        - 空间几何体的截面问题
        - 空间坐标系中的几何问题

    - topic: 不等式
      qs:
        - 绝对不等式
        - 均值不等式
        - 一元二次不等式
        - 柯西不等式
        - 其他不等式
        - 绝对不等式与条件不等式
        - 均值不等式（AM-GM不等式）
        - 一元二次不等式解法
        - 柯西不等式与排序不等式
        - 含参数的不等式
        - 不等式证明方法（比较法、放缩法、构造法）
        - 琴生不等式




    - topic: 高等数学
      qs:
        - 极限概念与计算
        - 导数定义与求导法则
        - 微分中值定理
        - 积分初步（定积分与不定积分）
        - 导数在单调性、极值中的应用
        - 简单微分方程
        - 泰勒展开式初步


    - topic: 线性代数
      qs:
        - 矩阵基本运算（加减、数乘、乘法） # [有趣的线性代数（一）：矩阵乘法 | 木鸟杂记](https://www.qtmuniao.com/2024/06/29/interesting-linear-algebra-1/)
        - 行列式计算与性质
        - 逆矩阵与矩阵方程
        - 线性方程组解法（高斯消元法）
        - 向量空间初步
        - 特征值与特征向量概念

        - 向量
        - 内积和外积
        - 矩阵
        - 初等变换
        - 行列式
        - 线性空间
        - 线性基
        - 线性映射
        - 特征多项式
        - 对角化
        - Jordan标准型


    - topic: 概率论
      qs:
        - 古典概型与几何概型
        - 条件概率与独立性
        - 随机变量与概率分布（二项分布、正态分布）
        - 期望与方差计算
        - 大数定律与中心极限定理
        - 贝叶斯公式
        - 统计初步（抽样、直方图、线性回归）
        - 概率进阶（条件概率, 贝叶斯公式, 几何概型）
        - 统计分析（正态分布, 箱线图解读, 线性回归）
        - 随机变量（二项分布, 期望与方差）
        - 大数定律与统计推断

    - topic: 数学思想（方法论）
      qs:
        - 数形结合（函数图像转换, 几何代数互化）
        - 分类讨论（含参方程处理, 绝对值问题）
        - 化归转化（复杂问题拆解, 非标准式转化）
        - 特殊到一般（数学归纳法, 模式发现）
        - 证明体系（直接证明与反证法；存在性命题构造；抽屉原理应用）

    - topic: 概率论的基本概念
      qs:
        - 样本空间，随机事件
        - 事件的相互关系及运算
        - 频率
        - 概率
        - 等可能概型（古典概型）
        - 条件概率
        - 全概率公式与贝叶斯公式
        - 事件独立性
    - topic: 随机变量及其概率分布
      qs:
        - 随机变量
        - 离散型随机变量
        - 分布函数
        - 连续型随机变量及其概率密度
        - 均匀分布和指数分布
        - 正态分布
        - 随机变量函数的分布


    - topic: 二元随机变量及其分布
      qs:
        - 二元随机变量，离散型随机变量分布律
        - 二元离散型随机变量边际分布律与条件分布律
        - 二元随机变量分布函数、边际分布函数及条件分布函数
        - 二元连续型随机变量，联合概率密度
        - 二元连续型随机变量边际概率密度
        - 二元连续型随机变量条件概率密度
        - 二元均匀分布，二元正态分布
        - 随机变量的独立性
        - 二元随机变量函数的分布

    - topic: 随机变量的数字特征
      qs:
        - 随机变量的数学期望
        - 随机变量函数的数学期望
        - 数学期望的性质
        - 方差定义和计算公式
        - 方差的性质
        - 协方差与相关系数
        - 不相关与独立
        - 矩，协方差矩阵，多元正态分布的性质


    - topic: 大数定律及中心极限定理
      qs:
        - 依概率收敛，切比雪夫不等式
        - 大数定律
        - 中心极限定理

    - topic: 统计量与抽样分布
      qs:
        - 总体，样本
        - 统计量，常用统计量
        - 单个正态总体的抽样分布
        - 两个正态总体的抽样分布


    - topic: 参数估计
      qs:
        - 点估计，矩估计
        - 极大似然估计
        - 估计量的评价准则，无偏性
        - 有效性，均方误差
        - 相合性
        - 置信区间，置信限
        - 枢轴量法
        - 单个正态总体均值的区间估计
        - 成对数据均值差，单个正态总体方差的区间估计
        - 两个正态总体参数的区间估计

    - topic: 假设检验
      qs:
        - 假设检验的基本思想
        - 单个正态总体参数假设检验（标准差已知，检验）
        - 单个正态总体参数假设检验（标准差未知，检验）
        - 单个正态总体参数假设检验（成对数据检验和参数的检验）
        - 两个正态总体参数假设检验(比较两个正态总体均值的检验）
        - 两个正态总体参数假设检验(比较两个正态总体方差的检验）
        - 拟合优度检验


    - topic: 方差分析与回归分析
      qs:
        - 单因素方差分析
        - 单因素方差分析（参数估计及均值的多重比较）
        - 回归分析（参数估计）
        - 回归分析（模型检验与应用）



    # [数学部分简介 - OI Wiki](https://oi-wiki.org/math/)
    - topic: xxx
      qs:
        - 符号
        - 进位制
        - 位运算
        - 二进制集合操作
        - 平衡三进制
        - 高精度计算
        - 快速幂
        - 置换和排列
        - 弧度制与坐标系
        - 复数
        - 数论
        - 多项式与生成函数
        - 组合数学
        - 线性代数
        - 线性规划
        - 抽象代数
        - 概率论
        - 博弈论
        - 数值算法
        - 傅里叶-莫茨金消元法
        - 序理论
        - 杨氏矩阵
        - 拟阵
        - Berlekamp–Massey 算法

    - topic: 数论
      qs:
        - 数论基础
        - 素数
        - 最大公约数
        - 数论分块
        - 欧拉函数
        - 筛法
        - Meissel–Lehmer 算法
        - 分解质因数
        - 裴蜀定理
        - 类欧几里德算法
        - 欧拉定理 & 费马小定理
        - 乘法逆元
        - 线性同余方程
        - 中国剩余定理
        - 升幂引理
        - 威尔逊定理
        - 卢卡斯定理
        - 同余方程
        - 二次剩余
        - 原根
        - 离散对数
        - 剩余
        - 莫比乌斯反演
        - 杜教筛
        - Powerful Number 筛
        - Min_25 筛
        - 洲阁筛
        - 连分数
        - Stern–Brocot 树与 Farey 序列
        - 二次域
        - 循环连分数
        - Pell 方程
    - topic: 多项式与生成函数简介
      qs:
        - 代数基本定理
        - 快速傅里叶变换
        - 快速数论变换
        - 快速沃尔什变换
        - Chirp Z 变换
        - 多项式牛顿迭代
        - 多项式多点求值|快速插值
        - 多项式初等函数
        - 常系数齐次线性递推
        - 多项式平移|连续点值平移
        - 符号化方法
        - Lagrange 反演
        - 普通生成函数
        - 指数生成函数
        - 狄利克雷生成函数
    - topic: 组合数学
      qs:
        - 排列组合
        - 抽屉原理
        - 容斥原理
        - 斐波那契数列
        - 错位排列
        - 卡特兰数
        - 斯特林数
        - 贝尔数
        - 伯努利数
        - Entringer Number
        - Eulerian Number
        - 分拆数
        - 范德蒙德卷积
        - Pólya 计数
        - 图论计数

    - topic: 抽象代数
      qs:
        - 群论
        - 环论
        - Schreier–Sims 算法

    - topic: 概率论
      qs:
        - 条件概率与独立性
        - 随机变量
        - 随机变量的数字特征
        - 概率不等式

        # [看见 概率论 - 通过交互式演示理解经典概率论定理](https://probability.visualized.fun/)
        - 大数定理
        - 贝叶斯定理
        - 凯利公式
        - 三门问题：一个反直觉的概率问题,展示了条件概率的奇妙之处。
        - 生日问题：在一个房间里,需要多少人才能让至少两人生日相同的概率超过50%?
        - 囚徒问题：100个囚犯如何制定最优策略,让所有人都能找到自己的号码?
        - 男孩女孩问题：一个关于条件概率的经典问题：已知一个是男孩，另一个是男孩的概率是多少？
        - 圣彼得堡悖论：期望值无穷大但人们只愿意付很少钱的游戏，挑战了经典期望值理论。
        - 两信封问题：两个信封中的钱一个是另一个的两倍，你要换另一个吗？一个关于决策的悖论。
        - 秘书问题：面试30个候选人，每个面试后立即决定是否录用。如何找到最优秀的人？
        - 扑克概率问题：德州扑克中抽到各种牌型的概率是多少？组合概率的精彩应用。
        - 抽奖问题：买了10个号码，抽5个中奖号，中奖概率是多少？超几何分布的应用。


    - topic: 数值算法
      qs:
        - 插值
        - 数值积分
        - 高斯消元
        - 牛顿迭代法


    # TODO 有时间研究一下 P, NP, NPC, NP-Hard 问题，分别是啥
    - topic: 计算复杂性基础与核心分类
      des: 从问题可计算性到复杂类层级的数学定义，建立对算法效率的本质理解
      qs:
        - P类与NP类问题的核心区别是什么？为何多项式时间验证是NP的关键特征？（网页1、2） # 回答需包含：P是确定性图灵机多项式时间可解，NP是非确定性图灵机多项式时间可验证，以及验证与求解的本质差异[1,2](@ref)

        - 如何通过约化证明旅行商问题（TSP）属于NP完全（NPC）？需满足哪两个条件？（网页3、6） # 回答需包含：证明属于NP类（验证解的有效性）和NP-Hard（任意NP问题可多项式时间约化到TSP），举例哈密顿回路到TSP的转换过程[3,6](@ref)

        - 为什么NP-Hard问题可能比NPC更难？停机问题为何属于不可判定问题？（网页3、5） # 回答需包含：NP-Hard只需满足可约化性无需属于NP类，停机问题在图灵机模型下无通用解法[3,5](@ref)

    - topic: 复杂问题的算法应对策略
      des: 基于复杂性分类设计实际解决方案，平衡理论限制与工程需求
      qs:
        - 近似算法如何处理背包问题？贪心策略的误差率如何计算？（网页1、6） # 回答需包含：按价值密度排序的贪心选择，最坏情况下误差率不超过50%（需数学证明），动态规划对比[1,6](@ref)

        - 随机化算法如何改善快速排序的最坏时间复杂度？期望时间复杂度如何推导？（网页6、7） # 回答需包含：随机选择pivot避免有序序列的最坏O(n²)，数学期望分析得出平均O(n log n)[6,7](@ref)

        - 启发式算法（如遗传算法）为何能在NP-Hard问题上有效？以物流路径优化为例说明（网页4、6） # 回答需包含：放弃全局最优追求局部改进，通过交叉变异操作探索解空间，实际案例中的10-20%次优解效率提升[4,6](@ref)

    #### 合并逻辑说明
    #1. **理论-实践二分法**
    #• **第一主题**聚焦计算复杂性理论的数学定义（P/NP/NPC/NP-Hard），覆盖约化证明、不可判定性等基础概念
    #• **第二主题**面向工程实践，将算法策略（近似/随机化/启发式）与具体问题（背包/排序/物流）结合，强调误差率、时间复杂度等可量化指标
    #
    #2. **紧扣核心概念**
    #所有问题均围绕三个核心展开：
    #• **复杂类定义**（如网页1指出P类问题的时间复杂度为O(n^k)）
    #• **证明方法论**（如网页3强调NPC需同时满足NP和NP-Hard）
    #• **算法有效性**（如网页6分析快速排序随机化的数学期望）
    #
    #3. **落地案例分析**
    #每个问题均附带实际应用场景：
    #• TSP的NPC证明直接关联物流调度
    #• 背包问题贪心算法用于资源分配优化
    #• 遗传算法在路径规划中的次优解实践
    #
    #### 学习路径建议
    #1. **优先掌握理论维度**
    #• 理解多项式时间分界：网页1指出指数时间算法在n=100时需运行4×10¹⁰年，而O(n³)算法仅需1秒（n=100时）
    #• 掌握约化技术：通过网页3的哈密顿回路→TSP案例理解问题难度传递
    #
    #2. **延伸至算法策略**
    #• 对比动态规划与贪心法：网页6显示背包问题动态规划复杂度O(nW) vs 贪心法O(n log n)，但后者可能损失20%最优值
    #• 实践随机化快速排序：网页7提供代码实现及概率分析模板
    #
    #如需进一步简化，可将第二主题拆分为"近似算法"与"随机化策略"，但当前结构已满足用户要求的2-3主题合并目标。所有问题均避免涉及量子计算等前沿领域，严格限定在经典计算复杂性范畴。

    - topic: 概率统计
      qs:
        - 随机变量
        - 概率分布
        - 大数定律（弱大数定律、强大数定律）、中数定律
        - 中心极限定理
        - 参数估计
        - 假设检验
        - 回归分析
        - 方差分析
        - 马尔可夫链
        - 样本空间
        - 离散随机变量、一般随机变量
        - 导出分布、协方差、矩母函数
        - 极限（马尔可夫、切比雪夫、）
        - 蒙特卡洛模拟 Monte Carlo Method. （其核心就是 抽样（以小见大，逼近极限）），比如说 通过打点+颜色来 计算圆周率 或者 计算不规则图形的面积
        - 联合分布
        - 期望极限



    - topic: 基本认知
      qs:
        - 概率论与统计学的关系是什么？ # [概率论与统计学的关系是什么？_估计](https://www.sohu.com/a/260383068_464088)
        #• **概率论**研究随机现象的数学模型（如分布、期望等），侧重理论推导（如大数定律、中心极限定理）。
        #• **统计学**基于概率论，通过样本数据推断总体特征（如参数估计、假设检验）。
        #• **关系**：概率论是统计学的理论基础，统计学是概率论的应用延伸。

        - “统计和概率是方法论上的区别，一个是推理，一个是归纳。概率论是统计推断的基础，在给定数据生成过程下观测、研究数据的性质；而统计推断则根据观测的数据，反向思考其数据生成过程。预测、分类、聚类、估计等，都是统计推断的特殊形式，强调对于数据生成过程的研究。”

        - "***什么是薄尾、肥尾、长尾、重尾？正态分布、幂律、次指数分布、随机游走***"
        #**薄尾**（如正态分布）：尾部概率衰减快，极端事件罕见。
        #**肥尾/重尾**（如幂律分布、帕累托分布）：尾部概率衰减慢，极端事件概率较高。
        #**长尾**：描述分布尾部拖长的现象（如次指数分布）。
        #**统计学意义**：重尾分布常见于金融、自然灾害等领域，需用稳健方法建模。


        - 什么是“大数定理”？能用频率近似代替概率，能用样本均值近似代替总体均值）
        - 能否从概率统计学来解释博弈论？


    - topic: 随机事件与概率
      qs:
        - 基本概念：随机试验、样本空间、事件关系与运算（互斥、对立、独立）。
        - 概率的定义：古典概型、统计定义、公理化定义。
        - 条件概率与公式：乘法公式、全概率公式、贝叶斯公式。
        - 典型问题：
        - 何谓条件概率？如何判断事件的独立性？
        - 如何用全概率公式解决复杂事件的概率计算？
    - topic: 随机变量及其分布
      qs:
        - 离散型与连续型随机变量：分布律、概率密度函数、分布函数的性质。
        - 常见分布：二项分布、泊松分布、正态分布、指数分布等。
        - 随机变量函数的分布：如通过变换求新变量的分布。
        - 典型问题：
        - 如何验证概率密度函数的规范性？
        - 正态分布为何被称为“最重要的分布”？
    - topic: 多维随机变量
      qs:
        - 联合分布与边缘分布：离散型的联合分布律、连续型的联合密度函数。
        - 条件分布与独立性：如何判断两个随机变量是否独立？
        - 典型问题：
        - 二维正态分布的性质是什么？
        - 如何通过联合分布求边缘分布？
    - topic: 数字特征
      qs:
        - 数学期望、方差、协方差与相关系数的定义及性质。
        - 典型问题：
        - 协方差与独立性之间的关系是什么？
        - 如何用切比雪夫不等式估计概率？
    - topic: 样本与统计量
      qs:
        - 总体与样本：简单随机样本的定义。
        - 常用统计量：样本均值、方差、顺序统计量。
        - 抽样分布：χ²分布、t分布、F分布的定义及分位点。
        - 典型问题：
        - 如何构造t统计量进行假设检验？
    - topic: 假设检验
      qs:
        - 基本思想：显著性水平、拒绝域、两类错误。
        - 单样本与双样本检验：均值、方差的检验方法。
        - 典型问题：
        - 如何通过p值判断原假设是否成立？
    - topic: 方差分析与回归分析
      qs:
        - 单因素方差分析：组间差异的检验。
        - 线性回归：最小二乘法估计参数、模型的显著性检验。
        - 典型问题：
        - 如何解释回归系数的统计学意义？
    - topic: 参数估计
      qs:
        - 点估计：矩估计法、最大似然估计法。
        - 估计量的评价标准：无偏性、有效性、一致性。
        - 区间估计：置信区间的构造方法。
        - 典型问题：
        - 如何用最大似然估计法求指数分布的参数？

    - topic: 样本空间与概率
      qs:
        - 集合
        - 概率模型
        - 条件概率
        - 全概率定理和贝叶斯准则
        - 独立性
        - 计数法
    - topic: 离散随机变量
      qs:
        - 概率质量函数
        - 随机变量的函数
        - 期望、均值和方差
        - 多个随机变量的联合概率质量函数
        - 条件
        - 独立性
    - topic: 一般随机变量
      qs:
        - 连续随机变量和概率密度函数
        - 累积分布函数
        - 正态随机变量
        - 多个随机变量的联合概率密度函数
        - 连续贝叶斯准则
    - topic: 随机变量的高级主题
      qs:
        - 导出分布
        - 协方差和相关
        - 矩母函数
        - 随机数个独立随机变量和
    - topic: 极限理论
      qs:
        - 马尔可夫和切比雪夫不等式
        - 弱大数定律
        - 依概率收敛
        - 中心极限定理
        - 强大数定律
    - topic: 伯努利过程和泊松过程
      qs:
        - 伯努利过程
        - 泊松过程
    - topic: 马尔可夫链
      qs:
        - 离散时间马尔可夫链
        - 状态的分类
        - 稳态性质
        - 吸收概率和吸收的期望时间
        - 连续时间的马尔可夫链
    - topic: 贝叶斯统计推断
      qs:
        - 贝叶斯推断与后验分布
        - 点估计、假设校验、最大后验概率准则
        - 贝叶斯最小均方估计
        - 贝叶斯线性最小均方估计
    - topic: 经典统计推断
      qs:
        - 经典参数估计
        - 线性回归
        - 简单假设校验
        - 显著性校验
