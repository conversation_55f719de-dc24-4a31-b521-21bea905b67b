---


# TODO [labuladong投稿视频-labuladong视频分享-哔哩哔哩视频](https://space.bilibili.com/14089380/upload/video)

## [Go语言实现多个方法解决10个经典数组问题 - 掘金](https://juejin.cn/post/7101464114924847117)
#- type: array
#  topics:
#    - topic: Array
#      des: "***数组的本质是有索引的有序集合***"
#    - topic: 写一个算法，把一个二维数组顺时针旋转 90 度
#    - topic: 求一个数组中连续子向量的最大和
#    - topic: 寻找一个数组中前 k 个最大的数 (寻找 k 值)
#    - topic: 一个数组，除了一个元素外，其他元素都是相等度，求那个元素？lc137
#    - topic: 数组很长的情况下 N 个有序数组求交集并集
#
#    - topic: 两个数组，求两个数组的交集
#    - topic: 两个无序数组找到他们的交集
#    - topic: 二维矩阵顺时针原地旋转 90 度
#
#    - topic: 合并两个有序数组 lc88
#
#    - topic: 连续子数组的最大和 offer42
#
#    - topic: 数组中值出现一次的数字
#    #*What's set? How to implement set? using (hashmap, RBT, array, linked-list) Compare these with time complexity, space complexity and usage scenarios*
#
#    - topic: set 的两个特征？ # 没有重复对象 + 没有特定排序
#    - topic: 怎么实现 set？ # struct 好于 map，因为空 struct 是零内存
#    - topic: 手写用 map 实现 set，支持 CURD？


# TODO 自己手写一下
#- topic: z
#  qs:
#    - 字符串之实现 Sunday 匹配
#    - 字符串泄漏之反转字符串(301)
#    - 字符串中的第一个唯一字符
#    - 字符串之验证回文串
#    - 滑动窗口最大值
#    - 最长公共前缀
#    - 两个数组的交集
#    - 最接近的三数之和



# TODO
#- url: https://github.com/wangzheng0822/algo
#  des: 王争《数据结构与算法之美》对应的repo


- type: zzz
  tag: algo
  score: 5
  using:
    url: https://github.com/EndlessCheng/codeforces-go
  repo:
    - url: https://github.com/algorithm-visualizer/algorithm-visualizer
      des: 响应最快、最好用的算法可视化工具


    # TODO
    #- url: https://github.com/wuYin/timewheel
    #- url: https://github.com/rfyiamcool/go-timewheel
    #- url: https://github.com/ouqiang/timewheel
    - url: https://github.com/antlabs/timer
      des: TimeWheel. 最小堆实现的5级时间轮
      qs:
        - "***timewheel = hashmap + list + ticker***. 这三个是核心，除此之外，可能还有用来CURD task的3个chan，slot数量、task之间延迟时间 之类的一些辅助字段" # [Go语言中时间轮的实现 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/444)
        - timewheel 用来解决哪类问题? 存在什么缺点? # [高效管理定时事件](https://huizhou92.com/zh-cn/p/%E9%AB%98%E6%95%88%E7%AE%A1%E7%90%86%E5%AE%9A%E6%97%B6%E4%BA%8B%E4%BB%B6/)
        - timewheel 的一些variants (Hash有序时间轮, multiple-level-timewheel)  相比 timewheel 有啥feat?


    - url: https://github.com/go-shiori/go-readability
      des: 【Reliability工具】
      record:
        - 【2025-07-10】有个认知变化，这类Readability类工具其实是用于爬虫的清洗阶段（具体来说，基本原理就是通过遍历Dom之后，通过正则提取其中的内容，对不同标签有不同加权和降权，然后把div标签替换为p标签，重新遍历，计分。最后根据分值，重新拼接内容），而非爬取阶段。所以不需要考虑是否支持fetch URL，是否支持动态js爬取、解析准确度和噪声处理之类的，都不是这类工具该做的。这类工具的技术选型时的排序是：1、编程语言是否与爬虫匹配 2、是否支持图片处理 3、输出数据格式。另外，这些工具也可以用在 稍后阅读APP、浏览器阅读模式、新闻聚合平台、大模型预训练 之类的场景，本质上来说就是用来提取指定网页的核心信息。
        - 【2025-07-10】移除【extractus/article-extractor（nodejs实现，不考虑）】、【mozilla/readability（最经典的readability工具，但是不支持提取图片，并且是js实现（所以适合集成到浏览器插件里，比如Easy Scraper），所以不考虑）】、【GeneralNewsExtractor（青南写的那个GNE）】


    - url: https://github.com/uber-go/ratelimit
      des: 比【juju/ratelimit】更易用。这个pkg之前因为time.Sleep()导致的bug在golang1.23已经fix了（之前time.Sleep()设定50us, 实际上会sleep 80us左右，还是在可用状态，但是在golang1.16之后会sleep 1ms以上，也就是20倍以上，如果设定5000qps，实际上则是5000/20=250qps，属于完全不可用状态。）。结论是，time.Sleep()的精度问题是普遍存在的，也无法解决，目前只能做到ms级精度，部分pkg能做到us级精度，至于ns级精度就不要想了。
      rel:
        - url: https://github.com/mennanov/limiters
        - url: https://github.com/throttled/throttled


    - url: https://github.com/cenkalti/backoff
      des: 【backoff】可以理解为某种interval的TCP window的AIMD。具体来说就是，指数退避算法会利用抖动（随机延迟）来防止连续的冲突，每次间隔的时间都是指数上升，以避免同一时间有很多请求在重试可能会造成无意义的请求。

    - url: https://github.com/mhuzaifadev/search-algorithms


    # TODO
    #- url: https://github.com/DinghaoLI/TopK-URL
    #- url: https://github.com/axiomhq/topkapi
    - url: https://github.com/segmentio/topk
      des: 【TopK】【排序算法】
      score: 5

    - url: https://github.com/deatil/go-cryptobin
      des: 非常好用，与js兼容的AES加密库，省得折腾了。支持以下算法：对称加密（AES/DES/3DES/SM4/ChaCha20/Twofish/Tea）、非对称加密（RSA/SM2/EIGamal/Gost）、国密（SM2/SM4）。
      record:
        - 【2025-07-04】移除【FiloSottile/age】。age只支持ChaCha20，远不如【go-cryptobin】。但是相比之下，支持 密钥管理（专用工具(age-keygen) SSH/YubiKey集成）。但是我用不到。


    - url: https://github.com/jxskiss/base62
      des: base62, 实现“短链接模块”时需要用到

    - url: https://github.com/wenj91/wrr
      doc: https://mp.weixin.qq.com/s?__biz=MzA4MTc4NTUxNQ==&mid=2650525165&idx=1&sn=136bf923194c8dea95f603c7b83baf57


    - url: https://github.com/hashicorp/golang-lru
      des: 【PRA(Page-Replacement-Algo)/scheduler】LRU/LFU

    - url: https://github.com/ogxd/gxhash
      doc: https://docs.rs/gxhash/latest/gxhash/
      score: 5


    - url: https://github.com/BurntSushi/fst
      des: fst 是一个用有限自动机做有序 set / map 的查询的库，效率比 HashMap 差一些，但非常非常节省内存。

    - url: https://github.com/libp2p/go-libp2p-kad-dht
      des: 【DHT-hash】KAD-DHT
      #        - 用二进制 (32/64/128) 表示一个节点的 id，两节点的 id 异或运算得到节点间的距离，可以方便进行网络划分
      #        - 每个节点保持的路由信息更丰富，同样是将整个网络按照划分成`log2N`份，在 chord 中，是保持 log2N 个路由节点，但在 kad 里面，是保存了`log2N`个队列。使得节点查询更迅速，命中概率更高

    - url: https://github.com/matoous/go-nanoid
      des: Golang random IDs generator.

    - url: https://github.com/klauspost/compress
      des: Compression-Algo

    - url: https://github.com/Project-OSRM/osrm-backend
      des: OSRM 路径规划器，查找现实地理地址的最短距离

    - url: https://github.com/go-kratos/aegis
      des: Service Reliability Algorithm. feats, circuit-breaker, ratelimit.

    - url: https://github.com/antlabs/strsim
      des: 一个字符串相似度计算库，支持多种相似度算法

    # TODO [shaj13/raft: raft is a golang library that provides a simple, clean, and idiomatic implementation of the Raft consensus protocol](https://github.com/shaj13/raft) 简易实现，比 hashicorp/raft 更清晰
    - url: https://github.com/hashicorp/raft
      doc: https://mp.weixin.qq.com/s/OmUbYHZhpvqsflyr6Y6QbQ
      score: 5
      des: raft
      sub:
        - url: https://github.com/hashicorp/raft-wal
          des: raft-wal
        - url: https://github.com/hashicorp/raft-boltdb
          des: raft是separate storage设计，更灵活，可以在不同使用场景下使用各自最优的storage

    - url: https://github.com/hashicorp/memberlist
      des: 【gossip】memberlist, gossip based membership and failure detection. 使用 gossip 协议来管理集群成员资格和节点故障检测。memberlist 通过在集群中的节点之间传播成员信息和故障报告，来维护集群的成员列表和状态。memberlist 通过引入一些额外的机制（如 Lifeguard 扩展），来提高协议在处理慢速消息处理、网络延迟或丢包等问题时的鲁棒性。目前性能最好的，容错性最好的，最终一致性的分布式协议。

  topics:
    - topic: 算法思维
      qs:
        - 【算法思维（六种）】回溯、搜索（穷举搜索法）、DP、枚举、贪心、分治、（滑动窗口、位运算、双指针、迭代、递归Recursion）分别是啥？那一些算法思维通常会与哪些关键字相关呢？
        #- 动态规划：最优解、最长、最短、最大、最小、最优子结构、状态转移等。
        #- 滑动窗口：连续子数组、最长、最短、最大、最小、固定窗口大小等。
        #- 位运算：二进制、位操作、位计数、位掩码等。
        #- 双指针：快慢指针、左右指针、对撞指针、两数之和等。
        #- 分而治之：分割问题、解决子问题、合并子问题的解等。
        - 快排=分治
        - 归并=分治+贪心
        - floyd=DP
        - Dijkstra=贪心+搜索
        - 双指针滑动窗口=枚举
        - 数独问题=回溯


        - "***有哪些经典的动态规划问题？ 01背包、完全背包问题、爬楼梯问题、（斐波那契数、不同路径、零钱兑换、爬楼梯、打家劫舍、编辑距离）***" # [经典动态规划问题 - Luyu Huang's Blog](https://luyuhuang.tech/2021/02/20/classic-dp.html)
        - 做动态规划题的步骤？有哪三步？（1、定义数组元素的含义 2、找出数组元素之间的关系式 3、找出初始值）
        - 动态规划和 DFS 有什么区别？ # 二叉树的子问题是否有交集。二叉树的子问题是没有交集的，所以大部分二叉树都用递归或者分治法 (DFS) 就可以解决。如果子问题是有交集的，需要用动态规划解决。

        # [Go 面试题：滑动窗口技巧](https://mp.weixin.qq.com/s/F0UhJBqMBl8IQCjF10AgvQ)
        # [我写了套框架，把滑动窗口算法变成了默写题](https://mp.weixin.qq.com/s?__biz=MzAxODQxMDM0Mw==&amp;mid=2247485141&amp;idx=1&amp;sn=0e4583ad935e76e9a3f6793792e60734)
        - 滑动窗口 sliding-window（一容（window），二变（left，right），三扩（right右移），四缩（left右移））

        - 【分组推理】假如说我现在有 12 个球，里面有一个球是坏的，它的重量跟其他的球不同。能否再用三次称量，就我们还有一个天平，能否用三次称量就知道哪个球是坏球？这种分组推理法还有什么类似的问题，就是也可以用这种分组推理法来解决呢？就是类似于我上面说的 12 个球分 3 次来找到坏球这种问题，能否再给我列出来一些类似的类似的案例或者说问问题啊。 # 12个球分三次，找到假球。陷阱在于不确定假球更重还是更轻，二分法

        # [动态规划(dp)入门 | 这tm才是入门动态规划的正确方式! | dfs记忆化搜索 | 全体起立!!_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1r84y1379W/)
        - "***“栈和队列其实都可以看成是一种受限的数组”，为啥说stack是一种受限的数组？能否认为“queue就是FIFO，stack就是 LIFO”？***" # 栈也是一种受限的序列，它只能够操作栈顶，不管入栈还是出栈，都是在栈顶操作。stack只有两种操作：push, 添加元素到栈的顶端 (末尾); pop, 移除栈最顶端 (末尾) 的元素。以上两种操作可以简单概括为"后进先出 (LIFO = last in, first out)"。

        - 栈常见的应用有进制转换，括号匹配，栈混洗，中缀表达式（用的很少），后缀表达式（逆波兰表达式）等。合法的栈混洗操作，其实和合法的括号匹配表达式之间存在着一一对应的关系，也就是说 n 个元素的栈混洗有多少种，n 对括号的合法表达式就有多少种。感兴趣的可以查找相关资料
        - 树其实是一种特殊的图，是一种无环连通图，是一种极大无环图，也是一种极小连通图。从另一个角度看，树是一种递归的数据结构。而且树的不同表示方法，比如不常用的长子 + 兄弟法，对于 你理解树这种数据结构有着很大用处，说是一种对树的本质的更深刻的理解也不为过。树的基本算法有前中后序遍历和层次遍历，有的同学对前中后这三个分别具体表现的访问顺序比较模糊，其实当初我也是一样的，后面我学到了一点，你只需要记住：所谓的前中后指的是根节点的位置，其他位置按照先左后右排列即可。比如前序遍历就是根左右，中序就是左根右，后序就是左右根，很简单吧？



    - topic: 并发问题
      isX: true
      des: "***有哪些经典并发问题?***"
      table:
        - name: 哲学家就餐问题 # 5 个人之间有 5 根筷子，想吃饭就需要 2 根筷子，怎么才能保证最短时间内 5 个人都吃到饭呢？（不出现死锁或者活锁）怎么分别用（服务生解法、资源分级解法、chandy/misra解法（最优解法，且有很强的拓展性，引入了用餐券来解决这个问题））解决？
          url: https://colobu.com/2022/02/13/dining-philosophers-problem/
          问题类型: 资源分配
          主要难点: 死锁、饥饿
          典型同步机制: 互斥锁、信号量
          经典解决方案: 限制同时进餐人数/资源分级
          应用场景: 资源分配系统

        - name: 理发店问题
          url: https://colobu.com/2022/02/27/barbershop-problem/ # [经典并发问题: 大型理发店](https://colobu.com/2022/03/06/hilzers-barbershop-problem/)
          问题类型: 服务调度
          主要难点: 线程管理、等待队列
          典型同步机制: 互斥锁、条件变量
          经典解决方案: 队列管理/服务线程协调
          应用场景: 服务型系统

        - name: 银行家算法
          问题类型: 死锁避免
          主要难点: 资源分配安全性
          典型同步机制: 算法检测
          经典解决方案: 安全状态检测
          应用场景: 资源分配系统

        - name: 生产者消费者问题 (Producer-Consumer)
          问题类型: 缓冲区管理
          主要难点: 同步协调
          典型同步机制: 信号量、管程
          经典解决方案: 双信号量控制
          应用场景: 数据管道系统

        - name: readers-writers问题
          问题类型: 数据访问控制
          主要难点: 读写竞争
          典型同步机制: 读写锁
          经典解决方案: 读写优先级策略
          应用场景: 数据库系统

        - name: ABA问题
          问题类型: 无锁编程
          主要难点: 内存值误判
          典型同步机制: 原子指令
          经典解决方案: 版本标签
          应用场景: 无锁数据结构

        - name: 临界区问题
          问题类型: 互斥访问
          主要难点: 竞争条件
          典型同步机制: 互斥锁
          经典解决方案: Peterson算法
          应用场景: 共享资源保护

        - name: 屏障同步
          问题类型: 阶段同步
          主要难点: 协同等待
          典型同步机制: 屏障
          经典解决方案: 计数器+条件变量
          应用场景: 并行计算
      qs:
        - 能否用同一个场景来类比解释以上这些并发问题，这样更直观，比如说都用图书馆来举例。
        - "***【ABA问题】怎么避免?***"

        - 能否用买咖啡举例deadlock时的4种情况（race、活锁、死锁、饥饿）？ # （race 被插队）、（活锁 互让一步）、（死锁 互不相让）、（饥饿 有个人下单 100 杯）
        - "***产生死锁的原因？产生死锁的4个必要条件（相互排斥、等待条件、没有抢占、循环等待）？有哪些形象的类比？***" # 我说说我的理解哈，有 x 和 y 两个人，同时有 x 和 y 两个门，本来 x 拿着 x 钥匙进入 x 门，y 同样进入 y 门就可以了。那么死锁是怎么回事呢？x 和 y 都想进入 x 门，这就是互斥（x 门只能让 x 或者 y 其中一个人进入）。两个人都各自有一把钥匙了，但是又想要对方的那把钥匙（这个就是请求保持条件）。只能这两个人自己主动释放，没有其他人出来协调资源（这个就是不可剥夺）。两个人都在等对方主动让出钥匙（这个就是循环等待条件）。其实想想，不就是生活中大部分吵架打架不就是这么来的吗？这个就是死锁

        #- `互斥条件`(相互排斥)，一个资源每次只能被一个进程使用
        #- `请求与保持条件`(等待条件)，当进程因为请求资源而阻塞时，对已经获得的资源保持不放
        #- `不剥夺条件`(没有抢占)，进程已获得的资源，在未使用完之前，不能强行剥夺，只能在使用完成后自己释放
        #- `环路等待`(循环等待)，多个进程之间形成一种头尾相接的循环等待资源关系，每个进程都在等待下个进程所占用的资源

        - 【readers-writers问题】有哪些解决该问题的方法? # read-preferring, write-preferring, CFS. 这三个都要trade-off，因为读优先会导致写饥饿，写优先会导致读饥饿，CFS则可能并不适用于常见的“读多写少”场景
        - Thundering-herd problem
        - 【脑裂问题(split-brain)】
    #              脑裂问题基本上由网络故障导致，除此之外还有节点故障、时钟漂移、网络分区等情况。总之，都是无法通信的情况。核心思路就是把无法通信的机器标记为不可用。通过监测心跳、多数投票、领导者选举等机制，系统可以识别出失效的节点，并将其标记为不可用，以避免脑裂问题的发生。
    #
    #              我们用生活中的例子来类比，split-brain就是在分布式场景中发生的，就像是一群人要做个事，这些人需要能够互相沟通，来保证想法一致。所以有哪几种情况会出问题？无非是联系不上或者这些人中的某些人本身挂了。
    #
    #              (网络故障, 节点故障, 时钟漂移, 网络分区, 软件错误)
    #
    #              联系不上就是网络问题嘛，本身挂了就是节点故障。联系不上还有一种可能就是两边的时间不同步，说好了10点打电话，但是时间不同步肯定联系不上（这个就是时钟漂移）。
    #
    #              至于“软件错误”，指的是类似zk或者etcd这种分布式服务本身出问题了（比如说failover之类的容错机制出问题了（比如说太敏感了，一点小问题还以为是某个node出问题了））。其实正常来说，不太可能出问题，所以不需要考虑。

    # [lengocgiang/trivial_concurrency_exercises: I wrote this repo, to help you more understand for go-routine and go-channel. With three exercises from http://whipperstacker.com/2015/10/05/3-trivial-concurrency-exercises-for-the-confused-newbie-gopher/.](https://github.com/lengocgiang/trivial_concurrency_exercises)

    # [mtyurt/go-concurrency-exercise: Solutions to Golang concurrency exercises](https://github.com/mtyurt/go-concurrency-exercise)




    - topic: zzz # FIXME
      isX: true
      qs:
        - 怎么使用 golang 实现 java 的迭代器？想要支持所有 golang 类型，怎么封装

        - 怎么实现 gin 的路由算法
        - 怎么实现 gin 的中间件

        - 怎么自己实现阻塞读并且并发安全的 map
        - slice of map 怎么去重（里面的 map）？核心是什么？ # 还是很简单，直接把这个slice of map

        - 用 golang 手写 redis 的 pub/sub？至少给出伪代码
        - 写一个 golang 爬虫，自动伸缩，自适应流控，也就是说在对方限流时就减少并发数，没有限制时使用最大并发数？类似 TCP 的滑动窗口

        - 手写怎么用 chan 实现定时器 # [GO语言-channel-定时器定时器 channel是GO语言中非常核心的机制，定时器的内部很多就是channel实现 - 掘金](https://juejin.cn/post/7113553728954695693) time.NewTicker + for死循环 就ok了

        - golang 用 chan 实现排序（快排和归并排序 # [Golang 实现多协程快速排序 - 知乎](https://zhuanlan.zhihu.com/p/47383158)

        - 数组中有N+2个数，其中，N个数出现了偶数次，2个数出现了奇数次（这两个数不相等），请用O(1)的空间复杂度，找出这两个数。 # [LeetCode-- 数组中找到出现奇数次的2个数字 - 知乎](https://zhuanlan.zhihu.com/p/373580530) 两种方法，hash法或者xor法，推荐hash法。也就是直接把array转hashmap，然后

        - 数组中出现次数超过一半的数字 # 很简单，直接array转hashmap（模拟PHP的array_count_values()），然后再遍历这个map判断一下就ok了

        - 链表相加 [leetcode 链表相加 - 知乎](https://zhuanlan.zhihu.com/p/480342701)

        #
        - 使用 chan 下载远程文件，在控制台打印进度条 # [单行进度条](

        - 怎么实现单文件分块并发下载？ # [Go Playground - The Go Programming Language](https://go.dev/play/p/sVTuMqf6ZHv)

        - 手写 retry 方法（retry 里面的 func）？核心是什么？ # https://go.dev/play/p/UTRnHf9zAjW # 设计Retry函数，三个参数嘛 max_attempts, func, sleep_time.
        - 并发去 ping n 个网站，最多重试 3 次，怎么提升该场景的性能

        - 翻转字符串 # 字符串先转 `[]rune()` 才能修改，否则是只读类型。其次以字符串⻓度的 1/2 为轴，前后赋值(str[l-1-i])。

        - 翻转slice。能否用golang generics实现一个兼容string和int的SliceReverse的函数？ #

        - 判断两个给定的字符串排序后是否一致 # O(n). 给定两个字符串，判断其中一个字符串的字符重新排列后，是否能变为另一个字符串。只需要一次循环遍历 s1 中的字符在 s2 是否都存在(strings.Contains())即可

        - 高并发下的锁与map读写问题
        - 为 sync.WaitGroup 中Wait函数支持 WaitTimeout 功能.
        - 手撕代码生产者消费者模型
        - 写个递归实现无限级分类
        - 写一个验证标准 ipv4 的算法
        - 怎么用最快速度请求某个接口？
        - 要求我们在最短时间内抓取到某个新闻网站中的 1000 篇新闻详情，怎么实现？

        - 多线程轮流打印问题：可以大概分为两类，固定值打印和非固定值打印
        - 实现两个协程，其中一个产生随机数并写入到 chan 中，另一个从 chan 中读取，并打印出来，最终输出 5 个随机数
        - "***固定值交替打印：假设有 4 个协程，分别编号为 1/2/3/4，每秒钟会有一个协程打印出自己的编号，现在要求输出编号总是按照 1/2/3/4 这样的顺序打印，共打印 100 次。怎么优化？***" # 核心是构造一个`current chan`和`next chan`的方法

        - 写一个函数，算出两个文件的相对路径。a相对于b的路径计算方法？计算两个文件的相对路径


    #    - topic: 《多线程轮流打印问题》
    #      isX: true
    #
    #
    #    - topic: 实现阻塞读的并发安全Map
    #      url:
    #
    #    - topic: 在⼀个⾼并发的web服务器中，要限制IP的频繁访问。现模拟100个IP同时并发访问服务器，每个IP要重复访问1000次。每个IP三分钟之内只能访问⼀次。
    #      url: https://github.com/lifei6671/interview-go/blob/master/question/q011.md
    #
    #    - topic: 手写`最终成功模式`，使用场景是，我们需要在 10 个文件中成功读取 7 个，怎么加速呢？最好的方式是，获取文件失败，就获取下一个，只要获取了 7 个，就直接返回
    #      url: https://www.flysnow.org/2020/08/04/golang-goroutine-channel
    #
    #    - topic: 手写`胜者为王模式`多个协程对同一份资源进行处理，只要有一个协程完成了，我们就直接返回结果”。从而减少耗时，提高成功率。比如，一张图片存在 5 台 OSS 上，怎么加速获取呢？我们就可以开 5 个协程，同时获取这张图片，谁获取到了就使用谁的
    #      url:
    #
    #    - topic: retry ping 网站
    #      url: https://go.dev/play/p/ZUnQAvquU67
    #
    #    - topic: 怎么能在一个50gb的文件里用4gb的内存计算一个单词的出现次数? （利用多线程在 Go 中更快地读取大文件）
    #      url: https://zhuanlan.zhihu.com/p/96898995
    #
    #    - topic: 在线聊天服务
    #      url:
    #    - topic: RingBuffer实现
    #      url: https://go.dev/play/p/uHZDuVYlET0



    # TODO DHT 不可能三角梳理一下（BSE Balance数据平衡性、Scale 可拓展性、Efficiency 查询效率）
#    - topic: DHT
#      des: "***几种经典DHT算法：为什么需要实现不同的DHT算法? 比较 (一致性哈希 Consistent Hashing(hash ring), CARP(Cache Array Route Protocol), chord(Local Resilient Hashing), HRW, KAD 算法(kademila，是对于 chord 算法的优化), Rendezvous) 这几种经典的 DHT 算法? DHT有多种实现算法，最关键的问题在于如何定义数据分割策略和节点快速查询***"
#      table:
#        - name: KAD
#          哈希空间划分方式: 二叉树结构
#          数据复制策略: 路由表存储
#          数据查找时的通信开销: 路由表查找 (O(log N))
#          node和key的映射方式: 二叉树结构
#          怎么解决数据倾斜问题: 虚拟节点
#
#        - name: Consistent Hashing
#          哈希空间划分方式: 环形结构
#          数据复制策略: 顺时针方向下一个节点
#          数据查找时的通信开销: 顺时针查找 (O(N)~O(log N))
#          node和key的映射方式: 环形hash空间映射
#          怎么解决数据倾斜问题: 虚拟节点
#
#        - name: Local Resilient Hashing
#          哈希空间划分方式: 区域划分
#          数据复制策略: 邻近node间复制
#          数据查找时的通信开销: 本地区域内查找
#          node和key的映射方式: 区域划分和hash函数映射
#          怎么解决数据倾斜问题: 邻近node间复制和局部保留
#
#        - name: HRW (Highest Random Weight)
#          url: https://github.com/tysonmote/rendezvous
#          哈希空间划分方式: 无拓扑结构
#          数据复制策略: 较少的数据复制
#          数据查找时的通信开销: 全网计算权重 (O(N))
#          node和key的映射方式: 随机权重映射
#          怎么解决数据倾斜问题: 权重随机性 (无主动平衡)
#
#
#        - name: chord
#          url: https://github.com/arriqaaq/chord
#          哈希空间划分方式: 环形ID空间
#          数据复制策略: 后继节点复制
#          数据查找时的通信开销: Finger表跳转 (O(log N))
#          node和key的映射方式: 同空间哈希映射
#          怎么解决数据倾斜问题: 虚拟节点 (可选)
#      qs:
#        # [韩信大招：一致性哈希](https://mp.weixin.qq.com/s?__biz=MzAxMTA4Njc0OQ==&mid=2651445555&idx=1&sn=e1b66373d68d25d16b0a8481380bc536)
#        - DHT是啥? # DHT的本质就是，每个节点只维护一部分路由，每个节点只存储一部分数据，从而实现整个网络中的寻址和存储
#        - 为啥使用哈希环（一致性哈希算法）就能随意增删节点，而不需要担心数据重新分配的问题？手写一个哈希环，以及增删节点操作
#        - "***核心需求：设计一致性哈希算法，有哪些核心需求? 平单分负平(bmsls) (balance, monotonicity, spread, load, smooth)***"
#        # - `平衡性balance`，哈希结果尽可能的平均分散到各个节点上
#        # - `单调性monotonicity`，一致性哈希的目标，是节点变更，不会改变网络的映射关系
#        # - `分散性spread`，同一份数据，存储到不同的节点上，换言之就是系统冗余。一致性哈希致力于降低系统冗度
#
#        - 请比较 普通哈希（哈希取模策略）、一致性哈希 和 哈希槽 的优缺点？ # [分布式系统基石之一（一致性 hash 算法）](https://mp.weixin.qq.com/s?__biz=MzkyOTU5MTc3NQ==&mid=2247499003&idx=1&sn=1019e972eb970b2872f46af39f97a06b)
#
#        - 我们无法保证节点均匀分配在哈希环上，从而导致数据倾斜问题。怎么用 虚拟节点 解决节点太少导致的 数据倾斜 问题（以保证其balance）？
#        - How to implement DFA using trie tree in golang?
#        - 能否聊聊`KAD 算法`？
#        #- 用二进制 (32/64/128) 表示一个节点的 id，两节点的 id 异或运算得到节点间的距离，可以方便进行网络划分
#        #- 每个节点保持的路由信息更丰富，同样是将整个网络按照划分成 `log2N` 份，在 chord 中，是保持 log2N 个路由节点，但在 kad 里面，是保存了 `log2N` 个队列。使得节点查询更迅速，命中概率更高
#        - CARP hash # [CARP hash](


    - topic: BF
      picDir: algo/bloomfilter
      table:
        - name: BF  (Bloom filters)
          url: https://github.com/bits-and-blooms/bloom
          技术组合: BF = bitmap + k hash
          类比: 【停车场登记处】停车场入口有3名登记员（哈希函数），每辆车（元素）入场时，登记员各自指定一个停车区（bit位置）标记为占用（bit=1）。离场时无法清除标记，后续车辆可能因重复标记被误认为在场（假阳性）
          写入流程: |
            1. 车辆驶入，3名登记员独立计算其停车区（hash_i(x) % m）
            2. 所有对应车位标记为占用（bit=1）
          查询流程: |
            1. 登记员计算目标车辆的3个车位
            2. 若所有车位均被占用 → 回答“可能存在”
            3. 若任一车位空闲 → 回答“一定不存在”
          空间效率: 5
          是否支持删除: false
          是否支持实时更新: false
          查询性能: O(k)
          适用场景: 静态黑名单、URL去重

        - name: 布谷鸟过滤器 (Cuckoo Filter)
          url: https://github.com/seiflotfy/cuckoofilter
          技术组合: two hash functions + fingerprint storage + 4-way bucket
          类比: 【智能立体车库】车库分4列货架（4-way bucket），每辆车分配两个专属车位（双哈希函数），并留下指纹（fingerprint）。若车位被占，系统将原车挪到备用车位（踢出机制）。离场时可精准清除指纹
          写入流程: |
            1. 车辆驶入，计算两个专属车位（h1(x), h2(x)）
            2. 若车位空闲 → 存入指纹
            3. 若车位被占 → 踢出原车指纹至其备用位（j = i ⊕ hash(指纹)）
          查询流程: |
            1. 计算目标车辆的两个车位位置
            2. 检查任一车位是否匹配指纹
            3. 匹配成功 → 回答“存在”
          空间效率: 4
          是否支持删除: true
          是否支持实时更新: true
          查询性能: O(1)平均
          适用场景: 完全优于BF

        - name: CBF  (Counting Bloom filters)
          技术组合: CBF = counter + k hash
          类比: 【带计数器的充电桩】停车场每个车位配计数器（counter数组）。车辆入场时，登记员指定的车位计数器+1；离场时-1。计数器归零时车位释放
          写入流程: |
            1. 车辆驶入，3名登记员计算其车位
            2. 所有对应车位计数器+1
          查询流程: |
            1. 登记员计算目标车辆的3个车位
            2. 若所有计数器 > 0 → 回答“可能存在”
          空间效率: 3
          是否支持删除: true
          是否支持实时更新: true
          查询性能: O(k)
          适用场景: 流数据计数、动态删除场景

        - name: dlCBF
          技术组合: d-left hash + fingerprint + counter
          类比: 【分流式计数收费站】收费站分d条通道（d-left hash），每辆车按指纹分流到某条通道的计数窗口。多条通道并行处理，避免拥堵
          写入流程: |
            1. 车辆按指纹哈希值分流至d条通道之一
            2. 在选定通道的计数器中+1
          查询流程: |
            1. 计算目标车辆的分流通道（d选1）
            2. 检查该通道计数器 > 0 → 回答“可能存在”
          空间效率: 4
          是否支持删除: true
          是否支持实时更新: true
          查询性能: O(d)
          适用场景: 高并发计数删除（网络优化）

        - name: Bloomier Filter
          技术组合: perfect hash + XOR
          类比: 【精准钥匙柜】钥匙柜使用完美哈希（perfect hash）分配专属格子，每个格子存储钥匙的XOR编码值。查询时直接定位格子解码，100%准确（无冲突）
          写入流程: |
            1. 用完美哈希为钥匙分配专属格子
            2. 将钥匙信息编码为XOR值存入
          查询流程: |
            1. 用相同哈希定位钥匙格子
            2. 解码XOR值 → 匹配则回答“存在”
          空间效率: 2
          是否支持删除: false
          是否支持实时更新: false
          查询性能: O(1)
          适用场景: 键值存储（路由表、配置）


        - name: Layered BF
          技术组合: multi-layer BF cascade
          类比: 【多层安检通道】安检分3层（多层BF），每层独立检查行李。首层粗查（高误报），可疑行李进入二层精查，最终层几乎零误报
          写入流程: |
            1. 行李通过首层安检（第1个BF）
            2. 可疑行李进入第二层（第2个BF）
            3. 高危行李进入最终层（第3个BF）
          查询流程: |
            1. 行李需逐层通过安检
            2. 任一BF判定不存在 → 立即拦截
            3. 通过所有层 → 回答“存在”
          空间效率: 2
          是否支持删除: false
          是否支持实时更新: false
          查询性能: O(k×L)
          适用场景: 高精度低误报过滤（垃圾邮件）

        - name: 商过滤器QF (Quotient Filter)
          技术组合: quotient-remainder + linear probing
          类比: 【分区编号车库】车库按商值（quotient）分区分组，每组内用余数（remainder）编号停车。车辆查询时先找分区，再线性扫描余数（linear probing）
          写入流程: |
            1. 按商值定位车辆所属分区
            2. 在分区内线性扫描空位 → 存入余数编号
          查询流程: |
            1. 用商值定位目标分区
            2. 在分区内线性扫描余数编号
            3. 找到匹配余数 → 回答“存在”
          空间效率: 4
          是否支持删除: true
          是否支持实时更新: true
          查询性能: O(1)平均
          适用场景: 高频删查数据库索引

        - name: Cache Filtering
          技术组合: BF/CBF + cache system
          类比: 【缓存哨兵系统】停车场入口设哨兵（BF/CBF），先检查车辆是否在“可疑名单”（缓存穿透防护）。若不在名单，放行进入车库（缓存系统）；否则拦截
          写入流程: |
            1. 新车辆到达哨兵系统
            2. 若不在可疑名单 → 放行至缓存车库
          查询流程: |
            1. 哨兵用BF/CBF检查车辆
            2. 若在可疑名单 → 直接拦截
            3. 若不在名单 → 放行至缓存系统
          空间效率: 4
          是否支持删除: true
          是否支持实时更新: true
          查询性能: O(k)
          适用场景: 缓存穿透防护
      qs:
        - 【BF使用场景】查重或者判断是否存在这两个使用场景，那么具体有哪些使用场景呢？ # 归根到底，是查重或者判断是否存在这两个使用场景。比如判断用户是否存在、是否重复购买、防止缓存穿透、敏感词过滤、爬虫URL去重

        - "***为啥 BF 存在 FPR和很难删除的问题?***" # 优点：读写操作都是O(n)，并且因为存储bin数据，所以占用空间很小（bitmap）。最大的缺点当然是FPR（hash导致的），并且很难删除（因为存的是 hash 值，如果两个值的 hash 值相同 (hash 冲突)，无法只删其中一个值（如果使用计数方式删除，可能会存在`计数回绕问题`））

        - "***实现BF（为啥我们说“BF就是bitmap+hash嘛”）：都是用来判断某个元素是否属于集合，为啥不直接用hashmap呢？为啥用BF而不是直接用bitmap呢？）***"
        # 因为hashmap虽然能保证准确率，但是内存占用太大。bitmap香蕉更好，但是使用bitmap时，当样本分布极度不均匀的时候，会造成很大空间上的浪费。另外，当元素不是整型（比如 URL）的时候，BitSet 就不适用了。这两个问题，我们都可以用hash解决
        # [Bloomfilter (using bits-and-blooms/bitset)](
        #  bitmap的冲突率要比 BF 要高，有一个经典需求，“判断某个元素是否属于集合”，hashmap和bitmap都可以解决，但是hashmap内存占用太多，所以用bitmap，但是 bitmap 也有其缺点，比如：
        #  - 当样本分布极度不均匀的时候，会造成很大空间上的浪费
        #  - 当元素不是整型（比如 URL）的时候，BitSet 就不适用了
        #  bitmap的这两个问题都可以通过hash解决
        #  *核心流程是一个元素映射到布隆过滤器时，元素会被每个 hash 函数进行 hash 映射，每次映射出 bit 位数组下标后就会将其标记，所以一个元素可能会占用多个下标，多个元素因为哈希碰撞可能会共用同个下标。*

        - 【BF查询流程】怎么判断布隆过滤器中是否有某个元素？ # 布隆过滤器还拥有 k 个哈希函数，当一个元素加入布隆过滤器时，会使用 k 个哈希函数对其进行 k 次计算，得到 k 个哈希值，并且根据得到的哈希值，在数组中把对应下标的值，置为 1。判断某个数是否在布隆过滤器中，就对该元素进行 k 次哈希计算，得到的值在数组中判断每个元素是否都为 1，如果每个元素都为 1，就说明这个值在布隆过滤器中
        - 【BF写入流程】

        - 【细节问题】初始 bitset 大小设置问题？哈希算法选择问题（推荐 murmur3 算法）？设置在缓存之前好还是缓存之后好？如何保证布隆过滤器中数据与数据库数据一致性，如何防止漏查？新增数据如何同步到布隆过滤器中？

        - 【BF计数回绕问题】

        - "***【CBF】为啥相较bloomfilter，“CBF支持 动态删除元素，更快的查找性能，空间开销更小”，是怎么实现的?***"
        #  动态删除元素：
        #  Bloom Filter：经典Bloom Filter不支持元素的删除操作，因为一旦某个位被设置为1，就无法恢复为0而不影响到其他元素的检查结果。为了支持删除，需要使用Counting Bloom Filter，但这也带来了额外的空间开销。
        #  Cuckoo Filter：Cuckoo Filter通过使用两个哈希函数为每个元素计算两个可能的位置（对偶位置），当一个位置被占用时，可以尝试将元素移动到另一个位置，从而支持元素的删除。这种设计允许在不破坏其他元素检查结果的情况下删除元素。
        #  更快的查找性能：
        #  Bloom Filter：查找性能较弱，因为需要检查k个哈希函数映射的位置是否都是1，随着元素数量的增加，误判率也会增加。
        #  Cuckoo Filter：Cuckoo Filter的查找性能较高，因为它只存储指纹信息，并且可以直接计算出元素的两个可能位置，因此查找时只需要检查这两个位置即可。
        #  空间开销更小：
        #  Bloom Filter：空间利用效率较低，因为它需要为每个元素设置k个位，而且随着元素数量的增加，误判率增加，可能需要更多的空间来维持较低的误判率。
        #  Cuckoo Filter：Cuckoo Filter的空间利用率较高，因为它保证了一个比特只被一个元素映射，允许删除操作，且理论上在false positive rate很低的时候，Cuckoo Filter可以做到更节约空间。此外，Cuckoo Filter的设计允许更灵活的空间调整，如一致性Cuckoo Filter（CCF）实现了细粒度的容量调整，进一步节省了空间开销。

        - 原理就是把bitmap的bit 升级为计数器 (Counter). 添加元素，就给对应的 Counter 分别 +1; 删除元素，就给对应的 Counter 分别减一。用多出几倍存储空间的代价，来实现删除功能。


    # "***wrr 也可以看作是某种 PR 调度算法 是吧，那实现 wrr 算法？几种实现方法 random, RBT, LVS, nginx。能否分别实现这几种***" # https://go.dev/play/p/7I7Ya3281fW?v=
    - topic: PR算法//调度算法 基本认知
      des: 有哪些常见的PR算法?
      table:
        - name: CFS (Completely Fair Scheduler)
          类型: 进程调度
          核心目标: 公平性最大化
          决策依据: 虚拟运行时间
          优点: 低延迟公平
          缺点: 实时性弱
          时间复杂度: O(log n) (红黑树)
          典型场景: Linux默认调度器

        - name: WFS (Weighted Fair Scheduler)
          类型: 进程调度
          核心目标: 加权公平分配
          决策依据: 任务权重
          优点: 资源分配可控
          缺点: 权重配置复杂
          时间复杂度: O(log n) (加权红黑树)
          典型场景: 云资源调度

        - name: CBWFS (Constant Bandwidth Server)
          类型: IO调度
          核心目标: 带宽保证
          决策依据: 带宽预留+时间片
          优点: 满足QoS要求
          缺点: 静态配置不灵活
          时间复杂度: O(1) (时间片轮转)
          典型场景: 实时音视频流

        - name: MLFQ (Multilevel Feedback Queue)
          类型: 进程调度
          核心目标: 响应时间优化
          决策依据: 动态优先级调整
          优点: 交互任务响应快
          缺点: 参数敏感
          时间复杂度: O(1) (多优先级队列)
          典型场景: Unix/Windows桌面

        - name: SJPF (Shortest Job Process First)
          类型: 进程调度
          核心目标: 周转时间最小化
          决策依据: 预估执行时间
          优点: 理论最优周转
          缺点: 长任务饥饿
          时间复杂度: O(log n) (最小堆)
          典型场景: 批处理系统

        - name: LLQ (Low Latency Queuing)
          类型: 网络调度
          核心目标: 低延迟保证
          决策依据: 优先级队列
          优点: 严格延迟控制
          缺点: 可能饿死低优先级
          时间复杂度: O(1) (严格优先级队列)
          典型场景: VoIP/实时游戏

        - name: RTP (Real-Time Protocol)
          类型: 实时调度
          核心目标: 实时性保证
          决策依据: 任务关键级
          优点: 可预测性高
          缺点: 静态优先级
          时间复杂度: O(1) (固定优先级队列)
          典型场景: 工业控制系统

        - name: Deadline
          类型: IO调度
          核心目标: 请求延迟限制
          决策依据: 请求截止时间
          优点: 避免请求饿死
          缺点: 不适合SSD
          时间复杂度: O(log n) (红黑树)
          典型场景: 数据库IO

        - name: NOOP (No Operation)
          类型: IO调度
          核心目标: 最小CPU开销
          决策依据: 无排序直接提交
          优点: 零计算开销
          缺点: 随机IO性能差
          时间复杂度: O(1) (FIFO队列)
          典型场景: SSD设备

        - name: LNP (Least Number of Processes)
          类型: 负载均衡
          核心目标: 资源利用率最大化
          决策依据: 当前进程数
          优点: 实现简单
          缺点: 忽略任务差异
          时间复杂度: O(1) (计数器+选择器)
          典型场景: 集群调度

        - name: HRRN (Highest Response Ratio Next)
          类型: 进程调度
          核心目标: 平衡等待与执行
          决策依据: 响应比=(等待+执行)/执行
          优点: 无饥饿问题
          缺点: 需要预估时间
          时间复杂度: O(n) (线性扫描)
          典型场景: 批处理系统

        - name: EDF (Earliest Deadline First)
          类型: 实时调度
          核心目标: 截止期限保证
          决策依据: 绝对截止时间
          优点: 理论最优实时性
          缺点: 过载崩溃风险
          时间复杂度: O(log n) (最小堆)
          典型场景: 航空控制系统

        - name: HPF (Highest Priority First)
          类型: 实时调度
          核心目标: 优先级保障
          决策依据: 静态优先级
          优点: 实现简单
          缺点: 优先级反转风险
          时间复杂度: O(1) (优先级队列)
          典型场景: 嵌入式系统

        - name: FIFO (First-In-First-Out)
          类型: 通用调度
          核心目标: 顺序公平
          决策依据: 到达时间
          优点: 实现极简
          缺点: 护航效应
          时间复杂度: O(1) (队列)
          典型场景: 管道通信

        - name: RR (Round Robin)
          类型: 进程调度
          核心目标: 时间片轮转公平
          决策依据: 固定时间片分配
          优点: 响应时间可预测
          缺点: 上下文切换开销大
          时间复杂度: O(1) (环形队列)
          典型场景: 网络包调度

        # 以下为缓存置换算法部分
        - name: LRU (Least Recently Used)
          类型: 缓存置换
          核心目标: 最小化缓存缺失率
          决策依据: 最近访问时间
          优点: 实现简单，适应局部性原理
          缺点: 历史敏感，偶发访问易被误删
          时间复杂度: O(1) (哈希表+双向链表)
          典型场景: CPU缓存/数据库缓存

        - name: TLRU (Time-aware Least Recently Used)
          类型: 缓存置换
          核心目标: 时间感知的缓存优化
          决策依据: 访问时间+过期时间
          优点: 适合时效性数据
          缺点: 需维护时间戳
          时间复杂度: O(1) (哈希表+时间堆)
          典型场景: 内容分发网络

        - name: PLRU (Pseudo-LRU)
          类型: 缓存置换
          核心目标: 硬件友好的LRU近似
          决策依据: 状态位标记访问
          优点: 位操作高效
          缺点: 非精确LRU
          时间复杂度: O(1) (状态位向量)
          典型场景: CPU高速缓存

        - name: SLRU (Segmented LRU)
          类型: 缓存置换
          核心目标: 保护频繁访问项
          决策依据: 分段队列管理
          优点: 减少热点数据误删
          缺点: 多队列开销
          时间复杂度: O(1) (双队列+哈希表)
          典型场景: 数据库缓存

        - name: LFRU (Least Frequent Recently Used)
          类型: 缓存置换
          核心目标: 平衡频率与时效
          决策依据: 访问频率+新鲜度
          优点: 兼顾长短期访问模式
          缺点: 参数调整复杂
          时间复杂度: O(1) (哈希表+频率计数器)
          典型场景: Web代理缓存

        - name: LFUDA (LFU with Dynamic Aging)
          类型: 缓存置换
          核心目标: 解决新项目歧视
          决策依据: 频率计数+动态老化
          优点: 公平对待新老项目
          缺点: 额外计数开销
          时间复杂度: O(log n) (最小堆+哈希表)
          典型场景: 文件系统缓存

        - name: LIRS (Low Inter-Reference Recency Set)
          类型: 缓存置换
          核心目标: 减少低价值缓存
          决策依据: 访问间隔预测
          优点: 高效过滤一次性访问
          缺点: 实现复杂度高
          时间复杂度: O(1) (双栈+哈希表)
          典型场景: 大规模缓存系统

        - name: ARC (Adaptive Replacement Cache)
          类型: 缓存置换
          核心目标: 自适应LRU/LFU平衡
          决策依据: 双队列动态调整
          优点: 抗扫描流能力强
          缺点: 元数据开销大
          时间复杂度: O(1) (4个链表+自适应逻辑)
          典型场景: ZFS/Oracle DB

        - name: MRU (Most Recently Used)
          类型: 缓存置换
          核心目标: 保留旧数据
          决策依据: 最近访问项优先淘汰
          优点: 适合顺序扫描场景
          缺点: 可能保留无用数据
          时间复杂度: O(1) (栈+哈希表)
          典型场景: 数据库索引扫描

        - name: CAR (Clock with Adaptive Replacement)
          类型: 缓存置换
          核心目标: ARC的简化实现
          决策依据: 时钟指针+适应函数
          优点: 比ARC更轻量
          缺点: 调优难度高
          时间复杂度: O(1) (时钟算法+自适应逻辑)
          典型场景: 嵌入式系统缓存

        - name: LIFO (Last In First Out)
          类型: 缓存置换
          核心目标: 利用时间局部性
          决策依据: 最后进入优先淘汰
          优点: 实现简单
          缺点: 可能淘汰热点
          时间复杂度: O(1) (栈)
          典型场景: 栈式访问场景

        - name: FILO (First In Last Out)
          类型: 缓存置换
          核心目标: 同LIFO
          决策依据: 同LIFO
          优点: 同LIFO
          缺点: 同LIFO
          时间复杂度: O(1) (栈)
          典型场景: 同LIFO

        - name: LRU-K
          类型: 缓存置换
          核心目标: 改进LRU精度
          决策依据: 第K次访问时间
          优点: 抗扫描流
          缺点: 存储开销大
          时间复杂度: O(log k) (历史访问记录)
          典型场景: 大数据系统

        - name: 2Q (Two Queues)
          类型: 缓存置换
          核心目标: LRU改进
          决策依据: 双队列(Am/FIFO)
          优点: 简单高效
          缺点: 队列大小敏感
          时间复杂度: O(1) (两个队列+哈希表)
          典型场景: 文件缓存
      qs:
        - PR算法 是啥? 是不是调度算法其实就那几种？比如CFS, WFS, CBWFS, FIFO, RR, MLFQ, SJPF, LLQ, RTP之类的，因为我看到不管是linux kernel的进程调度算法、io调度算法，还是MQ（包括kafka）的调度算法其实都差不多，我的理解对吗？ # PRA的核心思想是根据数据项的访问历史来预测未来的行为，从而选择最不可能被再次使用的数据项进行替换。可以看到很多置换算法和scheduler算法是类似的，是因为两者都涉及到在有限资源的环境下做出决策，以优化系统的整体性能。然而，它们在目标、应用场景和实现机制上有所不同。置换算法主要用于操作系统中的虚拟内存管理。它们的目标是在物理内存有限的情况下，高效地管理内存页的置换，以减少页面错误（page faults）并提高内存的使用效率。

        # [面试中 LRU / LFU 的青铜与王者](https://halfrost.com/lru_lfu_interview/)
        - "有哪些scheduler算法：缓存淘汰算法不仅仅只有 LRU / LFU 这两种，还有哪些? (FIFO, CFS, WFS, CBWFS, RR, SJF, LLQ, RTP, Deadline, NOOP, MFQ, LNP, HRRN, EDF, HPF)"
        #- fifo就是来啥工作做啥工作，做完之后就做下一个
        #- SJF (Shortest Job First) sjf就是先易后难，也是很常见的工作模式（事实上我一般就是先易后难，但是问题也显而易见）
        #- cfs就是给给所有工作分配相同的时间（时间一到，时钟中断tick，直接切换任务。注意是时间导向，而非结果导向）
        #- wfs/wfq (weighted fair scheduler/queue)则是给所有工作加权，按照重要程度分配时间。cbwfs(Constant Bandwidth...)类似。
        #- MFQ (Multilevel Feedback Queue)

        - MLFQ # [冠以图灵奖之名的调度算法：MLFQ 多级反馈队列！_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV18T1FYMEiJ/)
        - LFU-DA = LFU Dynamic Aging
        - Approximated LRU (second chance)
        - 几种常见的variants，比如 LRU-DA, Approximated LRU, LRU-K, 2Q(LRU-2), ARC 的区别？我们可以把Page Replace类比成写笔记，比如说，我喜欢写笔记，但是我要求我的文档不能超过1w行，所以需要删除部分内容。如果说LRU是回顾整个笔记全部内容，找到最久未动的部分，删除掉，准确但开销较大。其他几种variants算法分别可以类比成什么呢？
        #主要区别在于它们如何处理页面访问历史和如何做出置换决策。LRU-DA和近似LRU减少存储需求，LRU-K和2Q简化决策过程，而ARC则自适应调整以提高效率。
        # LRU-DA 限制访问记录长度，减少空间需求，但可能不如完整LRU准确。
        # Approximated LRU 简化版LRU，使用时间戳或计数器近似模拟，减少实现复杂性。
        # LRU-K 维护一个短期访问列表，快速选择置换候选页面，减少置换频率。
        # 2Q (Two-Queue) 分为两个队列处理最近和不经常访问的页面，简化决策过程。
        # ARC 自适应算法，结合时钟算法和LRU-K，动态调整参数以优化性能。
        #
        #- LRU-DA：只回顾最近编辑的一部分笔记行来决定删除哪些，这减少了您需要检查的笔记数量，空间需求减少但可能不够准确。
        #- Approximated LRU：使用笔记旁边的时间戳或编辑次数来近似判断哪些笔记行不常用，简化了判断过程。
        #- LRU-K：维护一个短期编辑的笔记行列表，当需要删除笔记行时，优先考虑这个列表中的行，减少您需要考虑的笔记行数量
        #- 2Q：将笔记行分为“最近编辑”和“不常编辑”两组，当需要删除笔记行时，先查看“不常编辑”的组，简化了决策过程
        #- ARC：结合多种策略，比如最近编辑频率和编辑时间，动态决定哪些笔记行可以删除，自适应调整以优化文档内容。

        - "***手写 LRU 算法：为啥用 双向链表+HashMap 实现？请讲讲 LRU 算法的实现原理？不考虑线程安全/考虑线程安全两种场景，要求 SET/GET 操作 O(1) 时间复杂度，可以使用所有标准库，解释下你写的算法（这个也太高频了吧，10 份面试题里 8 份有这个）***" # https://go.dev/play/p/G5HXveY6I_v # 使用 HashMap 作为数据存储，把DLL本身的数据作为 HashMap 的 key 使用（既能排序又是基于hash的ds），这样可以直接获取 value。DLL要遍历才能查到某个 key 对应的 value，使用HashMap O(1)。1、判断是否命中缓存，且有序 (队头队尾)。2、list 需要遍历才能判断是否命中缓存，很慢。3、dict 和 set 可以很快判断是否命中，但是无序。

        - 直接使用双向链表本身存数据不就行了？为啥需要使用 HashMap？ # 需要 MoveToFront() # 总结：双向链表要遍历才能查到某个 key 对应的 value，这点我没想到。相当于冗余了 key 的数据，用来便于直接查找。其实 list 搭配 HashMap 使用是很常见的一个套路了，没啥好说的。

        - 能否给我讲讲PRA的second chance？是不是redis的approximeted LRU就是基于second chance实现的？




    - topic: 【技术选型】hash func
      des: "***请（从 单向性，抗冲突，映射分布均匀性, 差分分布均匀性 这几个方面）来比较 hash algo (murmurhash, CityHash, xxHash, FNV, SpookyHash, maphash, crc32, crc64)***"
      table:
        - name: MurmurHash # MurmurHash = mu(multiply) + r(rotate). murmurhash是现在比较常用的一种hash算法，不少开源的布隆过滤器实现都是采用了murmurhash算法来实现。murmurhash算法的名字来源于它的两个重要操作MU（multiply）以及R（Rotate），它在设计上并不是为了密码学上增加单向加密的难度，所以它不适用于安全领域。如果是向做一个快速的hash且希望hash碰撞满足预设指标的话，murmurhash是一个不错的hash算法。
          url: https://github.com/spaolacci/murmur3
          单向性: 1（纯线性运算可逆向）
          性能: 4（短数据极快）
          抗碰撞性: 4（SMHasher测试优异）
          映射分布均匀性: 5（空间分布完美均匀）
          差分分布均匀性: 4（输入变化敏感）
          典型应用场景: 布隆过滤器、哈希表

        - name: CityHash
          url: https://github.com/google/cityHash
          doc: Google开发的长文本优化哈希
          单向性: 1（无密码学混淆）
          性能: 5（长数据最优）
          抗碰撞性: 4（谷歌标准碰撞率）
          映射分布均匀性: 5（长文本分布均匀）
          差分分布均匀性: 4（差分变化稳定）
          典型应用场景: 大文件哈希处理

        - name: xxHash
          url: https://github.com/Cyan4973/xxHash
          单向性: 1（代数可求解）
          性能: 5（RAM速度瓶颈）
          抗碰撞性: 4（SMHasher零碰撞）
          映射分布均匀性: 5（完美离散分布）
          差分分布均匀性: 5（雪崩效应极强）
          典型应用场景: 实时数据校验

        - name: aHash
          url: https://github.com/tkaitchuck/aHash
          单向性: 1（依赖硬件特性）
          性能: 4（依赖硬件加速）
          抗碰撞性: 4（防DoS攻击设计）
          映射分布均匀性: 4（硬件优化分布）
          差分分布均匀性: 4（AES级雪崩）
          典型应用场景: 防DoS攻击哈希表

        - name: simhash
          url: https://github.com/yanyiwu/simhash
          doc: 文本相似度计算专用局部敏感哈希
          单向性: 1（设计允许碰撞）
          性能: 2（计算复杂）
          抗碰撞性: 1（设计允许碰撞）
          映射分布均匀性: 1（相似输入聚集）
          差分分布均匀性: 5（相似性保留）
          典型应用场景: 文本相似度计算、网页去重

        - name: phash
          url: https://github.com/azr/phash
          doc: 图像感知哈希
          单向性: 1（无密码学混淆）
          性能: 2（图像处理耗时）
          抗碰撞性: 2（相似图像碰撞）
          映射分布均匀性: 2（视觉特征相关）
          差分分布均匀性: 3（局部变化敏感）
          典型应用场景: 图像相似度识别

        - name: SHA-256
          单向性: 5（密码学不可逆）
          性能: 2（计算成本高）
          抗碰撞性: 5（密码学抗碰撞）
          映射分布均匀性: 5（密码学均匀）
          差分分布均匀性: 5（强雪崩效应）
          典型应用场景: 区块链/数字签名

        - name: BLAKE3
          单向性: 5（树形结构混淆）
          性能: 3（并行加速）
          抗碰撞性: 5（优于SHA-256）
          映射分布均匀性: 5（树形均匀分布）
          差分分布均匀性: 5（128轮混淆）
          典型应用场景: 高性能加密场景

        - name: Argon2
          单向性: 5（内存-时间均衡）
          性能: 1（内存硬函数）
          抗碰撞性: 5（抗ASIC碰撞）
          映射分布均匀性: 5（内存依赖分布）
          差分分布均匀性: 5（时间-空间均衡）
          典型应用场景: 密码存储

        - name: FNV
          单向性: 1（简单乘法可逆）
          性能: 3（短数据较快）
          抗碰撞性: 2（高碰撞率）
          映射分布均匀性: 3（局部聚集）
          差分分布均匀性: 2（变化不敏感）
          典型应用场景: 低成本内存表

        - name: SpookyHash
          单向性: 1（无密码学设计）
          性能: 4（跨平台一致）
          抗碰撞性: 5（Jenkins零碰撞设计）
          映射分布均匀性: 5（多维均匀）
          差分分布均匀性: 5（比特级敏感）
          典型应用场景: 分布式系统

        - name: crc32/64
          单向性: 1（线性校验可逆）
          性能: 3（校验专用）
          抗碰撞性: 2（非抗碰撞设计）
          映射分布均匀性: 3（校验位分布）
          差分分布均匀性: 2（线性变化）
          典型应用场景: 网络校验和
      qs:
        - 为啥说“哈希的本质是某种摘要算法（或者说压缩映射（把任意长度的数据往固定长度上映射））”，为了把“无限数据”压缩为几乎不会重复的“有限数据”? # 哈希函数的本质是扫描字符串过程中，根据`之前的结果`, `当前位置`, `当前字符的值`，使用一个公式计算出当前结果，属于一种摘要算法。
        - How to design hash algo?
        - 为啥 MurmurHash 能够做到“高性能，低碰撞率”？
        - 怎么评价hash来作为调度算法？或者说，使用hash作为调度算法的场景，和其他典型调度算法如FIFO、CFS之类的，有啥区别？再或者说，有哪些场景下，需要使用hash作为调度算法？ # 按我的理解，用hash作为调度算法，实际上和Round-Robin起到的作用差不多，是吗？只不过一个就是按照1:1:1进行调度，而hash则是根据“取余运算”，实现某种结果上的均衡性。

        - "~~hash, hashmap, dht-hash 之间有啥关系?~~"

        - 常见的哈希函数？ 答：直接定址法、除留余数法、平方取中法、随机数法、数字分析法、叠加法等。 # [数据结构—哈希表 - 糖拌西红柿 - 博客园](https://www.cnblogs.com/TheGCC/p/14206791.html)



    # FIXME 自己手搓一下，写完之后删掉这个，还有上面的repo
#    - topic: TopK
#      isFold: true
#      qs:
#        - "***TopK的本质不就是排序算法吗？为啥TopK用堆排（最小堆）比用快排更好？***" # [前端进阶算法10：别再说你不懂topk问题了 · Issue #73 · sisterAn/JavaScript-Algorithms](https://github.com/sisterAn/JavaScript-Algorithms/issues/73) 因为TopK只需要找到最大的K个元素，不需要对整个数组进行排序。所以堆排在这种场景下，空间复杂度、时间复杂度都更好。
#
#        - 快排 quicksort 实现TopK # [](https://go.dev/play/p/d91W5fi31zo)
#        - heap sort实现TopK # https://go.dev/play/p/hYMtKaeP84f
#        - 10 亿的数据，每个文件 1000 万行，共 100 个文件，找出前 1 万大
#        - 求一个数组的中位数？用快速中位数算法或者最小堆
#        - 从 1000 万个数字中选择出 1000 最大的数。3 种方法，排序，再选前 1000 个，堆，快排
#        - 从海量数据 (int 数据类型) 中找到前 50 的数字
#        - 亿级数据里查找相同的字符以及出现次数
#        - 给你一个 1T 的文件，里面都是 IP 字段，需要对这个文件基于 IP 地址从小到大进行排序？
#        - 一个文本文件，大约有一万行，每行一个词，要求统计出其中最频繁出现的前 10 个词，请给出思想，给出时间复杂度分析。
#        - 给出 N 个单词组成的熟词表，以及一篇全用小写英文书写的文章，请你按最早出现的顺序写出所有不在熟词表中的生词。
#        - 手写 TopK 算法：从 100 万条字符串中，找出出现频率最高的 10 条？（比如在搜索引擎中，统计搜索最热门的 10 个查询词；在歌曲库中统计下载最高的前 10 首歌等）
#        - 100GB url 文件，使用 1GB 内存计算出出现次数 top100 的 url 和出现的次数？尽量减少 sys call 和 IO。尽力使用完已给资源。
#        - 搜索引擎会通过日志文件把用户每次检索使用的字符串记录下来，每个查询串的长度为 1~255B。假设目前有 1000 万个记录（这些查询串的复杂度比较高，虽然总数是 1000 万，但如果出去重复后，那么不超过 300 万个。一个查询串的复杂度越高，说明查询它的用户越多，也就是越热门的 10 个查询串，要求使用的内存不能超过 1GB
#        - 有 10 个文件，每个文件 1GB，每个文件的每一行存放的都是用户的 query，每个文件的 query 都可能重复。按照 query 的频度排序。
#        - 有一个 1GB 大小的文件，里面的每一行是一个词，词的大小不超过 16 个字节，内存限制大小是 1MB。返回频数最高的 100 个词。
#        - 提取某日访问网站次数最多的那个 IP。
#        - 10 亿个整数找出重复次数最多的 100 个整数。
#        - 搜索的输入信息是一个字符串，统计 300 万条输入信息中最热门的前 10 条，每次输入的一个字符串为不超过 255B，内存使用只有 1GB。
#        - 有 1000 万个身份证号以及他们对应的数据，身份证号可能重复，找出出现次数最多的身份证号。


    - topic: 分布式一致性协议
      isX: true
      table:
        - name: Raft
          一致性类型: 强一致性
          消息方法: 中心化（Leader驱动心跳和日志复制）
          容错性: |
            容忍 (n-1)/2 节点故障
            仅能容忍故障崩溃（Crash Fault），不抗拜占庭故障
          性能: 中等延迟，吞吐量适中
          弹性: 支持分阶段动态成员变更
          实现复杂度: 简单（角色明确，流程清晰）

        - name: Paxos
          一致性类型: 强一致性
          消息方法: 多轮提案（Prepare & Accept）
          容错性: 实际容错节点数为 f ≤ (n-1)/2（向下取整）
          性能: Basic Paxos低吞吐，Multi-Paxos优化后提升
          弹性: 动态变更需依赖外部协调服务（如Chubby），非协议原生支持
          实现复杂度: 复杂（需处理活锁等问题）

        - name: ZAB
          一致性类型: 强一致性
          消息方法: 中心化（原子广播 + 反向心跳）
          容错性: 容忍 (n-1)/2 节点故障
          性能: 中等延迟，类似Raft
          弹性: 依赖ZooKeeper同步历史日志
          实现复杂度: 中等（ZAB是ZooKeeper的共识协议）

        - name: Gossip
          一致性类型: 概率最终一致性（收敛时间依赖传播轮次与网络拓扑）
          消息方法: 去中心化（随机传播数据）
          容错性: 容忍网络分区和高节点故障率
          性能: 高吞吐，传播延迟高
          弹性: 天然支持节点动态加入/退出
          实现复杂度: 中等（需平衡传播效率与带宽）

        - name: QuorumNWR
          一致性类型: 可调一致性（强/最终）
          消息方法: 法定人数读写（W/R配置驱动）
          容错性: |
            - CP强一致性（【写容错】f < N - W（允许最多 N-W 个节点故障）【读容错】f < N - R）
            - AP最终一致（W + R ≤ N）
          性能: 灵活（由W/R配置决定）
          弹性: 动态调整N/W/R参数适应负载
          实现复杂度: 简单（参数配置为核心）
      qs:
        - "***比较几种常见的分布式协议 (raft, gossip, paxos, zab, QuorumNWR) (consistency, msg methods, elect, tolerance)***" # [常见的分布式协议与算法 - luozhiyun`s Blog](https://www.luozhiyun.com/archives/304)
        # - paxos：我保证一旦观察到被多数承认的确定状态则该状态将一直有效。
        # - raft：都选我，只要大多数人承认我是头那我就把自己当头。
        # - zab：a：快告诉我谁是头？b：我告诉你现在我看到谁是头。a：大家认为谁是头我就认为谁是头。
        # - gossip: 嘿，我听说那个谁现在是头儿了，真的吗？让我去确认一下
        - 日志与复制状态机：有序的日志记录了“什么时间发生了什么”，怎么理解这句话？复制状态机的基本原理？
        #  主备模型（Primary-backup）：也称“状态转移”模型，主节点（Master）负责执行如“+1”、“-2”的操作，将操作结果（如“1”、“3”、“6”）记录到日志中，备节点（Slave）根据日志直接同步结果；
        #  复制状态机模型（State-Machine Replication）：也称“操作转移”模型，日志记录的不是最终结果，而是具体的操作指令，如“+1”、“-2”。这些指令按照顺序被依次复制到各个节点（Peer）。如果每个节点按顺序执行这些指令，各个节点将最终达到一致的状态。
        - Quorum机制
