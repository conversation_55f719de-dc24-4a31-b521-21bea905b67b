---
# w1 (2024-12-30, 2025-01-05)

#- task: fix docs-alfred 仍然存在的问题
#  pid: docs
#  date: 2025-01-05
#  sub:
#    - task: "gh #type 现在这些type都是tag，而非type"
#    - task: "gh shadcn/ui CMD+enter 应该跳转到 /x/langs/#js 而非现在的 x/langs/#langs，我怀疑这个问题和上面的应该是同一个bug"
#    - task: 彻底解决 docs-alfred 中取前一天所有 feed 的时间问题
#    - task: 考虑对 docs-alfred 从合并 yaml2md 调整为分文件夹直接渲染单个 yama 文件，并处理相关调整
#    - task: 给docs-alfred打个tag v1.8.0，并且记录到Monthly里


# 2024-12-30
# 今天工作上的目标就是把测试环境的后台整个跑通。剩下的时间就把昨天没弄完的docs-alfred处理掉。

#- task: 完成 docs for 项目的发版，将代码版本设为 1.8.0 并加上 goreleaser 的 changelog、release 相关内容
#- task: 研究通过 iota 结合 Golang 错误码自动生成的最佳实践
#- task: 查看只应用和真应用在引白定情况下的调用链
#- task: 查看 ZZZ 项目中 Nextjs 生态下的 UI 库

# 今日复盘：今天还行，白天把pre-test的项目优化相关，处理掉了13个东西。晚上把docs-alfred的“配置即文档”也搞定了。真心不错。明天预计

# 2024-12-31
# 1、把本地scratch和chrome的网页都处理掉
# 2、今年的last day了，把在这边做的东西打个能交付的版本放到测试环境。今天做了几个事吧：
## 1、切换数据库之后，早上修改之前arch-serv里的相关代码需要连接的数据库。fix部分因为切换数据库导致挂掉的API。
## 2、下午fix了一些因为昨天refactor以及切换数据库导致的一些bug，比较细碎。
## 3、移动端适配：登录页面的移动端适配，以及在移动端下隐藏navbar和sidebar，并且首页在移动端下展示为sidebar
## 4、把目前的代码打包后，拉到测试环境。交付一个基本可用的版本。

# 今天还真就还行，把这两个事都弄完了。本来今天打算摸摸鱼就混过去了，结果下午

# ErrSizedGroup

# 2025-01-01
# 今天确实没啥事。早上11点半出门吃饭，顺便给我爸打个电话。下午把前两天的事收收尾，什么docs-alfred的bug、清理本地的chrome和scratch。把上面这两个处理完之后，还是搞搞nextjs，之前那个仿写nextjs的东西

# 把本地的scrapy相关代码都删掉了
# itjuzi, mzt, comics90, zhihu, ziru, dytt, mzsock, d1bz, boss, doutu
# pipelines_files.py, pipelines_mongo.py, pipelines_mysql.py, pipelines_pgsql.py, pipelines_redis.py, pipelines_images.py

# 起床玩会电脑出门吃饭，多溜了会弯，回去就2点多了。睡了一觉，3点。起床之后开始清理上面那两项。到了5点左右，开始写nextjs，，就还是想把之前nextjs仿写项目的那个继续搞搞，但是又不会写嘛，两眼一抹黑，就又去看了下nextjs官方文档，看了一遍，大概用了40min左右。就又去仿写street-unsplash那个项目，相对简单一些。

# 2025-01-02
#

# 2025-01-03

# 早上最后再在本地开发环境点一点，看看还有啥bug，fix掉。中午之前发版到test环境，作为v1.0.0-alpha

# cloudflare worker 反代 dockerhub

# 拒绝压力的底气，这一点说的特别好。嗯。这就是我说为什么摸鱼的必要性，做side project的必要性。嗯这个以后这个后面要写一段文字专门聊聊的。

# 今天真就整天都扑在公司项目上，从早到晚。早上加了个issue “自测目前后台的bug，并fix”，早上自测了一下后台，找了13个bug（不止），一个一个都fix了。到晚上还剩了一个小章之前写的statical和我那个跳转筛选的bug没有解决。重新开了个新分支处理这两个。然后再自测一遍。明天要上生产环境的。晚上把之前处理docs-alfred之后，gh-merge不work的问题解决了一下（实际上并不是代码问题，而是works.yml里面某个语法错误导致的，但是我之前没加logging，所以就不知道具体报错。晚上加上了。）。之后又继续写 street-viewer 那个nextjs仿写项目，还是没写完。

# 2025-01-04
## 估摸着大部分公司都要25号左右才放假了。

# 今天要把项目上到生产环境。早上把bug都fix掉。

#  2025-01-05
# 周日，公司年会，去嘉兴了。

# 想发一些感悟啊，就是今天大概的一个情况，然后其实既是对于今天的呃 1 月 5 号吧，然后大概一个情况的一个说明，也是说是一些小小的人生感悟，其实几点吧，然后，嗯嗯，首先第一呃就是今天做了什么？今天公司年会嘛，然后早上 9 点钟的高铁从上海呃虹桥南站到了嘉兴南站，呃 9 点半就到嘉兴了，然后很快吧，对吧？然后，嗯到了嘉兴之后，我步行就走路走到这个公司在的地方，然后呃 11 点，大概 11 点多吧，然后然后又到公司附近老乡鸡，正好公司那个他那个楼下就有一家老乡鸡去吃了个午饭，吃完饭 12 点左右这找了一家钟点房，呃休息了三个小时，然后下午 3 点钟到了，就公司年年会开始吧，然后这个就是大概，然后无非就是年会嘛，无非就是吃吃吃喝喝玩玩乐乐，对吧？呃就几场，一个就是公司在在在在他嘉兴这边的公司里面，嗯开了一个就是大大家的这个会，然后呢，也不说年呃，也不说会吧，就是大概一个总结吧，然后呃做了做小游戏，热热场嘛，破冰，然后呢呃然后就是去呃找了个酒呃找了个餐餐馆吃饭呃一共摆了三桌吃饭呃，然后还行抽，然后年会抽奖还中了一个，还中了一个空空气炸锅，然后我这转手就在闲鱼上面出掉了，大概可能能卖卖个二百四五十块钱吧，然后呢，呃呃这个，这个，这个嗯，其实我对于晚上我的这表现还是打打分，还是一个比较高的分数吧，那人生也不说第一次吧，也算是比较，呃，这 KTV 里面主动唱歌哎，我觉得我唱了，然后主动给领导敬酒，哎，敬礼也没有敬一圈吧，主要几个核心的领导也都敬敬到位了，对吧？然后，嗯嗯，然后这是包括我中奖之后的这个，呃，这个大概的一个，哎，感想吧，我觉得也是那样，效果还不错，对吧？然后，嗯，反，反正大家都挺，挺乐呵的，对吧？把，把大家都给弄笑了，对吧？挺乐的，我觉得蛮，蛮好的，包括我，对于我就是，然后我在在我唱歌之前其实就是挺冷场的，然后我唱了之后整个气氛就起来了，对吧？我觉得我这个还不错，然后他他们这边这几个，这几个负责人我基本上也都也都照了面了，也算是，呃，第一次见吧，然后我觉得就有一些感，感想大概是几个人的这个性格是怎么样子，我都已经摸的比较清楚了，对吧？我觉得还不错，然后呢，这是大概的一个情况，然后呢，怎么说呢，就是再则，嗯，这是今天的大概的情况，然后下来说一说感想吧，其实几点感想，我倒并不认为说是我怎么就是这个社会化吧，就是见我刚刚列的这几项是什么，就是说什么有的选没得选之类的，这个问题，我更倾向等于说是，就是我的这个，我更倾向于从一个好的方面去呃去想这个问题吧，应该说，呃，这是其，这是其二，其三呢，就是我是说是，嗯嗯，怎么再说吧，大概就是这个。
# 还有说我这个酒量真的挺可以的啊，喝了这么多，喝了可能喝了有，嗯，喝了有我那个葡葡萄酒，当然我没有喝白的嘛，晚上第一点那个吃饭的时候喝的是葡萄酒，哎，其实这么一想，其实也没喝多少吧，应该说就是，嘿嘿，真的就你想我，我前前两天呃，自己一个人喝酒喝喝啤酒都是 500 毫升的罐，都是一口气喝个三瓶，一下 1,500 毫升就进去了，但是这啤酒嘛，其实真要说的话呢，今天晚上喝的确实不多，一个难怪，仔细想，确实喝的不多，难怪我就怎么喝完怎么感觉这个这个这个这个这个什么意思呀？然后包括身体机能都都非常棒，嗯，难怪呢，就是因为确实喝的不多，一开始葡萄酒其实其实也没喝多少，其实葡萄酒真喝多少呢，可能喝了有也就喝了一杯嘛，也就喝了一杯吧，两杯可能最多也就两杯，可能我也没意识到两杯吧，大概啤酒喝了可能有大概喝了，因为他那个是一个 330 毫升，可能都没有，我不知道那个是多少毫升的，反正 mini 装的那种的可能就是 200 来毫升的，那个我可能喝了有个一共加起来喝了能有三瓶，4 瓶。四瓶可能有个四瓶吧，那加起来可能都不到都不到 1,000 毫升嘛，就就就就就这个量。

# 1、游戏葡萄的网页被砍掉了，现在只能通过公众号获取文章。所以mlc扫出来，游戏葡萄的那些网页都挂了。在docs里移除所有相关网页。





# w2 (2025-01-06, 2025-01-12)

# 2025-01-06
# 用量付费 VS 订阅制 横切和竖切

#- task: 修改之前关于处理books.yml的工作流。之后在月刊里记录当月看过的书，年底通过月刊来得到当年的books.xxx.yml。之前整理是先把想看的书都放到books.xxx.yml里，然后再一本一本看。这个只适合有大量的整块时间，且不需要写（）读书笔记的情况。一旦不符合两个必要条件。这个workflow就不适用了。
#  date: 2025-01-06
#  pid: docs

# 昨天晚上公司年会嘛，晚上2点左右才睡。今天早上困意中8点起床，为了蹭个酒店早饭。本来想着休整一下就去公司的，结果一回去也懒得出来了，待到了12点才走。到了公司，下楼吃了个午饭，下午2点开会拉需求，一直到快下午4点聊完。然后启程回沪，高铁+打的到公司，路上加起来一共2h。不到6点到公司。晚上回去也懒得搞公司的项目了。晚上把《PPHC》和《深入高可用系统原理与设计》处理掉了，还可以。

# 2025-01-07
# 今天白天把客服统计这块的东西都搞一下，估摸着快的话，今天也就都处理掉了。晚上还有时间的话，把《七周七数据库》和《七周七并发模型》处理掉。
# 今天需要把前两天买的绿联网线口开个发票，跟着高铁票一起报销。

# 不得不说，直接把之前那种用task写到md里，优化成现在这种做到yaml里。真是天才般的想法。主打一个详略得当。有值得写的task，就写出来。没有就直接写到注释里。这不比md清晰一百倍？

# cursor把sonnet3.5的用量从250次，限制到50次了，史诗级削弱。有一说一，真的很好用，但是即使升级了$20/月的PRO用户

# 需要意识到的一点是，nextjs是个全栈框架，而非单纯的前端框架。当然，这取决于你怎么看待和使用他。正如laravel实际上也是个前端框架，但是我们只会把他当后端框架用一样（虽然laravel有个专门的API框架lumen，但是没人用，由此可见一斑）。

# 2025-01-08
# 工作：把客服相关的东西，给相关人员看一下，看看他们还有什么优化意见。今天需要跟客服组这边把需求对完，然后把东西发给运营组，粗略地聊聊需求，把具体需求列出来，加到issue（发给陈总看）。
# 注意：今天下午发日报时，先单发给陈总。再发到群里。
# 今天早上肯定是摸鱼了，能把手上的东西都处理完就阿弥陀佛了，倒也不必说是摸鱼。就两个事：1、把docs.md的内容整理到docs-record.yml（顺便把这篇重新挪回blog） 2、想想killthefeed怎么实现 3、把面试的几个点列一下

# 今天早上就是，呃，今天早上摸鱼了，就是办那 3 个事嘛，对吧？然后，嗯，下午，嗯，一觉起来睡了一觉啊，下下午确实晕晕乎乎的，嗯，睡一觉，一觉起来 2 点了，然后正蒙着呢，HR 说让我让我去面试，让我去面一个人，然后，然后我就看看，找了点那个面试的东西嘛，然后面完了，差不多吧，面面，面了一快快一个小时，然后面完了，4 点了吧？4 点了，然后，嗯，回来之后，回来之后去，嗯，聊那个他主播的后台的那个需求，然后聊需求，然后也是哼哧哼哧做，一开始对这玩意还没有什么那啥，聊需求可能聊的有差，也就也得将近半半个小时，然后瞎胡做做，然后做到，然后，嗯，稀里糊涂做呗，然后做着做着才慢慢才知道这玩意是怎么回事，其实挺简单的，然后完事之后，嗯，反正稀里糊涂中，中间其实还很多，其实应该先做后端，对吧？其实就是应该先去解决数据源的问题，拿数据源就是从 Excel 去导入嘛，然后数据处理中间还有点问题，到现在它这个数据处理还是有问题的，然后但都解，解决掉了，数据源解决之后，然后就是一个能够兼容 charts 和它的 table 的一个展示列表的一个接口，对吧，做掉，嗯，基本上今天晚上就到这，然后做完之后，但是它这个玩意现在跟前端的这个页面还是不兼容，这个明天要处理一下，基本上这个玩意也就完事了。

# 1、明天早上出门时把快递邮出去，并询问东西能否先放在原处。2、明天晚上找住处。

# 2025-01-09
# 今天的主要xxx还是放在工作上。因为今天之前的青旅月租到期了，晚上要再找个住的地方，所以要早早走。所以白天要赶紧把公司的东西弄完。

# 现在住的这个地方太冷了，那个晚上可以把什么衣服什么类的之类的往睡袋里面一塞，第二天早上起来的时候既能晚上睡觉的时候能保暖，第二天起来的时候衣服也是暖和的，这样就两全其美了。
# 昨天晚上从之前住的青旅搬走了。然后晚上哼哧哼哧折腾了一个多小时，然后才找着现在住的这个地方。这地方还不错，就是 35 块钱一天然后有热水，免费热水。然后是独立的洗衣机，就是自用的洗衣机。然后是 4 个床位。挺好的，挺好的。

# 2025-01-10
# 今天打算摸一整天鱼。除了工作，我现在手头就两个事，看书和写ktf。今天打算看书。现在已经是5点了。截止目前为止，早上就是看我的newsletter，看到一个 review-2024 的repo [saveweb/review-2024](https://github.com/saveweb/review-2024)，其中有几篇颇有意思的。下午也是瞎刷刷网页，到 2:10 有个面试，一直面到 3:10，感觉也没问啥，时间就是过的很快。然后找了找有什么 nextjs blog的方案，就到了现在了。今天要把月刊里的几本书都过掉。后面就不操心这个事了。6本书，预计11点回家。那平均下来就是60min/本。

# 2025-01-11
# 今天估计摸不了鱼了。多少得干点活。今天有几个事要注意：1、最重要的，晚上回去要洗衣服。所以要早点回去，大概10点就回去，洗完衣服估计就是11点半之前，晾好睡觉。2、早上趁着陈总出差还没回来，要把昨天的那些东西都先处理掉。实话说任务艰巨。3、下午就得弄公司的项目了。4、晚上下班之后到10点，可以再看看书。

# 这两天在看书，其实很多书的核心内容都是类似的，但就是很难做到。知易行难嘛。但是恐怕值得反思的是“不知知不易，恐将行更难”。

#- task: 给docs-alfred加了个，parse报错信息里加上具体文件名。如果之后再出现写gh.yml的格式出现错误时，可以直接排查到。 # 前两天对gh.yml有些调整，然后昨天commit之后跑docs直接报错了。我还以为代码出问题了，看到还是 unmarshal 有问题，就知道是gh.yml有问题，但是之前的报错没有具体报错文件，所以排查不到。只能加上这个。
#  pid: docs
#  date: 2025-01-11

# 晚上说是要把手头的几本书看完，结果弄了两个事：1、把之前的diary-2024.md，给弄成yaml形式了（真的挺费时间）。2、想要结合PQ4R来读一下《卓有成效的工程师》这本书。

# 之前注册的，我现在这个wx号的辅助账号找不到了。就拿新注册的那个广电卡，注册了一个wx号，号码是：wxid_6tabkda7dnwl22

# 2025-01-12

- task: 2025w2
  date: 2025-01-12
  pid: PWR
  des: 本周是今年的第二周。工作上主要是上周年会之后，定下来的几个需求（客服组统计、运营组统计、直播统计）。但是呢，陈总从周三出差到周六，总计4天。也就前两天干了点活，后面两天连摸两天。工作上现在运营组还完全没处理，其他两个还有点东西要收个尾。个人学习呢，
# 昨天早上就是，嗯，昨天早上不是把衣服给洗了吗？然后没衣服穿了，就一直躺被窝里面刷淘宝刷，然后一，嗯，然后早上八点多把衣服晾起来，然后就等着晾。干嘛？衣服晾干刷淘宝，一直刷到下午三点多四点钟瞎胡刷呗，玩手机，中间下午又睡了一觉，然后下午起来之后，嗯，去那个哪吃点了个，去那个去老乡鸡吃了个饭，然后去了公司，那个时候是 5 点到公司，5 点，5 点，然后，呃，东搞西搞，嗯把，就是又把那个那天那个 diary 那个东西彻底收了个尾吧，算是 2024DIARY 啊，然后那个收了个尾，然后之后是那个，之后是之后，我，我本来在犹豫说要不要把那个处理掉，就是那个说 books 的那个可视化的那个东西啊，那还是弄的一直到晚上八点多才开始弄的，然后挺快的，挺快。然后只用了反正是晚上走的时候基本上就弄完了嘛，然后挺快的，然后昨天晚上就算是弄完了吧，就是一个基本的东东西弄完了，还有一些需要优化的，昨天其实就干了这些事。

# 产业规模之大了，上坡路下坡路，相当于是把一个两米多高的巨人塞到1米5的箱子里

# MBP 就像是一根拴狗绳。牢牢的把我拴在了不超过半个小时距离上不超过两公里的范围内这种规则是超越时间和空间的

# 洗发水现在来看还真的是一个必需品。如果不用洗发水的话真的会有头皮屑，啊，这个是很难受的。你用清扬之类的去屑洗发水确实没有头皮屑。

# 必需品和耐用品，其实就是外包员工和正式员工。这就是嗯比如说我之嗯，对于我来说就是消耗品和耐用品之间的区别吗？嗯。外包员工就像是消耗品，嗯，甚至不在于价格，就是本质上来说是心理账户，我如果我把它归类到消耗品，那它本质上来说不属于我的东西。本质上来说他不是我的东西，我也不会说是甚至不在意价格，就价格高低，不一定消耗品的价格就比耐用品的价格。更低只是说他的可替代性以及心理账户的问题

# 其实做软件架构做的多了，包括这两天在看这个，嗯，这两天也在看一些塔勒布的书。包括看一些关于软件工程的书，嗯，包括结合我当下的一个经历，然后人生的小小思考吧，就是说嗯怎么说呢？啊，比方说我二尤其是今年年初嘛，一下整个人一下子颓了，对吧？然后那其实就是21年以来的这么一个事情，21年以来的这么一个事情，然后随着越发感觉到一个一个东西吧，或者说就是一个一个一个一个东西，就是说哎就是失败，永远失败是必然的，失败是失败是永恒的，不是说必然的，失败是永恒的胜利是偶尔的啊，然后嗯，怎么？说这个话怎么说呢？就是说你看我们做做架构，对吧？我们做做架构，然后也就是叫什么？呃，网络是延迟的。嗯，嗯，CPU是怎么的什么什么是怎么的什么什么是怎么的？反正就是就是那个话，对吧？找找来看看，然后然后那个我们说这个有人说这个当下的这个经济状况是什么？债务是刚性的。资产是弹性的。然后还有一个叫什么什么的，什么是什么的，然后怎么说呢？那包括结合塔勒布的哲学来说，就是说白了，世界你需要知道的一个事情就是世界是不均匀的。世界是不均匀的。嗯，不是说哦。努力就有回报，而是说是嗯从统计学意义上来说，你只有不断试错才能够拿到一个还不错的回报。嗯，嗯，必然是这样子的，那么其实就是我就是那个高端意志已经不去一去不复返了，那个就是说包括我今年也其实是降薪的，本质上来说就降薪，嗯，之前那种哈。嗯，每年都能翻30%涨百分之涨薪30%这个高端位置。不复返了，对吧？但是你说我不是说哦这时代完蛋了，不是说时代完蛋，行业完蛋了，那我就我自己也完蛋去。这不存在不可能，我说不可能就是说嗯。应该是说那还是那句话，嗯，失败是永恒。的嗯，成功是暂时的，那么在还能成功，就是我们希望就是就是在那尽尽尽力去获得一个还不错的结果，然后不断去试错，我们既然说你，既然你选择了远方，那你就是那句话就是尽量选择远方便不顾。风雨兼程的吧？那你既然是哦你既然说是不是说闲不住的人，而是说你也想做点儿事儿，想想说是哦还想折腾折腾，那那那那那那你就只能去试做，是吧？那就如果说你不想不想折腾了，不想折腾月薪3000的，你说中国现在你可能说是这个高高薪的工作，那确实卷月薪三四千的工作还是有的是的，对吧？那你就躺着。有钱无非如此，无非如此了，也倒也不是说是让我从21年受到这个挫折之后，我可能有一点儿一蹶不振，因为然后一直到今年有一点儿一蹶不振了，应该是。就是这个存款花完了嘛，然后有点儿一蹶不振，倒也倒也不必如此就是说嗯往上蹦蹦蹦不着摔地上，吧唧一下摔地上了，对吧？我直接说倒也不必如此就是关灯了。看不动了，就找个平台歇一歇，倒也不是说我搬不动了，完了看不动了，啪叽我往下跳，对吧？啊，不必，不必，真的。

# “失败是永恒的，成功是暂时的”，“不以物喜，不以己悲”




---
# w3 (2025-01-13, 2025-01-19)

# 扩大技术视野，寻找更嗯就是coser这类的工具是帮我们更快的在已经有了方案的情况下帮我们做去就是去帮我们去做具体执行。正如是嗯老板招聘员工一样，其实都是去做执行的嘛。把自己的想法去分开，然后去去做执行老板本身其实更更多的一个工作在于一找方向，二，一是找方向，定方向。二是找资源。那么其实放到我们这个里面，放到我们去套用在我们使用的使用这个事情上面来说的话，那其实我们嗯真正阻碍我们去使用QQ的东西只有一个就是我们对于技术本身的sense，对需求本身的理解，对于行业的认知。除此之外没有其他的。

# 所以之前如果说gp这种东西更类似于画，那么现在我现在我那么现在我更愿意称之为staff就是员工，而不是一个因为跟staff之间的区别在于什么？嗯，它的它提供的一个方案你只能呃就是仅供参考吧。来给你提效用的，对吧？仅供参考，不确定性很高。嗯，那staff的话他其实在某种程度上，这个基点是某种程度上已经过了，至少在确定性范围内，他的这个技术基点是已经过了。他是本身是有确定性的。那么更多就是做一个提效工具，就是我们的staff给我们的想法去嗯，落地。和实施那么这里其实可以套用很多我们在做呃人力资源管理。选育用留太里面的经验了

# 2025-01-13

#- task: 工作相关的
#  date: 2025-01-13
# 1、之前那个 recaptcha
# 2、task里那几个跨库sql。修改sql
# 3、运营模块：

# 想起来确实应该用chart来
#- task: 给 blog 做一个chart. 优化一下/2000 下面的几个archive类文档，都可以想想怎么优化。
#  pid: docs
#  date: 2025-01-13

#- task: 把昨天晚上给blog加的books统计，优化一下
#  pid: docs
#  date: 2025-01-13
#  现在还有一些问题：
#  1、移动端适配。现在移动端完全不适配
#  2、饼图样式。我需要的是饼图，鼠标hover到该tag上，显示该tag里的type占比，而不是直接分type展示。
#  2、需要饼图和折线图并排展示，而不是分成两行。
#  3、不同评分，加tag颜色。比如说，10分 金色 7-8分 绿色 等等。我不知道这个在nextui里叫啥，就是 antd 里的 tag
#  4、下面table上面加个tab，默认每个tag一个tab，注意筛选联动（也就是如果筛选tag的话，下面的tab就只展示该tag，不展示其他的）
#  5、单选select用nextui，现在用的是原生select
#  6、筛选逻辑有问题，点网文筛出来mysql，需要查一下问题
#  7、其实不需要时间筛选，移除掉。这里需要注意的是，如果横轴太长的话，可以动态调整每个区间的width，保持整个折线图大小不变。我不知道你现在这个是否能够支持该功能。
#  8、点击查看详情，使用 drawer，而不是 modal

# falsy(null, false, -1, ...), truthy


#- task: 晚上下班之后，今天就启动那个Expedia站点。反正闲着也是闲着。今天晚上先把整个前端页面搞出来。然后再看跟服务端交互怎么搞。
#  pid: gs
#  date: 2025-01-13

# 今天真的是颇有收获，做了不少事。早上来了之后，日常看当天的newsletter一个小时，把昨天的blog的books统计图表优化了一下。下午写了会公司项目，主要是售后统计（运营需要的）。晚上先把expedia站点随手搞了一下，然后

# 2025-01-14

#- task: 下班之后继续搞那个Expedia # 想了一下还是直接用gozero了，没必要折腾nunu，ROI有点低。虽然gozero用不成gorm是个问题。下班前要一下锦江酒店后台账号
#  pid: gs
#  date: 2025-01-14









# 公司项目现在还有3个东西没弄：
#- task: 把售后统计模块做掉，预计在7点之前交付。
#  pid: gs
#  date: 2025-01-15
#  sub:
#    - task: 4、优先处理跨库操作 # 从13:40 弄到 14:30 # 实际上弄到了 15:00
#      date: 2025-01-15

#    - task: 1、售后统计。这个要先改之前的task。前台就是一个数据展示的东西，很简单。 # 预计4点之前，把相关的钱后端全部弄完  # 一直到6点半才弄完
#      date: 2025-01-15

#    - task: 2、客服统计和售后统计的dashboard，今天需求定下来了，实现掉。 # 大概半个小时搞定
#      date: 2025-01-15

#    - task: 5、5点左右发一版。把上面的东西都处理掉。

#    - task: 上线之前看一下为啥那个直播 live/stat 是个白页 # 还真就是因为那个fs导致的没有打包
#      date: 2025-01-16

#
#    - task: 3、把gitlab上之前遗漏的需求捡一捡 # 下午4点开搞这个
#      date: 2025-01-16
#      sub:
#        - task: 验证excel数据是否合法。以防录入脏数据 # 5点才弄完呐
#        - task: 给dashboard里加个直播统计的tab # 5点半搞完了，直接复用代码

# 原本预计的3点搞完上面那个，结果还是弄到了4点才弄完。只能说确实颇有收获，很多之前花了很长时间，却又淡忘，又或者呢当时本就没有close的东西，经过这次“温故”，又捡回来了。真的是令人愉快的事情。
# 几个东西都还没弄。明天还是这三个继续弄吧。





#- task: 下班之后先把昨天晚上写的那个 用来 tampermonkey。昨天只写了copilot的批量打开和批量清空。一会补充一下豆包的。顺便研究一下那个专门用来写tm脚本的scaffold. 看看究竟还能不能用。写完发到论坛里。 # 主要是我对这些东西有执念，极简主义者嘛，必须做减求空。
# 最后：反思了一下写这东西是否有必要。应该说是非常有必要了。能想象到如果当时没写那个flomo的“一键清空”脚本。会怎么样呢？有了这个东西，以后就可以把这个当flomo用了。隔三差五就可以清理一下，保证所有查过的东西，都能被整理回docs，并且最终被彻底消化吸收掉。
# 玩了一下 create-tampermonkey，
#  date: 2025-01-16



#- task: 晚上还要把ktf的表设计弄一下 # 参考ttrss
#  date: 2025-01-16
# 搞ktf表，结果ttrss的sql跑不了，只有DDL，没有数据。就想着去supabase里搞一下。就又弄了半天。

# 这个月还是得抓住两个事：看书和搞ktf。其他都是顺带的。只不过很多事情确实是交叉在一起了，也只能像是打架子鼓一样，东边搞一会，西边搞一会。赶紧把统计这个东西做掉吧，后面主要就搞那个同程艺龙了。晚上下班之后搞一搞Expedia那个。对我来说，搞EXP和搞KTF，就是一码事嘛。技术栈都是一模一样的，就当是练手了。

#- task: 晚上再整理一下 scratch, chrome tab, finder里的文件之类的。TM的什么熵增啊，稍微不管就很乱，得TMD天天收拾。
#  date: 2025-01-15

# 昨天晚上下班之后，弄的两个都是瞎搞。

# 2025-01-15
# 春节假期基本上定了，就按照法定节假日放假，一共8天。掐头去尾就是只有6天。也还行了。
# 今天主要就是两个事，把公司的东西交付掉。把我docs的task相关重新refactor一下。做完就撤。

# 今天想到。这次

#- task: 清理下docs里2 3月份的月刊
#  date: 2025-01-15

# 一点想法 blog不急着发，写的不好或者没价值，及时发了，后面也要改，甚至删掉，不如再多沉淀一下，发点确实有价值的东西。

#- task: 搞清楚之后task应该组织。可以把OKR和project都组织起来。不需要像现在一样，一个task需要在（横向的（或者说具体的））task.yml 里写一份，还要在（纵向的（或者说抽象的））OKR.yml和project.yml里再写一份。
#  date: 2025-01-15
#  pid: docs
#  sub:
#    - task: 重新 refactor 一下整个task相关的目录结构 # 注意：docs.yml 也需要调整
#    - task: 搞清楚之后，需要修改现在的task-KTF.yml, task-popjob.yml 之类的
#    - task: 需要新建一个 OKR.2025.yml，拆分年度目标 # 这里需要找些OKR相关的书来看
#    - task: 把 docs-record.yml 也整理到相应的task.yml里
#    - task: "重新梳理了之前task相关的key，移除了之前的方案 `tag: [week, main]` 或者 `tag: [PWR, w50]`这种，以后就是3个key，比如okr, pid, type"
#      date: 2025-01-16


# 做成task，这里应该思考一下，task和月刊之间的关系。乃至很多之间的关系 yaml不支持跨文件跳转，否则会好很多
# 其实这个才是正常流程，正常人的脑回路就不应该直接把具体task写到 project.yml 里，否则会多臃肿啊。正常人肯定是遇到问题了。就顺手记录到task.yml里，需要查看到时候，加个tag什么的，抽取到相应的topic//tag里。

# 结论：其实没必要太纠结于 OKR、项目、年度复盘以及task，这4者之间的关系。
# 具体操作：从实现上来说，很简单呐。其实就是给task加个pid和okr来进行标识。另外，新增一个OKR.yml，以及
# 制定Object需要：根据SMART，可量化、时间约束 # 这也是我之前纠结的一个点，“task有没有必要加SMART”，其实task里的内容本身都是很具体的，已经拆分到很细了，本身都是很快可以搞定的。
# 另外，一点小小观察。object和task确实是不同的，比如说，拿跑步举例的话，object可能是半马破130，或者年跑量超过2000km。这个看起来是task，尤其是后者，但是实际上并不是。真正的task一定是拆分到颗粒度很细的，可以用最多两三天时间就完成掉的。而不是这种跨越几个月，甚至一年的。从这个维度上来说，其实OKR里的object本质上就是中长周期的task，但是相较于细粒度的task，更需要按照SMART原则，为什么呢？因为人类太健忘了，长周期的object也不够具体，就更容易忘记。所以。再换个角度来说，object和task的区别正如声明式和命令式，一个是结果导向的，另一个是过程导向的。再换个角度来说，不断打磨自己的OKR工作流，整个人也更有目的性，毫无疑问的，效能//生产里也会更高。

# 回到上面“OKR、项目、年度复盘”之间的关系。现在来看，OKR确实是比

# 计划要调整了。工资是发的12月的，因为12-10入职，所以少了10天工资。只有9500。生活费1.5k、往返机票2.5k、春节红包4k，这就只剩下1.5k了。
#- task: 发工资之后 # 红包4k+往返机票2k+房租5k+2月生活费1.5k = 12.5k。钱肯定是不够的，那就先住在现在这个青旅了。这样就只需要7500了，还剩下2000，送礼什么的也都够了。其实也挺好，2月4号收假，再上个10天，就又能发工资了。
#  date: 2025-01-15
#  sub:
#    - task: 留着春节用的（大概6500）
#      sub:
#        - task: 给老爸老妈春节各包一个2k的红包 # 合计4k
#          date: 2025-01-28
#          pid: relationship
#        - task: 给我小舅和大舅春节都邮 轩尼诗VSOP干邑白兰地（双支装） 之类的洋酒（查了一下¥799） # 合计1600 # 寇兰山76 # Bin2 # Bin389 # 各自送了两瓶 奔富Bin2，每单¥450，
#          date: 2025-01-20
#          pid: relationship
#        - task: 给我大舅妈、姥姥、奶奶，包括大伯、姑姑、三叔都邮点坚果之类的东西（各自¥200左右） # 合计 200*6 = 1200
#          date: 2025-01-20
#          pid: relationship
#
#    - task: 买往返机票（大概2000）
#      date: 2025-01-15
#      des: 说了放假日期之后，就直接买往返机票
#
#    - task: 交房租（大概5k）
#      des: 到时候可能要重新找个住的地方，现在住的地方如果不月租的话，每天¥60，就有点太贵了。大概25号前后房价，也就付10天的房租。短租的话能省大概10天的房租，就没必要长租了。【2024-12-14】决定过完年回来还是再住一个月青旅，综合来说成本最低的。【2025-01-07】过年回来就租个单间吧，青旅洗衣服、洗澡什么的都不太方便，也没有个人空间。估计这个要手上有个5k左右，能付得了“押一付三”。
#      date: 2024-02-10
#
#    - task: 留着生活费（大概1500）




# 2025-01-16
# 今天毫无疑问就是把公司项目那些bug做个总收尾，晚上发一个最终的版，交付出去。

#- date: 2025-01-16
#  pid: docs
#  task: 现在gh-merge是直接用 merge-yml-template 这个 find ... cat > res.yml 实现的。但是这个就带来一个问题，如果某个文件的最后一行没有空行，直接merge的话，就会导致后一个文件的 -type 会直接 append 到前一个文件的最后，而不是换行。导致解析错误。

#- task: "fix Module not found: Error: Can't resolve '../diary/2025/w1.yml' in '/home/<USER>/work/docs/docs/docs'"
#  pid: docs
#  date: 2025-01-16

# 毫无疑问，goods是应该用这个 table 去组织的。另外我还想说的是，那个可能绕了一圈，绕了一圈可能确实又绕回去了，我下一步可能还真就需要用一个后台管理系统来做这个 CMS 的东西，不是 CMS 来把这个 Docs 东西其实可能真就不用 docusaurus 了，可能就真就又换成那个要换成后后台管理系统的，也就是说那甚至说 blog 也要换一个 next js 写的那个可能也要再换一个新吧，应该说然后呢？就是说怎么说呢？就是绕了一圈嘛，兜兜转转绕了一圈，挺挺好的吧，换就换了，也就是 Docs 后面可能有做成后台，整个一个后台管理系统，然后这个 vlog 项目可能去用hugo了，大概是这样。

# 上面这段是昨天晚上到一些小感慨//想法。如果这么搞的话，可能docs-alfred就没啥用了。其实也是，本身也没必要做一个第三方工具来通过“配置即文档”通过yaml实现md渲染。总结上面这段话，想要什么？其实就是把现在的docs改成后台管理，把blog做成真正的blog。
# 1、goods是肯定需要做成table的。现在这种做成文档肯定不好。
# 2、gh如果要做成table，应该怎么做呢？其实现在这种做法更直观，我们构建一个日常使用场景，现在这个docs，右侧TOC起到的作用其实就是做成table之后，筛选框的作用。这里想到，其实我现在的这个gh是个两级结构，比如说 DB -> RDB/MySQL/kvdb
## 1、问题放到哪？repo肯定是渲染成table的，
## 2、如果渲染成table，能带来哪些好处？又带来哪些坏处？好处：现在这种方式，本身已经是sub/rep的repo，无法渲染
## 2.1、现在这种方案（渲染成md）有啥缺点？
### 1、其实有点不够清晰，比如说很容易让人困惑，到底是两级还是三级结构。比如说到底是 ms -> prometheus， 还是 ms -> observability -> prometheus? 仔细想了一下，其实做成三级是更合理的。比如说 DB -> RDB -> MySQL. 但是需要注意的是，确实有一些是没有二级分类的。
### 2、相比于用next渲染，现在用md渲染，很多细节没办法控制，比如说table size, font size。总而言之就是渲染更灵活。想怎么渲染改下代码就可以了。当然，能实现这个关键在于我现在所有数据都是基于yaml的。至于渲染只是辅助手段。
## 3、渲染成table可能存在哪些问题？
### 1、可能没办法直接复用md的很多样式了。比如说code block、url之类的。都需要自己渲染。
### 2、【响应可能会变慢】
### 3、如果渲染成table，我需要通过alfred直接跳转到相应的筛选项，table的筛选组件支持吗？ url类似 /repo?
### 4、【打开文件时不够灵活】比如说有时我需要打开2、3个tab，来比较repo，
# 结论：我不可能脱离docusaurus另外搞，至少现在不能。把goods做成table。

# “代码是优化出来的”，项目同样如此，除非你认知很高，经验丰富，否则该趟的坑一个绕不过去。那么，拼的是什么？拼的是谁能更快从坑里出来，也就是快速改错，或者说快速迭代（并且不再犯类似错误）的能力。

#- task: 把gh.yml整合成table # 直接在现在的gs项目里，把表设计一下。然后写个大概的渲染。看一下预览效果。
#  pid: docs
#  date: 2025-01-16

# “OKR嘛，每年最多3个object，每个Object最多3个KR”

# 2025-01-17
# 离春节放假还有11天，也就是一周(20号到27号)
# 今天真就从早上到现在（4点半）快摸了一天鱼了。还是在想两个事，一个是怎么refactor现在的整个docs项目。另外就是基于前一个需求，在找一些nextjs blog方案。看到了innei写的Shiro。然后就发现一个可怕的事实：前端还真就挺有门槛的，正如后端远远不止是CURD一样。如果只是能写个后台的前端代码或者写个什么网页，那也不算是真正的前端。想到了这点，我就有点怀疑，自己是不是真的有能力搞定计划中的ktf和popjob这两个项目的前端。

# 2025-01-18
# 周六happy，今天白天要多做公司的东西，晚上早点下班，回去之前理个发

# 是否可以

#- task: 把goods.yml渲染成table # 就一个type筛选框，里面的table只展示当前在用物品（再列表中展示 param, price, 购买时间）。点击物品的detail后，弹出drawer，里面的table展示那些 not use 的该类型物品（用table disabled 展示）。下面渲染qs。
#  pid: docs
#  date: 2025-01-18


# vue应用，页面刷新之后404了，怎么fix? # 可能三个问题：1、vue应该使用history hash模式（而不是history模式） 2、nginx处 location 要做容错处理。 3、

# 即使是cursor，正确的使用流程也仍然是，比如说有类似的3个功能，我们应该自己手写一个，让cursor根据我们已经写过的代码，自己给另外2个功能生成，这样就可以节省时间。那么节省出来的时间，可以做什么？可以优化我们的第1个功能，把可以复用的func封装起来，让其他两个复用。以此来减少工作量。这样就是我之前说的“”，一个完美的实现。

# 其实应该想想，不同水平的coder，在使用cursor时的工作流。
# cursor现在的问题在于，通常只会给到一个非常糟糕的实现，并且还是在大量chat之后才能实现。当然，也有一些人在偶尔使用，并且正好遇到
# 今天下午在开始写这个dashboard同比和环比之后，正好 reset了cursor，结果整整30多次usage，用了大概4-5h，现在我不得不把代码又回滚到最开始。

# 口腔溃疡还是得直接吃维生素B。嗯，喝啤酒什么的有用，但是其实并没有那么大的用处，并不是立竿见影，吃为生吃为必是立竿见影的好，并且喝啤酒终究还是有一些问题的。

#- task: 把之前从美团小贷（马上消费金融）借的831+378 都还掉了 # 现在应该还要还招行的17k，再加上美团的12k
#  date: 2025-01-18
#  pid: minst
#  okr: money#loan



#- task: 买了回家机票和返程的高铁票 ¥1358 + ¥1000 MU6477 G111
#  date: 2025-01-18
#  pid: minst
#  okr: money#saving

# 昨天早上想用orbstack代替docker desktop，结果orbstack有bug，结果折腾了半天又换回desktop了。然后又看了下，能不能把之前的xxx调明白了。也没弄成。下午搞公司的东西，弄那个同比环比，ck sum操作搞了快2h，也没弄成。然后赶着下班前，把其他一些小功能

# 2025-01-19
# 又到周日了，几个事：（早上去公司，搞到11点）、中午去吃饭，吃完饭再回来。

#- task: 整理一下上周末早上，清理淘宝店铺时整理出来的。主要是写一下为啥不需要。
#  date: 2025-01-19
# FE马拉松//ViVi跑世界：1、价格偏高，所以这个店铺对我来说。2、但是这点对我来说是个伪需求
# 圣农//泰森食品：1、这两个对我来说都属于默认选项了，也就不需要再收藏了。
# 安记食品：
# RedChef旗舰店：红厨
# 淘鲜达：
# RAB
# 怡宝旗舰店：
# UltraGear优极旗舰店：
# nedao旗舰店：
# 小米官方旗舰店：
# 速力达运动：
# 立白集团官方旗舰店：
# 秒洁官方旗舰店：
# COROS高驰旗舰店：
# Beta顶级户外装备点//妖妖户外正品：
# NatureHike旗舰店：

#- task: 给我的blog做个lighthouse测试 # 周末搞，其实很简单，chrome本身就内置了。我的blog首页
#  pid: docs
#  date: 2025-01-20


#- task: 搞一下 goods.yml 的渲染 # 其实主要是优化上周弄的那个books渲染，基本上比较满意了。一直弄到晚上8点半//9点，才开始弄这个。搞这个搞到11点50
#  date: 2025-01-20



#- task: 买了点东西：1、睡袋内胆（挪客，79） 2、ACG裤子（大概450左右） 3、皮肤衣（UG的，捡便宜买的，原价599的，我199到手了） 4、AX3000t路由器（149） # 合计不到900
#  date: 2025-01-20
#  pid: minst
#  okr: money#saving

#- task: 过年回家，给家里换个AX3000t路由器。并且也弄个网心云。
#  pid: minst
#  okr: money#pi
#  date: 2025-01-20
#  des: 买了，回去之后看下家里上下行带宽到底多少


# 今天周日嘛，昨晚又睡的很晚。早上一直到10点，早上11点到了公司，弄到下午1点20出去吃饭。吃完饭散步一直到3点50才回来（本来想去老乡鸡的，但是又嫌太远了，就在公司楼下吃了，结果吃完饭散步差不多2个多小时）。搞了会自己的东西，看看bz，然后跟老板聊dashboard的具体需求，一直到5点左右。后面就是优化books.yml渲染，实现goods.yml渲染，具体上面有写。

- task: 2025w3
  date: 2025-01-19
  pid: PWR
  review: |
    本周是今年的第三周。万事开头难，今年的这个头开的还不错。无论是工作上还是自身成长上，都颇有收获。

    - 工作：还是后台统计那些东西，主要是处理了售后统计和dashboard，还有自测修bug。
    - 自身成长：1、这周最主要的是进一步明确了个人OKR，怎么做到YAML里。2、本周前两天还写了一个油猴脚本，之前不是在kimi、豆包、copilot之类的地方存了大量chat嘛，都没处理掉。写了个脚本，用来批量打开，批量删除。3、渲染books和goods

    总评：本周工作上还是做的少了点，自身成长上也没啥突破性进展（可能除了搞定OKR？这个确实挺突破性的，把很多东西都理顺了）。下周是年前最后一周了，也是这个后台项目的关键节点。需要把更多精力放在公司项目上，希望提前交付，留出足够时间fix问题。至于自己的东西，下周也就搞一下OKR和gh的渲染，晚上下班之后随手搞搞就行。


---
# w4 (2025-01-20 -> 2025-01-26)

#- task: 按照老板的需求，重新写一下这个dashboard # 23号一定要交付掉，不能再拖了
#  date: 2025-01-24
#  pid: gs
#  sub:
#    - task: 理解需求，定义ck的table
#      date: 2025-01-20
#    - task: 写后台task
#      date: 2025-01-21
#    - task: 画前端页面，希望在下午6点前交付
#      date: 2025-01-22 # 一直到现在，22号下午快4点，才把这些东西弄完
#    - task: 再优化一下之前的那个 task_dashboard，还有点数据没处理干净。或者说double check一下这块的代码，防止出什么问题
#      date: 2025-01-22 # 预计半个小时搞定
#      sub:
#        - task: 赔付单统计 金额有问题
#        - task: 违约单统计 金额有问题
#        - task: 追款单统计有问题
#    - task: dashboard的UI又调整了，按照新需求重新画
#      date: 2025-01-23
#      sub:
#        - task: 聊需求，对齐
#          date: 2025-01-23 # 从2:40到3:30聊完。在gitlab里记录了一下相关问题，就到4:10了。
#        - task: 理解需求，给ck的st_dashboard表加字段，查明白这些数据都怎么查出来
#        - task: 改task_dashboard.go 把新加字段的相关数据刷到ck。搞定task
#          date: 2025-01-23 # 7点开始搞，8点出门吃个饭，8点半回来，开始继续搞，预计10点半搞完
#        - task: 先把新加的那两个字段 in_chan和out_chan先搞定吧，然后再搞其他的 # 智障了
#          date: 2025-01-24
#        - task: 开始搞UI，24号晚上7点之前发出去
#          date: 2025-01-24 # 早上发出去

# 2025-01-20

#- task: 把blog迁移到 jahir.dev
#  pid: docs
# 取消了，为啥呢？因为其实没必要折腾blog，之前就说了，blog只是docs的附属工具，只是用来把一些东西放出去。并且因为我的Monthly里的内容实在太多，切换了其他blog工具和theme之后，展示效果一定没有现在这个好，所以也是白费功夫，实在没有必要。并且我现在把这些data都挪回docs了，那就在docs这边把这些东西都渲染好就ok了。

#- task: 重新恢复blog到之前的样式，删掉books组件什么的
#  date: 2025-01-20 # 早上处理的这个。以及
#  pid: docs


#- task: 早上把chrome里现在打开的tab都清掉 # 本来是应该昨天做掉的事
#  date: 2025-01-20 # 现在是15:15，处理掉了


#- task: 继续优化docs里goods渲染
#  date: 2025-01-20
#  pid: docs

#- task: 画一下我现在的docs相关workflow
#  pid: docs
#  date: 2025-01-20

#- task: 本地用 Portainer 代替现在的docker-desktop # 不打算用各种GUI来管理了，并且Portainer这种web端管理，可能以后还用得到。
#  date: 2025-01-20


# 归根到底的核心矛盾在于，任务管理和日程管理是两码事。而我的 task-okr-xxx 工具就是用来解决这个问题的

# 2025-01-21

#- task: 把这个裤子退款了？买条必迈的。其实现在我对裤子的要求很简单，宽松、不需要腰带、速干面料（最好微防水）、最好是卡其、军绿、枪灰之类的偏休闲的配色
#  date: 2025-01-21
#  okr: money#saving
#  pid: minst



# 再次使用spotify，为啥现有的音乐平台都没有这个功能呢？我之前

# 2025-01-22

#- task: 把简历弄到rendercv，就是那个yaml写简历的网站
#  date: 2025-01-22


#- task: 折腾一下deepseek+cline，还有字节刚刚推出的Trae
#  date: 2025-01-22

#- task: 把美团的6-5的券用了
#  date: 2025-01-22


#- task: 晚上下班之后挑挑裤子
#  date: 2025-01-22
#

#- task: 今天晚上11点就回去，11点半到家，12点前睡觉
#  date: 2025-01-22 # 连续3天晚上都是1点半之后才睡觉，现在还不睡午觉，真的顶不住。状态倒是还维持得住，但是也没多好。

# 晚上7点多又在搞blog搞了差不多1个小时。写了一下午公司代码，想着歇一会，就去搞。其实blog对我来说，并没有那么重要。甚至不搞blog，直接塞到docs里也完全没有问题。并且之所以搞这个，是因为

#

# 2025-01-23

# What did I do today? emm,

# 2025-01-24

#- task: 中午去老乡鸡吃法，早去早回 # 把鸡汤券和小鸡炖蘑菇券用了 # 中午去那家吊龙米线了，感觉挺不划算的。不是大前天弄了个那家的¥0.1的冰豆花的券吗？然后就看那家还有其他什么吃的，就点了个¥19.9的米线套餐。真的，份量太少了。基本上只有半碗，米线也只有几口。反正完全不值这个价格。回来路上又去华莱士把那个¥2.5两个汉堡的券用掉了，才不饿。
#  date: 2025-01-24

# 在公司真就应该买个玻璃杯之类的，现在买的这个stojo折叠杯。

# 鸡骨泥

# 2025-01-25

#- task: 清理一下scratch
#  date: 2025-01-25

# 2025-01-26
# 明天再上一天班，就放假了。安排一下春节假期相关task

#- task: 晚上8点半就回去，9点之前到大鼓米线，把那个鸡排的券给用了 # 没有用成
#  isX: false
#  date: 2025-01-26

- task: 2025w4
  date: 2025-01-26
  pid: PWR
  review: |
    主要是下周回家之后的安排：到家当天就是除夕夜，之后就是大年初一。真正能做事的也就周四到周日，这4天。估计跟朋友聚会也要占个1到2天。也就是说大概只有2天时间。也就是按照原计划，把家里的书整理整理，其他事情是想多了。

    可以看到本周就2个事：1个公司的，1个自己的。公司的事情就是dashboard。自己的事情就是给docs加个admin。




# w5 (2025-01-27 -> 2025-02-02)

# 回到docs是否要做成admin的问题：
## 有个核心问题之前没考虑到，就是admin基本上都不支持移动端
## 图片怎么处理？ vite 配置 assetsInclude，可以直接访问
# 想到一个挺有意思的事情，如果现在又转去把docs做成admin的话。其实就又变成了某种优化版MALI。这就是“螺旋式上升”啊。看了一下，之前的MALI里只有物品管理、rss管理、APP管理、日常生活，这4个。现在docs里的相关东西可太多了
# 其实可以把这些东西简单理解为mock数据嘛。所以我打算直接把这玩意做成build时，把YAML处理成JSON，然后就像处理API一样处理这些数据。 # 相当于解耦了。之后无论我YAML怎么写，只要JSON结构不变，相关的前端代码就不用变。复杂度就降低了。
#- task: 找个admin，把docs迁移过去
#  pid: docs
#  date: 2025-01-26

#  sub:
#    - task: 找个靠谱的admin # 找了半天，也搜了一下“后台用vue还是react”，发现做admin还是vue比较靠谱。不谈vue相较于react的性能，对新手来说更好上手之类的。react做后台其实就两个方案，ANTD和MUI，但是这两种UI风格其实都挺丑的，至少说不符合国人普遍审美。并且react admin的生态确实比较差。相比之下vue做后台的方案就很多了。
#      date: 2025-01-21

#    - task: 实现这个“配置即代码”的YAML转JSON # 想了一下还是应该用 golang 来写这个东西，为啥呢？因为一个是我对 TS 还是不是很熟嘛，另外这个东西其实是因为涉及到这个文件操作嘛，它实际上用的是 nodejs，现在很大问题，现在就是昨天，昨天写的时候很大问题就是在于说的这个，在于说就是他现在这个 TS 跟 nodejs 的这个，就 ESM 跟那个 commonjs 的本身这个问问题，对吧？没有解决导致的那个 bug，其实这会还挺花时间的。所以我现在用 Golang 写的好处在于什么？就是首先就是之前的什么压木解析，这这些东西，就这些，这这几个东西本身在压木解析之前已经做掉了嘛，另外一个是它的这个 mark 当的渲染，我现在本身就做成一个，它本身是已经做成抽象类了，对吧？然后我去既然主要要加一个 JSON 的话，我其实再去加一个那个 yaml yaml to，对，之前有一个 yaml to markdown，现在我再加一个 y yaml to JSON 就可以了，对吧？然后这是其一，其二是，其二是其实就是我之前一直之所以想用 TS 去做，就是为了把他的这个工具，工具跟这个项目放到一块，现在想想其实这个这个设计本身就是不是不是不是一个好设计，其三就是之前放到一块的很大目的是为了便便于调试嘛，就是这边写完 TS 代码之后，那边调试是比较方便的，但是刚刚想了一下，其实用 golang 写调试也不不难，因为我们生成的其实是一个中间态的这么一个文件，只要这个文件是 OK 的，其实跟最终渲染的结果，只要保证这个中间这个文档是 OK 的，这个文件解析并写入，最终写入这个文件是 OK 的，那么最终的这个结果就是 OK 的。所以说不不不存在什么调试问题，就是这么三个原因吧。用 golang，golang 我毕竟经验会丰富很多嘛，所以说可能做这个东西也会比较顺手一些。可能如果用 ts 来做的话，我这个工作量可能至少在两天甚至三天这么一个工作量，那么用 golang 的话可能就一个上午就做完了，跟你说一下吧，然后明天明明天再弄吧。
#      sub:
#        - task: okr # 渲染OKR的yaml文档：自己在docs里，通过把新搞的这个 OKR.2025.yml 渲染成类似 飞书OKR 这样的样式。可以预计到，如果要渲染okr的话，现在这个task一定有大量脏数据要修改 # 这玩意估计要弄一整天才能搞定，预估要5-6h。这玩意直接用ts写就行了，类似上周给blog写的books统计。
#          pid: docs
#          date: 2025-01-20
#        - task: gh.yml # 渲染gh.yml，今天晚上快速出一版，看看大概效果，如果不符合预期，就fail fast。如果符合预期再做点优化。这个可能需要移动端适配做的比较好。 # 根据qs里的q的数量，和x.yml里标注出来的repo做个对比，如果基本匹配，就把这个做成配置项。
#          pid: docs
#          date: 2025-01-20

#    - task: refactor成两个项目了

#    - task: 要综合调整一下docs里面的两个前端项目的ghac里的build。打包成admin和docs两个项目。在admin分支调好了，直接merge到main分支。
#      date: 2025-01-26

#  des: 一开始想的是直接用ts做到docs项目里。纠结了好几天，这几天公司的事情也很多，一直没时间搞这个，只能没事时想想怎么实现，一直到周四晚上加班到挺晚的，晚上散步时想到，不如就直接做到docs-alfred里面好了。今天搞了一会就弄完了，直接做了个通用方法来把YAML直接转JSON，什么逻辑都没加。这块比较有难度的地方在于，加了个工厂模式+模板模式的实现。



#- task: 家里的书
#  date: 2025-01-31
#  sub:
#    - task: 把家里打算处理掉的书先弄出来，拍个照片
#      date: 2025-01-29
#    - task: 古诗词：把之前笔记本里的古诗词，整理到books.2024.yml # 希望今天早上能搞完。下午开始处理昨天已经整理出来的，几本可以快速解决掉的书。下午5:30，终于弄完了
#      date: 2025-01-30
#    - task: 家里的书：把家里的书扫一遍，整理到books.2025.yml # 没啥用的书，都应该清理掉。
#      date: 2025-01-30
#      sub:
#        - task: 把这两天收拾出来，不要的书，都扔掉
#          date: 2025-01-31
#        - task: 把昨天收拾出来的书，在多抓鱼上出掉 # 才卖了¥19.4 也还可以，
#          date: 2025-02-01
#    - task: 先把目前月刊里的几本书都处理掉。预估1天。
#      date: 2025-01-30




# 2025-01-27
# 本周是在家的一周，正好到2月3号嘛。本周主要就是看书。主要任务就是把月刊里的几本书看完，把家里的书看完。至于产出，如果能再写篇blog，那就太好了

#- task: 回家。下午2点到2点半到浦东机场就行，坐地铁90min，那就是1点出发。12点出门，吃个饭。
#  date: 2025-01-28

# 最后还是没发。怎么说呢？也征求了我爸和我妈的相关看法。最后还是觉得自己
#- task: 春节给所有微信联系人群发短信 # 人生以来第一次，还是要跟人建立“微联系”
#  date: 2025-01-28

# 2025-01-29 大年初一。周三了。今天回老家。晚上回来跟LTY聚下。

#- task: 和LTY吃饭。大年初一，从老家回来之后，和LTY约了一下，晚上吃了个饭，顺便在铝厂转了转。
#  date: 2025-01-29

# 2025-01-30

# 2025-01-31

# 美团 金币提现 就是纯骗局
# 19.96起30w，19.97起40w，现在19.98，换一分要50w（按提示文字估算）
# 而看视频5min才3w金币（并且要一直手动刷，否则计数就停了）

#- task: 把昨天收拾出来的两个耳机都出掉，还有那个TPLINK的路由器也都出掉。加起来大概能有个450左右（两个耳机都是200左右，路由器50）。
#  date: 2025-02-01



#- task: IDE现在js跳转有问题，需要重装goland看看 # 重装之后还真就好了
#  date: 2025-01-31

# 回顾一下，一整天也就早上弄了会。中午12点快1点，ZXN和他爸从西安回来了，出去一起吃饭，吃完饭就5点多了。刚刚到家没几分钟，又有其他人叫他爸吃饭，就又跟着一起去了。弄到9点多才回来。昨天真喝了不少酒，100ml的酒杯，中午一杯，晚上一杯半。加起来差不多5~6两。

#- task: 和ZXN和他爸他们一起吃饭。和ZXN聊了聊。 # 从中午吃到晚上9点
#  date: 2025-01-31

# 2025-02-01
# 今天就两个事，早上把昨天没弄完的admin后台几个列表处理好，还有前两天没处理掉的书。下午赶紧开始看那几本技术书。三号要搞公司的东西。二号要把周刊结束掉，发出去。估计没法弄nameit了。
# 复盘：早上列出来的两个都没弄。

# [阿里云服务器可以用来干啥？ - 开发调优 - LINUX DO](https://linux.do/t/topic/378241/7)
#- task: 在本地搞一下青龙面板薅羊毛，如果好用的话，就部署到vps（但是感觉也用不到，到时候直接弄到我mac的docker里就可以了） # 主要是VPS里也就一个rsshub还算是需要部署，其他真就毫无必要。1panel, QD,
#  date: 2025-01-29

# 2025-02-02
# 今天要先把昨天的两个东西弄完。
# 中午跟张岚先阿姨他们一起吃了个饭。

#- task: 弄下vv的表情包
#  date: 2025-02-02
#  isX: false
#  des: 没搞，wx不支持批量导入表情包，vv的表情包又太多了，100多个，逐个手动导入太费劲。找到了几个automation工具，但是也都不算成熟，ROI太低了，没啥意思

#- task: 删掉了之前task的imp这个key
#  pid: docs
#  date: 2025-02-02
#  des: 之前imp是用来标识某个task是否重要的，但是现在基本上可以用task的sub数量来判定了，并且还更准确。加个imp属实是多此一举了。

# 想了一下，应该把这个加到task里。为什么呢？因为既然这个事情占据了一下午时间，为什么不加呢？并且确实也是个经历，也有一些思考，学到一些东西
#- task: 中午和张岚先阿姨、王振龙叔叔他们一起吃个饭
#  date: 2025-02-02
#  review: |
#    真的挺多感慨的，先说一些我表现比较好的点：
#
#    做的不好的点：我应该主动出去结账的
#
#    这次回家，感觉比较深的一点。我家的收入情况确实是已经不如社会平均水平了，但是我爸妈还浑然不觉，或者说自欺欺人。我爸妈已经基本上完全跟社会脱节了。

# 2025-02-03
# 春节在家的last day了，原本预计的几个事情都没做：1、看书。2、看技术书。3、做docs（把admin里的几个数据都处理好）。4、把vps上线。5、写完周刊，发不出去。
# 今天也可以给整个春节假期做个复盘了。
# 今天要把 1、2、5 这三个事情做完。4也要做完。这几个都是杂项，比较好处理。我这几天遇到的一个误区也是，3其实

#- task: 把vps项目重新上线
#  date: 2025-02-03
#  sub:
#    - task: 处理一下vps这个repo，几个问题：1、以后backup的数据都弄到backup分支。2、把backup.sh做成pre-commit的task 3、
#      date: 2025-02-02
#      sub:
#        - task: 试图压缩vps这个项目
#          des: 除了直接git-filter-branch以外，还真就没啥好办法。最后还是。
#
#    - task: 买个clawcloud 1C1G的机器，搭下vps几个服务 # 根本买不到 $7/年 的机器 # 2025-02-03 今天才发现是要自己手动去填写code才能买$7的机器。但是用了招行的visa卡无法支付，真TM傻逼，这网站也是够难用的，editor连图片复制上传都不支持，只能点击上传，还不支持多文件，只支持单文件上传。
#      date: 2025-02-03
#    - task: teamspeak
#    - task: ZeroTier
#  review: |
#    从下午一直搞到晚上快9点才弄完。这次没用centos，专门一开始用了archlinux（因为想过之后想把开发环境也切换到arch），但是玩了半天才发现arch确实是一个专供desktop使用的distro（我找到的ansible-arch-linux）。然后就重装成Rocky9.5了，基本上和centos是适配的，除了需要换成dnf（而不是yum以外）。
#
#    我也反思了一下为啥，即使专门写了个ansible项目，每次重新装机还是手忙脚乱的，就很烦。我希望可以做一个做一个足够stable（比如说能够兼容各种错误情况，而不是遇到报错，就注释掉，或者自己手动调整），并且可以通过在taskfile里各种配置就直接调用各种ansible
#
#    这个1c1g的机器跑了3个服务，CPU利用率才4%

# 2025-02-04
# 真正的last day，也是在高铁上度过的一天。早上10点的车，到晚上10点才到上海。经停太原、郑州、合肥。倒是没啥问题，在车上玩一天MBP嘛，对我来说是小问题了。
# 晚上复盘一下，预期的玩一天MBP，结果也就早上玩了一会，到12点左右，一共不到70min。主要是网太慢了，一个隧道接一个隧道，出入隧道都没网（我没用wifi，直接用的流量），另外，玩了一会有点晕，就开始睡，结果基本上就睡了一整天，后面玩玩手机，刷刷bz视频。到了9:14就到上海虹桥站了，坐机场线到三林南（花了¥11），又叫了个网约车到住的地方（三林民宿，花了¥15）。想着能快点到家，但是也得40多分钟。实话说真不便宜，坐地铁大概是60min左右，合计5块钱左右。还真就是做地铁更划算。

#- task: 2025w5 （以及春节假期复盘）
#  pid: PWR
#  date: 2025-02-04
#  des: 昨天晚上梳理了一下，整个春节假期除夕夜晚上才到家，后面几天的安排其实还挺有节奏的，正好是初一、初三、初五基本上都在外面。初二、初四、初六这三天在家里。也正因如此，假期安排的几个事情，基本上也都没做完。初一回老家，初三和张晓楠他们吃饭从中午到晚上，初五和张岚先阿姨他们一起吃饭，也是一下午。初二整理书，一整天。初四稀里糊涂啥都没搞，什么都弄了点，但是没有一个完成的。初六（也就是昨天）买了个服务器，然后把vps上线了。

# 2025-02-05
# 春节假期结束后上班第一天，优化那个dashboard嘛。
# 我没有任性的资本。这边这个老板是挺sb的，小气、没格局。

#- task: 把 dashboard 优化一下，预期耗时2天
#  date: 2025-02-05
#  sub:
#    - task: 拆API，每个tab都单独拆一个API

# 2025-02-07
# 刚刚才发现今天已经是7号了，已经是上班的第三天了。真的都有点恍惚了。在家待了几天，真的太舒服了。昨天下午到今天中午，把之前要的那个直播统计的导出，弄了一下。之前一直没有这个嘛，昨天写了一下。希望今天能把这个dashboard的实时弄完。也是一个task + 3个API（就是以date为维度的那个，再来一套）
# 今天真得复盘一下这几天做的东西，有哪些收获。
# 明天再上一天班，周日休息，坚持坚持

#- task: 直播统计 相关问题
#  date: 2025-02-07
#  pid: gs
#  des: 其实一开始弄错了核心需求，导致昨天还
#  item:
#    - 查找表中重复的oid数据？如果是 oid 和 status 组合重复的呢？那如果是 oid、audit_time, status 这三个字段的组合唯一索引呢


#- task: 处理一下春节假期带回来的那几张纸，什么论语、诗词之类的
#  date: 2025-02-07

#  sub:
#    - task: 添加并且格式化了 poem.yml, lyrics.yml。其实就是把之前在 books.yml 里的古诗词格式化了

# 2025-02-08
# 周六，春节调休，不过我们本来就要上班。
# 今天真的想10点左右就回去。


#- task: dashboard 实时 相关数据
#  pid: gs
#  date: 2025-02-08
#  des: 其实从dashboard到这个live统计及导出，做起来有点吃力，或者说心累。归根到底，还是一开始思路没理清楚。当然，最后的这个正确的思路确实也不可能说一开始就考虑到这些情况和corner case，做出正确判断。当然，这个部分其实就是经验问题了。当然，话说回来，我也没想到我本地跑只要1s多的API，到了生产环境只是最后返回的数据从2.xMB到了4.xMB，API的RTT就到了29s。就像我前两天跟HR说的，“一开始50多个字段，就是1个API把数据全部拉出来的。后面改需求了，需要扩充到130个字段，我就懒得再拆API了，我肯定想的是直接在原来的API上写就完事了，肯定想不到只是扩充了字段，加了平台/供应商这个数据维度，会多出来这么多麻烦事啊。当然，从结果来看，肯定当时就应该意识到这个问题的，果断选择拆分，而不是在之前的API上继续堆数据”。包括
#  item:
#    - 我有个golang编程问题，如果多个struct有一些可以复用的字段，我能否封装起来，然后让这些struct直接调用呢？需要注意的是，我想直接inline，也就是作为这些struct的一级，而非某个key下面的 # 善用匿名嵌入struct来增加代码复用性 named embedding vs anonymous inline embedding
#    - ck的 时间类数据类型（Date、Date32、DateTime、DateTime32 和 DateTime64）， 时间日期函数
#
#    - 这个说白了也是个tradeoff。如果处理超时，那就一定会遇到数据不全的问题，如果不做超时机制，那task就hang住了，走不下去。那通常建议怎么处理这种问题呢？
#  sub:
#    - task: dashboard_rt task
#      sub:
#        - task: 处理 aggr 聚合all_all数据，但是全部返回都是0的问题 # 查了快2h，说白了还是指针引用和值引用的问题
#      review: |
#        正确的思路是，先修改一个task，改成现在这种以hour为维度的，刷到ck里，并确保数据正确。然后让cursor仿照这个代码对其他的几个Query进行实现。最好还能看下怎么复用之前的Query，以提高代码复用性。昨天下午用cursor搞了一下午，一致都是让他把9个一次性生成，然后run task，很明显这个思路不行。很耗时，并且几乎没有准确性。
#
#        记录一下，昨天还遇到的一个问题。就是这里的这个task，因为query数据量太大的，可能是min级别的查询时间
#
#        我直接用generics来复用了 task_dashboard 和 task_dashboard_rt 里的代码，比如说里面的插入ck，。这类代码复用方面的解决方案，无非几种：泛型、接口、反射、代码生成。在这个场景下都不如直接用generics。
#
#        关于cursor的一些感想：1、刻板印象中认为“自己实现一遍，然后让AI根据我们自己写的代码进行实现/修改”，其实从我自己的实际使用经验来看，这个很大程度上这个是错的。还是像samuel所说“”，真实情况很可能是反过来的，正确的打开方式应该是，跟AI多轮chat，确定最佳实践之后，让他自己实现一次。然后我们可以高效且准确地结合IDE等现代化开发工具，在其他地方修改。因为这种多次实现的往往是多个文件多次跳转，要读取大量代码的场景，并不是AI的长项，“一旦超过1000行代码，AI就糊涂了”。2、
#    - task: 仿照date作为维度的3个API，现在这个以hour作为维度的，同样需要3个API
#    - task: 渲染前端样式

#- task: 复盘一下春节开工以来遇到的一些技术问题，做个review。注意直接标注在我的task里（通过qs和review）
#  date: 2025-02-07
#  pid: gs


# 2025-02-09
# 周日了
# 今天下午两点多到的公司，把昨天晚上那个 task_dashboard_rt 需要



#- task: 2025w5
#  date: 2025-02-09
#  pid: PWR
#  des: 本周干啥了？本周是春节节后的第一周，真的特别想回家。本周主要时间和精力都聚焦在工作上，把


---
# w6 (2025-02-10 - > 2025-02-16)

#- task: admin
#  pid: docs
#  date: 2025-02-10
#  sub:
#    - task: 在admin逐个页面渲染这些JSON. 这几个页面本身都不复杂，就是一个list，最多有个查看详情。目前只需要做这4项。
#    - task: 做gh.yml的时候，需要注意结合docs-alfred的跳转 # 其实现在这种做法，跳转更清晰了。就两种情况：是否有sub/rep/rel，如果没有就跳转type筛选后的列表，如果有就直接打开该主repo的drawer
#    - task: 需要单独给books, goods这几个单独做个task # 还以为要加个parse的key来指定解析yaml的方式，看了一下才发现，归根到底，问题在于
#      date: 2025-02-02
#    - task: 想了一下，也算是昨天的一个感想，其实gh.yml还是需要在docs里做个yaml2md的。因为现在
#    - task: 优化admin
#      sub:
#        - task: tv 1、需要添加筛选项：类型。2、描述这栏太窄了
#        - task: movies 1、需要添加筛选项：类型、导演。2、综合调整一下width
#        - task: 研究一下怎么才能默认expand这个sidebar
#
#    - task: 【把blog也弄到docs里】这样更方便。反正这三个都不会开源了。
#      date: 2025-02-09

#
#    - task: 【自建umami】基于上一点，把docs, admin, blog都放到一个repo里的话，我想到，是否应该自己部署一个umami（就启用GA了）。查了一下，就看到sealos，感觉还不错，这种应用商店运维起来就很好用了（感觉比1panel这种运维面板好用）
#      date: 2025-02-10
#      item:
#        - rockylinux 执行chsh时报错 command not found，怎么处理？ # 需要先 dnf install util-linux-user，因为RHEL系系统（如Rocky Linux/AlmaLinux/CentOS）默认未安装chsh命令
#
#    - task: admin 里渲染那些个JSON
#      date: 2025-02-12
#      sub:
#        - task: movies
#          date: 2025-02-12
#        - task: fc2
#          date: 2025-02-12
#        - task: 渲染poem和lyrics # 将昨天添加的古诗词的yaml做到admin的渲染中，在渲染结果里添加词牌名、作者，并将这两项渲染到一个list里，同时设置作者和词牌名为筛选项。[2025-02-11] 想了一下，还是应该拆分出来。毕竟古诗是没有词牌的，不是吗？
#          date: 2025-02-12
#        - task: 看看能否直接把docusaurus的这个docs直接整合到soybean的这个admin里
#          des: 直接在route里通过url引入这个文档的url就可以了。可以理解为iframe
#        - task: books
#          date: 2025-02-13
#        - task: goods
#          date: 2025-02-13
#        - task: gh
#          date: 2025-02-13
#        - task: okr
#          date: 2025-02-13
#
#    - task: docs-alfred
#      sub:
#        - task: gh
#        - task: books
#        - task: goods
#        - task: okr
#
#  des: 报了一个 ERR_PNPM_NO_IMPORTER_MANIFEST_FOUND 的错。昨天下午过来，也就搞了三个东西：1、把blog迁移到docs里面。2、把admin从之前的pure-admin换成了现在的soybean-admin。3、处理前两点，commit时一直报错 ERR_PNPM_NO_IMPORTER_MANIFEST_FOUND，搞不定。最终解决方案是：一开始以为是添加admin导致的，经过验证（删掉了admin，直接commit仍然报错）发现，这玩意确实无解，也跟admin没啥关系。最好的处理方法就是直接--no-verify commit 掉，保证main是最新代码并且可以work，之后把本地项目删掉之后重新clone就可以了。

# 2025-02-10

# 2025-02-11
# 下午提离职了，也是工作这么多年来，第一次正常走离职流程。很顺利。
# 没啥好说的，下午HR找我聊1月绩效，本来其实还是想再混段时间的。但是1月绩效只给了我59.5（70以下都是，我去年12月绩效是90分）。我之前就说了“如果转正到不了18k，我肯定走了”，这边能给的18k，是我预期薪资的下限，现在给个59.5的绩效，肯定转正之后的薪资是给不了18k了，那我也遵循自己的想法，说走就走。正好现在金三银四，出去看看新机会了。实话说要不是去年年底，着急过年找个工作，也不太可能去这家。在这边两个月，拿了合计8k+17k，也就还行吧。至少手头算是有点钱了。从2023年年底到现在，手头终于又算是有点钱了。
# 希望月底前能找到新工作。两个事，1、看看15号能发多少钱工资，再决定是否要买个新款MBP（买了新款MBP去星巴克） 2、留够至少两个月的钱（住宿800+吃饭900*2，合计3000）

# 2025-02-12
# 辞职后first day，今明两天把现在docs的这些东西彻底处理完，之后就可以开始背面试题了。


# 2025-02-13
# 那今天早上其实还真就颇有收获，反正是 10 点钟才到，哎，不是 9 点，9 点半吧，9:20 到的那个到的图书馆，然后然后搞到十，12:20 吧，大概十，十一十二三个小时，里面做了哪几个东西呢？其实先是把他那个就那几个渲染做一下 FC two 的渲染，然后包括主要是搜索，对吧？列表渲染，搜索这部分其实就是去学一下他的那个 vben 那个里面那些基础操作嘛，确实封装的蛮好的。然后呢，然后就是大，大概是 12 点左右吧，然后一边听着那个双宇合心跟老瓜的那个一个视频，一边搞的这个都挺有收获的。然后 12 点左右我想把它就是 admin 那个重新给融合回我的 Docs 项目里面去做了做，然后呢，一直到了融合回我的 Docs 里面去，然后这个这部分就要去跑那个 precommit 嘛，然后跑 precommit 大概用了半个小时，里面有一些东西要调整的，调整调，然后然后这是早上的，然后下午的话，我打算就是继续把这个弄完嘛，然后然后下午可能就要下午可能还有几个简单的那个都先渲渲染完，渲渲渲染完之后，然后然后我下午要整个去把那个他的那个之前 Docs or freed 里面的那个东西做掉，对吧 Docs or freed 里面的那个生成 Jason 的那个东西给处理掉，处理掉之后，然后就把这个渲染彻底做完嘛，还留，还留了几个，今天早上已经在上面打了发了，已走了，然后今天下午处理掉，今天这个事情就做的是比较圆满的。然后顺便就是把那个目前加的几个 task 给给做掉。

#- task: 把books和goods从之前的拆分文件，又重新弄成一个文件了
#  date: 2025-02-13
#  pid: docs

# 之前给books弄了个metadata.yml，用来管理type和tag，其实现在一点用没有。现在弄得更简单了，基本上现有的type就4种：Coding, 社科, 网文、经典小说。至于之前的tag，就直接做成array了。相当于每个type下面的tag就当作所有book的父tag了。

# 前两天才发现，叮咚买菜上，一直给我发的59-12的券，不是说不能用来买酒水吗？结果其实可以用来买啤酒，但是不能用来买可乐。并且很有意思的一点，我一直在叮咚上买的这个 光明啤酒超纯 500ML*4，售价¥10.9，整箱24瓶（也就是6提，按理说应该是10.9*6，但是整箱买是¥59.9，并且不能用券），前面按提买是可以用券的，用完券才¥8.8/提。不清楚是定价失误，还是说这个就是定价策略。

# 2025-02-14

#- task: 有哪些处理我的文件夹的图片文件夹的一个方法？我打算都弄到alist上，把alist当xxx用
#  date: 2025-02-14

#  sub:
#    - task: 1、删除所有本地图片之后，压缩git库。2、修改之前docs里的图片URL地址为R2的 # docs从之前的994个commit到现在的976个
#      date: 2025-02-14




#- task: 优化task结构，整理，并且优化
#  date: 2025-02-14
#  pid: docs

# 其实前两天处理

# 2025-02-15
# 周六的早上，早上7点前就起来了，半睡半玩手机，打了一发，又眯了一会到8点20，然后洗了个澡、把衣服扔到洗衣机，出门去三林图书馆。9点20到，比以往还晚了些（结果图书馆的人比预期的还少很多）。今天

#- task: 【还贷款】美团小贷（总计11k）
#  pid: minst
#  okr: money#loan
#  date: 2025-02-15
#  des: 昨天1月工资到账差不多18k，就顺手还了美团1700，然后就有美团贷后人员给我打电话，说有减免利息，另外8折结清。“一共减免2275，其中利息510，罚息417，另外本金也减免1348”。之前剩下没还的也就9638了，已经预期279天了。想了下就直接还掉了。合计要还7363。昨天还了3000，然后浦发卡限额了，今早还了4363。下午给出结清证明。



#- task: 早上清理//整理一下手机里的图片（大概160张，乱七八糟的啥都有，全部删掉）
#  date: 2025-02-15
#  des: 处理到还有71张图片，剩下的都是之前看书的截图，现在处理不了。按照安排的schedule，下周要看书，下周清理掉剩下的。


#- task: 去掉vben的整个登录页面，直接用cloudflare-pages-auth作为密码更方便
#  pid: docs
#  date: 2025-02-14
#  des: 这块有个问题就是，functions必须要放到 apps/web 目录下面，才生效。这个就是monorepo的问题了。

# 2025-02-16
# 想了一下，还是把那个，想了一下，还是把 mock 的那个服务单独部署一下，然后这个应该是最简单的操作了，明天早上把这个东西给搞定，然后下午，然后这是第一个。第二个呢？就是把那个本来想弄的那个，然后弄完这个之后 TOX 这块还有一个东西就是要去把那个，就是那个 OKR 跟 PID 那个给渲染出来，对吧？然后这两个东西中间去插一个任务，就是把那个这两个东西中间去插一个任务，就是把我那个本地的可能有 20 个 Scratch 处理掉吧。这个东西预计可能要大概两个小时左右，我 90 分钟到两个到 120 分钟吧。然后那个前面那个可能第一个部署上线，那个可能大要大概 60 分钟吧，然后后面实现这个预计可能在，可能在 3 个小时，3 到 4 个小时吧，明天就这三个东西。

#- task: 看一下为啥现在mock数据在生产环境跑不了 # dev环境能跑是因为会启动时会通过nitro启动一个mock HTTP server，需要注意的是，nitro是起了个HTTP服务。build时就不会打包这个mock服务，当然在生产环境跑不了。所以这里如果不去考虑什么第三方服务的话，无非两种方案：1、把这个nitro服务直接部署到cf上，然后把url在web的生产环境设置为VITE_GLOB_API_URL，这是改动最小的方案，但是昨晚搞了半天也还是没把nitro在cf上跑起来。也有点棘手的。2、像之前在leke的那个arch-web一样，mock直接使用vite-plugin-mock，把mock直接集成到web服务里。这样后面维护也简单，也是最契合我这个项目的方案。但是这样就有点麻烦了，工程量略大。
#  date: 2025-02-15
#  sub:
#    - task: 将mock的服务单独部署 # 早上想了一下，决定选择 方案2，但是我也懒得再一个一个把nitro的mock再弄到vite-plugin-mock了，看到vben的v2本身就是用的vite-plugin-mock，就决定直接用vben的v2了，也就是不是monorepo的版本。会很省事。倒也不必觉得有什么可惜的，
#      ests: 60min



#- task: 下午整理一下，之前弄出来的 scratches 文件夹，一共19个文件（之前13个，现在11个），预计要60min到90min
#  date: 2025-02-15
#  pid: docs
#  ests: 90min # 现在是15:10，预计16:30之前弄完。其实一直到晚上8点才弄完（当然中间搞其他的去了）。

#  des: "挺乱的，分成两部分嘛，一个是在入职leke之前没来得及整理的，另一部分这段时间堆积下来的。之前买来的及整理的有：1、之前把diary从md变成了yaml格式，从2023年底到2024年初的部分当时没弄完的内容。2、之前想搞个梳理docs的yaml，也就是docs-record.yml。后来不是给所有task搞了一个统一的key嘛，通过 `pid: docs` 就可以直接筛出来所有这些record，所以就不需要这个了。3、一些乱七八糟的社科相关内容。也是之前md格式下随便写的那些（现在类似内容都直接放到月刊里了）。4、mysql 45讲的那个md，放到docs里了，自己再看看，再整理到gh.yml，这玩意不容易搞的。"

#- task: 复盘 dashboard 的实现 # 直接做到/leke的qs里了。原本没啥思路，想了一下，找到一个很好的角度。从
#  pid: gs
#  date: 2025-02-16
#  des: 大概这个就是leke这个pid的最后一个task了

#- task: 把体检报告存一份，后面都懒得体检了，也能省点钱。存到google drive了。
#  date: 2025-02-16


- task: 2025w6
  date: 2025-02-16
  pid: PWR
  review: |
    这周过的有点快，又周日了。本周周二（11号）下午提的离职（离职倒也没啥好说的，待的不爽就走呗，没啥好说的。就是下午），然后就是12、13、14、15，4天。今天16号周日。这几天白天都在三林图书馆，处理之前一直想搞的那个admin管理个人数据的东西。现在还是没弄完，实话说还是前端水平不行，这点东西都弄了好几天，磨磨唧唧的。

    一点小感想，很多事情都是“小马过河”，周二离职了之后，就在想，现在住的地方也没有桌子，想写点代码都没地方，找了下附近的咖啡店、自习室、“加装上班公司”之类的，都不便宜（或者说都要钱），然后搜了一下附近的图书馆，看了下高德地图上的评价，评价都挺差的，预期就放的比较低，想着去大概看看，不行就再找地方。结果这个图书馆真心不错，可以说是远超预期。有充足的电源插座、热水、卫生间、舒服的桌椅，环境卫生，还很安静。不禁感叹真的是“小马过河”了，很多东西自己不去尝试、体验，真看真感觉，道听途说真的永远无法获得真知灼见。

    至于本周做了的事情，



#- task: 电子琴 # 可能过两年再说了。[2025-02-16] 算了，懒得搞了，学什么乐器的ROI都很低，并且电子琴不便携，真的没啥意思。搜了一下发现，乐器就没有便携的。真的没啥意思。
#  date: 2025-02-16
#  isX: false

#- task: 找找办法看看怎么搞个github学生认证账号，copilot免费账号有限额（只有2000 completions and 50 chat），现在github pro要 $10/Month 有点太贵了 # 可以看看有啥其他解决方案吗？只要能免费畅用sonnet模型就可以。[2025-02-16] 结论：在ds可以免费使用的当下，github copilot上使用sonnet模型，已经性价比不高了。具体来说，这类账号有两种，一种是¥25左右/年，只能激活IDEA的license，另一种是¥180/年（或者¥320/两年）的，这种才支持在IDEA/vscode里使用copilot，以及网页版copilot。
#  isX: false
#  date: 2025-02-16





# w7 (2025-02-17 -> 2025-02-23)
# 本周的核心任务就是

# 2025-02-17
# 今天的核心任务应该就是把admin的那些东西彻底搞定了，支线任务就是

# lucas.dev
#- task: 【域名】买个10年域名，后面就不考虑域名续费的事了  # lucas.dev 到期时间 2025年2月23日 20:44:48 到时候抢注一下
#  date: 2025-02-17
#  pid: docs

#  des: lucas.dev还是太贵了，10年将近800。想到阿里云一口价可能会有点便宜域名，真找到几个不错的，4、5个字符的top、fun域名，10年才200左右。huyuc.fun、axcs.top都挺便宜。买了个lucas1.fun，10年188. [2025-02-17] lucas.dev被续费了，原本想买个lucas1.tech或者lucas1.top，这两个分别230和188。但是也都不是我的best choice，毕竟是10年的事，感觉还是算了。看到 lucas1.xyz 首年只要¥5，就直接买了

#- task: 把之前域名的相关服务重新迁移到新域名
#  date: 2025-02-17
#  des: 主要是三部分：1、vps上服务，以及cf上的pages之类的。2、docs里面的域名批量替换。3、docs-alfred要替换成新域名。




#- task: 把这个admin剩下的OKR和PID的渲染搞定。
#  date: 2025-02-15
#  pid: docs
#  ests: 180min

#  sub:
#    - task: 删除了之前的 pid.yml（就是那个用来管理pid和okr 的metadata映射关系的文件）。本身这个是用来处理
#      date: 2025-02-17
#      pid: docs
#    - task: 重新refactor了一下OKR.yml的结构。对key重新调优了（1、把KR都改成了item 2、把name合并到target。3、删掉了所有object和KR的progress。4、删掉了dimension，这个还是有点纠结的，但是其实只保留一个 id/object 就足够了）
#      date: 2025-02-17



#- task: 用docs开源项目注册一下IDEA license # 用docs开源项目注册一下IDEA license，有效期是按照生成license开始，还是用户激活之后才开始算？
#  date: 2025-02-16 # 2025-05-20 到期，在此之前要拿到新license



# 2025-02-18
# 今天用一整天时间把两份月刊都整理好，发出去
# 昨天效率真的不高，


#- task: 之前的diary不是有一些直接用的array，没有做成map嘛。今天要处理成JSON，所以要统一格式。就批量修改了一下 # ^- (?!task:)(.*)$
#  date: 2025-02-18

#- task: 最近各种AI知识库挺火的，试着自己搞一下玩玩
#  date: 2025-02-18
#  des: 真不太行，今天试用了cherry-studio, 腾讯的IMA，还有豆包内置的知识库。都TM不支持上传YAML格式的文件。cherry是支持的，但是不能直接用DS模型，换用硅基流动的DS模型又特别慢。很差的体验。


# 昨天把那个 task 处理了一下，然后的的的磨蹭了半天，最后发现是那个 EOF，它只要是它现在就是解析的时候，你只要是语法不符合规范就直接 break 了，不然它一个死循环，for 循环它跳不出去，你知道吧？就有这么一个问题，它就是它，它跳不出去就 return 不了，它只要是压栈解析必须要做一个 for 的死循环。然后昨天就是把这个 task 那个处理做了一下，然后那个什么现在两个 JSON 不是都有了吗？一个是 gh 的 JSON，一个是这个 task 的 JSON，这两个 JSON 都有了，然后把这个 JSON 给转成 table。今天早上把这个做掉，然后然后今天剩下的时间就是把昨天，昨天不是还留了几项吗？留了几项吗？然后把昨天遗留的东西都给处理掉，基本上今天的任务就完成了。然后然后昨天预留的是不是还要做月刊什么的？剩下的时间就是主要就是做月刊之类的这些东西了。


# 2025-02-19



#- task: 把gh渲染到admin
#  date: 2025-02-17
#  pid: docs
#  item:
#    - 用泛型代替any。其实就是加一个interface，里面就是各种返回的struct，这个相比用any就多了一层约束。



#- task: 【调整现在docs-alfred里面对gh的渲染】现在无论是md还是JSON的渲染都有问题
#  date: 2025-02-17
#  pid: docs
#  sub:
#    - task: 1、md渲染需要标明所有gh的子文件夹的路径才能正常渲染
#    - task: 2、JSON渲染需要直接做一个统一的gh.json（其实这个跟docs-alfred需要的那个gh.yml文件类似）


#- task: 恢复一下之前渲染gh的md的那个x，直接渲染成gh.md文件。这个要看看具体怎么操作。之前那种写法不好，我希望像taskfile的run一样，相当于一个post-run的操作。
#  date: 2025-02-17
#  pid: docs



# 其实我感觉我做完手头这个东西之后也，其实真就没有说急着去面试嘛？我觉得把之前欠下来的那个月刊给处理掉，然后还有本月的月刊也要做掉，然后就是就是其实就是看那几本书，对吧？看完之后然后再去准备准备面试题，我觉得这样的一个节奏是比较好的。
#- task: 把这个docs的admin什么的弄完之后，就先不直接搞面试题了。先把之前的月刊弄完，再
#  pid: docs
#  date: 2025-02-18



#- task: 做一个alfred用来实现nameit。需要也可以直接作为cli使用。直接做到docs-alfred里  # 基于deepseek实现一个变量命名工具。做成可以直接放到cloudflare上。 # 我考虑过做这玩意的ROI，感觉确实有点低了。也考虑过直接用coze实现，但是UI太丑了，并且很慢。并且也想着顺便练练手。为什么要做这个工具呢？实现该工具的几种类型：1、codelf是直接搜github代码。目前已经是完全不可用的状态了。2、直接用prompt，返回数据太多，无法自定义parse返回数据，不够清晰 3、用coze这种AI应用，痛点在于UI太丑+慢（其实这个low-code就类似于alfred workflow嘛，但是我宁愿在代码里定义逻辑，也不想做成这种可视化）
#  date: 2025-02-19
#  sub:
#    #    - task: 技术栈 nextjs+deepseek 实现。需要部署到cloudflare上，看下是否要接入cloudflare SDK.
#    #    - task: IP限制
#    #    - task: 还需要做成cli工具
#    #    - task: 做完之后去linux.do推广一波
#    # 我想了一下，就是那个 name it，那个 name it 的那个东西，对吧？之前不是说是之前的想法比较多吗？不是说是要做生拿，拿那个 next js 做一个页面，然后还想那个能做成 COI 工具，然后还想做到做一个，做一个那个什么 all free 的那个东西，对吧？我刚想了一下，其实如果简单来做，就是我自己一个人用的话，那可能就是后面直接拿 golang 直接缩到那个 doc 扫水的这个项目里面去，然后那，那就是顺手的事嘛，无非就是，对吧？那可能做这个东西就是最多可能最多就一个来小时可能就搞定了，我觉得还是简单做吧，你是胜在实用嘛，就自己用的一个东西。
#    - task: 需要把 自定义单词 和 deepseek相结合
#    - task: 配置变量风格// slash/camel case?
#    - task: 需要在这个工具里实现//实践 Code Complete 之类各种代码规范书籍中，对变量命名的规范
#  review: |
#    一开始想用nextjs做个web页面，并且还能直接 Deploy Cloudflare嘛。但是想了一下ROI确实不高。所以，就决定了简单做，不用nextjs做成web应用了，直接用golang做成alfred workflow就可以了，反正是一个自用工具。我只是需要这么一个工具。并且这个还算高频应用，没必要做成网页。
#
#    其实这个东西的难点就三个：
#
#    - 1、接入ds的API
#    - 2、解析ds返回的数据，并提取我需要的内容
#    - 3、替换掉ds掉内容
#
#    其实实现过程中发现，这个东西归根到底还是要跟一个类似于 variable checker 的工具结合使用。否则其实没啥用。
#
#    【2025-02-19】想了一下，还是不打算弄这个了，原因如下：1、。这几点原因分先后。



#- task: 【折腾一下DS集成到alfred】怎么评价alfred中直接使用LLM？感觉还不太可用
#  date: 2025-02-19



#- task: 【用admin替换掉原docs项目】把之前docs/blog又恢复到之前的docusaurus blog mode了。移除掉docs项目，把里面的文档都挪到blog里。
#  date: 2025-02-19
#  pid: docs
#  sub:
#    - task: 【调整blog的右侧TOC样式】

# 一直纳闷为啥 hxha.xyz 这个域名应该已经过期两天了，为啥还能访问。查了一下 NameBeta，结果。其实是godaddy直接把我这个域名买断了，现在想续费要花 170港币，想啥呢？再白嫖几天，用不成了我就用我的新域名去了。但是话说回来，域名还真就是越短越好，现在这个新域名 lucas1.xyz 6位域名，还真就太长了。


# 之前这个taskfile里是通过 find {{.DIR}} -type f -name "*{{.YamlExtensions}}" -exec cat {} + > {{.OUTFILE}} 来实现文件夹下yaml合并成 algo.yml, db.yml, langs.yml 之类的YAML文件，再用 gh-merge 来合并这些YAML文件的。拆成了三个task：merge-yml-template、merge、gh-merge，这种写法的话，最后还需要一个move-yml来把生成的最终文件，移动到dst路径
#- task: 【移除掉原来docs-alfred里面的gh-merge】已经用不到了，并且现在这个 Taskfile.y2m.yml 里的主要task就是用来把那个gh文件夹转成gh.yml。今天早上弄的那个把其实就是把gh转成gh.md和gh.json，都是直接合并的了。所以就肯定用不到这个了。比较理想的状态是，这个taskfile里只需要直接调用 docs命令就可以了，具体的配置都做到 docs.yml里。
#  date: 2025-02-19
#  pid: docs



#- task: 【给docs.yml配置公共路径】修改一下docs-alfred，也是看Taskfile的vars想到的。这两天调整docs项目比较多，其实这些都是可以做成公共路径
#  date: 2025-02-19
#  pid: docs


#- task: 【修改gh.yml的数据结构】这是个大动作，牵扯到的地方不少，估计要费点功夫。也是之前的那个数据结构太糟糕了。
#  date: 2025-02-19
#  pid: docs




#  2025-02-19


#- task: 【购买域名lucc.dev】前两天买的域名lucas1.xyz太长了，真的怎么用怎么别扭。所以新买了这个域名。价格$8.4，折人民币¥61.6
#  pid: docs
#  date: 2025-02-20



#- task: 将电蚊香归置到消耗品类别
#  pid: minst
#  date: 2025-02-20



#- task: 【极简掉压缩裤】把压缩裤扔掉了
#  pid: minst
#  date: 2025-02-20


#- task: 【购买酷态科磁吸数据线】价格¥49
#  date: 2025-02-20


#- task: 【】早上把昨晚遗留没弄完的弄掉，主要是重新refactor整个gh的struct，这个调整确实比较大。 # 下午17:00整弄完了，带来一个经验教训。
#  date: 2025-02-20
#  pid: docs


#- task: 【】下午和晚上实现对gh和task（OKR）的渲染。以及清理掉上面所有未完成task的收尾。
#  date: 2025-02-20
#  pid: docs

#- task: 【修改goods列表的渲染】
#  date: 2025-02-20
#  pid: docs




# 2025-02-21
# 今天把上面的所有东西都收尾

# 感觉 lucc.dev也一般，明年买lukc.dev了

# 决定：决定不买手环//跑步手表了。中午拿着手机小跑2km，其实感觉也还可以。

#- task: 【完善那个用来处理豆包chat history的油猴脚本】之前的那个方案不可用，下午搞了半天，才想明白。换了种方案搞定了。 # 之前是点击分享，然后复制URL再在新tab打开，然后遇到的问题是分享按钮找不到（其实后面处理 批量删除 脚本的时候搞定了），然后发现其实直接点击之后，获取该网页的URL再在新tab打开也可以。另外，删除按钮也有点问题，同样是无法点击的问题（那个button在他parent node的那个li里，我们定位到的这个node无法点击）这玩意还真就颇费功夫，从下午5、6点，弄了差不多60min才弄完
#  date: 2025-02-21


#- task: 需要再优化那个用来整理豆包和copilot的tampermonkey脚本。当然，或许也可以找个xxx自己部署一下。
#  des: 今早不是又整理豆包的chat嘛，两个按钮基本上都不可用。
#  date: 2025-02-21



# 注销了Poe账号，虽然毫无意义





# 2025-02-22
# 周六，今天早上把昨天的东西收尾了一下。一直到下午4点多，算是基本上结束掉这个东西了。下午要处理月刊了。



#- task: 做完这个docs之后，也做个复盘
#  pid: docs
#  date: 2025-02-13
#  review: |
#    这玩意拖了也有段时间了，做的过程中也涉及到不少关键决策：
#
#    - 1、是否要用admin？还是说直接用notion/airtable？
#    - 2、究竟用哪个admin？使用vben相比其他admin项目，有啥好处？vben的优势就不多说了，技术栈新、移动端适配做的好
#    - 3、是否应该继续用vben本身使用的nitro这个mock服务，还是说自己用vite-plugin-mock？
#    - 4、为啥用NaiveUI而不是element-plus?
#    - 5、回到问题最开始，为啥想用
#    - 6、在做这个东西的过程中，其实对整个docs项目也做了比较大的refactor，比如说
#    - 7、








---
# w8 (2025-02-24 -> 2025-03-02)

# 2025-02-24

#- task: 把books列表的tag这列移除掉。
#  pid: docs
#  date: 2025-02-24



# 2025-02-25
# 昨天才发现，其实就是昨天下午到晚上不是把整个之前 data 里面的那个这些 yaml 重新 refactor 了一下吗？就才发现就是怎么说呢？就是很多东西吧。嗯哼。哎，就是既然我不是有有那几个文件做成 Multi Multi file yamo 了吗？那然后不是之前那个 tank 是体，是直接做成 slice 了，其实我我就发现如果做成 Multi Multi file yamo 之后，其实直接那个直接直接把那个 tag 提出来就 OK 了，就提成一个，单独提成一个 key，其实这个是最优选择，我怎么就很很，哎呦，我当时怎么就没想到还有这种方案呢？反正是现在处理一下嘛，然后我感觉还可以咯，是昨天真的还是一个比较大的一个突破吧。



#- task: 【对books.yml的一些处理】
#  pid: docs
#  date: 2025-02-25

#  item:
#    - 1、删除date这个key。
#    - 2、保留cate对应的sc(sub-category)，但是需要给所有cate都添加对应的sc
#    - 3、保留book的tags，但是直接渲染到des字段里，因为现在90%的书没有添加tags，所以不应该单独把tags作为一列。
#    - 4、重新refactor了整个books.yml的数据结构。
#  des: 其实倒也不必纠结于是否。这个优化一定是一个逐步的过程，肯定不可能一步到位。最近不也是。正如我现在可以把这段文字直接写在这里，而非写在MD里，然后再东拐西拐、挪来挪去再挪到最终的那个地方。这个task的核心在于重新重构了整个books的结构，之前不是拆成多文件了嘛，然后上周又合并到一个文件了，带来的问题就是



#- task: 【对goods.yml的处理】
#  pid: docs
#  date: 2025-02-25

#  item:
#    - 1、给goods添加score这个key。用来标识。
#    - 2、基于上一点，把之前注释掉的goods也标识为-1，把牙刷牙膏之类的消耗品标识为0分（也就是不算做个人物品，需要时购买即可）



#- task: 重新设计了data/life里面的很多YAML文件的key
#  pid: docs
#  date: 2025-02-25






# 2025-02-26
# 今天肯定就是把前面的这些，其实我弄这个月刊已经弄了很很多天了，至少有个三四天了吧。然后今天就是统一做一个收尾，其实月刊不是基本上已经处理掉了，然后其实不是说月看嘛？月刊，然后书手头的那个书，然后还有一些东西吧，就基本上今天包括这个后台的这个 admin 统一全部处理掉。


#- task: 【洗牙】人生中第一次洗牙 # 手头没钱了，本来定的12号弄。手上只有45了，正好到15号的饭钱。
#  okr: minimalist
#  date: 2025-02-26
#  des: 预约的中午11点，我10点半出发




#- task: 【移除掉ws.yml】打算移除掉
#  pid: docs
#  date: 2025-02-26
#  ests: 120min
#  des: 一个标志性的task。之前多次想要实现这个，但是一直没搞定。
#  sub:
#    - task: 【申请云片退款】用来测试短信服务的，17 年充了 50，到现在还有 40 多
#  item:
#    - 【天气相关】（包括【中国气象局 天气预报】、【Weather Spark】、【和风天气】），AI对很多功能的替代真就是潜移默化的，



#- task: 优化整个data的数据结构
#  date: 2025-02-27
#  pid: docs
# 今天在工作上取得了一些突破性进展：
#对 data 进行整理，将原本塞在 data life 文件夹里混乱的单文件 single yamo 重新梳理，清晰地划分为 GitHub、task、books、goods 这 4 个核心部分，同时把数据结构一致的 movie 和 TV 整合为 video，还新增了 Vicky，总共形成 6 个部分，使数据结构更加统一清晰。
#解决了一直纠结的在 yaml 里快速定位的问题，对于 GitHub 可通过复制 docs of read 对应的 url 进行全局搜索来快速定位，不过针对其他部分还未提及具体解决办法，但整体算是取得较大进展。
# 在处理 yaml 定位相关内容时，你取得了一系列进展。之前发现将内容定义成 key 可直接查看，对于 books 因书籍类型有限，通过添加对应的 k 实现快速定位。今日处理 VK 定位问题，上午照搬 books 的方式未成功，因 VK 存在 tag 和 type 两个维度且 k 极多。下午想到解决方案，即针对 VK 采用更细化的 multi multi file yamo 方式，将 qs 按 k 切分，每个文件包含一个 qs，从而达成清晰定位的目的。


# 2025-02-28

# 梳理一下我今天做的东西啊，就是今天是周四，然后截止现在我手头上其实三个东西，有三个活，一就是手上现在还没做完的这个后台的这个整个整个页面的一个优化吧，算是它也不是什么实现了，就是一个优化。然后二是什么？二是这个就是之前的几本书的那个相关内容的整理还没做完，这个其实可能还真就花一些时间，可能真就 4~5 个小时可能才能弄，才能弄完。然后呢，手上还有一个活，就是他的就是那个 KTF 嘛，mini Flex KTF 这玩意可能也得大概半天的时间吧，然后我今天周四嘛，然后本周不是 567 三天嘛，对吧？那么我就是保守来算的话，手上这三个东西反正如果说是顺利的话就一天半吧，不顺利的话我们 double 一下就是就是三天嘛，那就是我希望能够周末的时候，因为肯定还有其他乱七八糟的事情那么穿插着做弄，我希望能够在周末之前把这三个事全部做完，这样的话这周还是比较圆满的。



# 2025-03-01


#- task: 【docs/admin】前两天不是重新调整了books的数据结构嘛，所以就把之前的那部分pq4r的相关数据，都重新整合回来。
#  date: 2025-03-01
#  pid: docs



#- task: 【rss2newsletter】
#  date: 2025-03-01
#  pid: docs
#  ests: 240min # 大概就是一整个上午/下午
#  des: 原本【直接在miniflux的基础上实现一下 KTF】想基于miniflux实现（也就是对KTF的简易实现），但是早上仔细想了一下这个需求，我希望借助miniflux实现什么功能呢？无非是 监控feed是否work、想类似dashboard一样查看所有feed、、。难道这些不是我自己本身就能轻易实现的嘛？换句话说，我真正需要做的应该是去解决rss2newsletter的现有痛点，不是吗？
#  sub:
#    - task: fix docs-alfred里面rss2newsletter的timeout的bug，这个bug经常导致github actions一直在running，浪费执行时间
#      date: 2025-03-01
#      pid: docs
#  item:
#    - 用conc代替了之前这个rss2newsletter里面的 errgroup. 本身内置了并发控制、超时控制、错误收集。我之前这部分代码有个bug：偶发性的会整个hang住，执行不下去（导致github actions严重超时）。另外更好地实现了我的需求：收集所有错误feed `p := pool.NewWithResults[feeds.RssFeed]().WithContext(ctx).WithMaxGoroutines(10)`，创建一个结果池，用于收集处理结果。限制最大并发数。
#    - 独立newsletter 的五种商业模式 # [分享一篇文章探讨了独立newsletter 的五种商业模式， - 即刻App](https://m.okjike.com/originalPosts/6618dba93624666324362706)
#    - 我有个问题哈，我能否把一个SPA作为newsletter呢？也就是说我这个页面可能有一些比较复杂的交互。但是可以打包成一个html
#    - "***怎么preview某个邮件在各种email客户端的预览效果***" # [Email Marketing Platform | Litmus](https://www.litmus.com/)






# 直接把本地YAML同步到miniflux
# 设置配置项（是否）来控制从miniflux获取所有 unread feed，而不是直接走本地脚本
# 设置配置项（是否展示错误feed）来控制是否在newsletter中展示，所有fetch failed的feed
# miniflux to newsletter. 支持直接通过miniflux API来发送


#- id: ktf
#  goal: 把定的KTF这三步全部搞定
#  kr:
#    - id: mvp
#      goal: 完成KTF的MVP开发，并在3月底之前上线。先做个MVP，产品体验好过目前占据这个niche market的BriefCake之类的产品就可以了。
#    - id: rec
#      goal: 加上rss评分（也就是推荐系统），可以结合一阶段的情况，推广看看，在PH打打榜（正好PH现在就是各种AI产品大行其道）
#    - id: feed
#      goal: 做一个把各种优质rss作为底座的newsletter综合站//分发站，类似 tldr.tech 这种，不同领域的用户（设计、金融之类的）可以一键订阅自己行业的相关feed


## - name: 【BriefCake】
##  url: https://app.briefcake.com/
##  rep:
##    - name: 【mailchimp】
##      des: 需要付费
##    - url: https://feedbutler.app/en/feeds
##      des: 免费用户只能添加3条feed
#
#- task: 自己手搓一个类似工具。现在市面上的这几个都太难用了。做便宜点，估计真能赚点钱。这玩意成本也不高。核心成本就两块：拉feed和发邮件。
#
## 现有的几个feed-to-mail的工具的问题：
### 1、易用性很差：都很差，从UI到前端页面的响应速度都很差。UI上可以参考一下 rss.app，工具类肯定是 bento grid style了，易用性会好很多。
### 2、功能很差：
### 3、价格偏高：
#
## 用到现在的痛点：
### 1、处理时间相关的问题，现在这个还是有bug。提供更人性化、用户友好的时间处理方式。甚至
### 2、不好处理注释后的feed。这个就是我一直说的“退出机制”，关于这点我从22年就纠结到现在了，也尝试了几种方案，但是都不是很好。既要又要嘛，既想
### 3、将 ws 和 feed 做合并处理，实现 webstack 并使其在脱离本机时能以网页形式展示且可视化效果更好
### 4、解决用户推荐即 feed 流推荐的问题，优化推荐算法
### 5、我希望知道每个rss的更新频率。并且通过结合该rss本身的质量，有一个综合评分。这个可以把掘金给技术文章的评分机制直接拿来用。现有的这几个rss平台，本身提供的评分机制都太烂了，平台本身对评分行为没有正向激励，并且评分本身众口难调也存在一定问题。这个点将会是我这个killthefeed的一个突破口。 # 我印象中好像有个什么平台做过，直接把一些与rss本身相似，但是自己没有订阅过的feed推荐的产品。但是可用性也一般。产品名也想不起来了。
### 6、基于上个需求，给每个用户都提供一个关于所有rss的dashboard，这点需要想想需要哪些数据项。
#
## newsletter 的盈利模式是啥
#- task: 先把KTF的页面画一下
#  okr: ktf#ktf-mvp
#  duration:
#    start: 2025-01-16
#    end: 2025-01-30
#  sub:
#    - task: 先直接照着briefcake抄一个，但是我需要三栏式布局
#    - task: mock数据，定义好所有的API # 1. login(third party login)/register. 2. userinfo 3. add feed(or import opml file) 4. feed list. 5. feed digest. 6. pricing(strapi) 7. i18n (chinese, english, ...)
#    - task: 整体优化整个KTF页面，先用mock数据把整个流程跑通
#      okr: ktf#ktf-mvp
#    - task: 比较麻烦的一点：mail里newsletter的样式需要研究一下。查查看 newsletter layout 之类的
#
#- task: KTF 后端
#  okr: ktf#ktf-mvp
#  sub:
#    - task: draw the arch diagram. and think about tech selection.
#      okr: ktf#ktf-mvp.
#      des: gozero+pgsql+redis+k3s (etcd+traefik) +nats
#
#    - task: 表设计，参考一下ttrss//miniflux的 # 看看怎么才能在适配我们业务场景的情况下，尽量减少数据
#      okr: ktf#ktf-mvp
#      date: 2025-01-14
#
#    - task: 去参考一下那几个老牌服务
#      okr: ktf#ktf-mvp
#
#    - task: 多模板
#      okr: ktf#ktf-mvp
#    - task: 自定义推送时间
#      okr: ktf#ktf-mvp
#
#    - task: 错误feed警报，这个要想想
#      okr: ktf#ktf-mvp
#
#    # 想了一下，其实我现在需要的是一个feed管理页面，可以监控
#    # 其实这个真的可以多参考一下resend
#
#    - task: 生成 webstack，类似notion那种，可以分享给其他用户，或者密码访问
#      okr: ktf#ktf-mvp
#
#    - task: 自定义url
#      okr: ktf#ktf-mvp
#    - task: 结合ttrss和miniflux，集成相关功能
#      okr: ktf#ktf-mvp
#    - task: explore feeds.
#      okr: ktf#ktf-mvp
#
#    - task: 支持自定义template (类似于 hugginn 那种)
#      okr: ktf#ktf-mvp
#
#    - task: 搞个多平台推送：支持推送到 mail、微信、tg 等平台
#      okr: ktf#ktf-mvp
#      isX: false # 想了一下暂时不需要
#    - task: 支持本地yaml同步到我们平台
#      okr: ktf#ktf-mvp
## 定义前端埋点，
#
## 集成 kill-the-newsletter, rsshub 之类的







# 2025-03-02



#- task: 【把books整合到task】【把video整合到task】
#  pid: docs
#  date: 2025-03-02
#  sub:
#    - task: 封装了 BadgeButton 组件，用来提供可复用的button
#    - task: 用NaiveUI来重构drawer里面的样式。重构 gh, goods, task, wiki 的drawer样式。
#  review: |
#    "昨晚散步时想到的，因为昨晚的最后一个事，就是整合之前在月刊里MD格式的books到books.yml嘛，但是写着写着就发现，现在这个加了PQ4R的books真的太复杂了。并且久而久之现在这个books.yml更像是某种“赛博书架”，用来彰显读过哪些书（即使实际上并没有看过，只是随便看个5min的速读视频，或者从网上找几篇读后感）。并没有，也不能真实地记录我的阅读感受。综合以上的两点原因，我就想为什么不把books直接整合到task呢？其实只需要加一个 `pid: books` 不是吗？"
#
#    是啊，又有什么不是task呢？
#
#    当然，归根到底还是因为之前的books的数据结构有点太过于复杂，导致难以维护了。心智成本颇高。另外，其实“书影音”基本上也都是一次性消费品，除了极少数，基本上不会有人翻来覆去地看。所以倒也没必要专门做个books来维护，放到task里反而是比较符合实际情况的。
#  item:
#    - "NaiveUI的组件：`NDataTable`, `NTooltip`, `NCard`, `NSpace`, `NGrid`, `NGridItem`, `NImage`, `NText`, `NTag`,  等组件"
#    - "链接样式可以使用 `NButton` 的 `text` 类型替代自定义的 `.gh-link` 类"
#    - "数字序号可以使用 `NNumberAnimation` 组件"
#    - 文本可以使用 `NText` 替代普通 span
#    - "***最后，归根到底，应该有这样的意识，既然使用了NaiveUI库，那就应该尽可能去使用其本身提供的组件。而不是自己去实现。这也是个最起码的认知。***"




- task: 2025w8
  pid: PWR
  date: 2025-03-02
  item:
    - 周一到周二，其实还是延续了上周，继续在优化admin的那个。上周虽然admin已经搭出来了，但是具体细节还差点意思。另外上周末不是在整理月刊嘛，其实周一更多的还是在弄月刊，只不过月刊和books, goods 这些确实相关性很大。周二就把本地的所有东西都清理掉了，也把。在我看来，之所以这两天又花了很多时间和精力重新弄
    - 周三稀里糊涂一天。周四和周五（也就是27号和28号）都是搞admin嘛。周四是把整个data的所有数据结构重新调整了一下，尽最大可能复用，切分成了5块（gh, wiki, books, task, goods），这5个都是值得长期维护的，调整成了统一的数据结构，砍掉了其他的。周五则是根据周四调整好的数据结构，来实现相应的admin需求（docs项目里加了9个issue）。
    - 周六早上，周六下午则是把那个rss2newsletter搞了一下。归根到底是解决了一下这一年多使用中发现的痛点。1、之前如果type没feed就直接挂掉的bug。2、偶发的会hang住的bug。3、用conc替换掉了errgroup 4、实现了需要的实时查看 rss2newsletter的dashboard需求。可以直接看到当天所有 fetch failed 的rss，以及一些其他数据。5、处理了一些之前的小问题。
    - 周日就是把本周要做的三个事：admin、月刊（包括books）、rss2newsletter 没弄完的都收个尾。1、早上把books和video都整合到task 2、下午就是收尾之前月刊的相关东西（比如200多个bz的技术视频），以及把月刊又重新梳理了一下。3、整体清理了一下，做个彻底收尾。
  review: |
    颇为充实的一周，算是彻底处理干净了从离职以来的这些东西。有了一个还算满意的最终产出。从之前乱七八糟的data，到现在就收束到了4块：gh, task, wiki, goods。就很清晰整洁了。

    - 【工作】辞职后，失业ing
    - 【学习】也没学到啥，本周就是搞admin嘛，更多也是学到点FE相关的东西
    - 【生活】好像也没啥
    - 【自我成长】查看des




---
# 2025-03-03
# 又是一个周一。本周阴雨连绵，一周七天只有3天不下雨。




#- task: 【研究一下用AI在番茄混全勤】据说每个月能混个500块钱
#  pid: minst
#  date: 2025-03-03
#  des: 结论：这玩意不靠谱。早上看到一个帖子，现在番茄每天5000本新书试水，要知道去年12月份大概是每天1000本的水平。也就是这三个月以来，试水推翻了3、4倍。基本上是每个月都指数级增长了。



#- task: 【治脚气】买个 加热泡脚桶 + 足光散。合计才100，挺划算。
#  pid: minst
#  date: 2025-03-03 # 现在确实没这个条件，租房之后再弄这个
#  des: 正好有200-30的活动。泡脚桶原价55，到手价43。又买了6盒人福药业的足光散，合计¥33。又买了点洗衣液、衣物消毒液、痱子粉，合计¥25左右。



#- task: 【admin】继续优化admin的样式
#  pid: docs
#  date: 2025-03-04
#  des: 下午搞了下这个，基本上是比较满意了。
#  sub:
#    - task: markdown渲染
#    - task: 优化并统一4个table的tag样式
#    - task: task列表和goods列表里，状态为-1的，展示为类似 NextUI里面的 disabled rows的样式
#    - task: 需要实现docs-alfred里，repo直接跳转到docs里对应repo

#      date: 2025-03-04
#      des: 可以说，这个是我最需要的一个功能了
#      item:
#        - 带参菜单的问题，其实还挺复杂的。 router里在meta里添加query
#    - task: 搞完这个之后，晚上出去遛弯
#      date: 2025-03-04
#  item:
#    - "搞完那个md渲染之后，重新 pnpm dev或者 pnpm build时，都会报错 `At least one <template> or <script> is required in a single file component`. 为啥会有这个报错？通过回滚commit已经确定了，哪个commit的代码有问题了。 `git diff --name-only 旧commit 新commit` "
#    - 直接使用vben提供的 drawer组件（基于NaiveUI二次封装的），而不是直接使用 NaiveUI提供的。因为前者不仅提供了移动端适配，还做了基本layout。
#    - 但是如果使用vben的drawer组件，怎么控制drawer的width呢？






#- task: 【决策是否应该搞这个popjob项目】
#  pid: fl
#  date: 2025-03-03
#  des: 最终决定暂时不做这个了。原因如下：原计划上周开展 pop job 项目，目的一是尝试通过项目赚钱，二是使用 go Zero 搭建正经项目练手。实际仅做了起手式，未深入开展。原因主要为项目存在门槛，维护爬虫成本高，个人能力感觉难以独立完成该项目。且在逛 Reddit 时发现有人已做出类似成品（trawle），导致兴趣降低。






# 2025-03-09


#- task: 【玩了会梯子】三个事：1、用BPB-Worker-Panel 2、把clashx换成了FLClash. 3、玩了会 Hidify
#  date: 2025-03-09
#  des: 基于 clashx external-ui.






- task: 2025w9
  pid: PWR
  date: 2025-03-09
  item:
    - 【周一】
    - 【周二】周二休整一天，把之前的东西彻底做个收尾（1、），另外也找找方向，要做一个重要决策：之前两周弄的admin已经弄完了，之后是准备面试，还是搞个自己的产品，弄点被动收入。
    - 【周三】早上：
    - 1、记录昨晚那个admin调整的task的qs
    - 2、发了【吐槽claw cloud的bug】的帖子 ✅
    - 3、把cron.yml渲染到admin的工作台里todolist里（需要做一个前端的状态修改，）
    - 4、实现了昨天没弄完的 docs-alfred 跳转到admin的gh列表
    - 5、整理Chrome中与爬虫站（采集站）相关的杂乱数据及信息资料 ✅
    - 6、准备软考相关事宜，考虑到每年可领6000元，认为值得去做 ✅
    - 下午：
    - 1、下午要把后面这个popjob的开发计划定一下，确定第一版MVP时间安排。计划用30天完成项目，时间只少不多，严格按照项目的spring执行
    - 2、下午一直在看wiki（因为这个太乱了），看来看去，删掉了以下文件（me.ntl.yml, ss.temp.yml），并且整合到task
    - 3、把 fc2.yml 整合到task里
  review: |
    - "***1、[2025-03-05] 其实本身不应该存在 整理***"
    - "***2、[2025-03-05] 关于docs里的wiki，晚上突然福至心灵，想到一个很有意思的问题：为什么不把xxx都给挪到task里呢？不得不说，之前存在wiki里的大量问题，本身确实都是暂时的，比如说我为啥要把xxx什么的存到wiki里呢？。当然，我不得不说这也是个过程。需要注意，之前没有task.yml，更没有对这套task.yml的渲染，甚至当时连wiki都没有，又何谈渲染成现在这个样式呢？***"
    - 【2025-03-08】今天主要是做了点popjob的开发，早上本
    - 【2025-03-09】截止【2025-03-11】，从9号下午开始弄这个（就是把之前的nitro mock踢掉，直接做到admin里），一直到11号中午才搞定。整整2天，交付掉了。过程非常曲折。开一个task专门用来整理。

    总结：上周其实还真就没有close任何一件事情，总的来说，还是在弄那个vben的admin。但是也算是颇有收获：1、优化了整个data里面的结构（收束到只有4个）2、大幅提升了整个admin的RTT（比如说 把nitro实现的mock踢掉了）






---
# w10 (2025-03-10, 2025-03-16)


# 2025-03-11


#- task: 【梳理整个实现xxx的过程】迁移nitro实现的mock，直接合并到admin项目里
#  date: 2025-03-11
#  pid: docs
#  review: |
#    从9号弄到今天中午，整整两天。走了很多弯路。先简单记录一下整个过程（其实这个过程直接查该issue的commit history，更详细）：
#
#    - 1、移除了之前nitro实现的mock（在此之前还尝试了以下操作：1、cloudflare 服务绑定。2、把两个cloudflare pages做到一个实例的不同路由。 发现这两个都不可行。）
#    - 2、迁移到 vite-mock-plugin（把这几个API都实现了一下）
#    - 3、加了个cache来加速API（把RTT做到10ms以内了）
#    - 4、build之后，发不到生产环境不可用，还TM以为是自己配置问题，调了半天（至少2、3h）。然后才意识到应该去查查issue。查issue发现这个 vite-mock-plugin 现在不支持在生产环境使用了（pnpm build之后，无论是否开启 enableProd 都无法使用mock）。之后再查issue，发现 vite-plugin-fake-server 的作者自称开箱支持生产环境使用。
#    - 5、迁移到 vite-plugin-fake-server（并实现这几个API）
#    - 6、同样是开发环境可用，但是生产环境不可用（pnpm build时报错 fsevents 什么的，说白了就是跟nodejs不兼容嘛）。只能再想其他办法。
#    - 7、之后还是想继续用这个pkg，所以就想既然是不能读取文件。那我直接做成URL不就行了吗？然后fetch URL来获得JSON数据（而不是本地读取数据）。尝试了一下还是不行。仍然在build时报错。
#    - 8、迫于无奈，只能在昨天下午重新回滚到nitro mock。为了降低RTT和TTFB，就用了nitro内置的cache。结果发现仍然很慢。只能想想其他方案。
#    - 9、昨晚9点多出来散步，尝试问了下deepseek有什么解决方案（把上面的整个流程都问了一下，以及现在遇到的问题，以及自己希望的解决方案。其实我直接就说了，希望直接把JSON文件做成变量，但是我怀疑是否可以兼容现有的vue里对API数据的渲染的处理），deepseek提供了完整的回答（当然也从之前使用Mock Service Worker，到使用localstorage，再到直接把JSON文件做成变量。在明确需求的过程中，deepseek最终选择了第三种方案）。散步到家之后，做了个基本的实现，在昨晚确定了 /user/info 这个API可以兼容，并且数据可以（从JSON文件中成功）获取，以及最重要的确实可以build成功之后。基本上就确定使用这个方案了。今天早上调整了一下剩下几个API的结构，把所有API实现掉了。最终还是做到了10ms以内的渲染。
#
#    可以看到，一些关键决策：
#
#    - 是否仍然使用 nitro mock?
#    - 是否要使用mock？
#    - 是否要使用API？
#
#    ***可以看到最终的这个方案，实际上是放弃了使用mock。想来也对，既然我本身就有完整的JSON文件，还要mock干啥呢？并且众所周知，我们通常都把mock称为mock server，本身大概率就是一个单独的server。如果还是想要在原来的方案上执行，无异于想把1+1处理成3这样的笑话。果然换了个思路，问题就迎刃而解了。结论：还是得想清楚mock究竟是干啥用的。两个东西看起来像，但是未必真就是一个东西。***
#
#
#    一些查找资料：
#
#    - “用 vite-mock-plugin 替换 nitro来实现mock之后，调整部分deploy相关代码”
#    - cloudflare pages绑定workers 服务绑定 # [Bindings · Cloudflare Pages docs](https://developers.cloudflare.com/pages/functions/bindings/#service-bindings)
#    - Smart Placement [Service bindings - Runtime APIs · Cloudflare Workers docs](https://developers.cloudflare.com/workers/runtime-apis/bindings/service-bindings/#configuration)
#    - cloudflare pages deploy multi service
#    - Cloudflare pages, multiple “projects” with a single. cloudflare pages monorepo [Monorepos · Cloudflare Pages docs](https://developers.cloudflare.com/pages/configuration/monorepos/)
#    - 使用 vite-plugin-fake-server 替换 vite-plugin-mock
#    - 报错 “vite fsevents (Note that you need plugins to import files that are not JavaScript)”
#
#  item:
#    - pnpm add -Dw 是啥意思
#    - 我有个疑问哈，为啥这个cache真的很有效呢？讲道理我这个数据本身就是直接从json文件里获得，本身不就算是cache了吗？1、我之前看到你加cache还有点怀疑，没想到效果真的是立竿见影。能否给我讲解一下？ 2、缓存在 build 后是否会消失？ # 1、因为仍然需要读取以及数据处理操作。只是少了网络请求而已。 2、build后缓存会消失，因为缓存存储在内存中，每次服务重启，内存会被清空，每次构建都会创建新的进程。
#    #虽然数据是从 JSON 文件读取的，但每次 API 调用都会执行以下操作：
#    #- 数据解析和类型转换
#    #- 数组扁平化处理（多重循环）
#    #- 数据过滤和排序
#    #- 数组复制和新对象创建
#    #这些操作都是 CPU 密集型的，特别是在数据量大的时候。以 `gh.ts` 为例：
#    #// 每次调用都要执行：
#    #data.flat()  // 数组扁平化
#    #.filter()    // 数组过滤
#    #.map()       // 对象创建和属性复制
#    #.sort()      // 数组排序
#
#
#    - cloudflare page的服务绑定实际上是
#    - vite PWA到底是啥？ # 通过插件实现的增强功能，能够将你的 Web 应用转变为具备原生应用特性的渐进式网络应用。PWA 是一种结合 Web 和原生应用优势的技术，通过 Service Worker 和 Web Manifest 等核心技术，实现以下能力：离线访问（即使无网络，用户也能使用部分或全部功能）、安装到设备（像原生应用一样添加到桌面或主屏幕）、后台更新（自动静默更新资源，提升性能）、推送通知（支持消息推送（需浏览器支持））。传统 PWA 需手动编写 Service Worker 和 Manifest，而 Vite 插件自动生成这些文件，并集成到构建流程中。只需简单配置即可启用。
#    - vite JSON文件内连 sourceMap
#    - cloudflare pages 配置路由规则
#    - nitro.config.ts
#    - h3 在 nitro 里有啥用？
#    - "***nitro 怎么使用cache？我希望不需要给每个API单独配置，怎么给所有API做全局cache配置？另外，2、怎么验证这个cache是否生效？ x-nitro-cache  3、nitro的cache storage的driver怎么配置呢？默认生产环境memory driver，开发环境 fs driver  4、nitro cache的 varies 具体是怎么用的？可以理解为定义redis cache key的rule（不同url参数自动生成不同的key）***"
#
#    - "我的最终方案是直接把JSON文件作为变量使用，也正因为这种方案，所以无法直接查看API的RT了。我之前通过给这些API响应添加cache，把所有API的RT做到10ms以内了，我不确定，现在这种方案是不是本身就是走cache了？还是说仍然需要加个cache来加速响应速度？ 总结：现在这个方案和cache还是不同。按我的理解，三种方案（1、nitro那种从JSON文件读取 2、现在的把JSON文件作为变量使用 3、cache方案）的核心区别在于两点：JSON文件是否在内存？API响应是否被缓存？可以看到，cache和JSON文件作为变量，这两个是数据都已经在内存了，但是即使如此，方案2也仍然需要去对数据进行处理，而非直接O(1)地获取处理结果。 ***结论：没必要再做优化了，因为现在的 RT已经<10ms，且cache需要对所有参数单独操作。空间换时间的话，没必要。现在的方案已经是时间和空间的一个最佳 tradeoff 的点。***"



#- task: 本周处理掉，下周真正开始处理面试题
#  pid: docs
#  date: 2025-03-16
#  sub:
#    - task: 0、【newsletter】合并之前newsletter里面的dashboard到feeds mail里
#      url: https://github.com/xbpk3t/docs-alfred/issues/18
#      qs:
#        - "`margin: 0 auto 24px;` 这种是啥意思？ 顺时针（上右下左）；2个就是上下、左右；3个就是上、左右、下"
#        - css shorthand规则，除了 margin/padding/border 以外，还有哪些？
#        - 【背景与渐变】
#        - 1、background 可合并以下属性 background-color, background-image, background-position, background-size, background-repeat, background-attachment
#        - 2、linear-gradient() / radial-gradient() 渐变颜色和方向的简写
#        - 【字体与文本】
#        - 1、font 可合并以下属性 font-style → font-variant → font-weight → font-size/line-height → font-family
#        - 2、text-decoration 可合并以下属性 text-decoration-line, text-decoration-style, text-decoration-color
#        - 【布局相关】
#        - 1、flex 可合并以下属性  flex-grow, flex-shrink, flex-basis
#        - 2、grid / grid-template 定义网格布局的简写
#        - 【过渡与动画】
#        - 1、transition 可合并以下属性 transition-property, transition-duration, transition-timing-function, transition-delay
#        - 2、animation 可合并以下属性 animation-name, animation-duration, animation-timing-function, animation-delay, animation-iteration-count, animation-direction 等
#        - 【其他常见简写】
#        - 1、list-style  可合并以下属性 list-style-type, list-style-position, list-style-image
#        - 2、outline  可合并以下属性 outline-width, outline-style, outline-color
#        - 3、gap（Grid/Flex 布局） 可合并以下属性 row-gap 和 column-gap
#        - mjml-go # [My Wonderful HTML Email Workflow, using MJML and MDX for responsive emails • Josh W. Comeau](https://www.joshwcomeau.com/react/wonderful-emails-with-mjml-and-mdx/)
#        - "***邮件对各种html/css样式的兼容性***" # [Compatibility for mj-accordion - MJML](https://mjml.io/compatibility/mj-accordion)
#
#
#    - task: 1、docs-alfred搜索repo跳转

#      date: 2025-03-15
#      des: 很简单的问题，
#
#
#    - task: 2、docs里admin的QAList组件的渲染需要调整

#      date: 2025-03-14
#      qs:
#        - "vben 的table中怎么实现筛选框修改数据直接展示数据（而不需要点击 搜索按钮）？ `submitOnChange: true`"
#        - "vben 的table中为啥我的所有table list数据都在一个box里面scroll，而不是在这个页面里scroll?  `height: 'auto'`"
#
#    - task: 3、task的数据结构和渲染都需要调整
#      date: 2025-03-14

#      item:
#        - 想到一个问题，我觉得task的数据结构不对劲，到底是在哪个点呢？
#        - 1、item的作用：这点很重要，因为很容易把review和item弄混。当然这也是因为task这个概念本身很抽象，不具体。对于不同类型的task，item的内容本就不同，非长期使用和磨合不能真正熟练使用。
#        - 2、移除了qs里的x（只保留q, s, p, u）。注意这点需要删除掉data里面所有qs的x（不止是task的）。基于此，我还将修改上面那个渲染的需求。
#        - 3、把review从之前的数组重新弄回字符串（因为数组就很僵化，不灵活。数组按理说应该渲染成无序列表，但是如果这么弄的话，就非常丑了，完全无法表达出来需要的意思，还不如直接做成字符串。更自由更灵活）
#      sub:
#        - task: 【清理docs的commit里面的sk】
#          des: 搞这玩意把github commit从1443给刷到了799个。成功把整个docs项目（包括node_modules）的大小压到了19.2MB，感谢pnpm，感谢git-filter-repo
#          qs:
#            - "我希望能够使用git filter-branch来清理之前泄漏的sk，那么我先用 `gitleaks detect --source . -v` 会返回一堆类似 ... 的内容，但是我只想提取里面的File，怎么提取出来呢？这样子的话，我就一条命令就rewrite掉所有含有sk的文件了 " # git filter-branch --force --index-filter 'git rm -rf --cached --ignore-unmatch '"$(gitleaks detect --source . -v | awk -F': ' '/^File:/ {print $2}' | sort -u | tr '\n' ' ')" --prune-empty --tag-name-filter cat -- --all
#        - task: 把review弄成字符串了，要调整data里面之前的相关数据
#          date: 2025-03-16
#          des: xxx
#        - task: 【重新调整整个data里面的qs数据结构】 # 【2025-03-15】还是把xxx重新弄回数组。并且需要系统地调整之前的整个qs相关结构。我现在大概有个思路了。把之前的qs调整为topic，然后其中会有多个qs，qs对应的key有u, s, x, p 之类的。这样分别给topic和qs做个组件，其中topic直接调用qs就可以了。这种设计是最合理的，也是复用性最好的。【2025-03-16】改成tps->tp->qs这三层，分别对应之前的qs->q->s，之前的u改成url，p改成pic。“名不正则言不顺，言不顺则事不成”嘛。
#          date: 2025-03-16
#          des: 搞这个真就搞了一天。
#          qs:
#            - "今天不是把所有vue和api里面的struct都放到types里统一管理嘛，然后即使export了，所有interface都报错 Vue: GoodsCategory is a type and must be imported using a type-only import when verbatimModuleSyntax is enabled.  查了一下才知道要加个type才可以"
#
#
#    - task: 5、找一个类似alist，但是可以直接部署在cloudflare workers上（可以直接在web上管理cloudflare R2）的服务
#      date: 2025-03-15
#    - task: 6、看下Dropover到底有没有用
#      date: 2025-03-15



- task: 2025w10
  pid: PWR
  date: 2025-03-16
  item:
    - 【2025-03-11】上午把那个mock彻底搞定了，也把vben的这个admin的响应速度弄到还不错的水平。下午大概聊了一下podhunt的整个需求和架构。然后把昨天导出来的那个cursor chat history给整理了一下。
    - 【2025-03-12】
    - 【2025-03-13】徒劳无获的一天。早上把之前挂掉的rss feed重新弄了一下，中午都跑掉了，保证现在整个rss2newsletter里面的feeds没有挂掉的。这个东西干的还是很漂亮的，虽然不算麻利。下午则是继续搞那个rss2newsletter，想把之前拆开的dashboard和feed合并到一个里。本想用react-email的，调研了一下发现应该用mjml来实现。但是cursor又不能用了，迫于无奈用trae，一坨狗屎，把我的代码改的乱七八糟，一直弄到晚上9点多，没弄完出门散步去了。散步到12:10回去睡觉。
    - 【2025-03-14】按照昨晚的规划，今天应该是把前面所有工作彻底做个收尾。为什么呢？因为原本不是规划了两个项目嘛，一个是podhunt，一个是popjob，现在这两个不是都cancel掉了嘛。既然如此就需要真正开始准备面试了。但是在此之前还要处理以下问题：
    - 1、早上刷newsletter，看到pocket casts现在也有web端了。想到昨晚还在为昨天早上（以为xyz的rss全挂了，所以）把这些rss通过 listen notes 重新全部订阅新的rss，但是。
    - 2、又把flomo app恢复了。这也是做了很多权衡后，最后的。
    - 现在将近下午4点，终于把昨天那个弄完了。
    -
    - 【2025-03-15】今天把上面那个收尾的task处理掉了，可以真正开始准备面试题了。当然，我需要从处理之前的技术书（也就是task-temp.yml里面的那些）开始。
    - 【2025-03-16】昨晚临睡前还在想qs的数据结构怎么调整。早起确定之后，早上10点整才到图书馆开始弄这个（当然在此之前还有处理newsletter什么的），直到12点半才把data里面所有的qs相关数据格式换成新格式。下午2点回来就开始修改相应vue代码嘛。当然，也做了一些优化，什么把所有struct都整合到types里，避免重复代码之类的。也做了点样式调整。但是终究还是比预期的慢。这东西原本不应该有这么麻烦的。另外，今天中午把那个Cheetah Pro买了，国补之后850，感觉还可以。
  des: 本周懒得写什么review，正如item里列出来的。无非还是维护和优化docs里面的东西。然后把现在之前从14号到16号都是收尾之前的东西。13号弄rss2newsletter。下周正式开始




---
# w13 (2025-03-24 2025-03-30)






- task: 2025w11
  pid: PWR
  date: 2025-03-23
  topics:
    - topic: 【2025-03-17】
      des: 今天早上还是，下午正式开始处理之前的books。下午几种都整理了一下，宏观经济学、行为经济学、微观经济学、地理、国家比较分析、理财相关、个人效能之类的。都直接发了deepseek让他评分+犀利评价，从这些书里只筛选几本真正值得去读的书。目前希望可以用3天把今天下午码出来的这几本书看完整理好。也就是预计本周四弄完books相关所有task。晚上写了个《大棋局》的des。
    - topic: 【2025-03-18】
      des: TODO：1、解决掉PM相关书籍。2、整理地理学相关qs（不看书了直接整理中学地理相关知识点/考点）
    - topic: 【2025-03-19】
      des:
    - topic: 【2025-03-20】
      des: TODO：1、继续看宏观经济学相关书籍，彻底结束掉该task。 2、把新买的这个 Cheetah Pro 出掉二手。 3、晚上回去先把衣服洗了，洗完之后，把那双多威隐忍扔进去洗。
    - topic: 【2025-03-21】
      des: REVIEW：今天没去图书馆，早上6点半就起床了，磨蹭到7点半才下床。然后把衣服洗了，然后想着把鞋子也洗一下。这就是为啥今天没去了。本来想着洗一双，穿一双，但是想了一下还不如两双一起洗了。早上断断续续把昨晚没弄完的那个东西搞完了。
    - topic: 【2025-03-22】
      des: REVIEW：这两天都挺颓的。从前天晚上开始，昨天没去图书馆，今天也没去。现在想来两点原因：1、气温骤升，整个人状态就跟着不对，无精打采的。2、前天晚上把鞋洗了，第二天没去正常，昨晚临睡觉前，现在用的这条酷泰克的TypeC数据线还坏了，充不上电。那今天怎么去？只能今天重新买了条普通的TypeC数据线。真是后悔之前把那条TypeC数据线扔了，我是没想到这么贵的线还没用一个月就坏了，。***但是再往前追溯的话，也可能是因为这两天又重新开始跑步，整个人两腿沉沉很疲惫，***
  qs:
    - "我的TypeC数据线和充电器坏了，现在我急需需要新的，但是遇到以下问题：因为我需要买绿联之类的我想自己购买的品牌产品。所以我不想直接通过美团之类的外卖来购买（因为这些大部分都并非品牌产品，并且价高质低，比如说以上的品牌商品可能才30，但是通过这些渠道购买的白牌产品就要60）。但是通过网购买这些东西，通常需要1、2天才能到，无法满足“急用”需求。怎么处理这个两难问题？  ***按照是否紧急分为以下情况：如果需要在3h以内需要，那就直接闪送。如果半天内需要使用，那就当日达。综合来说，性价比最高的方案是暂时借用+当日达。***"
  review: |
    本周主要是看书，主要是之前这些东西都堆到task-temp.yml里太烦了。现在是【2025-03-23】下午3点半，又是无所事事的一天。今天又没去图书馆。为啥呢？因为我想着能八点半准时到，但是今天早上起床八点整，本身是能去的，但是因为TypeC充电线还没到，只能用别人的线，充完电就9点出头了。去的话就又迟了，想了下就懒得去了。

    昨晚就在想，我TM怎么就混成这样了？住在这么个青旅混住间，一堆乱七八糟的东西，也做不到极简（最近又买了一堆乱七八糟的东西，什么 、、、、）。

    - 把病历本扔掉了。我这个病历本里也就2019年年底在交大仁济医院看的胃食管反流，以及2021年年初在十院拔掉龋齿。
    - 把《老子》和《论语》扔掉了。

    回顾本周，确实一事无成。正如昨天所说，分成三部分：前两天看书，中间两天搞wiki（把之前wiki的YAML挪到task里），后面三天都没去图书馆纯躺平。

    ---

    其实躺平这几天我更想探讨一个问题

    我讨厌自从去年12月上班以来，手上稍微有点小钱之后买的这堆东西。真的让人恶心。茶叶、跑表、啤酒、饼干、足浴盆、什么的乱七八糟一大堆。

    我打算把以下物品出掉二手：

    - 跑表。
    - 去年年底买的那条NIKE ACG FN2451. 真就不如我平时穿的这条必迈跑步长裤，各方面都不如。无论是夏天还是冬天，都不如。那个阔腿裤真的穿起来很难受，很不适。

    我说了，我的东西就这四种：两个睡袋；一包衣服；一个MBP。

    其实我这两天纠结的就两个问题：

    - ***开放系统和封闭系统***

    再次审视我的goods，毫无疑问的，下一步可以极简掉的物品：

    - ***【MBP】、MagicKeyboard键盘、Airpods、Lighting数据线。*** 看来看去，下一步可以极简的东西就这几样。换了MBP之后，后三样东西都可以直接砍掉了。


    ---

    【2025-03-24】现在是第二个周一了，刚刚想到，其实这次“差点坠入虚无”并非因为什么，那些都是一些misleading，现在想来归根到底是因为看宏观经济学书籍导致的，又陷入了思维误区“既然迟早药丸云云”。但是话说回来，这次我应对的着实不差，只用了2、3天就完全意识到问题，并且摆脱出来了。






- task: 2025w13
  pid: PWR
  date: 2025-03-30
  topics:
    - topic: 【2025-03-24】 # 处理 mysql相关、algo相关books、kernel相关、k8s、langs相关、arch相关、kafka、LLM、编译器、网安、Rlang
      qs:
        - 今天把上个阶段的东西收个尾（也就是上周看的几本书什么的，在ds上的一些chat history）。
    - topic: 【2025-03-25】 # 今天先把昨天没有整理完腾讯元宝的chat history。之后把塔勒布的几本书看完，写篇blog
      qs:
        - 早上先把rss2newsletter.yml里面ytb-podcast的这些podcast，都整理到PocketCast里面。其实也是必然，既然之前xyz的podcast都整理到PocketCast里面了，剩下的这些ytb的也是迟早的事。然后
    - topic: 【2025-03-26】 # 今天还是继续处理ds里面的chat history，完善几篇书的读后感（《福格行为模型》、《》）。看完这些之后，看一些统计学相关的几本书，预计耗时3、4天。
      qs:
        - 1、早上到了图书馆之后，越看Task列表越不爽，感觉这种sub task的形式很拖沓。尤其是之前网文、h书的这几个动辄里面几十上百个sub task的。本来尝试把这些书都弄成item，搞了半天正则。已经弄好了之后，想到现在这种struct形式毫无疑问更清晰，万一以后还需要，就想到github的那个drawer里的table，想到其实把这些sub task直接在task的drawer里展示为table，是更好的方案。所以简单实现了一下。这个就不单独记录task了，半个小时内就弄完了的，没必要。
        - 2、下午一直到晚上散步前，把之前在ds的所有chat处理完了。这个真心不错。我觉得我尽力了。
    - topic: 【2025-03-27】 # 今天该看统计学和概率学相关的书籍了。这是个新领域。
      qs:
        - 1、今天一个重要决策：决定之后直接把Monthly做到task的newsletter里了。也在cron.yml里把这个「月刊task」移除掉了。所以把之前的那些Monthly。因为确实这个东西确实有些牵扯精力。但是我也不会直接
        - 2、移除了cron.yml里面之前的id这个key：原本是需要
    - topic: 【2025-03-28】 #
      qs:
        - 1、早上尝试写了个Anti-Fragile-Formula.md，也就是NNT的那个反脆弱应用到
        - "2、把之前 「blog/2023/Minimalist.md」 整合到 task里了（之前 `pid: wiki` 的minst那个task里）"
        - 3、清理一下这两天把ds里弄到scratch.md里的内容整理一下。 # 现在是下午三点半
        - 4、
    - topic: 【2025-03-29】 # 发现
      qs:
        - 今天早上确实没有close任何事情，都是干了个半截。总之到了周六周日，又是收尾的时候了。总之今天无论如何，是要把docs里task相关的几个调整，部署上线的。另外，希望能把scratch（以及ds的chat history）彻底清掉（这个本应是昨天的task）。这些搞完之后，就继续看统计、概率、博弈论相关的书呗。
        - 早上9点半整到图书馆。现在是中午一点整。
        - 【17:27】搞定了那几个docs项目相关的bug。爽写代码，写完了神清气爽
        - 【17:52】[自动签到，白薅50米（无需服务器） - 福利羊毛 - LINUX DO](https://linux.do/t/topic/207204/28) 把这个搞了一下，186和192两个手机号都加了。看看能跑不，半年100块钱。（换流量卡一直也是半年一次，正好搞这个就ok了）【2025-03-30】看到logging里两个都执行失败了，用apifox直接跑API，返回“验证码校验失败”。查了一下就是现在加了验证码校验，只能用headless去绕过
        - 【20:57】把昨天没写完的那个「笔记管理和信息源管理的两篇blog整合到YAML」弄完了。但是还没写那篇「知识管理：从输入到输出的通路」的blog。出门遛弯，大概11点回来直接睡觉了。晚上出门看点
        - 【23:27】散步回来把Task的数据重新梳理了一下（比如）
        - 今天想到的两个点：1、之后都可以像现在这种直接在周报里加上【time】，确实不错。2、晚上散步时想到，今天竟然把这么两篇从2022年左右就构思，不断迭代到现在的blog，都重新整理回YAML了，就觉得感慨万千。事实上，归根到底这个问题在于这两篇blog的结构确实有问题，并且这两篇blog也确实没必要发出来。现在来看写到YAML是结构更清晰的做法。反思一下：现在写blog都是把素材不断堆到Markdown，这种做法是不完善的。之后写blog说不定就直接都放到TASK的YAML里了，做到充分积淀之后再发出来。
    - topic: 【2025-03-30】 # 1、延续昨天整理笔记管理和信息源管理，把心心念念的那篇「知识管理」写到Task的YAML里。预计90min。2、写周报（以及收尾掉这个w13.yml）3、
      qs:
        - 【10:16】早上【09:50】到图书馆。按照惯例先处理newsletter
        - 【10:54】把task里books相关的
        - 【14:51】写周报，复盘本周task。以及列出来下周的TODO
        - 【17:46】在写「」。顺便整理了《比较政治分析》和《列宁著作》相关内容
        - 【22:20】整理 query-algo-vs.md 到Task/blog
        - 【22:33】【把cursor.md也整理回gh里cursor这个repo】
  review: |
    本周原本规划的是看书（经济学相关和NNT的几本书），但是到了周日，复盘时看到其实并没有按照规划完成。简单捋一下就可以看到（实话说在复盘时，原以为本周真就啥都没干，但是仔细看了一下这个w13.yml，其实还是做了不少事情的），本周close掉的事情：

    - 1、【Books】给两本书写了书评《兴亡：中国两千年治理得失》和《中国是部金融史》，两本垃圾。这个时间其实是浪费掉了，但是从reflect中获得了很多思考，也不算完全浪费。
    - 2、【Books】看了“列宁著作”和《比较政治分析》。
    - 3、【Blog】「笔记管理」和「信息源管理」的两篇blog整合到YAML
    - 4、【Blog】从《福格行为模型》着手，写了一篇blog来对全部“个人效能类书籍”有了一个完整的思考。
    - 5、【Docs】对admin的task的数据结构做部分优化，修改相应代码。
    - 6、【Newsletter】对 通识教育、信息差 有了更深入的认知。尤其是 通识教育金字塔，对我而言真的是很关键和有意义的思考。

    还不错的一周，但是不够聚焦（但是也不能说这些task都毫无意义，因为也都是必要的）。下周需要更聚焦于











#- task: 对admin的task的数据结构做部分优化
#  date: 2025-03-27
#  pid: docs
#  sub:
#    - task: 同时保留Qs和Tpcs两个用来处理qs的字段

#    - task: 同时保留des和review

#    - task: 把cron.yml数据整合到rss2newsletter

#    - task: 移除Goods的Drawer里面的「当前物品」，都放到「物品列表」里



#- task: 【不再展示blog里的月刊】把之前写的几个月刊都设置为unlisted
#  pid: docs
#  date: 2025-03-27
#  qs:
#    - 为啥写这个东西是有意义的？ # ***周刊，维持成本高，但是ROI低的事情*** 但是这种形式是对的，也就是，我要做，但不是这种方式，也不是之前的方式。而是一种时间成本更低，更可持续的方式来进行。但是也确实想不到除了这两种以外，还有什么方法。相比于之前，应该写一些有完整逻辑的，300字左右的短评，而非之前的一句话短评。这么做，成长会更快。用心写过的东西，印象也更深刻。久而久之这些短评就可以组织起来形成文章。短评无论如何是无法形成文章的。
#    #  因为没有必要，都说了“新闻要连起来看”，如果
#    #  但是，写周刊也有其正面作用，我现在的diary就是因为里面夹杂了大量随手记录的一些东西，导致文件很大，影响browser渲染和IDE加载速度。所以就想把这些东西单独抽出来。
#    #  并且，我今天试着写了一下周刊，发现相比于
#    #  我的想法是：我应该写周刊，但是并非“那种形式”，而是
#
#    - 为啥决定写月刊，而不是周刊或者双周刊？ # 其实就是几个维度，内容更新频率、读者需求、市场定位。周刊是正路，但是
#    - 我的月刊中填充哪些内容？为什么选择这些内容？ # 其实就是个人feed汇总精选，再加点自己的短评。但是需要注意的是，“我的diary不就是日刊嘛”，对也不对，***diary的对象是我自己，周刊的对象是他人***
#    - 【2025-03-28】为啥决定不再blog里的月刊呢？ # 1、最直接也是最关键的，月刊里对News的评论会暴露一些个人想法。如果说想要保证个人隐私，在社媒上“不点不发不评论”，那把这些评论集中写到blog就太蠢了。这里还有个悖论：其实没有流量，更遑论流量变现。但是如果有心人去看的话，又会翻到这些东西。都不是得不偿失了，而是只有坏处没有好处的事情。2、在对自己有一些好处（就是上面那个qs里列出来的，能够梳理自己的一些想法）的同时，也确实会牵扯一些精力（1、需要定期整理、排版、发布。）。3、既然有好处，那用什么来替代呢？之后直接写到 `pid: newsletter` 里。
#    - docusaurus的unlisted和draft有啥区别？draft只在dev环境展示，unlisted在prod和dev都展示。其实是我之前用反了。二者的唯一区别应该在于是否施工完成。



---
# w14 (2025-03-31 2025-04-06)


- task: 2025w14
  pid: PWR
  date: 2025-04-06
  topics:
    - topic: 2025-03-31
      qs:
        - 【10:40】早上【09:40】到图书馆，整理了一下今日newsletter。好像其实也没啥收获。
        - 【11:21】把之前blog里的的「奉化马拉松」和「golang-changelog」这两篇，都重新转成YAML格式，存在Task里了。
        - 【11:30】想到一个点。昨天下午开始，不是在蚂蚁搬家，把之前blog里的markdown，重新整理回YAML嘛。那么为啥不把之前的Monthly月刊也做成YAML格式呢？
        - 【14:28】午睡起来（【12:50】出门吃午饭，【13:40】散步回来，【13:50】开始午睡）。
        - 【17:16】【把之前blog里的所有月刊都整合到（以YAML为数据格式的）pid为blog的Task里】。直到【16:33】都在完全浪费时间，这种重复性工作，原本希望可以直接让LLM来帮我处理嘛，但是尝试了cursor和trae之后都TM搞的一坨，跟个傻逼一样，搞了120min，我TM真是个傻逼。之后也懒得手动逐条处理了，直接把这些markdown直接通过注释的形式弄到这个 w14.monthly.yml 里 。 # 一些反思：1、其实原本这些月刊写完之后，也基本上没有再看过。所以我决定直接通过注释的形式挪到YAML就可以了，不需要太当回事。 2、我考虑之后把把newsletter整合到月刊里。格式如下：把之前News以及TechNews之类的都直接放到item里。topics里面则用来存一些经过思考的相关问题（这也是符合其他地方对topics使用的规范）
        - 【20:29】记录一些想法：1、可以把这个从diary改成task了，为啥？因为其实这个系统本身已经转型了，现在所有的task分成两部分，
    - topic: 2025-04-01
      qs:
        - 【10:31】早上【09:30】到图书馆，日常处理gmail上的newsletter, 以及weibo, zhihu之类的
        - 【15:00】***之后使用PARA对task进行改造***：1、把之前的pid=video和pid=newsletter都改成pid=archive 2、把pid=books也拆分，部分拆分成blog，部分拆分成archive，这样就很清晰了。
        - 【15:33】早上把【彻底处理掉docs的图片相关问题】找个时间需要重新弄一下，之前找了个直接把。搞定上面那个之后，把现在存在Google Drive的个人生活图片也存到R2，新建一个travel的bucket
        - 【16:17】【把之前pid=money整合到pid=minst里】1、顺便注释掉了部分没什么用的task（其中大部分是pid=docs的task），主要是现在
        - 【16:50】【注释掉部分（没有意义的）task】之前是想作为一个纯粹的记录工具（所以一直保持这个文件名是diary，而非task），但是在之前在使用时会遇到什么问题呢？1、复盘困难。2、task都非常碎片化，不着重点（更类似于TODO管理，当然这个更多还是认知问题，我原先想做的本身也就是个TODO管理，也是用了一段时间之后才发现这个是有问题的）。在遇到了这些问题之后，从去年11月份（单纯在YAML里记录和查看），再到今年2月（开始搞vben之后，逐步在vben里展示task），再到使用了一段时间后发现以上问题后，对功能进行调整的最近一两周。现在这个task已经转型为了原先预期中的「个人wiki系统」（其实简单来说就是把pid=blog的内容都以YAML形式整合到这个task里面了）。原本在使用这个系统中遇到的困惑，也获得了一个暂时的纾解。得到以下结论：1、技术类书籍，最终还是得整理回gh.yml 2、
        - 【18:42】【移除了托管在cf的 web-archive 和 c2】1、c2就不说了，之前想找一个直接托管在cf上可以用来管理cf R2的workers时部署的。但是找了一圈没有一个好用的，今天重新部署了alist，自然就把这个删掉了。2、至于wa，之前是用来作为archive服务使用的，但是现在有task了，更重要的是在alist直接对R2进行CURD之后，直接把这些网页截图之类的东西存到R2上。直接用截图就比之前这种网页存档方便了。所以这个也用不到了（更何况wa除了cf workers，还需要搭配chrome插件使用，（移除之后）还能节省内存开销）。
    - topic: 2025-04-02
      qs:
        - 【09:45】日常整理每天的newsletter。 # 这里其实我想反思一下昨天晚上的一个电光火石之间的错误决策导致的后面一系列问题。昨天晚上从图书馆出来之后有点饿了，看了下之前美团的团购券，看到之前有个¥6块多的炒饭券，就去了。反思：
        - 【10:20】【流量卡换成电信小米卡//晨曦卡】敖丙卡Plus【19元235G+畅享速率＋长期套餐＋100分钟通话】
        - 【12:40】基本梳理清楚《国家比较的定量分析：周期中的我们》
        - 【11:20】处理掉了现在手头的所有ds的chat history，以及什么乱七八糟的一些东西。现在真的很乱。今天周三了，必须要赶紧开始看书了。
        - 【14:24】【添加 TaskDetailDrawer 组件。增加代码复用性。】中午12点40出门吃饭，还是惯例去Dicos恰了两个双鸡堡，中午13点40回来。 # 之前drawer里面的嵌套drawer是单独写了两个CardDrawer组件（导致复用性差，且不支持无限嵌套子drawer）
        # fix(admin): 添加 TaskTable 组件，让 task/index.vue 和 TaskDetailDrawer.vue 可以直接复用这个组件。之前这个drawer里面的table是直接基于naive-ui的NDataTable组件实现的，不是很好用。
        # 这是因为在TaskTable组件中，主表格的des字段配置了showOverflow属性为true，但是在gridOptions的columns配置中，des字段的tooltip显示是通过showOverflow属性来控制的。当在主页面使用TaskTable组件时，showOverflow属性生效并显示tooltip，但在抽屉中使用相同组件时，由于传入的数据结构和配置略有不同，导致showOverflow属性没有正确应用到des字段上。建议检查gridOptions中des字段的showOverflow配置是否正确传递给了抽屉中的表格实例。
        - 【21:24】彻底处理完
    - topic: 2025-04-03 # 今天还是先把昨天要做的（主要是chat history，以及）收尾掉。之后把整个中学和大学的数学定理过一遍。
      qs:
        - 【11:21】早上【09:30】到图书馆，先是日常整理newsletter（昨天确实有一些大新闻，刷了会zhihu，但是也几无所获）。然后 1、统一了所有pid=PWR的task的名称（比如说统一用【周报】//【周刊】这样的prefix之类的）
        - 【11:33】记录一下昨晚散步时（对最近比较纠结的是否要）的想法：1、为什么之前想去给record单独去增加一个数据结构（而不是像之前那样直接通过类似 `【time】task` 这样的形式进行记录）呢？因为感觉这样会比较格式化，更清晰。 2、为什么不考虑使用sub task呢？因为这样还需要逐个点击进入相应task，不够直观。 3、在这个最终方案里，怎么判断该task是随手记到【周报】里，还是做成单独的task？这个就是比较吃经验的地方了，一个简单的金标准就是，该task是随手就能做完的？是当天能做完的？还是至少要2、3天才能做完的？这个标准很简单，如果随手完成的当然就随手记，如果要2、3天才能做完的，那肯定是“事前有预计，事中有执行，事后有复盘”的task了，当然需要单独列出。 4、~~那我怎么把这些单独task，引入到我们这个周报里？比较好的方案是，添加一个slug的key，通过slug进行引用。但是暂时不实现。事实上我感觉可能也不太需要这个东西。~~ 不需要考虑把task引入这个周报task的问题，在每日的review里随手记录一下就可以了。这样也更灵活。
        - 【12:31】把yb的chat history清掉了，相应内容已经弄到。出门吃午饭，华莱士的“两堡一卷”。***一些小感想：图书馆真是个不错的选择，能给人一种模拟上班的，不至于让生物钟太乱。***
        - 【13:55】午睡起来。
        - 【17:38】重新refactor了一下task这个文件夹（在task下面新增了blog和TODO两个文件夹）。***顺便把scratches和ds的chat history清掉了。***
        - 【18:25】顺便处理了里的部分feeds 1、
        - 【22:01】把rss2newsletter里的feeds重构了一下。1、给type新增了record，用来记录该type的CHANGELOG。2、细分type（比如说把之前的coding拆分为golang, ts）
      des: 今日复盘：复盘时看到，现在确实每天摩擦性损耗不低，并且效能也一般。早上
    - topic: 2025-04-04
      qs:
        - 【09:39】刚到图书馆坐定。
        - 【12:46】吃饭前把所有rss2newsletter。其实就是继续搞昨晚未完成的事情，再加上原本就。顺手给docs-alfred的rss2newsletter里原本feeds list的table里新增列updateTime，以及updateFrequency。用来监控某个feed更新状态（比如是否已停更）。超预期完成，很满意。这个task的结构性损耗和摩擦性损耗都几乎为0
        - 【13:49】吃完饭上来。吃完饭去丰巢把新办的电信流量卡拿了。
        - 【17:47】今天下午4点半图书馆就闭馆了。把pid=docs的task都注释掉。***顺便梳理了一下整个docs的CHANGELOG***。其实做的时候就后悔了，最简单的操作是，直接在admin里filter掉所有pid=docs的task就可以了。没必要手动注释。
        - 【21:41】出门散步。目前在看收拾出来的这些blog以及bz和。不打算做成task了，我想了一下，这东西还是应该直接整理回gh比较好，顺便对相应的这些技术栈本身查缺补漏和回顾一下。预计明天一整天也还是要搞这个东西。
    - topic: 【2025-04-05】1、早上先把效能类处理完。2、下午处理coding相关post//视频
      qs:
        - 【15:05】刚刚把效能类的东西处理完。顺便按照早上的思考，把gh里之前的 `isX=true` 改成 `score=10`。早上【09:20】到图书馆。【12:50】出门吃饭。【13:50】回来又弄了一会。到【14:20】睡觉20min【14:45】起来。
        - 【16:23】随手搞了下
    - topic: 【2025-04-06】
      qs:
        - 【11:13】早上【09:10】到图书馆，到现在120min。先是10min处理掉了所有今天的newsletter。刷nl时，想到现在的feeds还是太多了，就重新整理了一下。以后的相应规则是：feeds中最多9个type，每个type最多9个feed。之后又验证了一下blog的cf-pages-auth是否生效了，结果看到其实没生效。只能再先处理掉这个问题。尝试拿blog和docs做对比，也没看到有啥区别。functions一直没生效。只能尝试把这个pages删掉重新部署。然后又遇到一个问题cf不允许直接删除deployments超过100的pages。查了一下文档，还需要下载一个 delete-all-deployments 的官方工具来清空掉deployments。【12:02】搞了半天发现确实搞不定，不知道究竟是啥问题。
        - 【13:56】今天希望可以把这个“极简生活大一统（包括效能类）”的东西搞完。从昨天下午弄到今天了。这个东西很重要，也确实不简单。我对这个东西的预期就是YAML打出来骨架，以及做出来那个类比图表（用来把那几篇《信息源管理》、《笔记管理》、《物品管理》的blog整合进来）就可以了。
        - 【16:19】经过再三思考，还是删掉了blog项目（看了一下262个commit，倒也无所谓。主要是决定之后就完全用YAML来写blog了，也没必要再留着这个repo了。这两天在做笔记管理的极简嘛，这也是一例）
        - 【19:55】想了一下，这个东西确实难搞。先就这样吧，先用
  review: |
    ## TODO

    - 塔勒布五部曲
    - 快速看一下现在列出来的 概率统计、概率学、数学、博弈论、行为科学统计 相关的这几本书

    ## REVIEW

    今天周日了，本周也是第一次完整基于time记录每天的主要活动，复盘可以看到。我本周的TODO完全扭曲掉了。

    原本希望处理上面列出来的这些书的，结果一点没看。







---


- task: 2025w15
  pid: PWR
  date: 2025-04-13
  des: w15 (2025-04-07 -> 2025-04-13)
  topics:
    - topic: 【2025-04-07】今天预期先把 # 周一
      des: 昨晚在收工之后，灵机一动，想到其实可以直接。今天先把上周五弄出来的那些之前rss2newsletter里的技术类feed处理完。之后再杀个回马枪，把这个“极简生活”的blog写完。【xxx】昨天feed没处理，archive没处理，晚上回去刷了一晚上关税战相关新闻。
      qs:
        - 【10:12】弄完了今天的newsletter。早上【9:00】到图书馆，之后刷feed，刷zhihu，刷weibo。然后又
        - 【13:30】吃饭回来。【12:30】下去吃饭，hls4个汉堡。散步20min。
        - 【14:10】午睡。中午散步时想到「仍然是PARA，但是archive不应该放到task里（task终归还是应该作为TODO管理，而非知识管理）。转念想到可能现在的gh不就是现成的archive工具吗？为什么不把这些最近特别纠结的archive内容都塞到gh里呢？」。午睡前把所有pid=archive的task都弄出来了。
        - 【15:42】把 history, TV, movie, jav, 唐诗宋词之类的整合到gh
    - topic: 【2025-04-08】
      des: 白天主要在刷这次关税战相关的feed，晚上继续把之前剩下的pid=archive的内容，挪到gh里（但是还没弄完）
    - topic: 【2025-04-09】
      qs:
        - 【11:07】调整task里的内容（1、采集站 放到type=freelancer 2、task里pid=blog的跑步，也整理回gh）。早上【9:30】到图书馆，同样还是刷了会关税战的最新进展。
        - 【17:57】在搞kernel相关过程中才发现，之前gfb的时候，不小心把kernel.yml给弄没了。卧槽，真TM后悔啊。
    - topic: 【2025-04-10】
      des: |
        从2025-04-02到昨晚，这波关税战2.0基本上按下暂停键了。

        从7号到今早，高强度刷这波关税战也可以按下暂停键了。真是场酣畅淋漓的大戏啊。这几天高强度刷NGA、weibo、zhihu、bz、huaren、twitter 之类的各种feed。另外这几天刷twitter，从侧面印证了几点：MAGA内部的分裂（Carlson说不要在当下对伊朗动武，下面被MAGA小将们喷了5000条，基本上都是GBA那套，“不会吧，你不会认为我们连伊朗都打不过吧”，MAGA们真就智商低到令人发指。）、看了下NNT对这次关税战的看法（同样被喷了几十条）。总的来说，倒也不必苛责自己对

        另外这两天也在反思的一个事情：

        1、为什么这次离职之后，没有xxx呢？TMD还是温水煮青蛙了。结果到现在又是2个月过去了，我还不如去xxx找个小房子呢

        2、这两天xxx天天被催。前两天晚上散步时想到，为啥我之前有14w存款的时候。没有把这些钱存理财，然后直接躺平呢？其实也是温水煮青蛙了。结果弄到现在这么被动。

        3、


      qs:
        - 【10:25】我TMD刚刚。现在捋一下正确思路：1、找到cf pages上仍然有kernel这些相关数据的deployments 2、找到相应的commit id 3、去GitHub上找到相应的commit code tree 4、从里面找到这个kernel.yml文件。 如果按照这个思路处理的话，一共也不要20min。结果从昨天下午4、5点，昨晚弄了一晚上。今天早上才搞定。
        - 【16:57】把之前整理rss2newsletter清理出来的那些技术类feeds处理掉了。应该说确实远远超出DDL了。原本上周末就应该处理掉的。但是也总算是弄完了。
        - 【21:49】把之前的那些个scratches处理掉了（其实还是之前pid=archive的那些内容），出门散步。今天可以休息了。
    - topic: 【2025-04-11】
      qs:
        - 【15:03】午睡起来。早上【9:20】到图书馆，继续搞这个scratches呗。
        - 【18:46】把minst那个blog弄完了
        - 【21:45】【注释掉了所有pid=fl, minst, gs 的task】顺手把 task页面的筛选项去掉了（既然现在task只有pid=PWR和blog这两种了）。其实是在搞上面那个
      des:
    - topic: 【2025-04-12】
      qs:
        - 【11:09】早上【10:00】到图书馆。之后日常刷newsletter，以及
        - 【12:24】搞定了 【把前两天没写完的那篇“极简生活”的blog弄完】和【把这两天技术图片弄到R2里（主要是leveldb相关）】
        - 【14:30】睡完午觉，中午吃完饭【13:40】上来。
        - 【把现在scratches里面那25个文件全部处理掉】
        - 【15:52】【把之前的newsletter从docs里抽取出来，又分门别类地弄到gh里对应的地方】这个其实偷了个小懒，弄了一个小时也只处理了百来行，确实有点慢了。就一股脑全都塞到temp.yml里某个repo下面了。
        - 【16:21】【处理ds和txyb的所有chat history】
        - 【18:04】搞定了以下：【docs的gh默认score desc排序】、【把docs-alfred的#type搜索 和#tag搜索 加回来，还需要加上相应的admin跳转】、
        - 【18:50】搞定了以下：【docs-alfred的跳转有问题】
      des: 今天还真就可以。把什么scratches, chat history, 极简生活blog 都弄完了。以及docs和docs-alfred的bug全部fix了。
    - topic: 【2025-04-13】 # 这里补一条
      qs:
        - 【10:57】早上【9:40】到图书馆。之后还是惯例刷newsletter，把昨晚的docs-alfred之类的收了个尾。
        - 【】1、看【概率学】、【概率统计相关】。写【怎么用概率统计来解释社会学定理？】
        - 【12:51】【把之前的（周维度为单个文件）的task，调整为年维度为单个文件】
        - 【16:34】【写周报】本周的周报写的比较详细，仔细复盘了上周，离职后的近两个月，以及下周task的大概安排。中午吃完饭【14:10】上来，【14:20】睡午觉20min
        - 【21:21】【给昨天添加的docs-alfred里的#tag#type支持fuzzy filter】、【给docs的gh也加上了qs】。【2025-04-15 15:33】另外注意，取消了【task里需要给blog加上task?blog=<slug>的param】这个task。因为意识到，需要保持“高内聚，低耦合”，如果添加该功能，就意味着task的blog变成了gh的一个pkg，两个模块就耦合了。
        - 【16:32】晚上把【塔勒布五部曲】看完了，也算是了却一桩心愿。
        - 【11:20】晚上彻底弄懂了“美元环流”，自己画了个图
        - 【00:15】看了《黑镜第七季》的解说
      des: 今天早上给几个之前task里pid=archive的什么经济学、历史、个人效能、团队管理、投资 之类的内容，都加上了score=10，以便突出重点。下午写了周报，然后又添加了一些docs相关功能。晚上把塔勒布的几本书看完了。
  review: |
    又是周末。时间飞快啊。

    ## 本周归根到底就是三件事：

    - 川宝搞的没头没尾的关税战。从周一晚上开始刷，周二周三，一直到周四早上，基本上都在刷。现在也来做个复盘。先说这轮关税战，应对的确实不错。从周一的“惊涛骇浪”、“woc，大的要来了”，再到周二的“”，到了周三周四，美国连续两天股债汇三杀。就已经完全变成笑料了。
    - 进一步完善了“极简主义”那篇blog
    - 主要就是把之前耗费了很多精力弄的task，精简化了。移除掉了所有pid，之后只保留PWR。其他什么archive之类的都整合回gh里（以及把pid=fl, minst, gs之类的也都顺手处理了）。把task和gh页面的筛选框也都去掉了。也实现了相应的docs-alfred功能，来辅助docs中gh的筛选操作。

    ---

    ## 下周安排

    下周毫无疑问要真正开始准备面试了。自从2月11号（2025w6）离职之后，到现在整整两个月了，之后做了这么几个事：

    - 1、基于vben搞了这个docs（不仅是实现，还有现在这套mock）
    - 2、整理data
    - 3、尝试搞podhunt和popjob这两个项目，但只是浅尝辄止。这个东西需要投入的时间成本太大了，暂时没法搞。只能放弃。
    - 4、极简xxx（包括 feeds、）
    - 5、从w11（3月20号左右）开始想把之前整理的那些宏观经济学、、、相关的书看一下。但是实际上也没有真看，搂了几本书看了看，什么“塔勒布系列”、《大棋局》、、，还有几本其实很垃圾，完全没必要看的书。某种程度上又是“赛前豪言壮语、赛中胡言乱语、赛后沉默不语”。最后也算是再次验证了2条经验吧，我们还是用这个“极简主义workflow”来分析一下这个事情。其实所有问题都是本就应该在Preview阶段就应该预判到的（这个东西就类似于我们做架构时的“封底估算”）：
      - 1、正如塔勒布所说“多读经典”，读书不在于读了多少本，真正高效的办法就是把真正的经典著作读10遍、100遍。“书读百遍，其义自见”。正确的做法是从原先的书单里每个系列只挑选最多两本（其实这点我也做了，但是奈何即使只有两本书，实际上也很多。这就涉及到第二点了）。
      - 2、如果即使已经给每个类别里的所有书，做了排序。如果种类太多，也还是需要看太多书。那么其实还需要对这些分类做个排序。抓大放小。比如说需要看 概率统计、随机过程、博弈论、、、宏观经济学、微观经济学等很多类别的书籍。那么应该从易到难，从学习roadmap来划分书籍分类的先后顺序。

    总的来说，

    ---

    至于下周基本上。那么同样按照PQ4R的方法论来 preview 一下：

    ***下周未必能处理完所有coding books里面的东西。因为现在分类就有9种，一天一种肯定是不够的。并且本身“一天一种”也很困难***。

    所以从易到难重新排序：网络协议、回顾、algo、mysql、kernel、kafka、langs、k8s、arch




---


- task: 2025w16
  pid: PWR
  date: 2025-04-20
  des: 通过 coding-books.yml 顺一下整个coding相关的面试知识点
  topics:
    - topic: 【2025-04-14】 # 周一 # 回顾
      qs:
        - 【11:10】弄完了今早的newsletter，顺便把之前的chrome里那些乱七八糟的东西收尾掉。早上【9:50】到图书馆。
        - 【11:55】把本周要弄的所有书，从coding-books.yml弄到本task里。
        - 【14:10】午睡起来。中午【12:40】出门吃饭，【13:40】开始午睡。
        - 【14:59】把几本需要细品的书（DP和《重构》这本代码规范）挪到gh里面了
        - 【17:02】把之前那篇【RDB数据类型】的blog，作为topic整理到gh了。另外，刚刚想到，如果像我。所以把
        - 【17:53】清理了R2里面blog文件夹，只保留了作为2024、2025这种年份的文件夹。其他文件夹都分门别类整理到相应文件夹下面了。

      des: 今天非常完美地按照计划执行了【回顾去年面试前看过的这几本代码相关的书】。实话说远超我的预期，这20多本书，我昨晚想着很可能是弄不完的。所以做了规划，这就是我现在的方法论，预判和“磨刀不误砍柴工”。把这些书拆成了几种：评分低不看的、需要细品的（整理回gh）、需要今天解决（只有11本了）。再用PQ4R的方法，高效处理这些。从而顺利完成任务。 可以看到今天整理到的主题主要包括：redis、网络协议、HTTP、docker、架构设计。


    - topic: 【2025-04-15】mysql #
      qs:
        - 【10:00】把日常feed刷完了，早上【9:30】才到图书馆。 # zzz
        - 【12:25】
        - 【12:25】用excalidraw重新画了昨天那个dockerfile最佳实践的图
        - 【15:44】【下午先把scratches里面的YAML文件处理干净，预计耗时20min】。中午【13:20】下去吃午饭，【14:20】上来之后直接睡午觉20min。
        - 【17:08】【把所有之前task/blog里的几篇blog（db-types, minst, query-algo-vs, ...），都整合回gh里面】今天中午不是想通了，之前想要的那个把blog写到task，并且通过slug去跳转，是错误的嘛。所以就直接把这些从task给弄到gh里面了。
        - 【17:37】【1、把gh的score=10的repo的背景色从lightsteelblue调整为lightblue（比之前清爽很多）】【2、fix了之前gh在移动端下不展示“操作栏”列的bug】
        - 【...】打算把《数据库分类》、《存储引擎横评》、《LSM》这三篇blog
        - 【21:10】开始弄这些东西。【19:30】回来吃了点东西，看视频，跟室友聊天。
        - 【23:53】【把两篇blog（《数据库分类》和《存储引擎横评》）合并为《从SE到DB分类》】借助ds，把这个东西搞定了。搞了个“以SE为抓手”的table。做了个整合。不错。
      des: 今天其实没弄完预计想搞的整个mysql，只能希望明天能搞完了。但是到晚上


    - topic: 【2025-04-16】mysql #
      qs:
        - 【10:00】到图书馆，早上8点半才起床，之后蹲坑、洗澡、吃饭全都弄完了才出门。
        - 【10:46】开始弄今天的东西
        - 【13:28】出去吃饭。早上用类比大概搞了下InnoDB的整个工作机制，但是还没弄完。
        - 【15:10】午睡完（【14:35】吃完饭上来，【14:50】睡觉20min）
        - 【19:00】从图书馆出来。下午本来想着把那个。结果task又没有聚焦，去搞xxx了。
        - 【22:15】【把之前在task/2025.yml 里面的内容，分门别类地整理到了gh、goods。比如说一些之前的新闻、ss相关、coding相关的内容】实话说，这个真的是ROI很高的东西。虽然从下午3点一直弄到晚上，但是也还算可以。【19:10】休息20min到，实际上休息了30min到【19:40】

    - topic: 【2025-04-17】搞mysql的第三天，还TM没弄完
      qs:
        - 【10:24】正式开始搞今天的东西。早上【9:30】到图书馆。日常搞了下今天的newsletter，又开了几个视频看了会。
        - 【14:49】准备午睡。
        - 【18:18】午睡起来之后。在想一个问题“mindmap是否有（类似excalidraw这样的）支持二次编辑的工具？”。找了一下实际上 DrawIO 是支持这个操作的。把之前在R2存的几个table转成YAML了，之后直接用“YAML-To-Table”渲染为table就可以了。处理了以下table：kvdb-vs.webp, chan-states.svg, MQ-vs.webp, CDC.svg, lsm-based-kvdb-diff.png, sharding-vs-partition.png, mysql_vs_pgsql.png, DHT-compares.png。都处理为YAML代码。


    - topic: 【2025-04-18】搞mysql的第四天了
      qs:
        - 【12:36】先给昨天下午到现在，搞drawio做个阶段性总结。总的来说还是：exd > drawio。刚开始觉得很好用，但是越用越奇怪，归根到底还是drawio相比exd，少了那种手绘的感觉。“我需要可以二次编辑的SVG图片”。实际上也很难说，到底是最终选择用exd还是drawio，我心里也暂时没个准，所以我暂时也不会把相关内容整理到gh。exd很好用，精致、。drawio的优势是组件。简单来说，exd的所有组件都是“指哪打哪”，drawio则是先有组件，再拖动和修改，就不是很直观。这里先说一下，为啥抛弃了之前用的Diagram As Code那几种工具（mermaid, plantuml之类的），因为实际上这些本质上是跟MD耦合的方案。这里的核心问题在于：导出的SVG图片无法直接导入进行二次编辑，换句话说，如果我想修改某个图片，还要找到之前的响应代码，才能修改（在这种情况下，也只有存在MD里的这些diagram才可以，又或者找个类似pollyoyo这样的支持云端存储这些plantuml代码的平台才可以）。我觉得到底用哪个，还是得多用多体验（后面同一个图，用两个工具都各自画一下，很快就会有结论），最后再下判断。
        - 【14:35】午睡起来，泡了杯茶，开始搞事。


    - topic: 【2025-04-19】第5天了
      qs:
        - 【15:31】今天状态确实很差，本来早上就不想来图书馆，想再睡一会。早上到了之后，一直到现在也啥都没干。从昨天下午到今天早上一直在看大同订婚案这个事，真的是太失望了。
        - 【15:41】把那个API-BP-Checklist的图画了
      des:
    - topic: 【2025-04-20】
      des: 周末歇了一天，没去图书馆。早上起来之后导了一管。然后补了个觉到10点才起床，蹲坑、洗澡，然后洗衣服（洗了两次）。之后点了个外卖。中午12点半外卖到了，吃到下午一点多，然后。看再多也毫无意义，甚至根本无法从现在的各种判例里找到基本逻辑和规则，归根到底就一条，偏向女性。所以怎么做也是一目了然了。

  review: |
    本周就是【处理coding-books.yml】里面有mysql, algo, kernel, k8s, kafka

    ---

    复盘来看，很失败的一周。但是也想通了一些问题。






---



- task: 2025w17
  pid: PWR
  date: 2025-04-27
  des: 2025-04-21 -> 2025-04-27 本周无论如何要把这些书弄完了
  topics:
    - topic: 【2025-04-21】 #
      qs:
        - 【14:06】早上啥都没干，早上【9:50】到图书馆，之后日常处理newsletter，以及【12:40】下去吃饭，【13:30】吃饭散完步上来，又刷了会zhihu到【13:50】睡午觉
        - 【15:27】开始回归主线。在此之前需要先处理掉以下任务：1、API-checklist（上周没弄完的） 2、redis的图 3、B树的图 4、bz的那些个技术视频（刚刚打开的）。希望今天能搞完这些。其实也就从上周六到昨晚，一共也就颓了两天。但是感觉真的是度日如年。现在想来也确实应该适度休息一下，轻松两天。
        - 【20:46】终于把那个API-checklist弄完了，还算满意。【21:52】正式弄完，也新增了TODO，剩下的之后再弄。
        - 【22:57】把之前scratches里面的那几个table什么的整合到gh了，该打TODO的也都打了。出门散步。


    - topic: 【2025-04-22】 # 今天真的还可以，早上嗯。还是搞那个？嗯，pq4r方法论，这个倒是没有close掉。下午连续处理了几个问题。一B站的所有视频之前打开的。二把相关的那个task做了百分之那三个三个子任务里面做掉了两个了。还只剩下一个，呃，最重要的是今天实际上是非常值得。有纪念意义的是今天是真正第一次嗯通过早上那个pcr那个方法论去拆分任务。然后去完成的。真正实践的这个第一次。嗯，另外我想说的是，其实这个时间不同于我们对于就是实现某个功能对于功能子任务的拆分。而需要注意的是这个实际上是不一样的。
      qs:
        - 【10:02】早上【9:20】到图书馆，稀里糊涂啥都没弄就这个点了。
        - 【17:26】搞定了预计的【16:30】【搞完所有bz视频】。最后还是delay了60min才弄完。但是实话说技术类并没有怎么处理。去芜存菁之后，直接整合到gh里了。主要是看了几个方法论方面的视频。
        - 【22:44】原定的【redis】目前解决了两个子task【redis datatype】和【七大缓存经典问题】，都各自画了exd图。还有一个最难的没搞。也已经严重delay了。
      des: 【4分】

    - topic: 【2025-04-23】
      qs:
        - 【清理一下Downloads文件夹里的本地图片】基本上都是一些技术图片
        - 【13:32】出门吃饭。早上真就啥都没干。也就清理了一下技术图片。
        - 【15:20】早上还搞了。之前一直很好奇【金坑观策员小冰】的板书是怎么画的，真心不错，终于找到的了，她是用“prodrafts+绘王数位屏”画的。我试用了一下prodrafts，发现并不好用，只能导出三种格式（原生格式、JPG的图片格式（导出的图片size很大，且不清晰）、PDF）。这也解释了为啥我导出的她那几期视频的图片size都是将近30MB。确实不靠谱。又看了一下绘王数位屏，发现也不太靠谱，她能把字写那么小，主要归功于此，但是数位屏又太贵了（1k左右），并且还有其他使用成本。数位板呢，相对便宜但是又不够好用。我也尝试了一下用exd来画她这个板书，其实可以相对还原，但是相较之下有以下问题：1、最重要的，效率不如直接用数位屏高（这点随着练习可以提升效率，但是也是个tradeoff，上面也列举了prodrafts的缺点，总的来说毫无疑问还是exd更好用，但是效率不如直接用数位板也是事实） 2、font size 需要自己手动调整，说白了数位板可以轻易写小字，但是在exd里有点麻烦。结论（综合来说）：纸笔 > exd > prodrafts+绘王数位屏 > iPad+iPad Pencil > ???
        - 【17:20】搞【cloudreve】从【16:16】搞到这个时候。真就浪费时间。1、cloudreve对R2的支持太差了，我确定我的配置没问题，但就是不行。最后决定后面自己搞下
        - 【18:35】开始。。其中，比较水的书有：《如何成为学习高手》、《10x程序员工作法》、《面试现场》、《技术面试官识人手册》、《浏览器工作原理与实践》、《分布式技术原理与实战45讲》（这书颠三倒四的）、《高并发系统实战课》、
        -
      des: 【3分】



    - topic: 【2025-04-24】
      qs:
        - 【09:48】早上【9:20】到图书馆，【9:36】泡完茶，开始干活。昨晚11点就上床睡觉了，估摸着11点半就睡着了，故意把手机放到楼下桌子上（防止最近又开始的早起玩手机的坏习惯），定了个7点的闹铃，确实被闹醒了，7点10分把闹铃关掉，洗了个澡，到7点半。洗完之后放空了一会，就7点50了。然后按照原计划搞了会
        - 【10:11】规划今天要完成的task（以及补充昨天的）
        - 【11:03】【恢复一下之前的mlc工具，扫描data里面的dead link。以及清理掉扫描出的dead link（ests 40min）】
        - 【11:41】开始搞
        - “新技术都是在现有技术的基础上发展起来的，现有技术又来源于先前的技术。将技术进行功能性分组，可以大大简化设计过程，这是技术“模块化”的首要原因。技术的“组合”和“递归”特征，将彻底改变我们对技术本质的认识。” # 虽说基于已有的技术或者架构模式进行组合，然后调整，大部分情况下就能够得到我们需要的方案，但并不意味着架构设计是一件很简单的事情。因为可选的模式有很多，组合的方案更多，往往一个问题的解决方案有很多个；如果再在组合的方案上进行一些创新，解决方案会更多。因此，如何设计最终的方案，并不是一件容易的事情，这个阶段也是很多架构师容易犯错的地方。


        - 【处理几本水书】《雷蓓蓓的项目管理实战课》、《图解TCP/IP》、《图解HTTP》、《代码之丑》

        - 【早上把上周（w16）周报写了，我觉得还是挺重要的。对很多东西有了新思考，也算是一个milestone】 # “我的心中，只有感恩”。感恩他们把我这个驴子前面的破布拿开了，让我早点看到真实。这个真实没有宏大叙述，只有赤裸裸的剥削。
        - 【12:36】出去吃饭，【13:42】午睡，【14:10】注销了bz账号（发现其实不需要在APP上操作，web也能直接注销），【14:40】看《项目管理实战课》
        - 【16:12】给exd加了record，也删掉了一些repo（某种程度上来说这个就是我之前一直在想的gh退出机制），一个值得xxx的时刻。
        - 【17:00】把images从R2挪到了本地。搞完之后直接把clawcloud的机器关机了。
        - 【22:00】今日复盘。
      des: 【4分】今天下午特别想对gh的整个数据结构做个refactor（就是那几个功能），所以开始加速搞手头的这些东西，想明天就把现在手头的几个TODO全部搞完，所以直接把之前的那些书整理到gh的对应repo里了，随手加了个 FIXME（因为这些书部分确实是经典，常读常新的那种，估计之后也就加到repo里了。也有一部分之后需要处理掉了的，只是暂存一下）。


    - topic: 【2025-04-25】
      des: 【4分】今天本身就是安排了把昨晚想搞的那个（gh数据结构调整）的功能弄完嘛。从结果来看，我打4分。下午6点半就把这几个功能全部弄完了。


    - topic: 【2025-04-26】
      qs:
        - 【把现在gh里的两个type=temp的所有repo分门别类处理一下（估计要至少2、3h）】
      des: 【2分】体感很差，原本下午想把golang相关的内容整理掉，但是下午感冒了，浑身疼，脑子也不转了。早上把type=temp的repo整理掉了，算是还行。


    - topic: 2025-04-27
      des: 【0分】感冒休整中，昨晚从7点回去之后，睡到早上10点。吃完午饭之后，下午三点午睡，睡到了晚上7点（中间起来玩了不到半个小时手机）。晚上看了会视频就累了，9点上床睡觉，早上8点起床。一整天确实啥都没干。

  review: |
    本周其实还是不错的。做了很多东西。但是到周六感冒了，就很难受。

    本周还是看那几本技术书，重点是周五周六两天，调整gh的数据结构，并且调整相关数据。在此之前的周一到周四，梳理各种（？？？）领域的相关知识点。






- task: 2025w18
  pid: PWR
  date: 2025-05-04
  des: 2025-04-28 -> 2025-05-04
  topics:
    - topic: 2025-04-28
      des: 【0分】感冒休整中，还是跟昨天一样。早上起来洗了个澡，买了点水果，把雍正王朝的解说又看了一遍。下午也是稀里糊涂看视频就过去了，到了晚上7点多感觉感冒又加重了，只能买了盒感康和VC银翘片，吃完药之后歇了会才睡。晚上10点睡觉。


    - topic: 2025-04-29
      qs:
        - 【11:28】今早7点就起来了，继续睡到9点多才起床。没啥犹豫就打算去图书馆了（因为早起感觉感冒确实好了不少，至少不是很严重了）。早上【9:54】到图书馆。
        - 【15:02】今天正式开始干活。不过今天不打算弄什么东西。
        - 【18:19】取消了【zboya/golang_runtime_reading】和【overnote/over-golang】、【《Go 语言设计与实现》】
      des: 【0分】

    - topic: 2025-04-30
      des: 【1分】感冒的第4天。今天也就随手一记了。早上10点到图书馆。继续弄昨晚弄了一晚上也没搞定的那个dosc-alfred的table排序（因为表头顺序不能乱），搞了半天才发现是之前这里一直都有问题，耗时将近7h的bug，随手一改就好了。之后又想fix一下“二层drawer点击取消之后，回到一层drawer，而非直接回到主页”，但是没搞定。今天白天基本上就是刷刷weibo，就是这两天中日友好医院肖飞那个事。其实相当于又歇了一天。

    - topic: 2025-05-02
      des: 昨天又睡了一天（早上睡到8点，起来之后，看了会视频吃完午饭12点去睡觉，睡了30min，13点前起床了，起来玩了会到xxx点，晚上7点又接着睡，一下子睡到晚上10点，起来洗漱刷NGA倒腾了一会，11点前睡觉。简单估算昨天白天睡了将近7个小时），今天起来是腰酸背痛，真的是体验了一把“差点”无法靠自己独立起床是啥感觉（早上7点就起床了，但是肚子剧痛，整个腹部都很僵硬，一直到7点40才哆哆嗦嗦起床），心想再这么待下去就TM真废了，所以早上8点10分就出门去图书馆，8点半多点就到了。刚刚打眼一看，今天就周五了，那本周可真是啥都没干。从上周感冒到今天歇了5天了。


- task: 2025w20
  pid: PWR
  date: 2025-05-18
  des: 2025-05-12 -> 2025-05-18
  topics:
    - topic: 2025-05-18
      qs:
        - 【16:38】把
        - 【21:42】晚上从图书馆出来之后，专门去理了个发，20mm左右的板寸，但是感觉还是有点偏长了。
    - topic: 2025-05-21
      des: 早上把xxx全都删掉了，因为早上打了一发。现在真是对，因为我知道。
  review: |
    从2号一下子歇到了今天。算总账的话，从4月27号阳了之后，也就到了今天才基本上恢复（不咳嗽也没痰了）。也20天了。这段时间干了点什么呢？1、刚阳了的那几天，闲着没事看《玄鉴仙族》，看了大概将近一周吧。2、后面这几天基本上就是刷刷知乎，刷刷bz（其实还是之前大同那个事，郁结于心，在想相关的问题）。

    今天







- task: 2025w23
  date: 2025-06-08
  topics:
    - topic: 2025-06-02
      des: 其实弄了很多，但是有点想不起来了。不记录。
    - topic: 2025-06-03
      des: 处理了一下docs，主要是处理掉那个“递归drawer”，也就是可以分层打开和关闭drawer。其他的就是：1、table的url, doc渲染 2、img下面标注了图片名 3、二级drawer点进去之后白页 4、topic里table的默认是否unfold渲染。 上面是主线任务，除此之外还做了图表diagram的技术选型。一些感想：之前胡乱随手加进去的repo太多了。
    - topic: 2025-06-04
      des: 昨晚吃烤鸭饭吃坏了，真的不该贪口喝那口鸭汤（变质酸汤，当时以为本身就是这个味道）的。腹泻+痔疮是真的痛苦，如果只有其一还好，两个加到一起，晚上12点、早上6点、9点、12点半，稍微走两步就想蹲坑。真TM难受。一整个早上浑浑噩噩也就过去了，中午也没敢吃饭。回想了一下，腹泻就不提了点，用小蚕点的，只花了4块钱，吃东西还是得点品牌连锁饭店，别贪小便宜。至于痔疮，事后仔细想来，大概是因为之前几天下雨，晚上就没出门，还晚上一坐就到晚上11点（并且那个小椅子很硬还很低，就更麻烦了）。
    - topic: 2025-06-05
      des: 周四，还真的不错。早上主要是 1、整理了 golang代码复用方案，画了个table，到12:20弄完。2、梳理golang的DS，一直到下午4点才弄完。3、清理之前的几个A5本子（实际上只弄了前3本）。中午去hls吃了4个汉堡，12:20出发，到13:40才回来。
    - topic: 2025-06-06
      des: 今天就是把昨天没弄完的A5本子的后面4本弄完了，涉及到很多方面的就不多说了，画了几个table（并发模型、马恩列斯毛、民主党派、mysql和pgsql、HTTP Hol问题），一直到下午5点半才弄完。早上不到6点就起床了（因为昨晚7点回去，8点看完初圣更新就睡了，一口气睡到10点半，起来洗漱完了接着睡，所以早上起的早。但是早起主要是因为肚子疼，上次阳了之后出的毛病，久卧之后早上起来就完全不能动了，下床都费劲，需要一点点挪下来），之后洗澡、休息，7点整在出租屋里搞了会，8点20出发去图书馆。
    - topic: 2025-06-07
      des: 也就是昨天了，记忆还算清晰。就是两个事，整理clash和k8s。就是为了把clash相关整合到mac，所以顺手把BPB搞了一下（相比之前，现在这个方案更好。并且弄到cf小号了，所以之前也不算事无用功）。梳理了几个GUI，并且也算是弄明白了从协议到内核，再到软件之间的关系。也确实花了点时间。一直弄到下午3点才弄完。之后尝试弄了会tree相关的东西，想把心心念念想搞的tree演进图画出来，但是没搞定。之后开始整理k8s repo，到晚上弄完了。晚上回去之后没怎么弄，看看视频，出门散步，回来就睡觉了。
    - topic: 2025-06-08
      des:
  review: |
    本周干啥了？其实还是整理

    几点思考：月复一月的整理docs，是为了什么？归根到底，是真的不懂，没搞懂。



- task: 2025w24
  date: 2025-06-16
  topics:
    - topic: 2025-06-09
      des: 主要还是【处理scratches】，早上还把之前遗留的docs的
    - topic: 2025-06-10
      des: 9号晚上回去之后在梳理history相关的东西，所以早上一直到12点，就继续把法、韩、美、苏、中国古代、新中国历史都梳理了一下，做成table。感觉还不错。之后还是继续处理scratches，只剩下了golang-types和hashmap（但是实际上截止今天2025-06-16还是没彻底搞懂）。搞hashmap的时候顺带搞了下 hash func比较，以及hash collision（这个是彻底搞懂了）。之后给docs的pic做了个画廊模式，支持方向键切换。
    - topic: 2025-06-11
      des: 早上9点半到。整理
    - topic: 2025-06-12
      des: 早上很傻逼，搞什么儒学发展mermaid，DS一直搞不对，最后我直接把ascii贴到EXD了。一直搞到11点半，非常浪费时间。早上9点40到，搞了将近2h，最后还没成果。感觉DS是真sb，响应慢+经常限流无响应+听不懂人话。现在用元宝+grok代替了。今天到主线任务是处理之前的chat history（昨晚开始弄的），
    - topic: 2025-06-13
      des: 继续处理chat history，一直到晚上11点50，基本上可以算是搞完了，总的来说也还算不错。
    - topic: 2025-06-14
      des: 昨天搞完chat之后，开始处理剩下的chrome tab，其实说穿了怎么就天天搞这个搞那个，都是“吃剩饭”呢？不就是之前没搞懂吗？
    - topic: 2025-06-15
      des: 不是还在弄chrome tab吗？早上搞了一下李海翔的那个【分布式原理】的几篇文档，真的是很有收获，称得上是高屋建瓴，很多之前零散的知识点都串起来了。
  review: |
    本周不就是两个task吗？处理scratches和chrome tab。归根到底不还是之前没搞懂，搞了不知道多少次还是没搞懂的东西？





- task: 2025w24
  date: 2025-06-16
  topics:
    - topic: 2025-06-16
      des: 昨晚两点才睡，早上9点半到图书馆。昨天周日没写w23周报，今天早上补上。
    - topic: 2025-06-17
      score: 3
      main: 1、处理picDir图片不展示的bug 2、梳理k8s的topic
      des: 早上9点50才到图书馆。早上10点半到中午2点，就处理了一下部分gh里picDir图片不展示的bug，具体排查过程直接把cursor的md上传到相应issue了，最后灵机一动直接用fetch解决掉了。下午则梳理了一下k8s的topic，确实很多东西都清楚了（这个之前完全是糊涂的，但是既定task也没做完）。另外，今天顺手把什么geektime的书都给挪到books了，所以还有一些想法，之前想搞什么task之类的东西真是魔怔了，啥都想做成task。现在task也没了不是也一点问题没有吗？都是无用功。直接在YAML里记录和查看就挺好，何必要做成web展示呢？并且经过实践验证，很多东西就是应该纵向record到相应领域，而非横向直接做到diary里，否则一定会忘，终究是无用功。
    - topic: 2025-06-18
      main: 黑灰产
      score: 3
      des: 今天既定的东西一个没弄。原本安排的是梳理k8s相关，全部处理掉。但是task还是xxx了，早上到了之后原本想着用40min处理掉什么黑灰产、互金之类的东西，结果搞了一早上，到了中午以为用20min再收个尾就弄完了，结果清理黑灰产产业链的时候的，发现其实并不懂，就自己画了个图，稀里糊涂一下午就过去了，抬头一看下午5点多了。从下午6点半开始搞compiler相关，算是把compiler相关的基本常识弄明白了。把两本书的图片提取出来。晚上没啥效率，回去就跟人吹牛聊天聊到10点出门散步，10点40回来还躺了会，一直到11点20想着“今天的东西还是得弄完”，下去把compiler相关的东西都处理了一下，一直到12点40，算是搞的差不多了，洗漱睡觉。另外，反思了一下为啥今天没啥进展，没效率。又印证了一个问题：中午去HLS吃汉堡真的很耽误时间，之前就说了，看起来一去一会加起来70min（往返单程20min多点，吃饭20min），但是背后的隐藏时间成本是2h左右。做个对比就很清晰了，如果点外卖，大概就是10min左右。这还只是一部分，还有消化的时间，去HLS吃4个汉堡，回来至少还要再消化1h才能午睡，与之对比，点外卖就无所谓了，随便消化一下就可以睡觉了，不用当回事。可以看到其实是多花了2h，但是一整个白天其实才从早9点到晚7点共10h。直接就是20%的时间被浪费了。确实很不划算。
    - topic: 2025-06-19
      score: 3
      main: 1、看compiler相关的4个geektime课程 2、geektime投资课，梳理
      des: 【】早上9点半到图书馆，
    - topic: 2025-06-20
      score: 3
      des: 做了很多东西，但是完全偏离主线，所以7分。早上先把之前部分docs的taskfile做成全局task，方便迁移（不然难道以后想跑这些task还要都把docs拉下来吗？）。不只于此，还全局搜了一下docs的cmd，把很多。其。这个一直做到下午两点。之后继续处理mac.yml里的部分内容，主要是对比了这么几部分：抓包工具对比、备份工具对比、网盘对比。

    - topic: 2025-06-21
      score: 2
      AM: 1、看了《Chinese Security Policy》这本书，原本预计早上10点看到11点就完事了，结果一下子弄到了下午4点。从本书倒是没学到啥，但是据此又重新梳理了地缘政治学说发展。知道了还有新现实主义、新自由主义、建构主义的说法。我之前的梳理确实有些问题。另外，这种用AI辅助看书，直接用gemimi就可以了，相比NotebookLM更好用。
      PM: 1、处理chrome tab，这个没啥，顺手40min弄完了。2、处理了昨晚没弄完的几个东西 pprof, TUN, FS, ugit
      night: 晚上回去之后刷了会视频之后，就是处理昨天安排的第4个事：“处理手机里技术相关截图”。这个弄到11点左右也没弄完，直接洗澡休息了。
      des: 周六，记忆犹新。也顺便梳理一下。今天第一次尝试使用“七步问题处理法”，确实很有效，给这些task排序之后，确实效率更高。但是发现最大的问题仍然在于某个task严重超时。

  review: |
    本周处理掉了什么？
    - 1、之前docs里geektime的内容（也就是搜索lianglianglee）。什么黑灰产、互金、投资、compiler、反洗房相关的。
    - 2、梳理k8s



- task: 2025w25
  date: 2025-06-16
  topics:
    - date: 2025-06-23
      des: 删掉了很多repo，主要是把CICD和之前的其他相关的type都合并到devops里了，大概删掉了几百了。我觉得呢，很有成效。就是应该删除掉实际上永远不会去用的这些repo，这些repo大部分其实都没啥用了。因为你不去删，你就不知道到底有啥用，就会FOMO，FOMO就是你觉得他万一有用呢？就把他添加进来了，久而久之就越滚越大，然后就变得很蠢，知道吧？
    - date: 2025-06-24
      des: 继续删repo
    - date: 2025-06-25
      task:
        - name: 解决2个docs的issue (tables渲染、score配置展示)

    - date: 2025-06-27



- task: 2025w26
  sub:
    - date: 2025-06-28
      task:
        - name: 优化一下hammerspoon的task脚本
          deletedAt: 2025-06-28 10:59
          PD: 120min
          AD: 6min
          score: 5

        - name: 梳理一下golang changelog。昨天整理到table里了，今天需要对照相关网页验证一下是否正确，并修改。
          deletedAt: 2025-06-28 15:14
          PD: 40min
          AD: 52min
          score: 2

        - name: 处理完scratches里之前遗留的
          deletedAt: 2025-06-28 00:35
          PD: 80min
          AD: 159min
          score: 2

    - date: 2025-06-30
      task:
        - name: 把doc的那个展示缩小到一页内，也就是50条以内。
          doneAt: 2025-06-30 09:47
          PD: 40min
          AD: 33min
          score: 4

        - name: 把docs里webstack的相关网站，挪到alfred，然后把webstack.yml移除掉
          doneAt: 2025-06-30 11:15
          PD: 40min
          AD: 53min
          score: 2

        - name: 处理掉efficacy里会出现RCK.png的bug
          doneAt: 2025-06-30 22:41
          PD: 40min
          score: 5

    - date: 2025-07-01
      task:
        - name: 梳理golang编译+汇编过程
          deletedAt: 2025-07-01 09:20
          PD: 80min
          score: 5

        - name: 根据昨天对hs的tasklist脚本的体验，提出的需求，进行优化
          deletedAt: 2025-07-01 09:20
          PD: 80min
          AD: 150min
          score: 2

        - name: 复习kvdb相关内容。kvdb的table以kvdb存储模型为轴
          deletedAt: 2025-07-01 16:32
          PD: 80min
          score: 5

        - name: 重新处理一下chezmoi，玩玩看
          deletedAt: 2025-07-01 21:12
          PD: 80min
          AD: 150min
          score: 2

        - name: 晚上回去之后，把现状手头的这3个笔记本的内容处理掉
          deletedAt: 2025-07-01 22:56
          PD: 160min
          AD: 438min
          score: 2


    - date: 2025-07-03
      task:
        - name: 晚上回去把白天梳理的这几个东西都重新梳理一下
          doneAt: 2025-07-03 09:16
          PD: 40min
          score: 5

        - name: 晚上梳理完之后，把新笔记本的这几页，确定拍照存档之后，直接撕掉
          doneAt: 2025-07-03 09:16
          PD: 40min
          AD: 416min
          score: 2

        - name: fix一下之前table无法直接按照table里内容 auto width 的bug
          doneAt: 2025-07-03 19:50
          PD: 40min
          AD: 76min
          score: 2

    - date: 2025-07-04
      task:
        - name: 刚刚删除了pic这个key。现在需要处理相关data里的内容。主要是整理现在的技术图片。
          doneAt: 2025-07-04 19:23
          PD: 80min
          AD: 1393min
          score: 2
  review: |
    我觉得很憋屈，本周执行的很差。摊子铺的很大，但是最终能落地的没有。又得转过头来擦屁股、收拾烂摊子，就很烦。归根到底还是task规划不合理，也没有“今日事，今日毕”。

    这周对docs做了比较大的改造（实际上还是延续上周）。从方法论来说，本周的一大进展就是，搞清楚了知行的方法论。确定了3w3h的框架，以及其和逻辑链的关系。




- week: w27 (2025-07-07 - 2025-07-13)
  task:
    - date: 2025-07-07
      task:
        - name: 分布式锁
          doneAt: 2025-07-07 12:34
          PD: 40min
          AD: 179min
          score: 2

        - name: 把那个sync scratch files across mutiple devices 的东西，直接写成taskfile的task
          doneAt: 2025-07-07 16:18
          PD: 80min
          AD: 81min
          score: 3


    - date: 2025-07-08
      task:
        - name: 找找还有哪些别人在用的hs脚本，我也搬过来用
          doneAt: 2025-07-08 12:35
          PD: 40min
          AD: 148min
          score: 2

        - name: skiplist 3w3h
          doneAt: 2025-07-08 12:54
          score: 2

        - name: 用taskfile搞定mac automation setup问题
          doneAt: 2025-07-08 19:28
          PD: 40min
          AD: 148min
          score: 2

    - date: 2025-07-09
      task:
        - name: 把mac-cleanup-py用taskfile自己写一个
          doneAt: 2025-07-09 11:52
          PD: 40min
          AD: 96min
          score: 2

        - name: 处理干净现在手头的Downloads文件夹里的这些技术图片。重命名、压缩、归类。
          doneAt: 2025-07-09 12:41
          PD: 40min
          AD: 188min
          score: 2

        - name: 尝试整合社科知识框架（1、清理相关文档内容 2、梳理“国家比较框架”）
          doneAt: 2025-07-09 18:13
          PD: 120min
          score: 2

    - date: 2025-07-10
      task:
        - name: 梳理一下是否要真的处理掉所有的docs里所有主要技术领域的3w3h
          doneAt: 2025-07-10 13:27
          PD: 40min
          AD: 195min
          score: 2

        - name: 处理完redis各项（持久化机制、主从复制、sentinel、cluster、分布式锁）的3W3H
          doneAt: 2025-07-10 15:01
          PD: 160min
          score: 2

    - date: 2025-07-11
      task:
        - name: 研究一下怎么用nix-darwin代替homebrew，以及相应配置
          doneAt: 2025-07-11 17:19
          PD: 120min
          AD: 466min
          score: 2

    - date: 2025-07-12
      task:
        - name: 梳理一下昨天弄nix的record
          doneAt: 2025-07-12 11:32
          PD: 40min
          AD: 62min
          score: 2

        - name: 把 docker, docker-compose 相关系统整理一下，3w3h
          doneAt: 2025-07-12 18:53
          PD: 160min
          AD: 271min
          score: 2

    - date: 2025-07-13
      task:
        - name: 去兼职中介面试
        - name: 想想自己的个人财务问题（手上的钱要能支撑到第一个月发工资，需要多少钱）
  review: |
    今天是周日，必须要写周报。又被逼到悬崖边上了，各种钱包里加一块最后20多块，还充了10块钱地铁卡，最后就剩下两天的午饭钱，幸亏从京东钱包里还翻出来12块钱，不然真是今天吃完，明天就断顿了。又是一年715，去年应该就是715前后搬到青旅的（查了一下是720），稀里糊涂也住了一年青旅了。

    这两天不是又没钱了嘛，昨晚就找了不少日结兼职，只有在周一上班并且拿到钱，才能把整个青旅房租、饭钱都续上。另外，我还得保证手上的钱能支撑到第一个月发工资（并且做好，如果又像在龙珠直播一样，干了一个月，一分钱没赚着，还要留着再打日结工的准备）。昨晚找日结，约了个日结中介的面试，结果还要先付钱，每天¥30的服务费，我先签了20天，就是¥600，但是我现在真没有啊，只能找各种小贷平台，但是我征信花了，现在也下不了款，一分都下不。

    - 【现在能拿到手的钱】2024年的退税（¥800多） + 3个月失业金（¥2305/月）+ 日结工20天（中介说 25-30/h，每天8h，保守算20/h，那就是每天160到180，20天合计3600（真TM低啊，真不容易，扣掉））
    - 【要花的钱】房租每个月900（我估计至少还要再住一个月）+ 每天饭前平均¥12（30天就是就算¥500）+

    简单来说，最难的就是撑到815，那天就有失业金拿了，甚至每个月还能攒

    归根到底还是我一直都是“屎不到屁股门不拉”，非得到这种完全山穷水尽了，才想着怎么盘活局面，从小到大一直都这样，也导致我整个人生走到了这步。

    ---

    就在写这篇review的时候看到 [上海“299元一天”的工作靠谱吗|上海市_新浪财经_新浪网](https://finance.sina.com.cn/jjxw/2024-04-01/doc-inaqiert7150906.shtml)，说明今天早上去的那个日结中介完全是套路，并且是很老掉牙的套路了，文中说的跟我遇到的完全一样。也看到黑猫投诉上“在青团社上看到电影院检票员35一小时日结，联系了线下面试，面试告知要交30一天的餐费，我交了450，签了协议之后协议上写着服务管理费，面试者说加微信看他朋友圈发的工作，发现跟在线上沟通的工作并不一致，而且工作地点偏远，线上说就近分配，而且价格低跟线上挂的信息不一致，有的还需押金。所以现在给我全款退回！！而且盖章上的名称跟平台招聘者名称不一致”、“中介的通常套路就是在网上发布，高于当地劳务市场的价格，比如小时工35块钱一个小时先把人骗过人，像叫人们在地铁口集合，这种一般都是小中介以及二手中介卖人头，而且工作的地方都不在当地，把人拉到很远的地方，比如东莞或者上惠州，深圳然后再赚一波车费钱，叫人们交押金的这种中介也不要去。”。那我怎么办呢？【李不在上班】、【李上瘾001】

    [985本硕失业小伙做日结保安完成了生命的救赎，保安服是面具，戴上面具就可以撕下伪装_哔哩哔哩_bilibili](https://www.bilibili.com/video/BV1UZGVzPEdQ/?spm_id_from=333.337.search-card.all.click)

    快递分拣为啥都是日结？因为干完一天，至少要歇三天。工资不日结，你根本撑不到发薪日。“女的当男的用，男的当叉车用，叉车当宝贝用”不是段子，是真写实，真以为分拣都是小件？给你来几个汽配城的件、石雕之类的就老实了。

    ---

    【23:56】晚上管xxx借了¥700（20号还他钱）。总之无论如何还是上面说的，活着撑到下个月15号（首期失业金到账¥2305/月），就不需要担心这些问题了。现在来看还有1个月，很简单，需要大概¥1300左右。除掉退税的¥800，我要自己挣¥500，其实也并不难。晚上找了个周四的活，3个小时能赚¥100。还差¥400，到下个月15号嘛，我觉得很轻松。还是应该把精力放在面试准备上。当然今天折腾这一整天也不算白费，也算是人生体验。也幸亏了下午发现的这个退税，不然真的就很被动了，马上又要付房租，并且饭钱也撑不了几天了。

    搞清楚了上面的问题，其实现在的判断关键就在于，是否要管小章借¥700，我现在房租到了16号晚上，也就是17号要再交¥150。手上的饭钱基本上也是到那天。也就是说如果在17号之前能拿到钱，那就都ok，如果到时候没有这个钱，就要靠这¥700撑到20号

    ---

    本周技术上，主要是周五牛刀小试nix，很不错。其他的就是按部就班整理面试题。




- week: w28 (2025-07-14 - 2025-07-18)
  task:
    - date: 2025-07-14
      task:
        - name: 想想自己的个人财务问题（手上的钱要能支撑到第一个月发工资，需要多少钱）
          doneAt: 2025-07-14 09:22
          PD: 40min
          AD: 14912min
          score: 1

    - date: 2025-07-15
      task:
        - name: 自己试验一下BE用swaggo，FE用Prism 是否真的可行
          doneAt: 2025-07-15 13:50
          PD: 40min
          AD: 93min
          score: 1

        - name: 再次梳理API相关
          doneAt: 2025-07-15 16:07
          PD: 80min
          AD: 387min
          score: 1

        - name: 梳理一下gateway和servicemesh之间的到底是啥关系
          doneAt: 2025-07-15 18:18
          PD: 40min
          AD: 80min
          score: 1

    - date: 2025-07-16
      task:
        - name: 系统整理一下github actions的3w3h
          doneAt: 2025-07-16 23:15
          PD: 120min
          AD: 423min
          score: 1

    - date: 2025-07-17
      task:
        - name: 晚上散步把手机里技术图片都重命名一下
          doneAt: 2025-07-17 09:44
          PD: 40min
          score: 4

        - name: 清理掉chrome里的TM插件以及其中的脚本。想想TM和chrome插件之间的优缺点。
          doneAt: 2025-07-17 13:43
          PD: 40min
          AD: 24min
          score: 4

    - date: 2025-07-18
      task:
        - name: 去虹桥金桥大厦做攻防箭兼职。需要5点半出发。手机充好电。
          doneAt: 2025-07-18 12:03
          PD: 40min
          score: 4
          review: 昨晚去的，两点想法（很散乱，称不上观点）：1、某种成年人花钱得到儿时免费就能获得的快乐，某种消费主义。GDP就是这么产生的。2、从赚钱来说其实是有点失败的，5点40出发（6点40到），12点半回来（工作时间从7点到10点半（10点结束，还要30min收拾东西拉回去，昨天弄完实际上已经11点了，打车用了30min，路上有30min，到家12点））。过去地铁¥4，返程滴滴¥39，总共钱¥100，所以实际到手¥57，时薪¥16（当然，如果是地铁回来就是¥25/h了）。

        - name: 记录一下昨天下午的想法。社会福利、被害妄想症、个人奋斗。以及随之而来的后现代必然走向其反面。
          doneAt: 2025-07-18 14:25
          PD: 40min
          score: 4

        - name: 重新自己梳理一下 lean-canvas
          doneAt: 2025-07-18 14:25
          PD: 120min
          AD: 141min
          score: 2
      review: 可以看到实际上周五歇了一天，周四晚上做兼职，12点到家，洗澡完12点半，看了两集电视剧，就TM快两点了。一觉睡到早上9点才起床。也不想去图书馆了，就歇了一整天，看了一天视频。

    - date: 2025-07-19
      task:
        - name: 下午5点到8点去做攻防箭兼职，需要4点20左右出发
          doneAt: 2025-07-20 10:06
          PD: 40min
          score: 4
          review: 下午4点20出发，5点10分到仓库，装好之后，5点40到场地，开始摆东西。6点出头活动开始，一直到8点半结束，把东西拉回去收拾到仓库里就9点20了。第二趟弄完9点40，彻底结束。回去路上又是50min，到家就10点半了。一直没喝水快渴死了，路上买了点水，回去一直等水，之后洗了个澡，看了两集电视剧，一点半才睡觉。至于报酬，从5点到9点半合计4.5h，¥150，时薪¥33其实真就还可以了。算了一下钱，手上的钱现在除了到8.15的房租，就剩¥30了，看来还是得至少赚个¥150，把饭钱解决了。我可能还得想想是否要去，因为周四晚上都是8-10，而末班车是10点44，基本上是赶不及的，因为10点结束之后要在场地收拾完东西，拉回去，在仓库里收纳好，再跑到地铁站。这几个分别大概是 20min, 20min, 15min, 10min，合计65min。即使如此，10点未必就能结束，所以是赶不及的，返程就要打滴滴，那就不划算了。所以后面找个能干满整个白天的兼职，钱肯定就够了。
      review: 白天梳理LSM但是还没弄完，暂不整理


    - date: 2025-07-20
      task:
        - name: 写w28周报
          doneAt: 2025-07-20 16:10
          PD: 40min
          AD: 36min
          score: 3

        - name: 梳理 raft 3w3h
          doneAt: 2025-07-20 16:11
          PD: 120min
          AD: 79min
          score: 5

        - name: 梳理 LSM 3w3h
          doneAt: 2025-07-20 16:11
          PD: 80min
          AD: 1413min
          score: 1

        - name: 解决掉docs现在的几个bug：1、dark模式配色 2、table里多行字符串导致的width问题 3、图片背景色
          doneAt: 2025-07-20 17:43
          PD: 80min
          AD: 81min
          score: 2

  review: 本周是真正有生以来第一次做兼职，某种“补课”吧，大学时候没上这课，刚刚进入社会也没上这一课，现在是真该补课了。之前是真TM不识人间疾苦，“不经搓磨老天真”。本周确实在技术、面试准备上差点意思，主要是API、ServiceMesh、ghac、Chrome相关（TM插件）、LSM、raft相关。






- week: w29 (2025-07-21 - 2025-07-27)
  task:
    - date: 2025-07-21
      task:
        - name: 再次整理k8s.yml里的repo，这次移除掉其中不重要的repo
          doneAt: 2025-07-21 13:10
          PD: 80min
          AD: 1171min
          score: 1

        - name: 梳理一下目前以tailwindcss为核心的整个css生态
          doneAt: 2025-07-21 16:33
          PD: 80min
          AD: 91min
          score: 2

    - date: 2025-07-22
      task:
        - name: 梳理一下linux distro，画个mindmap作为演进图，之前用table梳理这个东西并不恰当
          doneAt: 2025-07-22 10:14
          PD: 40min
          AD: 40min
          score: 2

    - date: 2025-07-23
      task:
        - name: 晚上6点准时走，去吃饭然后理发
          doneAt: 2025-07-23 10:54
          PD: 40min
          score: 4

        - name: 晚上回去把床底下压的那些东西整理一下
          doneAt: 2025-07-23 21:48
          PD: 160min
          score: 5

    - date: 2025-07-26
      review: 今天感觉还可以把之前的嗯，在纸上画的那些都已经基本上清完了，本身今天肯定本来是能全部弄完的，但是后来我不是想到说是把那个text给合并到那个里面去吗？晚上呃下午5点之后一直在做这。一直弄到晚上测试一下通过了，就没去搞明天呃，这就相当于今天做的这两个基本上就这两个事儿。嗯，还是很清晰的，明天呢就是几个东西嘛，一个是把这个清掉，对吧？然后那个之前的元宝的聊天记录也清掉，啊，乱七八糟就是相关的这些东西全部清掉，然后写个周报，明天就这么点儿事儿。

    - date: 2025-07-27
  review: 本周确实效率一般。比较大的收获就是找到了“不可能三角”来xxx。在周五也想到，现在什么都整理到





# 昨天继续整理
