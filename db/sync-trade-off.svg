<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="489px" height="277px" viewBox="-0.5 -0.5 489 277" content="&lt;mxfile modified=&quot;2019-08-14T13:20:10.457Z&quot; host=&quot;pebppomjfocnoigkeepgbmcifnnlndla&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36&quot; version=&quot;11.1.4&quot; etag=&quot;y7wED1nwFhYafx8PTX-6&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;ejbk1LHgJRvNFziyPCBa&quot; name=&quot;Page-1&quot;&gt;7Zvfc6IwEMf/Gh87g4CUPp5arw+11zm96b2mECVjJA6Eov3rbz2CP9jonM4VtI0PDvkSEpJPdl020nJ68+X3hCyioQgpb9lWuGw5/ZZt+3c+fK+FVSF4vl0I04SFhdTeCiP2TpVoKTVjIU33KkohuGSLfTEQcUwDuaeRJBH5frWJ4Pu9LsiUImEUEI7VFxbKSA2rY231B8qmUdlz21Jn5qSsrIQ0IqHIdyTnvuX0EiFkcTRf9ihfz105L8V1gwNnNzeW0Fj+ywUkf34ZjMXdQzLr//r9FL3fROMb1cob4ZkacJcEs2yhblmuynmQdAm9dCM55yC04TCViZjRnuAiASUWMdTsThjnFYlwNo2hGMB9UtC7bzSRDGb4mzoxZ2G47qabR0zS0YIE6z5zWE6gJSKLQ7oegrVuXsRSrZG2q8pldy3bKT6gq1FBR3R5cLraGwiweKmYU5msoEp5QcltVSnn22Vw6xVStLMCXFWNqIU33bS8ZQMHCs8JqFyEagjtjAwp27owUh1Mamgw2f6FYfIQJvu5Zzg53oVxukWcnslSpIaU614YKR+R6ok4ZamkcbD6AF5JMaZrwVWh5WloWZiW430QrTtEa5yQOCWBZEDN4NrHtYkymsJVPhHt8HokxrJ0qNymUeHHqnEEszONFpk0tCq0/KZp2YhWn0gCChep8YMVXI4uyKgVl4NwDQjjAgZpWFVYNR1itHFE+ELJ7GROME2vfsftWBVYoE/8gAbB9QbxKM+kY6YJ4j+OGY4Ln4QhdpiYNjKsk5iNI8P7NxhMRvg53ELv1et4Gm6TiX3N3FCeUGNp7U6t4HCcOAIa8fQcbL796ngabGGH+qF7vdhQPqp5bDhgHGT8LFv7rNCqtta8j8RR46MIjIM8lqFvHhre8zKGdtw76pjV7B3x9tejyA2zIwGkJl+14VMPM7wX9gDdngPts0b9yNA00Go2NLwxdi60L2NpmlxjzZaGMyJnescvY2i6/HCtyHBCZEhDls1N7Ng6uBfdODVHs10m5Onp/E9saVXvqE3t18pMkw+Bpo2dHX6w1jGrNwxxcDbkab2HYsKQgz9pFwANp0P6Io+NdzziHZvemHFwNuQnJSEoP2J++p9AvpKTbBwdzokU6AYvCcyb8ZWHk5A6dv/rkQ2K2/cv/p7beYnFuf8D&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><g transform="translate(106.5,12.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="46" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 48px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Backup</div></div></foreignObject><text x="23" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Backup</text></switch></g><g transform="translate(197.5,12.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="24" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 26px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">M/S</div></div></foreignObject><text x="12" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">M/S</text></switch></g><g transform="translate(277.5,12.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="24" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 24px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">MM</div></div></foreignObject><text x="12" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">MM</text></switch></g><g transform="translate(355.5,12.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="28" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">2PC</div></div></foreignObject><text x="14" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">2PC</text></switch></g><g transform="translate(430.5,12.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="38" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 40px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Paxos</div></div></foreignObject><text x="19" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Paxos</text></switch></g><g transform="translate(3.5,50.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="76" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 77px; white-space: nowrap; overflow-wrap: normal; text-align: right;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Consistency</div></div></foreignObject><text x="38" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Consistency</text></switch></g><g transform="translate(-0.5,90.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="80" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 81px; white-space: nowrap; overflow-wrap: normal; text-align: right;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Transactions</div></div></foreignObject><text x="40" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Transactions</text></switch></g><g transform="translate(30.5,130.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="49" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 50px; white-space: nowrap; overflow-wrap: normal; text-align: right;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Latency</div></div></foreignObject><text x="25" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Latency</text></switch></g><g transform="translate(7.5,170.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="72" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 73px; white-space: nowrap; overflow-wrap: normal; text-align: right;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Throughput</div></div></foreignObject><text x="36" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Throughput</text></switch></g><g transform="translate(21.5,210.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="58" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 59px; white-space: nowrap; overflow-wrap: normal; text-align: right;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Data loss</div></div></foreignObject><text x="29" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Data loss</text></switch></g><g transform="translate(29.5,250.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 51px; white-space: nowrap; overflow-wrap: normal; text-align: right;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Failover</div></div></foreignObject><text x="25" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Failover</text></switch></g><rect x="92" y="40" width="76" height="36" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(111.5,50.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="36" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 36px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Weak</div></div></foreignObject><text x="18" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Weak</text></switch></g><rect x="92" y="80" width="76" height="36" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(120.5,90.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="18" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 18px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">No</div></div></foreignObject><text x="9" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">No</text></switch></g><rect x="172" y="40" width="156" height="36" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(222.5,50.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="54" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 56px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Eventual</div></div></foreignObject><text x="27" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Eventual</text></switch></g><rect x="332" y="40" width="156" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(388.5,50.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="42" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 42px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Strong</div></div></foreignObject><text x="21" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Strong</text></switch></g><rect x="172" y="80" width="76" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(198.5,90.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="22" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 24px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Full</div></div></foreignObject><text x="11" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Full</text></switch></g><rect x="252" y="80" width="76" height="36" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(272.5,90.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 34px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Local</div></div></foreignObject><text x="17" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Local</text></switch></g><rect x="332" y="80" width="156" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(398.5,90.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="22" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 24px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Full</div></div></foreignObject><text x="11" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Full</text></switch></g><rect x="92" y="120" width="236" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(196.5,130.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 26px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Low</div></div></foreignObject><text x="13" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Low</text></switch></g><rect x="332" y="120" width="156" height="36" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(395.5,130.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="28" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 30px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">High</div></div></foreignObject><text x="14" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">High</text></switch></g><rect x="92" y="160" width="236" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(195.5,170.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="28" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 30px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">High</div></div></foreignObject><text x="14" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">High</text></switch></g><rect x="332" y="160" width="76" height="36" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(356.5,170.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 26px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Low</div></div></foreignObject><text x="13" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Low</text></switch></g><rect x="412" y="160" width="76" height="36" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(424.5,170.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="50" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 50px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Medium</div></div></foreignObject><text x="25" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Medium</text></switch></g><rect x="92" y="200" width="76" height="36" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(116.5,210.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="26" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 28px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Lots</div></div></foreignObject><text x="13" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Lots</text></switch></g><rect x="172" y="200" width="156" height="36" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(231.5,210.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="36" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 38px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Some</div></div></foreignObject><text x="18" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Some</text></switch></g><rect x="332" y="200" width="156" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(392.5,210.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="34" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 34px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">None</div></div></foreignObject><text x="17" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">None</text></switch></g><rect x="92" y="240" width="76" height="36" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(111.5,250.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="36" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 36px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Down</div></div></foreignObject><text x="18" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Down</text></switch></g><rect x="172" y="240" width="76" height="36" fill="#fff2cc" stroke="#d6b656" pointer-events="none"/><g transform="translate(176.5,250.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Read Only</div></div></foreignObject><text x="33" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Read Only</text></switch></g><rect x="252" y="240" width="236" height="36" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(334.5,250.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="70" height="14" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; vertical-align: top; width: 70px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Read/Write</div></div></foreignObject><text x="35" y="14" fill="#333333" text-anchor="middle" font-size="14px" font-family="Helvetica">Read/Write</text></switch></g></g></svg>
