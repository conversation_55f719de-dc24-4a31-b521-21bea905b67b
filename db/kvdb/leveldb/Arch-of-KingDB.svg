<?xml version="1.0"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xl="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 1118 3915" width="1118pt" height="3915pt"><metadata xmlns:dc="http://purl.org/dc/elements/1.1/"><dc:date>2015-05-25 16:22Z</dc:date><!-- Produced by OmniGraffle Professional 5.3.6 --></metadata><defs><filter id="Shadow" filterUnits="userSpaceOnUse"><feGaussianBlur in="SourceAlpha" result="blur" stdDeviation="3.488"/><feOffset in="blur" result="offset" dx="0" dy="4"/><feFlood flood-color="black" flood-opacity=".75" result="flood"/><feComposite in="flood" in2="offset" operator="in"/></filter><font-face font-family="Helvetica" font-size="30" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="532.22656" cap-height="719.72656" ascent="770.01953" descent="-229.98045" font-weight="bold"><font-face-src><font-face-name name="Helvetica-Bold"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="24" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="18" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="532.22656" cap-height="719.72656" ascent="770.01953" descent="-229.98047" font-weight="bold"><font-face-src><font-face-name name="Helvetica-Bold"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="18" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face><marker orient="auto" overflow="visible" markerUnits="strokeWidth" id="SharpArrow_Marker" viewBox="-3 -3 7 6" markerWidth="7" markerHeight="6" color="black"><g><path d="M 2.3333333 0 L -1.4000001 -1.4 L 0 0 L 0 0 L -1.4000001 1.4 Z" fill="currentColor" stroke="currentColor" stroke-width="1"/></g></marker><font-face font-family="Helvetica" font-size="18" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="-666.6667" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-style="italic" font-weight="500"><font-face-src><font-face-name name="Helvetica-Oblique"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="48" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="532.22656" cap-height="719.72656" ascent="770.01953" descent="-229.98047" font-weight="bold"><font-face-src><font-face-name name="Helvetica-Bold"/></font-face-src></font-face><font-face font-family="Helvetica" font-size="22" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="0" x-height="522.94922" cap-height="717.28516" ascent="770.01953" descent="-229.98047" font-weight="500"><font-face-src><font-face-name name="Helvetica"/></font-face-src></font-face><marker orient="auto" overflow="visible" markerUnits="strokeWidth" id="SharpArrow_Marker_2" viewBox="-4 -3 7 6" markerWidth="7" markerHeight="6" color="black"><g><path d="M -2.3333333 0 L 1.4000001 1.4 L 0 0 L 0 0 L 1.4000001 -1.4 Z" fill="currentColor" stroke="currentColor" stroke-width="1"/></g></marker><font-face font-family="Helvetica" font-size="18" units-per-em="1000" underline-position="-75.683594" underline-thickness="49.316406" slope="-666.6667" x-height="539.55078" cap-height="719.72656" ascent="770.01953" descent="-229.98047" font-style="italic" font-weight="bold"><font-face-src><font-face-name name="Helvetica-BoldOblique"/></font-face-src></font-face></defs><g stroke="none" stroke-opacity="1" stroke-dasharray="none" fill="none" fill-opacity="1"><title>Architecture of KingDB</title><rect fill="white" width="1118" height="3915"/><g><title>Architecture of KingDB</title><g><use xl:href="#id106_Graphic" filter="url(#Shadow)"/><use xl:href="#id107_Graphic" filter="url(#Shadow)"/><use xl:href="#id108_Graphic" filter="url(#Shadow)"/><use xl:href="#id111_Graphic" filter="url(#Shadow)"/><use xl:href="#id115_Graphic" filter="url(#Shadow)"/><use xl:href="#id117_Graphic" filter="url(#Shadow)"/><use xl:href="#id133_Graphic" filter="url(#Shadow)"/><use xl:href="#id170_Graphic" filter="url(#Shadow)"/></g><path d="M 35.5 1619.9038 L 641 1619.9038 C 647.07513 1619.9038 652 1624.8287 652 1630.9038 L 652 1838 C 652 1844.0751 647.07513 1849 641 1849 L 35.5 1849 C 29.424868 1849 24.5 1844.0751 24.5 1838 C 24.5 1838 24.5 1838 24.5 1838 L 24.499992 1630.9038 C 24.499992 1624.8287 29.42486 1619.9038 35.499992 1619.9038 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><path d="M 139.273834 140.9281 L 978.7262 140.9281 C 1001.3699 140.9281 1019.7262 159.28442 1019.7262 181.9281 L 1019.7262 409 C 1019.7262 431.64368 1001.3699 450 978.7262 450 L 139.273834 450 C 116.63016 450 98.273834 431.64368 98.273834 409 C 98.273834 409 98.273834 409 98.273834 409 L 98.27382 181.9281 C 98.27382 159.28442 116.63014 140.9281 139.27382 140.9281 Z" fill="#f0f0f0"/><path d="M 35.5 530 L 825.38165 530 C 831.4568 530 836.38165 534.92487 836.38165 541 L 836.38165 689 C 836.38165 695.07513 831.4568 700 825.38165 700 L 35.5 700 C 29.424868 700 24.5 695.07513 24.5 689 C 24.5 689 24.5 689 24.5 689 L 24.499992 541 C 24.499992 534.92487 29.42486 530 35.499992 530 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(42.76184 543.66223)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="91.66992">Client </tspan><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="90.57129" y="29" textLength="163.33008">Application</tspan></text><path d="M 278 609.02393 L 380 609.02393 C 396.56854 609.02393 410 622.4554 410 639.02393 L 410 639.02393 C 410 655.59247 396.56854 669.02393 380 669.02393 L 278 669.02393 C 261.43146 669.02393 248 655.59247 248 639.02393 C 248 639.02393 248 639.02393 248 639.02393 L 248 639.02393 C 248 622.4554 261.43146 609.02393 278 609.02393 Z" fill="#5badff"/><path d="M 278 609.02393 L 380 609.02393 C 396.56854 609.02393 410 622.4554 410 639.02393 L 410 639.02393 C 410 655.59247 396.56854 669.02393 380 669.02393 L 278 669.02393 C 261.43146 669.02393 248 655.59247 248 639.02393 C 248 639.02393 248 639.02393 248 639.02393 L 248 639.02393 C 248 622.4554 261.43146 609.02393 278 609.02393 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(253 624.52393)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="28.638672" y="23" textLength="94.722656">Client #1</tspan></text><path d="M 530 608 L 632 608 C 648.56854 608 662 621.43146 662 638 L 662 638 C 662 654.56854 648.56854 668 632 668 L 530 668 C 513.43146 668 500 654.56854 500 638 C 500 638 500 638 500 638 L 500 638 C 500 621.43146 513.43146 608 530 608 Z" fill="#5badff"/><path d="M 530 608 L 632 608 C 648.56854 608 662 621.43146 662 638 L 662 638 C 662 654.56854 648.56854 668 632 668 L 530 668 C 513.43146 668 500 654.56854 500 638 C 500 638 500 638 500 638 L 500 638 C 500 621.43146 513.43146 608 530 608 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(505 623.5)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="27.308594" y="23" textLength="97.38281">Client #K</tspan></text><path d="M 35.5 800.52393 L 765 800.52393 C 771.07513 800.52393 776 805.4488 776 811.52393 L 776 940.50806 C 776 946.5832 771.07513 951.50806 765 951.50806 L 35.5 951.50806 C 29.424868 951.50806 24.5 946.5832 24.5 940.50806 C 24.5 940.50806 24.5 940.50806 24.5 940.50806 L 24.499992 811.52393 C 24.499992 805.4488 29.42486 800.52393 35.499992 800.52393 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(42.76184 816.52393)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="133.40332">Database</tspan></text><path d="M 35.5 1066.68286 L 610.5 1066.68286 C 616.57513 1066.68286 621.5 1071.6078 621.5 1077.68286 L 621.5 1446 C 621.5 1452.0751 616.57513 1457 610.5 1457 L 35.5 1457 C 29.424868 1457 24.5 1452.0751 24.5 1446 C 24.5 1446 24.5 1446 24.5 1446 L 24.499985 1077.68286 C 24.499985 1071.6078 29.424852 1066.68286 35.499985 1066.68286 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="6"/><text transform="translate(42.76184 1081.51843)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="28.31543">W</tspan><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="27.788086" y="29" textLength="143.34961">rite Buffer</tspan></text><ellipse cx="76.674553" cy="1353.5365" rx="27.000044" ry="11.0000296" fill="white"/><ellipse cx="76.674553" cy="1353.5365" rx="27.000044" ry="11.0000296" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="49.67456" y="1315.2865" width="54" height="38.625" fill="white"/><rect x="49.67456" y="1315.2865" width="54" height="38.625" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><ellipse cx="76.674553" cy="1315.5365" rx="27.000044" ry="11.0000296" fill="white"/><ellipse cx="76.674553" cy="1315.5365" rx="27.000044" ry="11.0000296" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="51.17456" y="1341.1615" width="50.759995" height="14.75" fill="white"/><path d="M 390.07916 1342.0159 L 540.14276 1342.0159 C 556.7113 1342.0159 570.14276 1355.4473 570.14276 1372.0159 L 570.14276 1372.0159 C 570.14276 1388.5845 556.7113 1402.0159 540.14276 1402.0159 L 390.07916 1402.0159 C 373.51062 1402.0159 360.07916 1388.5845 360.07916 1372.0159 C 360.07916 1372.0159 360.07916 1372.0159 360.07916 1372.0159 L 360.07916 1372.0159 C 360.07916 1355.4473 373.51062 1342.0159 390.07916 1342.0159 Z" fill="#5badff"/><path d="M 390.07916 1342.0159 L 540.14276 1342.0159 C 556.7113 1342.0159 570.14276 1355.4473 570.14276 1372.0159 L 570.14276 1372.0159 C 570.14276 1388.5845 556.7113 1402.0159 540.14276 1402.0159 L 390.07916 1402.0159 C 373.51062 1402.0159 360.07916 1388.5845 360.07916 1372.0159 C 360.07916 1372.0159 360.07916 1372.0159 360.07916 1372.0159 L 360.07916 1372.0159 C 360.07916 1355.4473 373.51062 1342.0159 390.07916 1342.0159 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(365.07916 1357.5159)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="17.531799" y="23" textLength="36.023438">Buf</tspan><tspan font-family="Helvetica" font-size="24" font-weight="500" x="53.133362" y="23" textLength="129.39844">fer Manager</tspan></text><ellipse cx="76.674553" cy="1426.5365" rx="27.000044" ry="11.0000296" fill="white"/><ellipse cx="76.674553" cy="1426.5365" rx="27.000044" ry="11.0000296" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="49.67456" y="1388.2865" width="54" height="38.625" fill="white"/><rect x="49.67456" y="1388.2865" width="54" height="38.625" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><ellipse cx="76.674553" cy="1388.5365" rx="27.000044" ry="11.0000296" fill="white"/><ellipse cx="76.674553" cy="1388.5365" rx="27.000044" ry="11.0000296" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="50.67456" y="1414.1615" width="52" height="14.75" fill="white"/><text transform="translate(113.67456 1309.5365)" fill="black"><tspan font-family="Helvetica" font-size="18" font-weight="bold" x="0" y="18" textLength="137.99707">Incoming Buffer</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="40" textLength="189.08789">std::vector&lt;kdb::Order&gt;</tspan></text><text transform="translate(111.17456 1382.5365)" fill="black"><tspan font-family="Helvetica" font-size="18" font-weight="bold" x="0" y="18" textLength="105.996094">Flush Buffer</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="40" textLength="189.08789">std::vector&lt;kdb::Order&gt;</tspan></text><path d="M 31 2089.5239 L 825.38165 2089.5239 C 831.4568 2089.5239 836.38165 2094.4487 836.38165 2100.5239 L 836.38165 2471 C 836.38165 2477.0752 831.4568 2482 825.38165 2482 L 31 2482 C 24.924868 2482 20 2477.0752 20 2471 C 20 2471 20 2471 20 2471 L 19.999985 2100.5239 C 19.999985 2094.4487 24.924852 2089.5239 30.999985 2089.5239 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(39.58728 2107.2852)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="220.03418">Storage Engine</tspan></text><path d="M 593.83337 2354.414 L 736.38904 2354.414 C 752.9576 2354.414 766.38904 2367.8455 766.38904 2384.414 L 766.38904 2384.414 C 766.38904 2400.9827 752.9576 2414.414 736.38904 2414.414 L 593.83337 2414.414 C 577.26483 2414.414 563.83337 2400.9827 563.83337 2384.414 C 563.83337 2384.414 563.83337 2384.414 563.83337 2384.414 L 563.83337 2384.414 C 563.83337 2367.8455 577.26483 2354.414 593.83337 2354.414 Z" fill="#5badff"/><path d="M 593.83337 2354.414 L 736.38904 2354.414 C 752.9576 2354.414 766.38904 2367.8455 766.38904 2384.414 L 766.38904 2384.414 C 766.38904 2400.9827 752.9576 2414.414 736.38904 2414.414 L 593.83337 2414.414 C 577.26483 2414.414 563.83337 2400.9827 563.83337 2384.414 C 563.83337 2384.414 563.83337 2384.414 563.83337 2384.414 L 563.83337 2384.414 C 563.83337 2367.8455 577.26483 2354.414 593.83337 2354.414 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(568.83337 2369.914)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="20.896973" y="23" textLength="150.76172">Index Updater</tspan></text><path d="M 281.83536 2172.6562 L 502.33533 2172.6562 C 518.90387 2172.6562 532.33533 2186.0876 532.33533 2202.6562 L 532.33533 2202.6562 C 532.33533 2219.2249 518.90387 2232.6562 502.33533 2232.6562 L 281.83536 2232.6562 C 265.26682 2232.6562 251.83536 2219.2249 251.83536 2202.6562 C 251.83536 2202.6562 251.83536 2202.6562 251.83536 2202.6562 L 251.83536 2202.6562 C 251.83536 2186.0876 265.26682 2172.6562 281.83536 2172.6562 Z" fill="#5badff"/><path d="M 281.83536 2172.6562 L 502.33533 2172.6562 C 518.90387 2172.6562 532.33533 2186.0876 532.33533 2202.6562 L 532.33533 2202.6562 C 532.33533 2219.2249 518.90387 2232.6562 502.33533 2232.6562 L 281.83536 2232.6562 C 265.26682 2232.6562 251.83536 2219.2249 251.83536 2202.6562 C 251.83536 2202.6562 251.83536 2202.6562 251.83536 2202.6562 L 251.83536 2202.6562 C 251.83536 2186.0876 265.26682 2172.6562 281.83536 2172.6562 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(256.83536 2188.1562)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="72.126953" y="23" textLength="85.33594">Entry W</tspan><tspan font-family="Helvetica" font-size="24" font-weight="500" x="157.04102" y="23" textLength="41.332031">riter</tspan></text><path d="M 279.99994 2351.9131 L 500.5 2351.9131 C 517.06854 2351.9131 530.5 2365.3445 530.5 2381.9131 L 530.5 2381.9131 C 530.5 2398.4817 517.06854 2411.9131 500.5 2411.9131 L 279.99994 2411.9131 C 263.4314 2411.9131 249.99994 2398.4817 249.99994 2381.9131 C 249.99994 2381.9131 249.99994 2381.9131 249.99994 2381.9131 L 249.99994 2381.9131 C 249.99994 2365.3445 263.4314 2351.9131 279.99994 2351.9131 Z" fill="#5badff"/><path d="M 279.99994 2351.9131 L 500.5 2351.9131 C 517.06854 2351.9131 530.5 2365.3445 530.5 2381.9131 L 530.5 2381.9131 C 530.5 2398.4817 517.06854 2411.9131 500.5 2411.9131 L 279.99994 2411.9131 C 263.4314 2411.9131 249.99994 2398.4817 249.99994 2381.9131 C 249.99994 2381.9131 249.99994 2381.9131 249.99994 2381.9131 L 249.99994 2381.9131 C 249.99994 2365.3445 263.4314 2351.9131 279.99994 2351.9131 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(254.99994 2367.4131)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="76.56253" y="23" textLength="117.375">Compactor</tspan></text><path d="M 280 2262.0273 L 504.17072 2262.0273 C 520.73926 2262.0273 534.1707 2275.4587 534.1707 2292.0273 L 534.1707 2292.0273 C 534.1707 2308.596 520.73926 2322.0273 504.17072 2322.0273 L 280 2322.0273 C 263.43146 2322.0273 250 2308.596 250 2292.0273 C 250 2292.0273 250 2292.0273 250 2292.0273 L 250 2292.0273 C 250 2275.4587 263.43146 2262.0273 280 2262.0273 Z" fill="#5badff"/><path d="M 280 2262.0273 L 504.17072 2262.0273 C 520.73926 2262.0273 534.1707 2275.4587 534.1707 2292.0273 L 534.1707 2292.0273 C 534.1707 2308.596 520.73926 2322.0273 504.17072 2322.0273 L 280 2322.0273 C 263.43146 2322.0273 250 2308.596 250 2292.0273 C 250 2292.0273 250 2292.0273 250 2292.0273 L 250 2292.0273 C 250 2275.4587 263.43146 2262.0273 280 2262.0273 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><text transform="translate(255 2277.5273)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="11.71817" y="23" textLength="250.73438">System Statistics Poller</tspan></text><rect x="45" y="1739.8018" width="183" height="70" fill="#aaffd1"/><path d="M 45 1739.8018 L 228 1739.8018 L 228 1809.8018 L 45 1809.8018 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" stroke-dasharray="1,4"/><text transform="translate(50 1745.8018)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="28.480469" y="23" textLength="122.70703">Flush Start </tspan><tspan font-family="Helvetica" font-size="24" font-weight="500" x="57.14453" y="52" textLength="58.710938">event</tspan></text><rect x="448.96887" y="1739.8018" width="183" height="70" fill="#aaffd1"/><path d="M 448.96887 1739.8018 L 631.96887 1739.8018 L 631.96887 1809.8018 L 448.96887 1809.8018 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" stroke-dasharray="1,4"/><text transform="translate(453.96887 1745.8018)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="32.470703" y="23" textLength="121.39453">Flush End  </tspan><tspan font-family="Helvetica" font-size="24" font-weight="500" x="57.14453" y="52" textLength="58.710938">event</tspan></text><rect x="246.75" y="1739.8018" width="183" height="70" fill="#aaffd1"/><path d="M 246.75 1739.8018 L 429.75 1739.8018 L 429.75 1809.8018 L 246.75 1809.8018 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" stroke-dasharray="1,4"/><text transform="translate(251.75 1745.8018)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="15.115234" y="23" textLength="149.4375">Index Update </tspan><tspan font-family="Helvetica" font-size="24" font-weight="500" x="57.14453" y="52" textLength="58.710938">event</tspan></text><path d="M 31 2553.5254 L 384 2553.5254 C 390.07513 2553.5254 395 2558.4502 395 2564.5254 L 395 2806.2549 C 395 2812.33 390.07513 2817.2549 384 2817.2549 L 31 2817.2549 C 24.924868 2817.2549 20 2812.33 20 2806.2549 C 20 2806.2549 20 2806.2549 20 2806.2549 L 19.999989 2564.5254 C 19.999989 2558.4502 24.924856 2553.5254 30.999989 2553.5254 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(38 2567.7754)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="60">HST</tspan><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="57.788086" y="29" textLength="191.7334">able Manager</tspan></text><path d="M 472.38165 2553.5254 L 825.38165 2553.5254 C 831.4568 2553.5254 836.38165 2558.4502 836.38165 2564.5254 L 836.38165 2806.2549 C 836.38165 2812.33 831.4568 2817.2549 825.38165 2817.2549 L 472.38165 2817.2549 C 466.30652 2817.2549 461.38165 2812.33 461.38165 2806.2549 C 461.38165 2806.2549 461.38165 2806.2549 461.38165 2806.2549 L 461.38165 2564.5254 C 461.38165 2558.4502 466.30652 2553.5254 472.38165 2553.5254 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(479.3816 2568.8506)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="78.35449">Index</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="54" textLength="194.0625">(in-memory hash tables)</tspan></text><path d="M 525.47345 2697.2473 C 536.01764 2701.543 536.01764 2708.5078 525.47345 2712.8035 C 514.92932 2717.0994 497.83386 2717.0994 487.28973 2712.8035 C 476.7455 2708.5078 476.7455 2701.543 487.28973 2697.2473 C 497.83386 2692.9514 514.92932 2692.9514 525.47345 2697.2473" fill="white"/><path d="M 525.47345 2697.2473 C 536.01764 2701.543 536.01764 2708.5078 525.47345 2712.8035 C 514.92932 2717.0994 497.83386 2717.0994 487.28973 2712.8035 C 476.7455 2708.5078 476.7455 2701.543 487.28973 2697.2473 C 497.83386 2692.9514 514.92932 2692.9514 525.47345 2697.2473" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="479.3816" y="2666.7754" width="54" height="38.625" fill="white"/><rect x="479.3816" y="2666.7754" width="54" height="38.625" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 525.47345 2659.2473 C 536.01764 2663.543 536.01764 2670.5078 525.47345 2674.8035 C 514.92932 2679.0994 497.83386 2679.0994 487.28973 2674.8035 C 476.7455 2670.5078 476.7455 2663.543 487.28973 2659.2473 C 497.83386 2654.9514 514.92932 2654.9514 525.47345 2659.2473" fill="white"/><path d="M 525.47345 2659.2473 C 536.01764 2663.543 536.01764 2670.5078 525.47345 2674.8035 C 514.92932 2679.0994 497.83386 2679.0994 487.28973 2674.8035 C 476.7455 2670.5078 476.7455 2663.543 487.28973 2659.2473 C 497.83386 2654.9514 514.92932 2654.9514 525.47345 2659.2473" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="480.3816" y="2692.6504" width="52" height="14.75" fill="white"/><text transform="translate(543.38153 2661.0254)" fill="black"><tspan font-family="Helvetica" font-size="18" font-weight="bold" x="0" y="18" textLength="93.01465">Main index</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="40" textLength="266.16797">std::multimap&lt;uint64_t, uint64_t&gt;</tspan></text><path d="M 525.47345 2779.2478 C 536.01764 2783.5435 536.01764 2790.5083 525.47345 2794.804 C 514.92932 2799.0999 497.83386 2799.0999 487.28973 2794.804 C 476.7455 2790.5083 476.7455 2783.5435 487.28973 2779.2478 C 497.83386 2774.9519 514.92932 2774.9519 525.47345 2779.2478" fill="white"/><path d="M 525.47345 2779.2478 C 536.01764 2783.5435 536.01764 2790.5083 525.47345 2794.804 C 514.92932 2799.0999 497.83386 2799.0999 487.28973 2794.804 C 476.7455 2790.5083 476.7455 2783.5435 487.28973 2779.2478 C 497.83386 2774.9519 514.92932 2774.9519 525.47345 2779.2478" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="479.3816" y="2748.7759" width="54" height="38.625" fill="white"/><rect x="479.3816" y="2748.7759" width="54" height="38.625" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><path d="M 525.47345 2741.2478 C 536.01764 2745.5435 536.01764 2752.5083 525.47345 2756.804 C 514.92932 2761.0999 497.83386 2761.0999 487.28973 2756.804 C 476.7455 2752.5083 476.7455 2745.5435 487.28973 2741.2478 C 497.83386 2736.9519 514.92932 2736.9519 525.47345 2741.2478" fill="white"/><path d="M 525.47345 2741.2478 C 536.01764 2745.5435 536.01764 2752.5083 525.47345 2756.804 C 514.92932 2761.0999 497.83386 2761.0999 487.28973 2756.804 C 476.7455 2752.5083 476.7455 2745.5435 487.28973 2741.2478 C 497.83386 2736.9519 514.92932 2736.9519 525.47345 2741.2478" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="480.3816" y="2774.6509" width="52" height="14.75" fill="white"/><text transform="translate(543.38153 2743.0259)" fill="black"><tspan font-family="Helvetica" font-size="18" font-weight="bold" x="0" y="18" textLength="10.995117">T</tspan><tspan font-family="Helvetica" font-size="18" font-weight="bold" x="9.667969" y="18" textLength="134.05078">emporary index</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="40" textLength="266.16797">std::multimap&lt;uint64_t, uint64_t&gt;</tspan></text><path d="M 31 2898.0259 L 384 2898.0259 C 390.07513 2898.0259 395 2902.9507 395 2909.0259 L 395 3164.9995 C 395 3171.0747 390.07513 3175.9995 384 3175.9995 L 31 3175.9995 C 24.924868 3175.9995 20 3171.0747 20 3164.9995 C 20 3164.9995 20 3164.9995 20 3164.9995 L 19.999989 2909.0259 C 19.999989 2902.9507 24.924856 2898.0259 30.999989 2898.0259 C 30.999989 2898.0259 30.99999 2898.0259 30.99999 2898.0259 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(38 2914.6387)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="163.41797">File system</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="54" textLength="14.994141">(fi</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="14.994141" y="54" textLength="146.06543">les stored on disk)</tspan></text><rect x="77.515877" y="2994.4897" width="73.999992" height="89" fill="white"/><rect x="77.515877" y="2994.4897" width="73.999992" height="89" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="73.015877" y="2988.9897" width="21" height="22" fill="white"/><line x1="94.015877" y1="2994.4897" x2="77.515877" y2="3011.4897" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="77.015877" y1="3011.9897" x2="94.015877" y2="3011.9897" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="94.015877" y1="2995.4897" x2="94.015877" y2="3010.9897" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><g id="id106_Graphic"><rect x="99.76184" y="880.0239" width="162" height="42" fill="#dcdcdc"/><rect x="99.76184" y="880.0239" width="162" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(104.76184 886.5239)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="49.996094" y="23" textLength="52.007812">Put()</tspan></text></g><g id="id107_Graphic"><rect x="313.76184" y="880.0239" width="162" height="42" fill="#dcdcdc"/><rect x="313.76184" y="880.0239" width="162" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(318.76184 886.5239)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="33.320312" y="23" textLength="85.359375">Delete()</tspan></text></g><g id="id108_Graphic"><rect x="536" y="880.0239" width="162" height="42" fill="#dcdcdc"/><rect x="536" y="880.0239" width="162" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(541 886.5239)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="48.666016" y="23" textLength="54.66797">Get()</tspan></text></g><g id="id111_Graphic"><rect x="642.44543" y="2122.5239" width="162" height="42" fill="#dcdcdc"/><rect x="642.44543" y="2122.5239" width="162" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(647.44543 2129.0239)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="20.658203" y="23" textLength="110.683594">GetEntry()</tspan></text></g><line x1="90" y1="2651.5254" x2="89.04825" y2="2882.3" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="877.81024" y1="2581" x2="848.0816" y2="2580.8928" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><g id="id115_Graphic"><rect x="140.71428" y="1141.2738" width="162.00002" height="42" fill="#dcdcdc"/><rect x="140.71428" y="1141.2738" width="162.00002" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(145.71428 1147.7738)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="27.988281" y="23" textLength="96.02344">PutPart()</tspan></text></g><line x1="686.44446" y1="1164" x2="571.63617" y2="1163.2654" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><g id="id117_Graphic"><rect x="400.00003" y="1141.2738" width="161.99997" height="42" fill="#dcdcdc"/><rect x="400.00003" y="1141.2738" width="161.99997" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(405.00003 1147.7738)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="48.666016" y="23" textLength="54.66797">Get()</tspan></text></g><line x1="235.87305" y1="921.88904" x2="234.92743" y2="1125.3002" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="322.32947" y1="668" x2="240.36374" y2="865.1961" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="584" y1="669.02393" x2="583.05548" y2="868.3001" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="204" y1="1809.5698" x2="295.87915" y2="2156.6895" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="621.3059" y1="2352.9233" x2="621.49585" y2="1820.7" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="413" y1="1811" x2="592.2572" y2="2341.9148" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="665" y1="2415.9048" x2="665.91498" y2="2541.8257" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><path d="M 804.44543 2140 L 877.81024 2140 L 877.81024 2142 L 877.81024 2954 L 742 2954 L 412.23596 2954" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><g id="id133_Graphic"><rect x="46.4841" y="2643.4512" width="322.0318" height="42" fill="#dcdcdc"/><rect x="46.4841" y="2643.4512" width="322.0318" height="42" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/><text transform="translate(51.4841 2649.9512)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x="7.533478" y="23" textLength="22.652344">W</tspan><tspan font-family="Helvetica" font-size="24" font-weight="500" x="29.763947" y="23" textLength="274.73438">riteOrdersAndFlushFiles()</tspan></text></g><path d="M 436 2411.9131 L 436.4286 2698 L 436.4286 2922 L 412.23584 2922" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="469.76196" y1="2411.0254" x2="525.84338" y2="2540.292" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="355.74597" y1="2169" x2="263.87018" y2="1822.3096" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><path d="M 140.71428 1163 L 78 1163 L 78 1287.30005" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><text transform="translate(444.5 629.52393)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x=".49804688" y="23" textLength="20.003906">...</tspan></text><line x1="685.44446" y1="922" x2="686.4347" y2="2110.3" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><path d="M 876 527 L 1079 527 C 1091.1503 527 1101 536.84973 1101 549 L 1101 733 C 1101 745.15027 1091.1503 755 1079 755 L 876 755 C 863.84973 755 854 745.15027 854 733 C 854 733 854 733 854 733 L 854 549 C 854 536.84973 863.84973 527 876 527 Z" fill="#fff68f"/><text transform="translate(869 542)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="216.12305">The client threads execute </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="204.06445">Put(), Get() and Delete(), </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="221.1416">therefore they are the ones </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="84" textLength="198.0791">performing compression </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="106" textLength="177.09082">and checksum for the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="128" textLength="203.10645">entries they handle. With </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="150" textLength="169.08398">this design, the CPU </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="172" textLength="213.09082">workload is spread across </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="194" textLength="142.083984">the client threads.</tspan></text><path d="M 754.25415 1659.6211 L 1016.15894 1659.6211 C 1028.3092 1659.6211 1038.15894 1669.4708 1038.15894 1681.6211 L 1038.15894 1799.6211 C 1038.15894 1811.7714 1028.3092 1821.6211 1016.15894 1821.6211 L 754.25415 1821.6211 C 742.1039 1821.6211 732.25415 1811.7714 732.25415 1799.6211 C 732.25415 1799.6211 732.25415 1799.6211 732.25415 1799.6211 L 732.25415 1681.6211 C 732.25415 1669.4708 742.1039 1659.6211 754.25415 1659.6211 Z" fill="#fff68f"/><text transform="translate(747.25415 1674.6211)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="53.006836">The W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="52.69043" y="18" textLength="57.023438">rite Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="109.39746" y="18" textLength="21.00586">fer</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="129.418945" y="18" textLength="139.0957">, Storage Engine </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="274.1748">and Index are synchronized using </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="250.16309">a set of events built on top of a </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="84" textLength="141.05566">messaging library</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="139.728516" y="84" textLength="91.05469">, the Event </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="106" textLength="71.041992">Manager</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="70.057617" y="106" textLength="10.001953">. </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="79.743164" y="106" textLength="166.06934">This reduces overall </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="128" textLength="220.13965">architectural dependencies.</tspan></text><text transform="translate(220 23.809525)" fill="black"><tspan font-family="Helvetica" font-size="48" font-weight="bold" x=".2578125" y="47" textLength="677.48438">Architecture of KingDB v0.9.0</tspan></text><path d="M 916 2625.0518 L 1084.19434 2625.0518 C 1096.3446 2625.0518 1106.19434 2634.9016 1106.19434 2647.0518 L 1106.19434 2809.0518 C 1106.19434 2821.2019 1096.3446 2831.0518 1084.19434 2831.0518 L 916 2831.0518 C 903.84973 2831.0518 894 2821.2019 894 2809.0518 C 894 2809.0518 894 2809.0518 894 2809.0518 L 894 2647.0518 C 894 2634.9016 903.84973 2625.0518 916 2625.0518 C 916 2625.0518 916 2625.0518 916 2625.0518 Z" fill="#fff68f"/><text transform="translate(909 2640.0518)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="170.07715">The temporary index </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="113.0625">has the same </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="173.07422">structure as the main </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="84" textLength="150.09961">index, and is used </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="106" textLength="143.05957">when the primary </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="128" textLength="147.11133">index needs to be </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="150" textLength="163.08105">read-only (ex: when </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="172" textLength="181.08984">compaction is running)</tspan></text><text transform="translate(134.07547 158.70529)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="106.66992">Legend</tspan></text><path d="M 183.10721 226.27034 L 288.05936 226.27032 C 299.65732 226.27032 309.05936 235.67235 309.05936 247.27032 L 309.05936 247.27032 C 309.05936 258.86829 299.65732 268.27032 288.05936 268.27032 L 183.10721 268.27032 C 171.50923 268.27032 162.10721 258.86829 162.10721 247.27032 C 162.10721 247.27032 162.10722 247.27032 162.10722 247.27032 L 162.10722 247.27034 C 162.10721 235.67236 171.50922 226.27036 183.10719 226.27034 C 183.10719 226.27034 183.10721 226.27032 183.10721 226.27032 Z" fill="#5badff"/><path d="M 183.10721 226.27034 L 288.05936 226.27032 C 299.65732 226.27032 309.05936 235.67235 309.05936 247.27032 L 309.05936 247.27032 C 309.05936 258.86829 299.65732 268.27032 288.05936 268.27032 L 183.10721 268.27032 C 171.50923 268.27032 162.10721 258.86829 162.10721 247.27032 C 162.10721 247.27032 162.10722 247.27032 162.10722 247.27032 L 162.10722 247.27034 C 162.10721 235.67236 171.50922 226.27036 183.10719 226.27034 C 183.10719 226.27034 183.10721 226.27032 183.10721 226.27032 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke-dasharray="4,4"/><g id="id170_Graphic"><rect x="165.27374" y="292.64154" width="139.888855" height="36" fill="#dcdcdc"/><rect x="165.27374" y="292.64154" width="139.888855" height="36" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/></g><rect x="571.02008" y="226.27036" width="150.03186" height="42" fill="#aaffd1"/><path d="M 571.02008 226.27036 L 721.05194 226.27036 L 721.05194 268.27036 L 571.02008 268.27036 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" stroke-dasharray="1,4"/><path d="M 593.02008 292.26996 L 699.05194 292.26996 C 711.2022 292.26996 721.05194 302.11969 721.05194 314.26996 L 721.05194 322.26996 C 721.05194 334.42023 711.2022 344.26996 699.05194 344.26996 L 593.02008 344.26996 C 580.8698 344.26996 571.02008 334.42023 571.02008 322.26996 C 571.02008 322.26996 571.02008 322.26996 571.02008 322.26996 L 571.02008 314.26996 C 571.02008 302.11969 580.8698 292.26996 593.02008 292.26996 Z" fill="#fff68f"/><text transform="translate(586.02008 307.26996)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="20.003906">    </tspan></text><text transform="translate(320.74219 232.9924)" fill="black"><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="21" textLength="69.706055">Thread</tspan></text><text transform="translate(322.32947 299.64154)" fill="black"><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="21" textLength="182.21973">Method or function</tspan></text><text transform="translate(739.3051 235.85774)" fill="black"><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="21" textLength="108.839844">Event notifi</tspan><tspan font-family="Helvetica" font-size="22" font-weight="500" x="108.839844" y="21" textLength="19.561523">er</tspan></text><text transform="translate(737.71783 299.64154)" fill="black"><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="21" textLength="239.67969">Comment or explanation</tspan></text><path d="M 435.04794 2969.9995 L 728.71423 2969.9995 C 740.8645 2969.9995 750.71423 2979.8494 750.71423 2991.9995 L 750.71423 3043.9995 C 750.71423 3056.1497 740.8645 3065.9995 728.71423 3065.9995 L 435.04794 3065.9995 C 422.89767 3065.9995 413.04794 3056.1497 413.04794 3043.9995 C 413.04794 3043.9995 413.04794 3043.9995 413.04794 3043.9995 L 413.04794 2991.9995 C 413.04794 2979.8494 422.89767 2969.9995 435.04794 2969.9995 Z" fill="#fff68f"/><text transform="translate(428.04794 2984.9995)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="16.989258">W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="16.672852" y="18" textLength="278.10352">rites are done with calls to pwrite().</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="279.18457">Reads are done through read-only </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="119.021484">memory maps.</tspan></text><path d="M 435.04794 3083.8086 L 728.71423 3083.8086 C 740.8645 3083.8086 750.71423 3093.6584 750.71423 3105.8086 L 750.71423 3157.8086 C 750.71423 3169.9587 740.8645 3179.8086 728.71423 3179.8086 L 435.04794 3179.8086 C 422.89767 3179.8086 413.04794 3169.9587 413.04794 3157.8086 C 413.04794 3157.8086 413.04794 3157.8086 413.04794 3157.8086 L 413.04794 3105.8086 C 413.04794 3093.6584 422.89767 3083.8086 435.04794 3083.8086 Z" fill="#fff68f"/><text transform="translate(428.04794 3098.8086)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="304.13672">Data is never overwritten, and always </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="140.0625">written to a new fi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="140.0625" y="40" textLength="134.07715">le. Large entries </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="214.13672">have their own dedicated fi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="214.13672" y="62" textLength="28.010742">les.</tspan></text><text transform="translate(189.65869 3030.6509)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x=".49804688" y="23" textLength="20.003906">...</tspan></text><rect x="243.01578" y="2994.4897" width="74" height="89" fill="white"/><rect x="243.01578" y="2994.4897" width="74" height="89" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><rect x="238.51581" y="2988.9897" width="21" height="22" fill="white"/><line x1="259.51581" y1="2994.4897" x2="243.01581" y2="3011.4897" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="242.51581" y1="3011.9897" x2="259.51581" y2="3011.9897" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><line x1="259.51581" y1="2995.4897" x2="259.51581" y2="3010.9897" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><path d="M 249.99994 2203.206 L 222.1429 2203.206 L 222.1429 2517 L 222.1429 2519.8726 L 270.75 2519.8726 L 317.0158 2519.8726 L 317.0158 2630.3953" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><path d="M 397.65082 1401 L 393.43243 1401 L 393.43243 1587.9365 L 393.43243 1693 L 178 1693 L 140.71428 1693 L 140.71428 1722.3" marker-end="url(#SharpArrow_Marker)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><text transform="translate(64.285965 3099.8408)" fill="black"><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="18" textLength="79.057617">/path/db/fi</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="79.057617" y="18" textLength="24.020508">le1</tspan></text><text transform="translate(228.76582 3099.8408)" fill="black"><tspan font-family="Helvetica" font-size="18" font-weight="500" x="0" y="18" textLength="79.057617">/path/db/fi</tspan><tspan font-family="Helvetica" font-size="18" font-weight="500" x="79.057617" y="18" textLength="27.008789">leN</tspan></text><line x1="418.08136" y1="1413.5293" x2="476.73804" y2="1738" marker-start="url(#SharpArrow_Marker_2)" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/><path d="M 843.52783 798.40674 L 1036.19446 798.40674 C 1042.26953 798.40674 1047.19446 803.3316 1047.19446 809.40674 L 1047.19446 854.4066 C 1047.19446 860.48175 1042.26953 865.4066 1036.19446 865.4066 L 843.52783 865.4066 C 837.4527 865.4066 832.52783 860.48175 832.52783 854.4066 C 832.52783 854.4066 832.52783 854.4066 832.52783 854.4066 L 832.52783 809.40674 C 832.52783 803.3316 837.4527 798.40674 843.52783 798.40674 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><path d="M 844 886.41455 L 1036.6666 886.41455 C 1042.7417 886.41455 1047.6666 891.3394 1047.6666 897.41455 L 1047.6666 942.41443 C 1047.6666 948.48956 1042.7417 953.41443 1036.6666 953.41443 L 844 953.41443 C 837.92487 953.41443 833 948.48956 833 942.41443 C 833 942.41443 833 942.41443 833 942.41443 L 833 897.41455 C 833 891.3394 837.92487 886.41455 844 886.41455 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(872 816)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="136.66992">Snapshot</tspan></text><text transform="translate(887.5 902.3423)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="103.359375">Iterator</tspan></text><path d="M 717.7632 3359.1914 L 1051 3359.1914 C 1057.0751 3359.1914 1062 3364.1162 1062 3370.1914 L 1062 3791.0723 C 1062 3797.1475 1057.0751 3802.0723 1051 3802.0723 L 717.7632 3802.0723 C 711.68805 3802.0723 706.7632 3797.1475 706.7632 3791.0723 C 706.7632 3791.0723 706.7632 3791.0723 706.7632 3791.0723 L 706.7632 3370.1914 C 706.7632 3364.1162 711.68805 3359.1914 717.7632 3359.1914 C 717.7632 3359.1914 717.7632 3359.1914 717.7632 3359.1914 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><path d="M 744.09814 3430.1113 L 1014.5491 3430.1113 C 1020.6242 3430.1113 1025.5491 3435.0361 1025.5491 3441.1113 L 1025.5491 3486.1113 C 1025.5491 3492.1865 1020.6242 3497.1113 1014.5491 3497.1113 L 744.09814 3497.1113 C 738.023 3497.1113 733.09814 3492.1865 733.09814 3486.1113 C 733.09814 3486.1113 733.09814 3486.1113 733.09814 3486.1113 L 733.09814 3441.1113 C 733.09814 3435.0361 738.023 3430.1113 744.09814 3430.1113 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(738.09814 3445.6113)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="17.863647" y="29" textLength="246.72363">DatabaseOptions</tspan></text><text transform="translate(726.35046 3373.9048)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="225.07324">Parametrization</tspan></text><path d="M 745.15613 3521.1035 L 1015.60706 3521.1035 C 1021.6822 3521.1035 1026.60706 3526.0283 1026.60706 3532.1035 L 1026.60706 3577.1035 C 1026.60706 3583.1787 1021.6822 3588.1035 1015.60706 3588.1035 L 745.15613 3588.1035 C 739.081 3588.1035 734.15613 3583.1787 734.15613 3577.1035 C 734.15613 3577.1035 734.15613 3577.1035 734.15613 3577.1035 L 734.15613 3532.1035 C 734.15613 3526.0283 739.081 3521.1035 745.15613 3521.1035 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(739.15613 3536.6035)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="47.88562" y="29" textLength="186.67969">ReadOptions</tspan></text><path d="M 745.15613 3612.5234 L 1015.60706 3612.5234 C 1021.6822 3612.5234 1026.60706 3617.4482 1026.60706 3623.5234 L 1026.60706 3668.5234 C 1026.60706 3674.5986 1021.6822 3679.5234 1015.60706 3679.5234 L 745.15613 3679.5234 C 739.081 3679.5234 734.15613 3674.5986 734.15613 3668.5234 C 734.15613 3668.5234 734.15613 3668.5234 734.15613 3668.5234 L 734.15613 3623.5234 C 734.15613 3617.4482 739.081 3612.5234 745.15613 3612.5234 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(739.15613 3628.0234)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="47.32898" y="29" textLength="28.31543">W</tspan><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="75.117065" y="29" textLength="160.00488">riteOptions</tspan></text><path d="M 744.09814 3703.9434 L 1014.5491 3703.9434 C 1020.6242 3703.9434 1025.5491 3708.8682 1025.5491 3714.9434 L 1025.5491 3759.9434 C 1025.5491 3766.0186 1020.6242 3770.9434 1014.5491 3770.9434 L 744.09814 3770.9434 C 738.023 3770.9434 733.09814 3766.0186 733.09814 3759.9434 C 733.09814 3759.9434 733.09814 3759.9434 733.09814 3759.9434 L 733.09814 3714.9434 C 733.09814 3708.8682 738.023 3703.9434 744.09814 3703.9434 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(738.09814 3719.4434)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="37.858765" y="29" textLength="206.7334">ServerOptions</tspan></text><path d="M 339.76318 3359.1914 L 673 3359.1914 C 679.07513 3359.1914 684 3364.1162 684 3370.1914 L 684 3696 C 684 3702.0752 679.07513 3707 673 3707 L 339.76318 3707 C 333.68805 3707 328.76318 3702.0752 328.76318 3696 C 328.76318 3696 328.76318 3696 328.76318 3696 L 328.76318 3370.1914 C 328.76318 3364.1162 333.68805 3359.1914 339.76318 3359.1914 C 339.76318 3359.1914 339.76318 3359.1914 339.76318 3359.1914 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><path d="M 366.09814 3430.1113 L 636.54907 3430.1113 C 642.6242 3430.1113 647.54907 3435.0361 647.54907 3441.1113 L 647.54907 3486.1113 C 647.54907 3492.1865 642.6242 3497.1113 636.54907 3497.1113 L 366.09814 3497.1113 C 360.023 3497.1113 355.09814 3492.1865 355.09814 3486.1113 C 355.09814 3486.1113 355.09814 3486.1113 355.09814 3486.1113 L 355.09814 3441.1113 C 355.09814 3435.0361 360.023 3430.1113 366.09814 3430.1113 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(360.09814 3445.6113)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="52.86609" y="29" textLength="176.71875">Compressor</tspan></text><text transform="translate(348.35046 3373.9048)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="158.33496">Algorithms</tspan></text><path d="M 367.15613 3521.1035 L 637.60706 3521.1035 C 643.6822 3521.1035 648.60706 3526.0283 648.60706 3532.1035 L 648.60706 3577.1035 C 648.60706 3583.1787 643.6822 3588.1035 637.60706 3588.1035 L 367.15613 3588.1035 C 361.081 3588.1035 356.15613 3583.1787 356.15613 3577.1035 C 356.15613 3577.1035 356.15613 3577.1035 356.15613 3577.1035 L 356.15613 3532.1035 C 356.15613 3526.0283 361.081 3521.1035 367.15613 3521.1035 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(361.15613 3536.6035)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="104.545776" y="29" textLength="73.359375">Hash</tspan></text><path d="M 367.15613 3612.5234 L 637.60706 3612.5234 C 643.6822 3612.5234 648.60706 3617.4482 648.60706 3623.5234 L 648.60706 3668.5234 C 648.60706 3674.5986 643.6822 3679.5234 637.60706 3679.5234 L 367.15613 3679.5234 C 361.081 3679.5234 356.15613 3674.5986 356.15613 3668.5234 C 356.15613 3668.5234 356.15613 3668.5234 356.15613 3668.5234 L 356.15613 3623.5234 C 356.15613 3617.4482 361.081 3612.5234 367.15613 3612.5234 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(361.15613 3628.0234)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="65.361206" y="29" textLength="151.72852">Checksum</tspan></text><path d="M 38.191406 3359.1914 L 291 3359.1914 C 297.07513 3359.1914 302 3364.1162 302 3370.1914 L 302 3871.8086 C 302 3877.8838 297.07513 3882.8086 291 3882.8086 L 38.191406 3882.8086 C 32.116276 3882.8086 27.191406 3877.8838 27.191406 3871.8086 C 27.191406 3871.8086 27.191406 3871.8086 27.191406 3871.8086 L 27.191383 3370.1914 C 27.191383 3364.1162 32.116253 3359.1914 38.191383 3359.1914 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><path d="M 64.526276 3430.1113 L 256.22665 3430.1113 C 262.30179 3430.1113 267.22665 3435.0361 267.22665 3441.1113 L 267.22665 3486.1113 C 267.22665 3492.1865 262.30179 3497.1113 256.22665 3497.1113 L 64.526276 3497.1113 C 58.451145 3497.1113 53.526276 3492.1865 53.526276 3486.1113 C 53.526276 3486.1113 53.526276 3486.1113 53.526276 3486.1113 L 53.526276 3441.1113 C 53.526276 3435.0361 58.451145 3430.1113 64.526276 3430.1113 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(58.526276 3445.6113)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="56.007904" y="29" textLength="91.68457">Status</tspan></text><text transform="translate(46.778687 3373.9048)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="108.35449">Utilities</tspan></text><path d="M 65.29959 3521.1035 L 256.99997 3521.1035 C 263.0751 3521.1035 267.99997 3526.0283 267.99997 3532.1035 L 267.99997 3577.1035 C 267.99997 3583.1787 263.0751 3588.1035 256.99997 3588.1035 L 65.29959 3588.1035 C 59.22446 3588.1035 54.29959 3583.1787 54.29959 3577.1035 C 54.29959 3577.1035 54.29959 3577.1035 54.29959 3577.1035 L 54.29959 3532.1035 C 54.29959 3526.0283 59.22446 3521.1035 65.29959 3521.1035 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(59.29959 3536.6035)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="30.146088" y="29" textLength="143.4082">ByteArray</tspan></text><path d="M 65.29959 3612.0957 L 256.99997 3612.0957 C 263.0751 3612.0957 267.99997 3617.0205 267.99997 3623.0957 L 267.99997 3668.0957 C 267.99997 3674.171 263.0751 3679.0957 256.99997 3679.0957 L 65.29959 3679.0957 C 59.22446 3679.0957 54.29959 3674.171 54.29959 3668.0957 C 54.29959 3668.0957 54.29959 3668.0957 54.29959 3668.0957 L 54.29959 3623.0957 C 54.29959 3617.0205 59.22446 3612.0957 65.29959 3612.0957 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(59.29959 3627.5957)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="61.00302" y="29" textLength="81.694336">Order</tspan></text><path d="M 64.526276 3703.9434 L 256.22665 3703.9434 C 262.30179 3703.9434 267.22665 3708.8682 267.22665 3714.9434 L 267.22665 3759.9434 C 267.22665 3766.0186 262.30179 3770.9434 256.22665 3770.9434 L 64.526276 3770.9434 C 58.451145 3770.9434 53.526276 3766.0186 53.526276 3759.9434 C 53.526276 3759.9434 53.526276 3759.9434 53.526276 3759.9434 L 53.526276 3714.9434 C 53.526276 3708.8682 58.451145 3703.9434 64.526276 3703.9434 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(58.526276 3719.4434)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="51.02011" y="29" textLength="101.660156">Logger</tspan></text><path d="M 65.29959 3794.08 L 256.99997 3794.08 C 263.0751 3794.08 267.99997 3799.0049 267.99997 3805.08 L 267.99997 3850.08 C 267.99997 3856.1553 263.0751 3861.08 256.99997 3861.08 L 65.29959 3861.08 C 59.22446 3861.08 54.29959 3856.1553 54.29959 3850.08 C 54.29959 3850.08 54.29959 3850.08 54.29959 3850.08 L 54.29959 3805.08 C 54.29959 3799.0049 59.22446 3794.08 65.29959 3794.08 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(59.29959 3809.58)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="51.847748" y="29" textLength="100.00488">FileUtil</tspan></text><text transform="translate(75.127045 731.02393)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="157.078125">Client #1 calls the   </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="190.08984">Put() method of KingDB</tspan></text><path d="M 260.88092 719.26196 L 260.88092 719.26196 C 269.09943 719.26196 275.76184 725.92438 275.76184 734.1429 L 275.76184 736.38104 C 275.76184 744.59955 269.09943 751.26196 260.88092 751.26196 L 260.88092 751.26196 C 252.66241 751.26196 246 744.59955 246 736.38104 C 246 736.38104 246 736.38104 246 736.38104 L 246 734.1429 C 246 725.92438 252.66241 719.26196 260.88092 719.26196 Z" fill="#abf082"/><text transform="translate(251 724.26196)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">1.</tspan></text><text transform="translate(23.095215 964.96265)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="172.08984">The interface passes </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="120.09375">the data to the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="162.07031">PutPart() method of </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="47.012695">the W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="46.69629" y="84" textLength="57.023438">rite Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="103.40332" y="84" textLength="21.00586">fer</tspan></text><path d="M 215.11908 959.26697 L 215.11908 959.26697 C 223.33759 959.26697 230 965.9294 230 974.1479 L 230 976.38605 C 230 984.60455 223.33759 991.26697 215.11908 991.26697 L 215.11908 991.26697 C 206.90057 991.26697 200.23816 984.60455 200.23816 976.38605 C 200.23816 976.38605 200.23816 976.38605 200.23816 976.38605 L 200.23816 974.1479 C 200.23816 965.9294 206.90057 959.26697 215.11908 959.26697 Z" fill="#abf082"/><text transform="translate(205.23816 964.26697)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">2.</tspan></text><text transform="translate(122.095215 1207.40515)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="226.09863">Incoming Put() and Delete() </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="228.1289">operations are written to the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="102.058594">incoming buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="101.74219" y="62" textLength="21.00586">fer</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="121.76367" y="62" textLength="5.0009766">.</tspan></text><path d="M 100.88092 1201.7699 L 100.88092 1201.77 C 109.099426 1201.77 115.76184 1208.4324 115.76184 1216.6509 L 115.76184 1218.8889 C 115.76184 1227.1074 109.099426 1233.7698 100.88092 1233.7698 L 100.88092 1233.7698 C 92.662415 1233.7698 86 1227.1074 86 1218.8889 C 86 1218.8889 86 1218.8889 86 1218.8889 L 86 1216.6509 C 86 1208.4324 92.662415 1201.77 100.88092 1201.77 Z" fill="#abf082"/><text transform="translate(91 1206.7699)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">3.</tspan></text><text transform="translate(62.76184 1471.7202)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="187.10156">When the Incoming Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="186.78516" y="18" textLength="107.04199">fer is full, the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="27.017578">Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="26.701172" y="40" textLength="265.12207">fer Manager thread swaps it with </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="106.05762">the Flush Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="105.74121" y="62" textLength="187.11035">fer and passes it to the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="122.07129">Event Manager</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="121.086914" y="84" textLength="10.001953">. </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="130.77246" y="84" textLength="141.07324">The Incoming Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="271.5293" y="84" textLength="26.006836">fer </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="106" textLength="316.15137">continues to take incoming writes while </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="128" textLength="106.05762">the Flush Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="105.74121" y="128" textLength="203.08887">fer is being written to disk</tspan></text><path d="M 371.1502 1468.1084 L 371.1502 1468.1084 C 379.36871 1468.1084 386.03113 1474.77075 386.03113 1482.9893 L 386.03113 1485.2275 C 386.03113 1493.44604 379.36871 1500.1084 371.1502 1500.1084 L 371.1502 1500.1084 C 362.9317 1500.1084 356.26929 1493.44604 356.26929 1485.2275 C 356.26929 1485.2275 356.26929 1485.2275 356.26929 1485.2275 L 356.26929 1482.9894 C 356.26929 1474.7709 362.9317 1468.1084 371.1502 1468.1084 Z" fill="#abf082"/><text transform="translate(361.26929 1473.1084)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">4.</tspan></text><text transform="translate(47.23816 1928.3503)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="177.08203">The Flush Start event </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="38.021484">notifi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="38.021484" y="40" textLength="122.08008">es the Storage </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="136.10742">Engine that a buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="135.791016" y="62" textLength="44.006836">fer is </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="104.0625">ready to be fl</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="104.0625" y="84" textLength="49.04297">ushed</tspan></text><path d="M 225.5835 1998 L 225.5835 1998 C 233.802 1998 240.46442 2004.6624 240.46442 2012.8809 L 240.46442 2015.1191 C 240.46442 2023.3376 233.802 2030 225.5835 2030 L 225.5835 2030 C 217.36499 2030 210.70258 2023.3376 210.70258 2015.1191 C 210.70258 2015.1191 210.70258 2015.1191 210.70258 2015.1191 L 210.70258 2012.881 C 210.70258 2004.6625 217.36499 1998 225.5835 1998 Z" fill="#abf082"/><text transform="translate(215.70258 2003)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">5.</tspan></text><text transform="translate(47.488022 2232.7007)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="100.01953">The Entry W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="99.703125" y="18" textLength="36">riter </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="148.0957">thread passes the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="25.022461">buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="24.706055" y="62" textLength="76.04297">fer to the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="36">HST</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="34.004883" y="84" textLength="110.07422">able Manager</tspan></text><path d="M 201.35724 2218.7007 L 201.35724 2218.7007 C 209.57574 2218.7007 216.23816 2225.363 216.23816 2233.5815 L 216.23816 2235.8198 C 216.23816 2244.0383 209.57574 2250.7007 201.35724 2250.7007 L 201.35724 2250.7007 C 193.13873 2250.7007 186.47632 2244.0383 186.47632 2235.8198 C 186.47632 2235.8198 186.47632 2235.8198 186.47632 2235.8198 L 186.47632 2233.5815 C 186.47632 2225.363 193.13873 2218.7007 201.35724 2218.7007 Z" fill="#abf082"/><text transform="translate(191.47632 2223.7007)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">6.</tspan></text><text transform="translate(134.39276 2714.4014)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="209.10938">The parts of entries in the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="25.022461">buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="24.706055" y="40" textLength="174.07617">fer are written to the fi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="198.78223" y="40" textLength="19.010742">le </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="117.01758">system in HST</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="115.02246" y="62" textLength="90.043945">able format</tspan></text><path d="M 110 2710.6104 L 113.89276 2710.6104 C 122.72932 2710.6104 129.89276 2717.7737 129.89276 2726.6104 L 129.89276 2726.6104 C 129.89276 2735.447 122.72932 2742.6104 113.89276 2742.6104 L 110 2742.6104 C 101.163445 2742.6104 94 2735.447 94 2726.6104 C 94 2726.6104 94 2726.6104 94 2726.6104 L 94 2726.6104 C 94 2717.7737 101.163445 2710.6104 110 2710.6104 Z" fill="#abf082"/><text transform="translate(99 2715.6104)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">7.</tspan></text><path d="M 306.87292 1881.1792 L 310.76569 1881.1792 C 319.60223 1881.1792 326.76569 1888.3427 326.76569 1897.1792 L 326.76569 1897.1792 C 326.76569 1906.0157 319.60223 1913.1792 310.76569 1913.1792 L 306.87292 1913.1792 C 298.03638 1913.1792 290.87292 1906.0157 290.87292 1897.1792 C 290.87292 1897.1792 290.87292 1897.1792 290.87292 1897.1792 L 290.87292 1897.1792 C 290.87292 1888.3427 298.03638 1881.1792 306.87292 1881.1792 Z" fill="#abf082"/><text transform="translate(295.87292 1886.1792)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">8.</tspan></text><text transform="translate(471.17456 1473.8989)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="53.006836">The W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="52.69043" y="18" textLength="57.023438">rite Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="109.39746" y="18" textLength="44.006836">fer is </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="38.021484">notifi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="38.021484" y="40" textLength="158.11523">ed that the Storage </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="181.10742">Engine is done writing </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="55.0459">the buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="54.729492" y="84" textLength="135.10547">fer and updating </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="106" textLength="206.12988">the index, and is ready to </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="128" textLength="153.12305">handle the next buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="152.80664" y="128" textLength="21.00586">fer</tspan></text><path d="M 450.78168 1468.1084 L 454.67444 1468.1084 C 463.511 1468.1084 470.67444 1475.27185 470.67444 1484.1084 L 470.67444 1484.1084 C 470.67444 1492.94495 463.511 1500.1084 454.67444 1500.1084 L 450.78168 1500.1084 C 441.94513 1500.1084 434.78168 1492.94495 434.78168 1484.1084 C 434.78168 1484.1084 434.78168 1484.1084 434.78168 1484.1084 L 434.78168 1484.1084 C 434.78168 1475.27185 441.94513 1468.1084 450.78168 1468.1084 Z" fill="#abf082"/><text transform="translate(439.78168 1473.1084)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x=".92733765" y="18" textLength="10.010742">1</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="9.953705" y="18" textLength="15.011719">1.</tspan></text><path d="M 464.96887 1870.9956 L 468.86163 1870.9956 C 477.69818 1870.9956 484.86163 1878.1591 484.86163 1886.9956 L 484.86163 1886.9956 C 484.86163 1895.8322 477.69818 1902.9956 468.86163 1902.9956 L 464.96887 1902.9956 C 456.13232 1902.9956 448.96887 1895.8322 448.96887 1886.9956 C 448.96887 1886.9956 448.96887 1886.9956 448.96887 1886.9956 L 448.96887 1886.9956 C 448.96887 1878.1591 456.13232 1870.9956 464.96887 1870.9956 Z" fill="#abf082"/><text transform="translate(453.96887 1875.9956)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">9.</tspan></text><text transform="translate(44.087158 1637.6652)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="0" y="29" textLength="213.39844">Event Manager</tspan></text><path d="M 608 723.3413 L 611.89282 723.3413 C 620.72937 723.3413 627.89282 730.50476 627.89282 739.3413 L 627.89282 739.3413 C 627.89282 748.17786 620.72937 755.3413 611.89282 755.3413 L 608 755.3413 C 599.16345 755.3413 592 748.17786 592 739.3413 C 592 739.3413 592 739.3413 592 739.3413 L 592 739.3413 C 592 730.50476 599.16345 723.3413 608 723.3413 Z" fill="#f07677"/><text transform="translate(597 728.3413)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">1.</tspan></text><text transform="translate(635.25415 728.70337)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="195.07324">Client #K calls the Get() </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="146.08301">method of KingDB</tspan></text><path d="M 714 1136.6858 L 717.89282 1136.6858 C 726.72937 1136.6858 733.89282 1143.84924 733.89282 1152.6858 L 733.89282 1152.6858 C 733.89282 1161.52234 726.72937 1168.6858 717.89282 1168.6858 L 714 1168.6858 C 705.16345 1168.6858 698 1161.52234 698 1152.6858 C 698 1152.6858 698 1152.6858 698 1152.6858 L 698 1152.6858 C 698 1143.84924 705.16345 1136.6858 714 1136.6858 Z" fill="#f07677"/><text transform="translate(703 1141.6858)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">2.</tspan></text><text transform="translate(742.25415 1142.04785)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="154.03711">Get() checks the W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="153.7207" y="18" textLength="57.023438">rite Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="210.42773" y="18" textLength="26.006836">fer </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="9">fi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="9" y="40" textLength="232.09277">rst in case the entry was just </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="213.16113">inserted and has not been </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="102.049805">persisted yet</tspan></text><path d="M 714 1260.6196 L 717.89282 1260.6196 C 726.72937 1260.6196 733.89282 1267.7831 733.89282 1276.6196 L 733.89282 1276.6196 C 733.89282 1285.4562 726.72937 1292.6196 717.89282 1292.6196 L 714 1292.6196 C 705.16345 1292.6196 698 1285.4562 698 1276.6196 C 698 1276.6196 698 1276.6196 698 1276.6196 L 698 1276.6196 C 698 1267.7831 705.16345 1260.6196 714 1260.6196 Z" fill="#f07677"/><text transform="translate(703 1265.6196)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">3.</tspan></text><text transform="translate(742.25415 1265.9817)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="204.09082">If the entry is not in the W</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="203.77441" y="18" textLength="30.00586">rite </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="27.017578">Buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="26.701172" y="40" textLength="21.00586">fer</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="46.722656" y="40" textLength="147.049805">, Get() checks the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="209.13574">Storage Engine by calling </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="83.012695">GetEntry()</tspan></text><path d="M 754.25415 1382.5365 L 1016.15894 1382.5365 C 1028.3092 1382.5365 1038.15894 1392.3862 1038.15894 1404.5365 L 1038.15894 1456.5365 C 1038.15894 1468.6868 1028.3092 1478.5365 1016.15894 1478.5365 L 754.25415 1478.5365 C 742.1039 1478.5365 732.25415 1468.6868 732.25415 1456.5365 C 732.25415 1456.5365 732.25415 1456.5365 732.25415 1456.5365 L 732.25415 1404.5365 C 732.25415 1392.3862 742.1039 1382.5365 754.25415 1382.5365 Z" fill="#fff68f"/><text transform="translate(747.25415 1397.5365)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="268.12793">Unlike the Put() and Delete() that </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="56.039062">are buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="55.722656" y="40" textLength="207.08789">fered, the Get() accesses </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="216.13184">the Storage Engine directly</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="214.80469" y="62" textLength="5.0009766">.</tspan></text><path d="M 49.191406 3235.6724 L 1040 3235.6724 C 1052.15027 3235.6724 1062 3245.5222 1062 3257.6724 L 1062 3309.6724 C 1062 3321.8225 1052.15027 3331.6724 1040 3331.6724 L 49.191406 3331.6724 C 37.041142 3331.6724 27.191406 3321.8225 27.191406 3309.6724 C 27.191406 3309.6724 27.191406 3309.6724 27.191406 3309.6724 L 27.191406 3257.6724 C 27.191406 3245.5222 37.041142 3235.6724 49.191406 3235.6724 Z" fill="#fff68f"/><text transform="translate(42.191406 3250.6724)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="987.4863">KingDB uses various classes to perform utility tasks, data processing and parametrization. Because these classes are used </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="715.38574">everywhere, their interactions in the architecture have not be represented in this diagram. </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="715.06934" y="40" textLength="290.13574">They are listed below as a reminder </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="546.29297">that they all are important building blocks of the KingDB architecture.</tspan></text><path d="M 902.3015 2559.6377 L 906.19434 2559.6377 C 915.0309 2559.6377 922.19434 2566.801 922.19434 2575.6377 L 922.19434 2575.6377 C 922.19434 2584.4744 915.0309 2591.6377 906.19434 2591.6377 L 902.3015 2591.6377 C 893.46497 2591.6377 886.3015 2584.4744 886.3015 2575.6377 C 886.3015 2575.6377 886.3015 2575.6377 886.3015 2575.6377 L 886.3015 2575.6377 C 886.3015 2566.801 893.46497 2559.6377 902.3015 2559.6377 Z" fill="#f07677"/><text transform="translate(891.3015 2564.6377)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">4.</tspan></text><text transform="translate(932.1943 2501.3506)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="152.05078">GetEntry() queries </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="107.06836">the index to fi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="107.06836" y="40" textLength="55.0459">nd the </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="162.09668">location of the entry </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="99.043945">using its key</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="97.716797" y="84" textLength="5.0009766">.</tspan></text><path d="M 833.30127 2962.7886 L 837.1941 2962.7886 C 846.03064 2962.7886 853.1941 2969.9519 853.1941 2978.7886 L 853.1941 2978.7886 C 853.1941 2987.6252 846.03064 2994.7886 837.1941 2994.7886 L 833.30127 2994.7886 C 824.4647 2994.7886 817.30127 2987.6252 817.30127 2978.7886 C 817.30127 2978.7886 817.30127 2978.7886 817.30127 2978.7886 L 817.30127 2978.7886 C 817.30127 2969.9519 824.4647 2962.7886 833.30127 2962.7886 Z" fill="#f07677"/><text transform="translate(822.30127 2967.7886)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">5.</tspan></text><text transform="translate(859.5554 2968.1509)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="218.13574">Once the location is found, </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="192.08496">the entry is retrieved by </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="124.066406">accessing the fi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="124.066406" y="62" textLength="81.01758">le system </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="57.01465">directly</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="55.6875" y="84" textLength="5.0009766">.</tspan></text><text transform="translate(630.3059 2246.9524)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="154.08984">The Index Updater </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="38.021484">notifi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="38.021484" y="40" textLength="125.06836">es back once it </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="62" textLength="148.08691">has referenced all </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="84" textLength="54.026367">entries</tspan></text><path d="M 644.3174 2212.9243 L 659.44446 2212.9243 C 668.281 2212.9243 675.44446 2220.0876 675.44446 2228.9243 L 675.44446 2228.9243 C 675.44446 2237.761 668.281 2244.9243 659.44446 2244.9243 L 644.3174 2244.9243 C 635.48083 2244.9243 628.3174 2237.761 628.3174 2228.9243 C 628.3174 2228.9243 628.3174 2228.9243 628.3174 2228.9243 L 628.3174 2228.9243 C 628.3174 2220.0876 635.48083 2212.9243 644.3174 2212.9243 Z" fill="#abf082"/><text transform="translate(633.3174 2217.9243)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="6.052307" y="18" textLength="25.022461">10.</tspan></text><path d="M 286.23798 959.26697 L 540 959.26697 C 552.15027 959.26697 562 969.1167 562 981.26697 L 562 1033.26697 C 562 1045.41724 552.15027 1055.26697 540 1055.26697 L 286.23798 1055.26697 C 274.0877 1055.26697 264.23798 1045.41724 264.23798 1033.26697 C 264.23798 1033.26697 264.23798 1033.26697 264.23798 1033.26697 L 264.23798 981.26697 C 264.23798 969.1167 274.0877 959.26697 286.23798 959.26697 Z" fill="#fff68f"/><text transform="translate(279.23798 974.26697)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="18" textLength="261.10547">All Delete() must be persisted to </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="40" textLength="268.1455">disk, therefore they are all turned </tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" x="0" y="62" textLength="251.07715">into writes just like calls to Put()</tspan></text><path d="M 188.90494 357.01276 L 188.90494 357.01276 C 197.12344 357.01276 203.78586 363.67517 203.78586 371.89368 L 203.78586 374.13184 C 203.78586 382.35034 197.12344 389.01276 188.90494 389.01276 L 188.90494 389.01276 C 180.68643 389.01276 174.02402 382.35034 174.02402 374.13184 C 174.02402 374.13184 174.02402 374.13184 174.02402 374.13184 L 174.02402 371.89368 C 174.02402 363.67517 180.68643 357.01276 188.90494 357.01276 Z" fill="#abf082"/><text transform="translate(179.02402 362.01276)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">1.</tspan></text><text transform="translate(221.32947 360.01276)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x=".49804688" y="23" textLength="20.003906">...</tspan></text><path d="M 275.87308 357.01276 L 279.76584 357.01276 C 288.60239 357.01276 295.76584 364.1762 295.76584 373.01276 L 295.76584 373.01276 C 295.76584 381.8493 288.60239 389.01276 279.76584 389.01276 L 275.87308 389.01276 C 267.03653 389.01276 259.87308 381.8493 259.87308 373.01276 C 259.87308 373.01276 259.87308 373.01276 259.87308 373.01276 L 259.87308 373.01276 C 259.87308 364.1762 267.03653 357.01276 275.87308 357.01276 Z" fill="#abf082"/><text transform="translate(264.87308 362.01276)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x=".92733765" y="18" textLength="10.010742">1</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="9.953705" y="18" textLength="15.011719">1.</tspan></text><text transform="translate(322.32947 360.01276)" fill="black"><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="21" textLength="217.64746">Step-by-step overview</tspan><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="47" textLength="190.74902">of the write process</tspan></text><path d="M 605.0755 363.42535 L 605.0755 363.42535 C 613.294 363.42535 619.95642 370.08777 619.95642 378.30627 L 619.95642 380.54443 C 619.95642 388.76294 613.294 395.42535 605.0755 395.42535 L 605.0755 395.42535 C 596.857 395.42535 590.19458 388.76294 590.19458 380.54443 C 590.19458 380.54443 590.19458 380.54443 590.19458 380.54443 L 590.19458 378.30627 C 590.19458 370.08777 596.857 363.42535 605.0755 363.42535 Z" fill="#f07677"/><text transform="translate(595.19458 368.42535)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="2.375061" y="18" textLength="15.011719">1.</tspan></text><text transform="translate(637.5 366.42535)" fill="black"><tspan font-family="Helvetica" font-size="24" font-weight="500" x=".49804688" y="23" textLength="20.003906">...</tspan></text><path d="M 692.04358 363.42535 L 695.9364 363.42535 C 704.77295 363.42535 711.9364 370.5888 711.9364 379.42535 L 711.9364 379.42535 C 711.9364 388.2619 704.77295 395.42535 695.9364 395.42535 L 692.04358 395.42535 C 683.20703 395.42535 676.04358 388.2619 676.04358 379.42535 C 676.04358 379.42535 676.04358 379.42535 676.04358 379.42535 L 676.04358 379.42535 C 676.04358 370.5888 683.20703 363.42535 692.04358 363.42535 Z" fill="#f07677"/><text transform="translate(681.04358 368.42535)" fill="black"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="bold" x="5.4405212" y="18" textLength="15.011719">5.</tspan></text><text transform="translate(738.5 366.42535)" fill="black"><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="21" textLength="217.64746">Step-by-step overview</tspan><tspan font-family="Helvetica" font-size="22" font-weight="500" x="0" y="47" textLength="188.33203">of the read process</tspan></text><path d="M 844 977.35846 L 1036.6666 977.35846 C 1042.7417 977.35846 1047.6666 982.2833 1047.6666 988.35846 L 1047.6666 1033.3584 C 1047.6666 1039.4335 1042.7417 1044.3584 1036.6666 1044.3584 L 844 1044.3584 C 837.92487 1044.3584 833 1039.4335 833 1033.3584 C 833 1033.3584 833 1033.3584 833 1033.3584 L 833 988.35846 C 833 982.2833 837.92487 977.35846 844 977.35846 Z" stroke="black" stroke-linecap="round" stroke-linejoin="round" stroke-width="5"/><text transform="translate(848 993.2862)" fill="black"><tspan font-family="Helvetica" font-size="30" font-weight="bold" x=".051757812" y="29" textLength="134.98535">Multipart </tspan><tspan font-family="Helvetica" font-size="30" font-weight="bold" x="133.93848" y="29" textLength="50.009766">API</tspan></text><text transform="translate(299.71869 1916.9956)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="99.061523">The Storage</tspan></text><text transform="translate(309.71869 1938.9956)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="99.07031">Engine notifi</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="99.07031" y="18" textLength="19.010742">es</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="40" textLength="5.0009766"> </tspan></text><text transform="translate(314.08392 1961.6509)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="90.07031">that the buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="89.753906" y="18" textLength="21.00586">fer</tspan></text><text transform="translate(319.74982 1982.9956)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="137.08301">has been written </tspan></text><text transform="translate(457.96887 1903.3503)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="149.08887">The Index Updater</tspan></text><text transform="translate(468.0642 1924.8503)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="143.103516">thread now needs</tspan></text><text transform="translate(473.0642 1946.846)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="126.0791">to reference the</tspan></text><text transform="translate(482.96887 1969.0295)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="125.05078">entries from the</tspan></text><text transform="translate(487.96887 1990.846)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="25.022461">buf</tspan><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="24.706055" y="18" textLength="85.05176">fer into the</tspan></text><text transform="translate(497.96887 2013.3503)" fill="#474748"><tspan font-family="Helvetica" font-size="18" font-style="italic" font-weight="500" fill="#474748" x="0" y="18" textLength="43.03125">index</tspan></text></g></g></svg>
