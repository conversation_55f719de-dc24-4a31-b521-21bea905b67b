<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="processonSvg1000" viewBox="136.0 110.0 793.0 318.0" width="793.0" height="318.0"><defs id="ProcessOnDefs1001"><marker id="ProcessOnMarker1031" markerUnits="userSpaceOnUse" orient="auto" markerWidth="16.23606797749979" markerHeight="10.550836550532098" viewBox="-1.0 -1.3763819204711736 16.23606797749979 10.550836550532098" refX="-1.0" refY="3.8990363547948754"><path id="ProcessOnPath1032" d="M12.0 3.8990363547948754L0.0 7.798072709589751V0.0Z" stroke="#323232" stroke-width="2.0" fill="#323232" transform="matrix(1.0,0.0,0.0,1.0,0.0,0.0)"/></marker><marker id="ProcessOnMarker1035" markerUnits="userSpaceOnUse" orient="auto" markerWidth="16.23606797749979" markerHeight="10.550836550532098" viewBox="-1.0 -1.3763819204711736 16.23606797749979 10.550836550532098" refX="-1.0" refY="3.8990363547948754"><path id="ProcessOnPath1036" d="M12.0 3.8990363547948754L0.0 7.798072709589751V0.0Z" stroke="#323232" stroke-width="2.0" fill="#323232" transform="matrix(1.0,0.0,0.0,1.0,0.0,0.0)"/></marker></defs><g id="ProcessOnG1002"><path id="ProcessOnPath1003" d="M136.0 110.0H929.0V428.0H136.0V110.0Z" fill="#ffffff"/><g id="ProcessOnG1004"><g id="ProcessOnG1005" transform="matrix(1.0,0.0,0.0,1.0,342.572192513369,176.33333333333331)" opacity="1.0"><path id="ProcessOnPath1006" d="M21.450617283950617 0.0L112.77398164653066 0.0C141.37480469179815 0.0 141.37480469179815 64.35185185185185 112.77398164653066 64.35185185185185L21.450617283950617 64.35185185185185C-7.150205761316872 64.35185185185185 -7.150205761316872 0.0 21.450617283950617 0.0Z" stroke="#ffffff" stroke-width="2.0" stroke-dasharray="none" opacity="1.0" fill="#ccffe6"/><g id="ProcessOnG1007" transform="matrix(1.0,0.0,0.0,1.0,10.0,22.800925925925924)"><text id="ProcessOnText1008" fill="#323232" font-size="15" x="56.11229946524064" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">MV2PL</text></g></g><g id="ProcessOnG1009" transform="matrix(1.0,0.0,0.0,1.0,499.61497326203204,176.33333333333331)" opacity="1.0"><path id="ProcessOnPath1010" d="M21.450617283950617 0.0L112.77398164653066 0.0C141.37480469179815 0.0 141.37480469179815 64.35185185185185 112.77398164653066 64.35185185185185L21.450617283950617 64.35185185185185C-7.150205761316872 64.35185185185185 -7.150205761316872 0.0 21.450617283950617 0.0Z" stroke="#ffffff" stroke-width="2.0" stroke-dasharray="none" opacity="1.0" fill="#ffffcc"/><g id="ProcessOnG1011" transform="matrix(1.0,0.0,0.0,1.0,10.0,22.800925925925924)"><text id="ProcessOnText1012" fill="#323232" font-size="15" x="56.11229946524064" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">MVTO</text></g></g><g id="ProcessOnG1013" transform="matrix(1.0,0.0,0.0,1.0,658.0,176.33333333333331)" opacity="1.0"><path id="ProcessOnPath1014" d="M21.450617283950617 0.0L112.77398164653066 0.0C141.37480469179815 0.0 141.37480469179815 64.35185185185185 112.77398164653066 64.35185185185185L21.450617283950617 64.35185185185185C-7.150205761316872 64.35185185185185 -7.150205761316872 0.0 21.450617283950617 0.0Z" stroke="#ffffff" stroke-width="2.0" stroke-dasharray="none" opacity="1.0" fill="#ffcccc"/><g id="ProcessOnG1015" transform="matrix(1.0,0.0,0.0,1.0,10.0,22.800925925925924)"><text id="ProcessOnText1016" fill="#323232" font-size="15" x="56.11229946524064" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">MVOCC</text></g></g><g id="ProcessOnG1017" transform="matrix(1.0,0.0,0.0,1.0,342.572192513369,262.5648148148148)" opacity="1.0"><path id="ProcessOnPath1018" d="M21.450617283950617 0.0L112.77398164653066 0.0C141.37480469179815 0.0 141.37480469179815 64.35185185185185 112.77398164653066 64.35185185185185L21.450617283950617 64.35185185185185C-7.150205761316872 64.35185185185185 -7.150205761316872 0.0 21.450617283950617 0.0Z" stroke="#ffffff" stroke-width="2.0" stroke-dasharray="none" opacity="1.0" fill="#99ffcc"/><g id="ProcessOnG1019" transform="matrix(1.0,0.0,0.0,1.0,10.0,22.800925925925924)"><text id="ProcessOnText1020" fill="#323232" font-size="15" x="56.11229946524064" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Lock</text></g></g><g id="ProcessOnG1021" transform="matrix(1.0,0.0,0.0,1.0,499.61497326203204,262.5648148148148)" opacity="1.0"><path id="ProcessOnPath1022" d="M21.450617283950617 0.0L112.77398164653066 0.0C141.37480469179815 0.0 141.37480469179815 64.35185185185185 112.77398164653066 64.35185185185185L21.450617283950617 64.35185185185185C-7.150205761316872 64.35185185185185 -7.150205761316872 0.0 21.450617283950617 0.0Z" stroke="#ffffff" stroke-width="2.0" stroke-dasharray="none" opacity="1.0" fill="#ffff99"/><g id="ProcessOnG1023" transform="matrix(1.0,0.0,0.0,1.0,10.0,22.800925925925924)"><text id="ProcessOnText1024" fill="#323232" font-size="15" x="56.11229946524064" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Timestamp</text></g></g><g id="ProcessOnG1025" transform="matrix(1.0,0.0,0.0,1.0,658.0,262.5648148148148)" opacity="1.0"><path id="ProcessOnPath1026" d="M21.450617283950617 0.0L112.77398164653066 0.0C141.37480469179815 0.0 141.37480469179815 64.35185185185185 112.77398164653066 64.35185185185185L21.450617283950617 64.35185185185185C-7.150205761316872 64.35185185185185 -7.150205761316872 0.0 21.450617283950617 0.0Z" stroke="#ffffff" stroke-width="2.0" stroke-dasharray="none" opacity="1.0" fill="#ff9999"/><g id="ProcessOnG1027" transform="matrix(1.0,0.0,0.0,1.0,10.0,22.800925925925924)"><text id="ProcessOnText1028" fill="#323232" font-size="15" x="56.11229946524064" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Validation</text></g></g><g id="ProcessOnG1029"><path id="ProcessOnPath1030" d="M306.3315508021391 351.3703703703703L306.3315508021391 240.68518518518516L306.3315508021391 240.68518518518516L306.3315508021391 145.2360679774998" stroke="#323232" stroke-width="2.0" stroke-dasharray="none" fill="none" marker-end="url(#ProcessOnMarker1031)"/></g><g id="ProcessOnG1033"><path id="ProcessOnPath1034" d="M304.98930481283423 352.6574074074074L605.6524064171124 352.6574074074074L605.6524064171124 352.6574074074074L891.0794400438906 352.6574074074074" stroke="#323232" stroke-width="2.0" stroke-dasharray="none" fill="none" marker-end="url(#ProcessOnMarker1035)"/></g><g id="ProcessOnG1037" transform="matrix(1.0,0.0,0.0,1.0,806.9893048128342,373.25)" opacity="1.0"><path id="ProcessOnPath1038" d="M0.0 0.0L102.01069518716577 0.0L102.01069518716577 34.75L0.0 34.75Z" stroke="none" stroke-width="0.0" stroke-dasharray="none" opacity="1.0" fill="none"/><g id="ProcessOnG1039" transform="matrix(1.0,0.0,0.0,1.0,0.0,8.0)"><text id="ProcessOnText1040" fill="#323232" font-size="15" x="50.00534759358288" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Optimistic</text></g></g><g id="ProcessOnG1041" transform="matrix(1.0,0.0,0.0,1.0,307.6737967914438,373.25)" opacity="1.0"><path id="ProcessOnPath1042" d="M0.0 0.0L102.01069518716577 0.0L102.01069518716577 34.75L0.0 34.75Z" stroke="none" stroke-width="0.0" stroke-dasharray="none" opacity="1.0" fill="none"/><g id="ProcessOnG1043" transform="matrix(1.0,0.0,0.0,1.0,0.0,8.0)"><text id="ProcessOnText1044" fill="#323232" font-size="15" x="50.00534759358288" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Pessimistic</text></g></g><g id="ProcessOnG1045" transform="matrix(1.0,0.0,0.0,1.0,156.0,191.13425925925927)" opacity="1.0"><path id="ProcessOnPath1046" d="M0.0 0.0L135.5668449197861 0.0L135.5668449197861 34.75L0.0 34.75Z" stroke="none" stroke-width="0.0" stroke-dasharray="none" opacity="1.0" fill="none"/><g id="ProcessOnG1047" transform="matrix(1.0,0.0,0.0,1.0,0.0,8.0)"><text id="ProcessOnText1048" fill="#323232" font-size="15" x="66.78342245989305" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Mulit Version</text></g></g><g id="ProcessOnG1049" transform="matrix(1.0,0.0,0.0,1.0,156.0,277.36574074074076)" opacity="1.0"><path id="ProcessOnPath1050" d="M0.0 0.0L135.5668449197861 0.0L135.5668449197861 34.75L0.0 34.75Z" stroke="none" stroke-width="0.0" stroke-dasharray="none" opacity="1.0" fill="none"/><g id="ProcessOnG1051" transform="matrix(1.0,0.0,0.0,1.0,0.0,8.0)"><text id="ProcessOnText1052" fill="#323232" font-size="15" x="66.78342245989305" y="15.375" font-family="Comic Sans MS" font-weight="bold" font-style="italic" text-decoration="none" family="Comic Sans MS" text-anchor="middle" size="15">Single Version</text></g></g></g></g></svg>
