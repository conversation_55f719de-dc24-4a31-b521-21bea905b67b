<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="721px" height="281px" viewBox="-0.5 -0.5 721 281" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2020-06-05T01:09:51.896Z&quot; agent=&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/12.1.7 Chrome/78.0.3905.1 Electron/7.0.0 Safari/537.36&quot; etag=&quot;AzjkIJ3ghHeMgEih2rTW&quot; version=&quot;12.1.7&quot; type=&quot;device&quot; pages=&quot;1&quot;&gt;&lt;diagram id=&quot;Phr7d8e_Ybaym2yMxWj_&quot; name=&quot;Page-1&quot;&gt;5Zxbc6IwFMc/jY87w01KH/HSrjPautrd7u4bQhCmaByIq/bTb1DwkkOnuzOVQ9IXB07CJT/OJP9/IrTM7mJ7n3qraEQDkrQMLdi2zF7LMJxbi//mgd0hYDvaITBP4+AQ0k+BafxKimBZbR0HJLuoyChNWLy6DPp0uSQ+u4h5aUo3l9VCmlxedeXNCQhMfS+B0ec4YFHRrLZ2in8l8Twqr6xrRcnCKysXgSzyAro5C5n9ltlNKWWHrcW2S5KcXcnlcNzdG6XHG0vJkv3LAY/6zvTdn8Fm9SMcOWN3HCa/vhRn+eMl66LBg4wmHovpkoeH5E/+NPd3z3YlkpSulwHJz6q1zM4mihmZrjw/L93wHOCxiC0SvqfzzTBOki5NaLo/1gw84oQ+j2cspS/krMT2HTILeQlsV3mTJGVkexYq2nlP6IKwdMerFKVWgbzIuTLlNqcHqNtFLDp7eOVhXpEz8+OJT1j5RkH2PygbgPKYX8pO+HU7s5RvzfOtXpyyvBHPKScqIXVDexe7Uyd1E1LX36Y+IV4gI3SnWdAtCN2C0Ic0Y7zW91XgSZnqpt0s6m1I3YDU79avrxKnuvV+t14rdBtCNyH0ceQtGV1IyLttNIv3DeDttl0I/JDd2vSFbCSEbjdsEHUqoHcg9EKzSEu9YaPoLaA+6bu9fMR86D6ORoOnp35PQsxCB64bEHO9wrx0mRC0SphtdMzQZk7647775HaG/VyQ7JnLzvm4j8cZGs3p0ltlEWUK4EW38Tp0lO7DdJAPe9+G+W9/MnCHg9+HrJadt4nfO0Mz+ZBnsjamWRbPkg92jm3iBFYVZMeYmbZ9nUmSqjGwVqmhV3jHa+ANHZ/4lTk8c9pWW7vOdAg+3gqXKC9ecd4DHy80hRLjFWc48PFC+ycxXnFCAx8v9HkS4xWnLtDxltdXBG/ThjYDujr15FmFd64XMrR08kMWMxkdcsVSobwdBRBp6HgrFgXlxQtEGjpepRwcEGnoeJVycECkoeNVysEBkYaOFzo4+fWDkMNVE+/1QoY+Tn7ITsMgm9BuTPNTxwuSfWxPERK7uqcIbm5n2pVU2jGp0fgq6DQAZPQkhk5DesiiIMaHrJTfEAUxPl7oN6TPYVEW40OGrkN+yI2TFNB7dNdpxttuaFPmzeIkZruPBY3yz4kKaVHvSr75GVwIuoD7DC4EG7IFXYjE2gIMe+h4oQmR2eQBaYzOV6mVDiCN0fFC5yFz+gLBhs5XqaUOgBf7tQNLRdMhDnHokKHpkLmPAH0wOl8FvQbQEeiQFfQaYLIYG3L5kQmVIANDhw75E/yLDR8ytHXKQa56X6ZeyAquLYndBT5kBd9JEgc+fMgKrjCJEg4fsoJmTzQj+JCh2ZMesuio8SEr6PjEuaErQua7p6/Q7cvOPuVn9v8C&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="0" y="0" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(41.5,13.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="76" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 78px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Isolation Level</div></div></foreignObject><text x="38" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Isolation Level</text></switch></g><rect x="160" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(171.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="56" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 56px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">P0<br />Dirty Write</div></div></foreignObject><text x="28" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">P0&lt;br&gt;Dirty Write</text></switch></g><rect x="240" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(251.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="56" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 58px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">P1<br />Dirty Read</div></div></foreignObject><text x="28" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">P1&lt;br&gt;Dirty Read</text></switch></g><rect x="320" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(327.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="64" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 66px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">P4<br />Lost Update</div></div></foreignObject><text x="32" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">P4&lt;br&gt;Lost Update</text></switch></g><rect x="400" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(407.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="64" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 66px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">P2<br />Fuzzy Read</div></div></foreignObject><text x="32" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">P2&lt;br&gt;Fuzzy Read</text></switch></g><rect x="480" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(495.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="48" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 50px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">P3<br />Phantom</div></div></foreignObject><text x="24" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">P3&lt;br&gt;Phantom</text></switch></g><rect x="560" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(568.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="62" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">A5A<br />Read Skew</div></div></foreignObject><text x="31" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">A5A&lt;br&gt;Read Skew</text></switch></g><rect x="640" y="0" width="80" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(649.5,6.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="60" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">A5B<br />Write Skew</div></div></foreignObject><text x="30" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">A5B&lt;br&gt;Write Skew</text></switch></g><rect x="0" y="40" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(16.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="126" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 128px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">READ UNCOMMITTED</div></div></foreignObject><text x="63" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">READ UNCOMMITTED</text></switch></g><rect x="0" y="80" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(24.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="110" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 110px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">READ COMMITTED</div></div></foreignObject><text x="55" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">READ COMMITTED</text></switch></g><rect x="0" y="160" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(22.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="114" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 114px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">REPEATABLE READ</div></div></foreignObject><text x="57" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">REPEATABLE READ</text></switch></g><rect x="0" y="200" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(54.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="50" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 52px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Snapshot</div></div></foreignObject><text x="25" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Snapshot</text></switch></g><rect x="0" y="240" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(8.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="142" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 144px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">ANSI SQL SERIALIZABLE</div></div></foreignObject><text x="71" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">ANSI SQL SERIALIZABLE</text></switch></g><rect x="160" y="40" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(165.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="240" y="40" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(256.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="320" y="40" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(336.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="400" y="40" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(416.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="480" y="40" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(496.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="560" y="40" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(576.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="640" y="40" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(656.5,53.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="160" y="80" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(165.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="240" y="80" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(245.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="320" y="80" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(336.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="400" y="80" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(416.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="480" y="80" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(496.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="560" y="80" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(576.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="640" y="80" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(656.5,93.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="160" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(165.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="240" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(245.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="320" y="120" width="80" height="40" fill="#ffe6cc" stroke="#d79b00" pointer-events="none"/><g transform="translate(329.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="60" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Sometimes</div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Sometimes</text></switch></g><rect x="320" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(325.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="400" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(405.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="480" y="160" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(496.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="560" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(565.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="640" y="160" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(645.5,173.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="0" y="120" width="160" height="40" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(38.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="82" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 82px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Cursor Stability</div></div></foreignObject><text x="41" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Cursor Stability</text></switch></g><rect x="160" y="120" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(165.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="240" y="120" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(245.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="560" y="120" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(576.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="400" y="120" width="80" height="40" fill="#ffe6cc" stroke="#d79b00" pointer-events="none"/><g transform="translate(409.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="60" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Sometimes</div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Sometimes</text></switch></g><rect x="480" y="120" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(496.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="640" y="120" width="80" height="40" fill="#ffe6cc" stroke="#d79b00" pointer-events="none"/><g transform="translate(649.5,133.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="60" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Sometimes</div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Sometimes</text></switch></g><rect x="640" y="200" width="80" height="40" fill="#f8cecc" stroke="#b85450" pointer-events="none"/><g transform="translate(656.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="46" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 46px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Possible</div></div></foreignObject><text x="23" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Possible</text></switch></g><rect x="560" y="200" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(565.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="480" y="200" width="80" height="40" fill="#ffe6cc" stroke="#d79b00" pointer-events="none"/><g transform="translate(489.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="60" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 62px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Sometimes</div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Sometimes</text></switch></g><rect x="400" y="200" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(405.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="320" y="200" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(325.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="240" y="200" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(245.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="160" y="200" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(165.5,213.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="160" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(165.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="240" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(245.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="320" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(325.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="400" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(405.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="480" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(485.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="560" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(565.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g><rect x="640" y="240" width="80" height="40" fill="#d5e8d4" stroke="#82b366" pointer-events="none"/><g transform="translate(645.5,253.5)"><switch><foreignObject style="overflow:visible;" pointer-events="none" width="68" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 68px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;white-space:normal;">Not Possible</div></div></foreignObject><text x="34" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Not Possible</text></switch></g></g></svg>
