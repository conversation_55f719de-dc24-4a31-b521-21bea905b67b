<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 577.8671875 782" style="max-width: 577.8671875px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-498"><style>#mermaid-svg-498{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#mermaid-svg-498 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#mermaid-svg-498 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#mermaid-svg-498 .error-icon{fill:#552222;}#mermaid-svg-498 .error-text{fill:#552222;stroke:#552222;}#mermaid-svg-498 .edge-thickness-normal{stroke-width:1px;}#mermaid-svg-498 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-498 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-498 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-svg-498 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-498 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-498 .marker{fill:#333333;stroke:#333333;}#mermaid-svg-498 .marker.cross{stroke:#333333;}#mermaid-svg-498 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-498 p{margin:0;}#mermaid-svg-498 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-svg-498 .cluster-label text{fill:#333;}#mermaid-svg-498 .cluster-label span{color:#333;}#mermaid-svg-498 .cluster-label span p{background-color:transparent;}#mermaid-svg-498 .label text,#mermaid-svg-498 span{fill:#333;color:#333;}#mermaid-svg-498 .node rect,#mermaid-svg-498 .node circle,#mermaid-svg-498 .node ellipse,#mermaid-svg-498 .node polygon,#mermaid-svg-498 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-svg-498 .rough-node .label text,#mermaid-svg-498 .node .label text,#mermaid-svg-498 .image-shape .label,#mermaid-svg-498 .icon-shape .label{text-anchor:middle;}#mermaid-svg-498 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-svg-498 .rough-node .label,#mermaid-svg-498 .node .label,#mermaid-svg-498 .image-shape .label,#mermaid-svg-498 .icon-shape .label{text-align:center;}#mermaid-svg-498 .node.clickable{cursor:pointer;}#mermaid-svg-498 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-svg-498 .arrowheadPath{fill:#333333;}#mermaid-svg-498 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-svg-498 .flowchart-link{stroke:#333333;fill:none;}#mermaid-svg-498 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-498 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-svg-498 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-498 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-svg-498 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-svg-498 .cluster text{fill:#333;}#mermaid-svg-498 .cluster span{color:#333;}#mermaid-svg-498 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-498 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-svg-498 rect.text{fill:none;stroke-width:0;}#mermaid-svg-498 .icon-shape,#mermaid-svg-498 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-498 .icon-shape p,#mermaid-svg-498 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-svg-498 .icon-shape rect,#mermaid-svg-498 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-498 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-498_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-498_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-498_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-498_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-498_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-498_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_A_0" d="M336.184,62L336.184,66.167C336.184,70.333,336.184,78.667,336.254,86.417C336.324,94.167,336.465,101.334,336.535,104.917L336.605,108.501"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M298.455,192.271L275.296,204.726C252.137,217.181,205.818,242.09,182.734,260.129C159.649,278.167,159.798,289.334,159.872,294.917L159.947,300.5"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_0" d="M129.01,407.51L119.175,418.759C109.34,430.007,89.67,452.503,79.835,475.918C70,499.333,70,523.667,70,535.833L70,548"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_0" d="M190.99,407.51L200.658,418.759C210.326,430.007,229.663,452.503,239.406,469.335C249.149,486.167,249.298,497.334,249.372,502.917L249.447,508.5"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M217.515,614.515L206.929,625.929C196.343,637.343,175.172,660.172,164.586,677.086C154,694,154,705,154,710.5L154,716"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_0" d="M281.485,614.515L291.904,625.929C302.323,637.343,323.162,660.172,333.581,677.086C344,694,344,705,344,710.5L344,716"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_G_0" d="M336.684,230.5L336.6,236.583C336.517,242.667,336.35,254.833,336.267,273.083C336.184,291.333,336.184,315.667,336.184,327.833L336.184,340"></path><path marker-end="url(#mermaid-svg-498_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_H_0" d="M374.524,192.659L396.748,205.049C418.972,217.44,463.42,242.22,485.643,266.777C507.867,291.333,507.867,315.667,507.867,327.833L507.867,340"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(159.5, 267)" class="edgeLabel"><g transform="translate(-32, -12)" class="label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>等值连接</p></span></div></foreignObject></g></g><g transform="translate(70, 475)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(249, 475)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(154, 683)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(344, 683)" class="edgeLabel"><g transform="translate(-8, -12)" class="label"><foreignObject height="24" width="16"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(336.18359375, 267)" class="edgeLabel"><g transform="translate(-24, -12)" class="label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>非等值</p></span></div></foreignObject></g></g><g transform="translate(507.8671875, 267)" class="edgeLabel"><g transform="translate(-24, -12)" class="label"><foreignObject height="24" width="48"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>无条件</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(336.18359375, 35)" id="flowchart-Q-0" class="node default"><rect height="54" width="125.0703125" y="-27" x="-62.53515625" style="" class="basic label-container"></rect><g transform="translate(-32.53515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="65.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JOIN请求</p></span></div></foreignObject></g></g><g transform="translate(336.18359375, 171)" id="flowchart-A-1" class="node default"><polygon transform="translate(-59,59)" class="label-container" points="59,0 118,-59 59,-118 0,-59"></polygon><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>连接条件</p></span></div></foreignObject></g></g><g transform="translate(159.5, 371)" id="flowchart-B-3" class="node default"><polygon transform="translate(-67,67)" class="label-container" points="67,0 134,-67 67,-134 0,-67"></polygon><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>存在索引？</p></span></div></foreignObject></g></g><g transform="translate(70, 579)" id="flowchart-C-5" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>索引连接</p></span></div></foreignObject></g></g><g transform="translate(249, 579)" id="flowchart-D-7" class="node default"><polygon transform="translate(-67,67)" class="label-container" points="67,0 134,-67 67,-134 0,-67"></polygon><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>内存充足？</p></span></div></foreignObject></g></g><g transform="translate(154, 747)" id="flowchart-E-9" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>哈希连接</p></span></div></foreignObject></g></g><g transform="translate(344, 747)" id="flowchart-F-11" class="node default"><rect height="54" width="156" y="-27" x="-78" style="" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>排序归并连接</p></span></div></foreignObject></g></g><g transform="translate(336.18359375, 371)" id="flowchart-G-13" class="node default"><rect height="54" width="119.3671875" y="-27" x="-59.68359375" style="" class="basic label-container"></rect><g transform="translate(-29.68359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="59.3671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BNL优化</p></span></div></foreignObject></g></g><g transform="translate(507.8671875, 371)" id="flowchart-H-15" class="node default"><rect height="54" width="124" y="-27" x="-62" style="" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>笛卡尔积</p></span></div></foreignObject></g></g></g></g></g></svg>