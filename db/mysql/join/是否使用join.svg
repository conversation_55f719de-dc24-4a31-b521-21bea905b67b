<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns:xlink="http://www.w3.org/1999/xlink" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1504.4765625 278" style="max-width: 1504.4765625px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-352"><style>#mermaid-svg-352{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#mermaid-svg-352 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#mermaid-svg-352 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#mermaid-svg-352 .error-icon{fill:#552222;}#mermaid-svg-352 .error-text{fill:#552222;stroke:#552222;}#mermaid-svg-352 .edge-thickness-normal{stroke-width:1px;}#mermaid-svg-352 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-352 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-352 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-svg-352 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-352 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-352 .marker{fill:#333333;stroke:#333333;}#mermaid-svg-352 .marker.cross{stroke:#333333;}#mermaid-svg-352 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-352 p{margin:0;}#mermaid-svg-352 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#mermaid-svg-352 .cluster-label text{fill:#333;}#mermaid-svg-352 .cluster-label span{color:#333;}#mermaid-svg-352 .cluster-label span p{background-color:transparent;}#mermaid-svg-352 .label text,#mermaid-svg-352 span{fill:#333;color:#333;}#mermaid-svg-352 .node rect,#mermaid-svg-352 .node circle,#mermaid-svg-352 .node ellipse,#mermaid-svg-352 .node polygon,#mermaid-svg-352 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaid-svg-352 .rough-node .label text,#mermaid-svg-352 .node .label text,#mermaid-svg-352 .image-shape .label,#mermaid-svg-352 .icon-shape .label{text-anchor:middle;}#mermaid-svg-352 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-svg-352 .rough-node .label,#mermaid-svg-352 .node .label,#mermaid-svg-352 .image-shape .label,#mermaid-svg-352 .icon-shape .label{text-align:center;}#mermaid-svg-352 .node.clickable{cursor:pointer;}#mermaid-svg-352 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaid-svg-352 .arrowheadPath{fill:#333333;}#mermaid-svg-352 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaid-svg-352 .flowchart-link{stroke:#333333;fill:none;}#mermaid-svg-352 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-352 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaid-svg-352 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-352 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaid-svg-352 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaid-svg-352 .cluster text{fill:#333;}#mermaid-svg-352 .cluster span{color:#333;}#mermaid-svg-352 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-352 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaid-svg-352 rect.text{fill:none;stroke-width:0;}#mermaid-svg-352 .icon-shape,#mermaid-svg-352 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaid-svg-352 .icon-shape p,#mermaid-svg-352 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaid-svg-352 .icon-shape rect,#mermaid-svg-352 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaid-svg-352 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-352_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-352_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-352_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-svg-352_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-352_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-svg-352_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M673.441,44.261L619.599,51.384C565.757,58.507,458.072,72.754,404.229,83.377C350.387,94,350.387,101,350.387,104.5L350.387,108"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C1_0" d="M271.852,155.503L243.697,161.419C215.543,167.335,159.234,179.168,131.08,188.584C102.926,198,102.926,205,102.926,208.5L102.926,212"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C2_0" d="M350.387,166L350.387,170.167C350.387,174.333,350.387,182.667,350.387,190.333C350.387,198,350.387,205,350.387,208.5L350.387,212"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C3_0" d="M428.922,154.952L458.501,160.96C488.079,166.968,547.237,178.984,576.816,188.492C606.395,198,606.395,205,606.395,208.5L606.395,212"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_D_0" d="M813.441,44.261L867.284,51.384C921.126,58.507,1028.811,72.754,1082.654,83.377C1136.496,94,1136.496,101,1136.496,104.5L1136.496,108"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E1_0" d="M1041.961,157.459L1013.333,163.05C984.704,168.64,927.448,179.82,898.82,188.91C870.191,198,870.191,205,870.191,208.5L870.191,212"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E2_0" d="M1136.496,166L1136.496,170.167C1136.496,174.333,1136.496,182.667,1136.496,190.333C1136.496,198,1136.496,205,1136.496,208.5L1136.496,212"></path><path marker-end="url(#mermaid-svg-352_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E3_0" d="M1231.031,158.055L1258.272,163.546C1285.513,169.037,1339.995,180.018,1367.236,189.009C1394.477,198,1394.477,205,1394.477,208.5L1394.477,212"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(743.44140625, 35)" id="flowchart-A-0" class="node default"><rect height="54" width="140" y="-27" x="-70" style="" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>大公司规范</p></span></div></foreignObject></g></g><g transform="translate(350.38671875, 139)" id="flowchart-B-1" class="node default"><rect height="54" width="157.0703125" y="-27" x="-78.53515625" style="" class="basic label-container"></rect><g transform="translate(-48.53515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="97.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>禁止滥用JOIN</p></span></div></foreignObject></g></g><g transform="translate(102.92578125, 243)" id="flowchart-C1-3" class="node default"><rect height="54" width="189.8515625" y="-27" x="-94.92578125" style="" class="basic label-container"></rect><g transform="translate(-64.92578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.8515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>避免3+表复杂JOIN</p></span></div></foreignObject></g></g><g transform="translate(350.38671875, 243)" id="flowchart-C2-5" class="node default"><rect height="54" width="205.0703125" y="-27" x="-102.53515625" style="" class="basic label-container"></rect><g transform="translate(-72.53515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="145.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>禁止无索引大表JOIN</p></span></div></foreignObject></g></g><g transform="translate(606.39453125, 243)" id="flowchart-C3-7" class="node default"><rect height="54" width="206.9453125" y="-27" x="-103.47265625" style="" class="basic label-container"></rect><g transform="translate(-73.47265625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="146.9453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>禁止CROSS JOIN大表</p></span></div></foreignObject></g></g><g transform="translate(1136.49609375, 139)" id="flowchart-D-9" class="node default"><rect height="54" width="189.0703125" y="-27" x="-94.53515625" style="" class="basic label-container"></rect><g transform="translate(-64.53515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="129.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>允许合理使用JOIN</p></span></div></foreignObject></g></g><g transform="translate(870.19140625, 243)" id="flowchart-E1-11" class="node default"><rect height="54" width="220.6484375" y="-27" x="-110.32421875" style="" class="basic label-container"></rect><g transform="translate(-80.32421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="160.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>小表关联用INNER JOIN</p></span></div></foreignObject></g></g><g transform="translate(1136.49609375, 243)" id="flowchart-E2-13" class="node default"><rect height="54" width="211.9609375" y="-27" x="-105.98046875" style="" class="basic label-container"></rect><g transform="translate(-75.98046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.9609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>需要空值用LEFT JOIN</p></span></div></foreignObject></g></g><g transform="translate(1394.4765625, 243)" id="flowchart-E3-15" class="node default"><rect height="54" width="204" y="-27" x="-102" style="" class="basic label-container"></rect><g transform="translate(-72, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>确保关联字段有索引</p></span></div></foreignObject></g></g></g></g></g></svg>