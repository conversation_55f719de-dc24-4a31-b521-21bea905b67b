<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink"
     version="1.1"
     width="900px" height="600px"
     viewBox="0 0 900 600">

  <title>PG Overall Server Architecture</title>
<rect width="100%" height="100%" fill="#FFFFFF"/>
  <style type="text/css">
  .text_small   {font-style:normal;
                 font-weight:normal;
                 font-size:12px;
                 font-family:"Open Sans", sans-serif;
                 fill:black;
                }
  .text_normal  {font-style:normal;
                 font-weight:normal;
                 font-size:14px;
                 font-family:"Open Sans", sans-serif;
                 fill:black;
                }
  .text_big     {font-style:normal;
                 font-weight:normal;
                 font-size:24px;
                 font-family:"Open Sans", sans-serif;
                 fill:black;
                }
  .text_comment {font-style:italic;
                 font-weight:normal;
                 font-size:14px;
                 font-family:"Open Sans", sans-serif;
                 fill:black;
                }
  </style>

  <defs>

    <!-- Some notes in different sizes -->
    <symbol id="note_200x20" stroke="black" fill="lightyellow">
      <title>UML Note (200 x 20 px)</title>
      <path d="M 200,10 v 10 h -200 v -20 h 190 v 10 h 10 l -10,-10" />
    </symbol>
    <symbol id="note_250x20" stroke="black" fill="lightyellow">
      <title>UML Note (250 x 20 px)</title>
      <path d="M 250,10 v 10 h -250 v -20 h 240 v 10 h 10 l -10,-10" />
    </symbol>
    <symbol id="note_100x35" stroke="black" fill="lightyellow">
      <title>UML Note (100 x 35 px)</title>
      <path d="M 100,10 v 25 h -100 v -35 h 90 v 10 h 10 l -10,-10" />
    </symbol>
    <symbol id="note_170x50" stroke="black" fill="lightyellow">
      <title>UML Note (170 x 50 px)</title>
      <path d="M 170,10 v 40 h -170 v -50 h 160 v 10 h 10 l -10,-10" />
    </symbol>

    <!-- UML states (used for buffers) -->
    <symbol id="state_300x120">
      <title>UML State (300x120)</title>
      <rect x="0" y="0" width="300" height="120" rx="10" stroke="blue" fill="none"/>
    </symbol>
    <symbol id="state_350x120">
      <title>UML State (350x120)</title>
      <rect x="0" y="0" width="350" height="120" rx="10" stroke="blue" fill="none"/>
    </symbol>

    <!-- Discs -->
    <symbol id="disc" stroke="blue" fill="none" >
      <title>Disc</title>
      <ellipse cx="51" cy="13" rx="50" ry="12" />      <!-- top -->
      <path    d="M 1,13 v 60" />                      <!-- left -->
      <path    d="M 101,13 v 60" />                    <!-- right -->
      <path    d="M 1,73 A 50, 12, 0, 0, 0, 101,73" /> <!-- bottom -->
    </symbol>

    <!-- Laptop -->
    <symbol id="laptop" stroke="black" fill="none" >
      <title>Laptop</title>
      <path d="M 20,40 v -40 h 54 v 40 l 15,15 h -84 l 15,-15 h 54" />
      <rect x="23" y="3" width="48" height="34" />
      <!-- symbolize some lines -->
      <path d="M 30,10 h 20" />
      <path d="M 30,15 h 25" />
      <path d="M 30,20 h 10" />
      <path d="M 30,30 h 20" />
      <!-- symbolize keyboard -->
      <path d="M 25,50 h 45 l 2,2 h -50 z " />
    </symbol>

    <!-- marker start/end -->
    <marker id="arrowhead_start"
            markerWidth="10"
            markerHeight="10"
            refX="0"
            refY="3"
            orient="auto">
      <path d="M 6,0 l -6,3 l 6,3" stroke="black" fill="none" />
    </marker>
    <marker id="arrowhead_end"
            markerWidth="10"
            markerHeight="10"
            refX="6"
            refY="3"
            orient="auto">
      <path d="M 0,0 l 6,3 l -6,3" stroke="black" fill="none" />
    </marker>

  </defs>


  <!-- start of rendering area -->
  <!-- enclosing rectangle -->
  <rect x="1" y="1" rx="5" width="99%" height="99%" stroke="black" fill="none" />

  <!-- caption, client side -->
  <text x="15" y="40"  class="text_big">Client</text>
  <text x="140" y="40" class="text_big">Server</text>
  <use xlink:href="#laptop" x="5" y="210" />


  <!-- individual memory -->
  <g transform="translate(130, 70)">
    <use xlink:href="#state_350x120" x="0" y="0" />
    <text x="5" y="20"  class="text_normal">maintenance_work_mem (per connection)</text>
    <text x="5" y="45"  class="text_normal">work_mem (per query operation)</text>
    <text x="5" y="70"  class="text_normal">autovacuum_work_mem (per worker process)</text>
    <text x="5" y="95" class="text_normal">temp_buffer (per connection)</text>
    <text x="5" y="110" class="text_normal">...</text>
    <use xlink:href="#note_200x20" x="140" y="-15" />
    <text x="150" y="0"  class="text_comment">Individual Memory</text>
  </g>

  <!-- shared memory -->
  <g transform="translate(520, 70)">
    <use xlink:href="#state_300x120" x="0" y="0" />
    <text x="10" y="30" class="text_normal">shared_buffers (heap and index)</text>
    <text x="10" y="70" class="text_normal">wal_buffers (WAL records)</text>
    <text x="10" y="100" class="text_normal">...</text>
    <use xlink:href="#note_250x20" x="40" y="-15" />
    <text x="50" y="0"  class="text_comment">Shared Memory (per Instance)</text>
  </g>

  <!-- postmaster -->
  <g transform="translate(180, 215)">
    <rect width="250" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Postmaster</text>
  </g>
  <path d="M 90,230 h 75" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <g transform="translate(140, 230)">
    <circle r="8" stroke="black" fill="lightyellow" />
    <text x="-4" y="4" class="text_small">1</text>
  </g>

  <!-- backend processes -->
  <g transform="translate(150, 315)">
    <rect width="370" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Backend processes (one per connection)</text>
    <path d="M 5,0 v -5 h 370 v 30 h -5" stroke="blue" fill="none" />
    <path d="M 10,-5 v -5 h 370 v 30 h -5" stroke="blue" fill="none" />
  </g>

  <path d="M 90,240 153,303" stroke="black" fill="none"
        marker-start="url(#arrowhead_start)" marker-end="url(#arrowhead_end)"/>
  <g transform="translate(140, 290)">
    <circle r="8" stroke="black" fill="lightyellow" />
    <text x="-4" y="4" class="text_small">3</text>
  </g>

  <!-- connection between postmaster and backend processes -->
  <path d="M 360,250 v 50" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <g transform="translate(180, 255)">
    <use xlink:href="#note_250x20" />
    <text x="10" y="15" class="text_comment">Creates backend processes</text>
  </g>
  <g transform="translate(360, 281)">
    <circle r="8" stroke="black" fill="lightyellow" />
    <text x="-4" y="4" class="text_small">2</text>
  </g>

  <!-- backend process' access to individual memory -->
  <path d="M 460,300 v -100" stroke="black" fill="none"
        marker-start="url(#arrowhead_start)" marker-end="url(#arrowhead_end)"/>
  <!-- its access to shared buffers and WAL buffers -->
  <path d="M 498,300 v -205 h 30" stroke="black" fill="none"
        marker-start="url(#arrowhead_start)" marker-end="url(#arrowhead_end)"/>
  <path d="M 508,300 v -165 h 20" stroke="black" fill="none"
        marker-end="url(#arrowhead_end)"/>

  <!-- WAL writer -->
  <g transform="translate(550, 220)">
    <rect width="120" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">WAL Writer</text>
  </g>
  <path d="M 590,150 v 65" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <path d="M 590,255 v 230" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>

  <!-- Checkpoiner -->
  <g transform="translate(610, 340)">
    <rect width="140" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Checkpointer</text>
  </g>
  <path d="M 740,110 v 220" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <path d="M 605,355 h -130 v 130" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <path d="M 700,330 v -180" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <g transform="translate(570, 330)">
    <use xlink:href="#note_100x35" x="50" y="-50" />
    <text x="60" y="-35"  class="text_comment">Checkpoint</text>
    <text x="60" y="-20"  class="text_comment">Record</text>
  </g>

  <!-- BG writer -->
  <g transform="translate(610, 380)">
    <rect width="180" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Background Writer</text>
  </g>
  <path d="M 770,110 v 260" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <path d="M 605,395 h -120 v 90" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>

  <!-- Archiver -->
  <g transform="translate(610, 420)">
    <rect width="180" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">WAL Archiver</text>
  </g>
  <path d="M 620,485 l 30,-30" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>
  <path d="M 690,455 l 30, 30" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>

  <!-- Vacuum -->
  <g transform="translate(135, 380)">
    <rect width="120" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Autovacuum</text>
    <path d="M 5,0 v -5 h 120 v 30 h -5" stroke="blue" fill="none" />
    <path d="M 10,-5 v -5 h 120 v 30 h -5" stroke="blue" fill="none" />
  </g>

  <!-- Log Writer -->
  <g transform="translate(135, 430)">
    <rect width="120" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Logger</text>
  </g>

  <!-- Stats Collector -->
  <g transform="translate(290, 370)">
    <rect width="140" height="30" stroke="blue" fill="none" />
    <text x="10" y="20" class="text_normal">Stats Collector</text>
  </g>

  <!--       -->
  <!-- files -->
  <!--       -->
  <g transform="translate(145, 490)">
    <use xlink:href="#disc" />
    <text x="35" y="45" class="text_normal">Log</text>
    <text x="20" y="60" class="text_small">text lines,</text>
    <text x="20" y="75" class="text_small">sequential</text>
  </g>
  <path d="M 195,465 v 20" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>

  <g transform="translate(410, 490)">
    <use xlink:href="#disc" />
    <text x="10" y="40" class="text_normal">Heap and</text>
    <text x="25" y="55" class="text_normal">Index</text>
    <text x="15" y="70" class="text_small">binary blocks,</text>
    <text x="30" y="80" class="text_small">random</text>
  </g>
  <path d="M 450,485 v -135" stroke="black" fill="none" marker-end="url(#arrowhead_end)"/>

  <g transform="translate(295, 420)">
    <use xlink:href="#note_170x50" />
    <text x="5" y="15" class="text_comment">Read heap and index</text>
    <text x="5" y="30" class="text_comment">pages and transfer</text>
    <text x="5" y="45" class="text_comment">them to shared_buffers</text>
  </g>

  <g transform="translate(550, 490)">
    <use xlink:href="#disc" />
    <text x="30" y="45" class="text_normal">WAL</text>
    <text x="10" y="60" class="text_small">binary records,</text>
    <text x="20" y="75" class="text_small">sequential</text>
  </g>

  <g transform="translate(690, 490)">
    <use xlink:href="#disc" />
    <text x="16" y="45" class="text_normal">Archived</text>
    <text x="36" y="60" class="text_normal">WAL</text>
  </g>

  <!-- boarder between client and server side -->
  <path d="M 110,20 v 550" stroke="black" fill="none" />
  <g transform="translate(123, 190) rotate(90)">
    <use xlink:href="#note_200x20"  />
    <text class="text_comment" x="10" y ="15">Via TCP/IP or socket</text>
  </g>

  <!-- right side -->
  <g transform="translate(850, 0) rotate(90)">
    <text class="text_big" x="95">RAM</text>
    <text class="text_big" x="250">PROCESSES</text>
    <text class="text_big" x="500">FILES</text>
  </g>

</svg>
