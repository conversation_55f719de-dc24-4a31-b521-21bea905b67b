<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1014.05859375 2142.203125" style="max-width: 1014.05859375px;" class="flowchart" xmlns="http://www.w3.org/2000/svg" width="100%" id="shadow-svg-1750226956736-mermaid">
  <style>
    @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css');
  </style><style>#shadow-svg-1750226956736-mermaid{font-family:Comic Sans MS;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#shadow-svg-1750226956736-mermaid .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#shadow-svg-1750226956736-mermaid .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#shadow-svg-1750226956736-mermaid .error-icon{fill:#552222;}#shadow-svg-1750226956736-mermaid .error-text{fill:#552222;stroke:#552222;}#shadow-svg-1750226956736-mermaid .edge-thickness-normal{stroke-width:1px;}#shadow-svg-1750226956736-mermaid .edge-thickness-thick{stroke-width:3.5px;}#shadow-svg-1750226956736-mermaid .edge-pattern-solid{stroke-dasharray:0;}#shadow-svg-1750226956736-mermaid .edge-thickness-invisible{stroke-width:0;fill:none;}#shadow-svg-1750226956736-mermaid .edge-pattern-dashed{stroke-dasharray:3;}#shadow-svg-1750226956736-mermaid .edge-pattern-dotted{stroke-dasharray:2;}#shadow-svg-1750226956736-mermaid .marker{fill:#333333;stroke:#333333;}#shadow-svg-1750226956736-mermaid .marker.cross{stroke:#333333;}#shadow-svg-1750226956736-mermaid svg{font-family:Comic Sans MS;font-size:14px;}#shadow-svg-1750226956736-mermaid p{margin:0;}#shadow-svg-1750226956736-mermaid .label{font-family:Comic Sans MS;color:#333;}#shadow-svg-1750226956736-mermaid .cluster-label text{fill:#333;}#shadow-svg-1750226956736-mermaid .cluster-label span{color:#333;}#shadow-svg-1750226956736-mermaid .cluster-label span p{background-color:transparent;}#shadow-svg-1750226956736-mermaid .label text,#shadow-svg-1750226956736-mermaid span{fill:#333;color:#333;}#shadow-svg-1750226956736-mermaid .node rect,#shadow-svg-1750226956736-mermaid .node circle,#shadow-svg-1750226956736-mermaid .node ellipse,#shadow-svg-1750226956736-mermaid .node polygon,#shadow-svg-1750226956736-mermaid .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#shadow-svg-1750226956736-mermaid .rough-node .label text,#shadow-svg-1750226956736-mermaid .node .label text,#shadow-svg-1750226956736-mermaid .image-shape .label,#shadow-svg-1750226956736-mermaid .icon-shape .label{text-anchor:middle;}#shadow-svg-1750226956736-mermaid .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#shadow-svg-1750226956736-mermaid .rough-node .label,#shadow-svg-1750226956736-mermaid .node .label,#shadow-svg-1750226956736-mermaid .image-shape .label,#shadow-svg-1750226956736-mermaid .icon-shape .label{text-align:center;}#shadow-svg-1750226956736-mermaid .node.clickable{cursor:pointer;}#shadow-svg-1750226956736-mermaid .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#shadow-svg-1750226956736-mermaid .arrowheadPath{fill:#333333;}#shadow-svg-1750226956736-mermaid .edgePath .path{stroke:#333333;stroke-width:2.0px;}#shadow-svg-1750226956736-mermaid .flowchart-link{stroke:#333333;fill:none;}#shadow-svg-1750226956736-mermaid .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#shadow-svg-1750226956736-mermaid .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#shadow-svg-1750226956736-mermaid .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#shadow-svg-1750226956736-mermaid .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#shadow-svg-1750226956736-mermaid .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#shadow-svg-1750226956736-mermaid .cluster text{fill:#333;}#shadow-svg-1750226956736-mermaid .cluster span{color:#333;}#shadow-svg-1750226956736-mermaid div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:Comic Sans MS;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#shadow-svg-1750226956736-mermaid .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#shadow-svg-1750226956736-mermaid rect.text{fill:none;stroke-width:0;}#shadow-svg-1750226956736-mermaid .icon-shape,#shadow-svg-1750226956736-mermaid .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#shadow-svg-1750226956736-mermaid .icon-shape p,#shadow-svg-1750226956736-mermaid .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#shadow-svg-1750226956736-mermaid .icon-shape rect,#shadow-svg-1750226956736-mermaid .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#shadow-svg-1750226956736-mermaid :root{--mermaid-font-family:Comic Sans MS;}#shadow-svg-1750226956736-mermaid .action&gt;*{fill:#c6e6ff!important;stroke:#333!important;stroke-width:1.5px!important;}#shadow-svg-1750226956736-mermaid .action span{fill:#c6e6ff!important;stroke:#333!important;stroke-width:1.5px!important;}#shadow-svg-1750226956736-mermaid .decision&gt;*{fill:#ffd8a8!important;stroke:#333!important;stroke-width:1.5px!important;}#shadow-svg-1750226956736-mermaid .decision span{fill:#ffd8a8!important;stroke:#333!important;stroke-width:1.5px!important;}#shadow-svg-1750226956736-mermaid .external&gt;*{fill:#e0e0e0!important;stroke:#666!important;}#shadow-svg-1750226956736-mermaid .external span{fill:#e0e0e0!important;stroke:#666!important;}#shadow-svg-1750226956736-mermaid .note&gt;*{fill:transparent!important;stroke:#aaa!important;stroke-dasharray:3!important;font-size:10px!important;}#shadow-svg-1750226956736-mermaid .note span{fill:transparent!important;stroke:#aaa!important;stroke-dasharray:3!important;font-size:10px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750226956736-mermaid_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750226956736-mermaid_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="shadow-svg-1750226956736-mermaid_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="shadow-svg-1750226956736-mermaid_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="shadow-svg-1750226956736-mermaid_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M448.316,59L448.316,63.167C448.316,67.333,448.316,75.667,448.387,83.417C448.457,91.167,448.597,98.334,448.668,101.917L448.738,105.501"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C1_0" d="M391.115,256.798L371.023,272.248C350.932,287.699,310.749,318.599,290.658,341.05C270.566,363.5,270.566,377.5,270.566,384.5L270.566,391.5"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C2_0" d="M522.819,240.497L570.13,258.664C617.441,276.831,712.062,313.166,759.373,336.583C806.684,360,806.684,370.5,806.684,375.75L806.684,381"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C1_D_0" d="M270.566,446.5L270.566,452.417C270.566,458.333,270.566,470.167,270.566,479.583C270.566,489,270.566,496,270.566,499.5L270.566,503"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_D2_0" d="M806.684,457L806.684,461.167C806.684,465.333,806.684,473.667,806.684,481.333C806.684,489,806.684,496,806.684,499.5L806.684,503"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_0" d="M270.566,579L270.566,583.167C270.566,587.333,270.566,595.667,270.566,605.083C270.566,614.5,270.566,625,270.566,630.25L270.566,635.5"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_E1_0" d="M232.151,690.5L220.602,698.167C209.052,705.833,185.953,721.167,174.403,736.208C162.854,751.25,162.854,766,162.854,773.375L162.854,780.75"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_note1_0" d="M118.945,856.75L111.271,863.042C103.598,869.333,88.25,881.917,80.576,891.708C72.902,901.5,72.902,908.5,72.902,912L72.902,915.5"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_E2_0" d="M206.762,856.75L214.436,863.042C222.109,869.333,237.457,881.917,245.131,898.275C252.805,914.634,252.805,934.768,252.805,944.835L252.805,954.902"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_E3_0" d="M229.058,1009.902L217.432,1022.386C205.807,1034.87,182.556,1059.837,170.93,1086.335C159.305,1112.832,159.305,1140.859,159.305,1154.873L159.305,1168.887"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_E4_0" d="M276.552,1009.902L288.177,1022.386C299.803,1034.87,323.054,1059.837,334.679,1086.335C346.305,1112.832,346.305,1140.859,346.305,1154.873L346.305,1168.887"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_0" d="M335.566,681.599L371.398,690.749C407.23,699.899,478.895,718.2,514.727,736.475C550.559,754.75,550.559,773,550.559,782.125L550.559,791.25"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_0" d="M550.559,846.25L550.559,854.292C550.559,862.333,550.559,878.417,550.559,896.525C550.559,914.634,550.559,934.768,550.559,944.835L550.559,954.902"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_G1_0" d="M550.559,1009.902L550.559,1022.386C550.559,1034.87,550.559,1059.837,550.633,1077.654C550.707,1095.471,550.855,1106.138,550.929,1111.472L551.003,1116.805"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G1_G2_0" d="M514.403,1240.313L503.679,1252.256C492.955,1264.199,471.507,1288.084,460.783,1305.276C450.059,1322.469,450.059,1332.969,450.059,1338.219L450.059,1343.469"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G2_note2_0" d="M404.63,1398.469L397.207,1402.635C389.784,1406.802,374.938,1415.135,367.515,1422.802C360.092,1430.469,360.092,1437.469,360.092,1440.969L360.092,1444.469"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G1_G3_0" d="M587.714,1240.313L598.271,1252.256C608.829,1264.199,629.944,1288.084,640.501,1305.276C651.059,1322.469,651.059,1332.969,651.059,1338.219L651.059,1343.469"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G2_H_0" d="M495.487,1398.469L502.91,1402.635C510.333,1406.802,525.179,1415.135,532.602,1427.041C540.025,1438.947,540.025,1454.424,540.025,1462.163L540.025,1469.902"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_0" d="M540.025,1524.902L540.025,1533.308C540.025,1541.714,540.025,1558.525,540.096,1570.514C540.166,1582.503,540.306,1589.67,540.377,1593.253L540.447,1596.837"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I1_0" d="M499.889,1709.2L484.578,1721.806C469.268,1734.412,438.647,1759.624,423.336,1777.48C408.025,1795.336,408.025,1805.836,408.025,1811.086L408.025,1816.336"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I2_0" d="M581.162,1709.2L596.306,1721.806C611.45,1734.412,641.738,1759.624,656.881,1777.48C672.025,1795.336,672.025,1805.836,672.025,1811.086L672.025,1816.336"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I2_I3_0" d="M672.025,1871.336L672.025,1875.503C672.025,1879.669,672.025,1888.003,672.025,1895.669C672.025,1903.336,672.025,1910.336,672.025,1913.836L672.025,1917.336"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I3_note3_0" d="M672.025,1972.336L672.025,1976.503C672.025,1980.669,672.025,1989.003,672.025,1996.669C672.025,2004.336,672.025,2011.336,672.025,2014.836L672.025,2018.336"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_J_0" d="M806.684,579L806.684,583.167C806.684,587.333,806.684,595.667,806.684,603.333C806.684,611,806.684,618,806.684,621.5L806.684,625"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_note4_0" d="M756.649,701L748.425,706.917C740.202,712.833,723.755,724.667,715.532,735.833C707.309,747,707.309,757.5,707.309,762.75L707.309,768"></path><path marker-end="url(#shadow-svg-1750226956736-mermaid_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_0" d="M856.719,701L864.942,706.917C873.165,712.833,889.612,724.667,897.835,737.958C906.059,751.25,906.059,766,906.059,773.375L906.059,780.75"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(270.56640625, 349.5)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(806.68359375, 349.5)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(162.853515625, 736.5)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(159.3046875, 1084.8046875)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(346.3046875, 1084.8046875)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(550.55859375, 736.5)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(450.05859375, 1311.96875)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(651.05859375, 1311.96875)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(408.025390625, 1784.8359375)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g transform="translate(672.025390625, 1784.8359375)" class="edgeLabel"><g transform="translate(-7, -10.5)" class="label"><foreignObject height="21" width="14"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(448.31640625, 33.5)" id="flowchart-A-0" class="node default external"><rect height="51" width="144" y="-25.5" x="-72" style="fill:#e0e0e0 !important;stroke:#666 !important;dashed: !important" class="basic label-container"></rect><g transform="translate(-42, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>发生交通事故</p></span></div></foreignObject></g></g><g transform="translate(448.31640625, 211.5)" id="flowchart-B-1" class="node default decision"><polygon style="fill:#ffd8a8 !important;stroke:#333 !important;stroke-width:1.5px !important" transform="translate(-102.5,102.5)" class="label-container" points="102.5,0 205,-102.5 102.5,-205 0,-102.5"></polygon><g transform="translate(-77, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="154"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>是否有人员死亡或重伤？</p></span></div></foreignObject></g></g><g transform="translate(270.56640625, 421)" id="flowchart-C1-3" class="node default action"><rect height="51" width="227.8828125" y="-25.5" x="-113.94140625" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-83.94140625, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="167.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>立即报警110+联系保险公司</p></span></div></foreignObject></g></g><g transform="translate(806.68359375, 421)" id="flowchart-C2-5" class="node default action"><rect height="72" width="186" y="-36" x="-93" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-63, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="126"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>立即报警+120急救<br/>配合抢救，做好姿态</p></span></div></foreignObject></g></g><g transform="translate(270.56640625, 543)" id="flowchart-D-7" class="node default action"><rect height="72" width="260" y="-36" x="-130" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-100, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>不垫付、不探望、不调解、声称没钱</p></span></div></foreignObject></g></g><g transform="translate(806.68359375, 543)" id="flowchart-D2-9" class="node default external"><rect height="72" width="228" y="-36" x="-114" style="fill:#e0e0e0 !important;stroke:#666 !important;dashed: !important" class="basic label-container"></rect><g transform="translate(-84, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="168"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>垫付部分医疗费并保留凭证<br/>配合调查，争取减责</p></span></div></foreignObject></g></g><g transform="translate(270.56640625, 665)" id="flowchart-E-11" class="node default"><rect height="51" width="130" y="-25.5" x="-65" style="" class="basic label-container"></rect><g transform="translate(-35, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="70"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>交警扣车？</p></span></div></foreignObject></g></g><g transform="translate(162.853515625, 820.75)" id="flowchart-E1-13" class="node default action"><rect height="72" width="214.8515625" y="-36" x="-107.42578125" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-77.42578125, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="154.8515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>配合交资料，不交停车费<br/>15个工作日后取验车报告</p></span></div></foreignObject></g></g><g transform="translate(72.90234375, 984.40234375)" id="flowchart-note1-15" class="node default note"><circle cy="0" cx="0" r="64.90234375" style="fill:transparent !important;stroke:#aaa !important;stroke-dasharray:3 !important;3: !important" class="basic label-container"></circle><g transform="translate(-57.40234375, -15)" style="font-size:10px !important" class="label"><rect></rect><foreignObject height="30" width="114.8046875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 10px !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:10px !important"><p>新车防护：拍照+车衣覆盖<br/>取车受阻：报警/复议</p></span></div></foreignObject></g></g><g transform="translate(252.8046875, 984.40234375)" id="flowchart-E2-17" class="node default decision"><rect height="51" width="130" y="-25.5" x="-65" style="fill:#ffd8a8 !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-35, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="70"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>报告被拒？</p></span></div></foreignObject></g></g><g transform="translate(159.3046875, 1198.38671875)" id="flowchart-E3-19" class="node default action"><rect height="51" width="144" y="-25.5" x="-72" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-42, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="84"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>申请行政复议</p></span></div></foreignObject></g></g><g transform="translate(346.3046875, 1198.38671875)" id="flowchart-E4-21" class="node default action"><rect height="51" width="130" y="-25.5" x="-65" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-35, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="70"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>凭报告取车</p></span></div></foreignObject></g></g><g transform="translate(550.55859375, 820.75)" id="flowchart-F-23" class="node default action"><rect height="51" width="116" y="-25.5" x="-58" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>正常用车</p></span></div></foreignObject></g></g><g transform="translate(550.55859375, 984.40234375)" id="flowchart-G-25" class="node default"><rect height="51" width="116" y="-25.5" x="-58" style="" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>等待联系</p></span></div></foreignObject></g></g><g transform="translate(550.55859375, 1198.38671875)" id="flowchart-G1-27" class="node default external"><polygon style="fill:#e0e0e0 !important;stroke:#666 !important;dashed: !important" transform="translate(-78.08203125,78.08203125)" class="label-container" points="78.08203125,0 156.1640625,-78.08203125 78.08203125,-156.1640625 0,-78.08203125"></polygon><g transform="translate(-52.58203125, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="105.1640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对方/交警联系？</p></span></div></foreignObject></g></g><g transform="translate(450.05859375, 1372.96875)" id="flowchart-G2-29" class="node default action"><rect height="51" width="186" y="-25.5" x="-93" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-63, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="126"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>回应：联系保险公司</p></span></div></foreignObject></g></g><g transform="translate(360.091796875, 1499.40234375)" id="flowchart-note2-31" class="node default note"><circle cy="0" cx="0" r="50.93359375" style="fill:transparent !important;stroke:#aaa !important;stroke-dasharray:3 !important;3: !important" class="basic label-container"></circle><g transform="translate(-43.43359375, -30)" style="font-size:10px !important" class="label"><rect></rect><foreignObject height="60" width="86.8671875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 10px !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:10px !important"><p>保险应对：<br/>• 所有费用联系保险<br/>• 被诉追加保险<br/>• 拒赔无票据费</p></span></div></foreignObject></g></g><g transform="translate(651.05859375, 1372.96875)" id="flowchart-G3-33" class="node default external"><rect height="51" width="116" y="-25.5" x="-58" style="fill:#e0e0e0 !important;stroke:#666 !important;dashed: !important" class="basic label-container"></rect><g transform="translate(-28, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="56"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>持续等待</p></span></div></foreignObject></g></g><g transform="translate(540.025390625, 1499.40234375)" id="flowchart-H-35" class="node default action"><rect height="51" width="158" y="-25.5" x="-79" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-49, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="98"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>收到责任认定书</p></span></div></foreignObject></g></g><g transform="translate(540.025390625, 1674.8359375)" id="flowchart-I-37" class="node default decision"><polygon style="fill:#ffd8a8 !important;stroke:#333 !important;stroke-width:1.5px !important" transform="translate(-74.5,74.5)" class="label-container" points="74.5,0 149,-74.5 74.5,-149 0,-74.5"></polygon><g transform="translate(-49, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="98"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对方接受调解？</p></span></div></foreignObject></g></g><g transform="translate(408.025390625, 1845.8359375)" id="flowchart-I1-39" class="node default action"><rect height="51" width="214" y="-25.5" x="-107" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-77, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="154"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>签调解书，保险公司理赔</p></span></div></foreignObject></g></g><g transform="translate(672.025390625, 1845.8359375)" id="flowchart-I2-41" class="node default action"><rect height="51" width="214" y="-25.5" x="-107" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-77, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="154"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>对方起诉，保险公司应诉</p></span></div></foreignObject></g></g><g transform="translate(672.025390625, 1946.8359375)" id="flowchart-I3-43" class="node default action"><rect height="51" width="186" y="-25.5" x="-93" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-63, -10.5)" style="" class="label"><rect></rect><foreignObject height="21" width="126"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>法院判决后执行赔偿</p></span></div></foreignObject></g></g><g transform="translate(672.025390625, 2078.26953125)" id="flowchart-note3-45" class="node default note"><circle cy="0" cx="0" r="55.93359375" style="fill:transparent !important;stroke:#aaa !important;stroke-dasharray:3 !important;3: !important" class="basic label-container"></circle><g transform="translate(-48.43359375, -22.5)" style="font-size:10px !important" class="label"><rect></rect><foreignObject height="45" width="96.8671875"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 10px !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:10px !important"><p>证件取回：<br/>• 调解成功：签字取证<br/>• 判决后：凭判决书</p></span></div></foreignObject></g></g><g transform="translate(806.68359375, 665)" id="flowchart-J-47" class="node default action"><rect height="72" width="242" y="-36" x="-121" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-91, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="182"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>保留所有垫付凭证<br/>避免认全责，拒绝不合理要求</p></span></div></foreignObject></g></g><g transform="translate(707.30859375, 820.75)" id="flowchart-note4-49" class="node default note"><circle cy="0" cx="0" r="48.75" style="fill:transparent !important;stroke:#aaa !important;stroke-dasharray:3 !important;3: !important" class="basic label-container"></circle><g transform="translate(-41.25, -30)" style="font-size:10px !important" class="label"><rect></rect><foreignObject height="60" width="82.5"><div xmlns="http://www.w3.org/1999/xhtml" style="font-size: 10px !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="font-size:10px !important"><p>垫付风险：<br/>• 仅垫急救费(≤1万)<br/>• 保留凭证<br/>• 可申请救助基金</p></span></div></foreignObject></g></g><g transform="translate(906.05859375, 820.75)" id="flowchart-K-51" class="node default action"><rect height="72" width="200" y="-36" x="-100" style="fill:#c6e6ff !important;stroke:#333 !important;stroke-width:1.5px !important" class="basic label-container"></rect><g transform="translate(-70, -21)" style="" class="label"><rect></rect><foreignObject height="42" width="140"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>通过保险公司协商赔偿<br/>诉讼中主张返还垫付款</p></span></div></foreignObject></g></g></g></g></g></svg>