<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="svgTag" contentstyletype="text/css" height="510px" preserveAspectRatio="none" style="width: 749px; height: 510px; background: rgb(255, 255, 255); --primary-color: #a80036; --secondary-color: #fefece; --tertiary-color: #fbfb77; --quaternary-color: #3a3a3a; --text-color: #000000; --line-color: #a80036; --label-border-color: #a80036; --label-background-color: #fefece; --label-text-color: #000000; --box-back-color: #fefece; --box-stroke-color: #a80036; --font-stack: Open Sans; --participant-stroke-width: 1.5; --border-thickness: 1; --font-size: 14;" version="1.1" viewBox="0 0 749 510" width="749px" zoomAndPan="magnify"><title>Oauth2令牌颁发之授权码模式</title><defs><style>

    svg g ellipse,
    svg g circle,
    svg g rect {
      stroke: var(--primary-color);
      stroke-width: var(--participant-stroke-width);
      /* fill: url(#image); */
      fill: var(--secondary-color);
      stroke-dasharray: 7500;
      animation: draw 5s linear;
    }

    svg g path {
      fill: var(--tertiary-color);
      stroke: var(--quaternary-color);
      stroke-width: 1.5;
      stroke-dasharray: 7500;
      animation: draw 5s linear;
    }

    svg g polygon {
      fill: var(--line-color);
      stroke-dasharray: 7500;
      animation: draw 5s linear;
    }

    svg g line,
    svg g polyline {
      stroke: var(--line-color);
      stroke-width: var(--border-thickness);
      stroke-dasharray: 7500;
      animation: draw 5s linear;
    }

    svg g text {
      fill: var(--text-color);
      font-family: var(--font-stack), "Tahoma";
      font-size: 14;
    }

    svg g line.dashed {
      stroke-dasharray: 5, 5;
      animation: dash 1s infinite;
    }

    svg g line.dotted {
      stroke-dasharray: 2, 2;
      animation: dash 1s infinite;
    }

    svg g line.skipped {
      stroke-dasharray: 1, 4;
      animation: dash 1s infinite;
    }

    svg g line.labelDivider {
      stroke-width: 2px;
    }

    svg g .label {
      stroke: var(--label-border-color);
      fill: var(--label-background-color);
    }
    svg g .labelText {
      fill: var(--label-text-color);
    }

    svg g path.actor {
      stroke: var(--primary-color);
      stroke-width: 2;
    }

    svg g path.note {
      stroke: var(--quaternary-color);
      fill: var(--tertiary-color);
      stroke-width: 1;
      font-size: 12;
    }
    svg g polygon.note {
      stroke: var(--quaternary-color);
      fill: var(--tertiary-color);
      stroke-width: 1;
      font-size: 12;
    }

    svg g .transparent {
      fill: none;
    }

    svg g path.database {
      fill: var(--secondary-color);
      stroke: var(--primary-color);
      stroke-width: 1.5px;
    }

    svg g path.squiggly {
      fill: none !important;
      stroke: var(--line-color) !important;
    }

    svg g rect.box {
      fill: var(--box-back-color);
      stroke: var(--box-stroke-color);
    }
    svg g rect.titleBox {
      fill: none;
      stroke: white;
      stroke-width: 5;

    }
    svg g line.divider {
      stroke: var(--primary-color);
      stroke-width: 3.5;
    }
    svg g line.altDivider {
      stroke: var(--primary-color);
      stroke-dasharray: 2, 2;
    }


    svg g path.alt {
      fill: var(--primary-color);
      stroke: var(--primary-color);
    }

    svg g path.actorClass {
      fill: var(--primary-color);
      stroke: var(--primary-color);
    }

    </style>
    </defs><g><text font-family="sans-serif" font-size="14" font-weight="bold" lengthAdjust="spacing" textLength="197.7152" x="274.7927" y="27.9951">Oauth2令牌颁发之授权码模式</text><rect height="262.1953" width="10" x="148.046" y="149.7266"></rect><rect height="87.3984" width="10" x="338.4426" y="295.3906"></rect><rect height="87.3984" width="10" x="432.9338" y="178.8594"></rect><rect height="145.6641" width="10" x="689.7142" y="207.9922"></rect><line x1="23" x2="23" y1="118.5938" y2="429.9219"></line><line x1="152.1461" x2="152.1461" y1="118.5938" y2="429.9219"></line><line x1="343.1775" x2="343.1775" y1="118.5938" y2="429.9219"></line><line x1="437.2951" x2="437.2951" y1="118.5938" y2="429.9219"></line><line x1="694.1277" x2="694.1277" y1="118.5938" y2="429.9219"></line><svg x="-1.0448999999999984" y="55.7969" width="45px" height="45px" viewBox="0 -1 45 45" version="1.1"><g id="surface1"><path class="actorClass" name="participantshape" d="M 38.410156 29.089844 C 35.960938 26.640625 33.042969 24.824219 29.859375 23.726562 C 33.269531 21.378906 35.507812 17.449219 35.507812 13.007812 C 35.507812 5.835938 29.671875 0 22.5 0 C 15.328125 0 9.492188 5.835938 9.492188 13.007812 C 9.492188 17.449219 11.730469 21.378906 15.140625 23.726562 C 11.957031 24.824219 9.039062 26.640625 6.589844 29.089844 C 2.339844 33.339844 0 38.988281 0 45 L 3.515625 45 C 3.515625 34.53125 12.03125 26.015625 22.5 26.015625 C 32.96875 26.015625 41.484375 34.53125 41.484375 45 L 45 45 C 45 38.988281 42.660156 33.339844 38.410156 29.089844 Z M 22.5 22.5 C 17.265625 22.5 13.007812 18.242188 13.007812 13.007812 C 13.007812 7.773438 17.265625 3.515625 22.5 3.515625 C 27.734375 3.515625 31.992188 7.773438 31.992188 13.007812 C 31.992188 18.242188 27.734375 22.5 22.5 22.5 Z M 22.5 22.5 "></path></g></svg><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="31.9102" x="5" y="115.292">User</text><svg x="-1.0448999999999984" y="448.7188" width="45px" height="45px" viewBox="0 -1 45 45" version="1.1"><g id="surface1"><path class="actorClass" name="participantshape" d="M 38.410156 29.089844 C 35.960938 26.640625 33.042969 24.824219 29.859375 23.726562 C 33.269531 21.378906 35.507812 17.449219 35.507812 13.007812 C 35.507812 5.835938 29.671875 0 22.5 0 C 15.328125 0 9.492188 5.835938 9.492188 13.007812 C 9.492188 17.449219 11.730469 21.378906 15.140625 23.726562 C 11.957031 24.824219 9.039062 26.640625 6.589844 29.089844 C 2.339844 33.339844 0 38.988281 0 45 L 3.515625 45 C 3.515625 34.53125 12.03125 26.015625 22.5 26.015625 C 32.96875 26.015625 41.484375 34.53125 41.484375 45 L 45 45 C 45 38.988281 42.660156 33.339844 38.410156 29.089844 Z M 22.5 22.5 C 17.265625 22.5 13.007812 18.242188 13.007812 13.007812 C 13.007812 7.773438 17.265625 3.515625 22.5 3.515625 C 27.734375 3.515625 31.992188 7.773438 31.992188 13.007812 C 31.992188 18.242188 27.734375 22.5 22.5 22.5 Z M 22.5 22.5 "></path></g></svg><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="31.9102" x="5" y="441.917">User</text><rect height="30.2969" rx="2.5" ry="2.5" width="91.7998" x="107.1461" y="87.2969"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="77.7998" x="114.1461" y="107.292">User Agent</text><rect height="30.2969" rx="2.5" ry="2.5" width="91.7998" x="107.1461" y="428.9219"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="77.7998" x="114.1461" y="448.917">User Agent</text><rect height="30.2969" rx="2.5" ry="2.5" width="54.5303" x="316.1775" y="87.2969"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="40.5303" x="323.1775" y="107.292">Client</text><rect height="30.2969" rx="2.5" ry="2.5" width="54.5303" x="316.1775" y="428.9219"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="40.5303" x="323.1775" y="448.917">Client</text><rect height="30.2969" rx="2.5" ry="2.5" width="89.2773" x="393.2951" y="87.2969"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="75.2773" x="400.2951" y="107.292">Auth Login</text><rect height="30.2969" rx="2.5" ry="2.5" width="89.2773" x="393.2951" y="428.9219"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="75.2773" x="400.2951" y="448.917">Auth Login</text><rect height="30.2969" rx="2.5" ry="2.5" width="97.1729" x="646.1277" y="87.2969"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="83.1729" x="653.1277" y="107.292">Auth Server</text><rect height="30.2969" rx="2.5" ry="2.5" width="97.1729" x="646.1277" y="428.9219"></rect><text font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="83.1729" x="653.1277" y="448.917">Auth Server</text><rect height="262.1953" width="10" x="148.046" y="149.7266"></rect><rect height="87.3984" width="10" x="338.4426" y="295.3906"></rect><rect height="87.3984" width="10" x="432.9338" y="178.8594"></rect><rect height="145.6641" width="10" x="689.7142" y="207.9922"></rect><polygon points="136.046,145.7266,146.046,149.7266,136.046,153.7266,140.046,149.7266"></polygon><line x1="23.9551" x2="142.046" y1="149.7266" y2="149.7266"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="30.9551" y="144.6606" class="labelText">1</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="65.0001" x="44.0005" y="144.6606">访问客户端</text><polygon points="420.9338,174.8594,430.9338,178.8594,420.9338,182.8594,424.9338,178.8594"></polygon><line x1="158.046" x2="426.9338" y1="178.8594" y2="178.8594"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="165.046" y="173.7935" class="labelText">2</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="242.8424" x="178.0914" y="173.7935">重定向到授权页面+clientId+redirectUrl</text><polygon points="677.7142,203.9922,687.7142,207.9922,677.7142,211.9922,681.7142,207.9922"></polygon><line x1="442.9338" x2="683.7142" y1="207.9922" y2="207.9922"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="449.9338" y="202.9263" class="labelText">3</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="214.7349" x="462.9792" y="202.9263">用户名+密码+clientId+redirectUrl</text><polygon points="453.9338,233.125,443.9338,237.125,453.9338,241.125,449.9338,237.125"></polygon><line x1="447.9338" x2="688.7142" y1="237.125" y2="237.125"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="459.9338" y="232.0591" class="labelText">4</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="65.0001" x="472.9792" y="232.0591">返回授权码</text><polygon points="169.046,262.2578,159.046,266.2578,169.046,270.2578,165.046,266.2578"></polygon><line x1="163.046" x2="436.9338" y1="266.2578" y2="266.2578"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="175.046" y="261.1919" class="labelText">5</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="202.5094" x="188.0914" y="261.1919">重定向到redirectUrl+授权码code</text><polygon points="326.4426,291.3906,336.4426,295.3906,326.4426,299.3906,330.4426,295.3906"></polygon><line x1="158.046" x2="332.4426" y1="295.3906" y2="295.3906"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="165.046" y="290.3247" class="labelText">6</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="148.3512" x="178.0914" y="290.3247">使用授权码code换取令牌</text><polygon points="677.7142,320.5234,687.7142,324.5234,677.7142,328.5234,681.7142,324.5234"></polygon><line x1="348.4426" x2="683.7142" y1="324.5234" y2="324.5234"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="355.4426" y="319.4575" class="labelText">7</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="217.4707" x="368.488" y="319.4575">授权码code+clientId+clientSecret</text><polygon points="359.4426,349.6563,349.4426,353.6563,359.4426,357.6563,355.4426,353.6563"></polygon><line x1="353.4426" x2="693.7142" y1="353.6563" y2="353.6563"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="365.4426" y="348.5903" class="labelText">8</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="258.2926" x="378.488" y="348.5903">颁发访问令牌accessToken+refreshToken</text><polygon points="169.046,378.7891,159.046,382.7891,169.046,386.7891,165.046,382.7891"></polygon><line x1="163.046" x2="342.4426" y1="382.7891" y2="382.7891"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="9.0454" x="175.046" y="377.7231" class="labelText">9</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="117.0001" x="188.0914" y="377.7231">返回访问和刷新令牌</text><polygon points="34.9551,407.9219,24.9551,411.9219,34.9551,415.9219,30.9551,411.9219"></polygon><line x1="28.9551" x2="152.046" y1="411.9219" y2="411.9219"></line><text font-family="sans-serif" font-size="13" font-weight="bold" lengthAdjust="spacing" textLength="18.0908" x="40.9551" y="406.856" class="labelText">10</text><text font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="78.0001" x="63.0459" y="406.856">令牌颁发完成</text><!--SRC=[XLHHQnD147w_Nx7qDHmqKK7p44WLGH0i9Fq1wzuaMPBkrhszL7mhWaPCJ5fA1QLYWmy5uTeNAQAIFvEzDF_2lTtBtKPQVBcTPUQR-RwPkSMHv2pQVGu2bJrFKja3z0n7ibEP__gsU3TSJW_K-73-yttoOP2SlbvyFKZEpzJliUTX8hb0Ew56uX15-lJsi92Kq3tC9DemddeRcDnO1PZRUjJ37br546Ekk-kQ2NhAsvIPa5vgtH3H0D47OK92OsfwkJ0lBUtNSWBLwtYs_1YhU9eChXRVBr8bj8ybE1npow-PcjNbsv6AFwd9eHfSsbOiptuiJwSbo_f9K18GK054xeXUaT4IDb-_PgbL5yVdkh2QZ4hguerkw7zIP8BiuQ_uNC-ErUSl-L2o4gvAbx6Jk9HZ20_02-0Vhevoowiw_pDBARkmP3HLunEx8GNJR83soEMk0GkjrcW04I0BSLb9ktLsJhOA9WJ2iCcxm7IRMWB2Zhcu0h9EPVNN9cwwbStzQAW6Lyd9PKRVIP09A5hWrynELDr_GSN3P31nOSNQU66NChsUU1SrEZZW-vIrKGltGa0UAdoCIsXn9aFw2b2vWXpOTh7RspW8KZpQl2NWiKxHC2dkkn7rzCUypIS4tozyTrp2y282Hk0HxTC0d0HtaPjXYxzS4IlVGpVWc-aRKg0Vk37cVTdYGkTlTYZfCZrJL5dhZU0H2mWNJByUbSs_]--></g></svg>
