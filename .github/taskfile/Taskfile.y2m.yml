version: "3"

vars:
  DocsTool: github.com/xbpk3t/docs-alfred/docs@main

tasks:
  default:
    cmds:
      - go install {{.DocsTool}}
      - docs --config .github/workspace/docs.yml
    desc: 默认任务，安装依赖并生成所有文件


  clear:
    desc: 用来清除该task生成的文件
    cmds:
      - echo "Clear Generated Files (yaml, md, json, ...)"
      - for:
          - web/data
          - web/public/gh.yml
        cmd: rm -rf {{ .ITEM }}
      - echo "Done!"
