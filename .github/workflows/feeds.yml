name: Me<PERSON> Feeds and Send Mail

on:
  schedule:
    - cron: "0 22 * * *" # 每周六晚上10点（北京时间周日早上6点）
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to GitHub Pages
    runs-on: ubuntu-latest
    steps:
      - uses: szenius/set-timezone@v2.0
        with:
          timezoneLinux: "Asia/Shanghai"

      - uses: actions/checkout@main
      - uses: actions/setup-go@main
      - name: Install Task
        uses: arduino/setup-task@main
        with:
          version: 3.x
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Run Taskfile Feeds
        run: task feeds
