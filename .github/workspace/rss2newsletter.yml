---

feed:
  timeout: 30 # 最多retry 6次，所以设置为30（第一次retry 5s，则第6次 = 5 * 2^5 = 160s，合计315s）
  maxTries: 5 # 最大retry次数
  feedLimit: 30 #

resend:
  token: re_TN7uofYP_AM49MqLK1SA5ii5exCqBWafY
  mailTo: [<EMAIL>]


newsletter:
  schedule: daily # eg. weekly(latest 7 days), daily(feeds of the day).
  isHideAuthorInTitle: false # default: true

dashboard:
  isShowFetchFailedFeeds: true # 是否展示所有拉取失败的feed。用来分type获取所有 fetch failed 的数量。注意是4列：type、feed、feed url、error
  isShowFeedDetail: true # 是否展示所有feed的更新频率、上次更新时间。展示 所有feed的更新频率、上次更新时间。一共是4列：type

env:
  debug: false # debug模式下，直接生成HTML，而非发送邮件



feeds:
  - type: coding
    record:
      - 【2025-04-03】移除了【hitzhangjie】、【likakuli】、【sunsky303】、【有疑说】、【三点水 lotabout】（这个账号是真心不错（所有图表都是svg，并且知识梳理很清晰），也能看到绝对是原创号，但是更新略慢）、【thiscute.world】、【smallyu的博客】、【icepro`s blog】（没啥干货）、【Changelog News podcast】、
      - 【2025-04-03】移除【游艺Geek】、【小生凡一】、【程序猿有风】、【苏三说技术】、【铁铁37爱编程】、【Low Byte Productions】、【Jacob Sorber】、【桃僧】（rust、leveldb源码解析，但是断更了）、【happyyang的百草园】（基本上EOL了）、【macrozheng】、【木鸟杂记】（真的还不错，但是停更了）、【GOLANG-ROADMAP】（基本上就是把xxx视频化了，毫无意义，不如直接看文档，更清晰）、【戌米的论文笔记】（各种DB相关内核分析和讲解，非常不错。但是EOL了）、【绿导师原谅你了（蒋炎岩）】、【ShusenWang】（主要是推荐系统+搜索引擎。停更了）、【Sli97】（游戏开发类。也停更了）、【今天不coding】、【NotOnlySuccess】、【河北王校长】（最近讲淘宝架构的系列不错，可以反复看看，前面的视频就没啥意思了。对理解电商架构设计有帮助。也停更了）、【王木头学科学】、【SelectDB】（基于Doris实现的SelectDB，官方号）、【NineData】、【yuppt】网安相关、【BRIZER】、【吕秀军Mikael】（gozero-looklook视频教程）、【Coding with Lewis】（真不行，内容很一般）、【crow】（停更了）、【fasterthanlime】rust、【Pixeled】停更了
      - 【2025-04-04】移除youtube博主【Your Average Tech Bro】（类似【Joma Tech】风格，内容较轻，视频时间通常10min左右，其实是泛科技，看着不累，没干货，但是能补充信息）、【ThePrimeTime】、【3Blue1Brown】（广受好评的泛科技博主了，我也跟风关注了，但是其实我并不喜欢看）
      - 【2025-04-04】移除【v2ex xna】
      - 【2025-04-05】移除了bz的【勇敢的心bbk】（之前查找远程工作相关资料看到的，但是感觉没啥干货）、【程序员北边】（典型的技术营销号）、【极海Channel】、【Java陆总监】、【程序员路人】（技术营销号）
      - 【2025-04-05】移除了youtube的【SavvyNik】、【Oglo The Nerd】、【01Coder】、【Code Therapy】、【Be A Better Dev】、【Branch Education】、【Spanning Tree】、【Fireship】、【Dreams of Autonomy】、【Dreams of Code】、【NealWuProgramming】、【Anton Putra】
      - 【2025-01-12】移除掉订阅的几个gh topics的feed，比如
      - 【2025-02-03】移除【HackerNews Daily】 # https://github.com/headllines/hackernews-daily
      - 【2025-04-03】移除【sugarat的周刊】，质量一般，更新也慢
      - 【2025-04-03】移除【Brandur's Articles】、【Martin Fowler】、【Antirez Blog】，更新慢，质量差
      - 【2025-04-06】移除【[buttondown] Hacker Newsletter】
      - 【2025-04-06】移除【唐巧的博客】 # 猿辅导的核心开发，作为产品技术负责人孵化了 小猿搜题 和 小猿口算。"10 多年工作下来，技术、产品、供应链、运营、市场等工作都有涉及。" 挺牛逼的
      - 【2025-04-06】移除bz的【网工老蔚】
      - 【2025-04-06】移除bz的【王帅真】
      - 【2025-04-06】移除bz的【Koala聊开源】 # 也是介绍开源项目的月刊，但是视频。这个效率就不如文本。
      - 【2025-04-06】移除youtube【轩辕的编程宇宙】
      - 【2025-04-06】移除bz【IT老炮-仁科】
      - 【2025-04-06】移除【美团技术团队】
      - 【2025-04-07】移除【TLDR Tech】 # 用ktn订阅的tldr.tech
      - 【2025-05-29】移除bz【wharton0】 # 分享编程相关内容，包括语言应用、算法交易平台重构等，涉及面广。
    urls:
      - feed: https://tech.qimao.com/rss
        url: https://tech.qimao.com/
        des: 【七猫技术团队】 # golang相关技术分享很不错、很实用


      - url: https://space.bilibili.com/162183/video
        feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/162183
        score: 7
        des: 【原子能】 # 认知还不错，技术观点类，有点意思

      - url: https://space.bilibili.com/1324259795/video
        feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/1324259795
        score: 8
        des: 【硬核课堂】 # 干货很多

      - feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/359351574
        url: https://space.bilibili.com/359351574/video
        score: 5
        des: 【IT老齐】 # 偏后端架构，java技术栈

      - feed: https://hellogithub.com/rss
        url: https://hellogithub.com/periodical
        score: 9
        des: 【HelloGitHub 月刊】 # 内容非常好，但是rss的updateTime有问题

      - feed: https://osguider.com/blog/index.xml
        url: https://osguider.com/blog/
        score: 9
        des: 【开源服务指南】 # OSS项目推荐

      - feed: https://wiki.eryajf.net/rss.xml
        url: https://wiki.eryajf.net/learning-weekly/
        score: 5
        des: 【学习周刊 | 二丫讲梵】 # 也是周刊，一些OSS推荐

      - feed: https://weekly.howie6879.com/rss/rss.xml
        url: https://weekly.howie6879.com/
        des: 【howie6879的周刊】





  - type: golang
    record:
      - 【2025-04-06】移除bz的【架构师-刘志勇】、【程序员鸡翅】（技术营销号）、【我不是匠人】（cpp程序员，）
    urls:
      - feed: https://go.libhunt.com/newsletter/feed
        url: https://go.libhunt.com/newsletter/archive
        score: 7
        des: 【Awesome Go Weekly】

      - feed: https://golangweekly.com/rss
        url: https://golangweekly.com/issues
        score: 8
        des: 【Golang Weekly】

      - feed: https://tonybai.com/feed
        url: https://tonybai.com/articles/
        des: 【tonybai】 # 基本上是golang相关内容，尤其是golang changelog相关的blog，非常详细

      - feed: https://gopherdaily.tonybai.com/feed
        url: https://image.tonybai.com/gopherdaily/ # [bigwhite/gopherdaily: the archive of gopher daily articles](https://github.com/bigwhite/gopherdaily?tab=readme-ov-file)
        des: 【Gopher日报】 # Gopher日报，每日golang相关消息

      - feed: https://colobu.com/atom.xml
        url: https://colobu.com/
        des: 【晁岳攀（鸟窝）】

      - feed: https://huizhou92.com/zh-cn/index.xml
        url: https://huizhou92.com/zh-cn/
        des: 【huizhou92】 # golang相关

      - url: https://www.youtube.com/@talkgo_night/videos
        feed: https://www.youtube.com/feeds/videos.xml?channel_id=UCZwrjDu5Rf6O_CX2CVx7n8Q
        des: 【TalkGo】




  - type: DB
    urls:
      - feed: http://mysql.taobao.org//monthly/feed.xml
        url: http://mysql.taobao.org//monthly/
        des: 【数据库内核月报】

      - feed: https://postgresweekly.com/rss
        url: https://postgresweekly.com/issues
        score: 8
        des: 【Postgres Weekly】


      - feed: https://pigsty.cc/blog/index.xml
        url: https://pigsty.cc/blog/
        des: 【pigsty】 # pigsty作者，pgsql吹


      - feed: https://rsshub.shuaizheng.org/dbaplus
        url: https://dbaplus.cn
        topic: 【DBAPlus】


  - type: Arch
    record:
      - 【2025-04-06】移除【hhwyt 黄金架构师】 # 没啥意思
    urls:
      - feed: https://blog.fleeto.us/rss.xml
        url: https://blog.fleeto.us/
        des: 【伪架构师】

      - feed: https://www.chenshaowen.com/atom.xml
        url: https://www.chenshaowen.com/
        des: 【陈少文】


      - url: https://www.youtube.com/@interviewpen/videos
        feed: https://www.youtube.com/feeds/videos.xml?channel_id=UC-4nsAH5j9AIhv5tHoQSP9g
        des: 【Interview Pen】 # 架构设计方面


      - url: https://www.youtube.com/@ByteByteGo/videos
        feed: https://www.youtube.com/feeds/videos.xml?channel_id=UCZgt6AzoyjslHTC9dz0UoTw
        des: 【ByteByteGo】 # 有点浅了，但是还行吧

      - feed: https://www.cnblogs.com/kubesphere/rss
        des: 【KubeSphere 云原生周刊】



  # TODO [三十的前端课投稿视频-三十的前端课视频分享-哔哩哔哩视频](https://space.bilibili.com/2114295304/upload/video)
  - type: ts
    record:
      - 【2025-04-03】移除了【前端圈】、【Josh tried coding】、【Theo - t3․gg】、
      - 【2025-04-04】移除【shanyue 山月前端周刊】，不仅EOL了，并且这个网站的rss有bug（updateTime没设置）
      - 【2025-04-06】移除【Moonvy 设计素材周刊】他这个rss有问题，并非只有设计素材周刊内容，还有。并且本身内容也不丰富。
      - 【2025-04-06】移除【zishu.me】 # 前端相关的周刊
      - 【2025-05-29】移除bz【三十的前端课】
    urls:
      - feed: https://kill-the-newsletter.com/feeds/qplt4vehrg0thsd3.xml
        des: 【前端食堂】

      - feed: https://cprss.s3.amazonaws.com/javascriptweekly.com.xml
        des: 【JavaScript Weekly】

      - feed: https://cprss.s3.amazonaws.com/frontendfoc.us.xml
        url: https://frontendfoc.us/
        des: 【Frontend Focus】

      - feed: https://weekly.tw93.fun/rss.xml
        url: https://weekly.tw93.fun/
        score: 5
        des: 【tw93 潮流周刊】 # 一个前端coder的blog

      - feed: https://www.ftium4.com/rss.xml
        url: https://www.ftium4.com/
        score: 7
        des: 【ftium4的博客】 # 交互设计相关的weekly

      - feed: https://rsshub.shuaizheng.org/web/articles
        des: 【Web Articles】

      - feed: https://cprss.s3.amazonaws.com/nodeweekly.com.xml
        des: 【Node Weekly】




  # TODO [王木头学科学投稿视频-王木头学科学视频分享-哔哩哔哩视频](https://space.bilibili.com/504715181/upload/video)
  # TODO [绿导师原谅你了投稿视频-绿导师原谅你了视频分享-哔哩哔哩视频](https://space.bilibili.com/202224425/upload/video)
  # TODO [一高数投稿视频-一高数视频分享-哔哩哔哩视频](https://space.bilibili.com/1035929235/upload/video)
  - type: LLM
    urls:
      - feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/355575530
        url: https://space.bilibili.com/355575530/upload/video
        des: 【AI论文小小编】

      - feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/517221395
        url: https://space.bilibili.com/517221395/video
        des: 【ZOMI酱】

      - feed: https://qiankunli.github.io/pages/atom.xml
        url: https://qiankunli.github.io
        des: 【李乾坤】 # 最近的全是LLM相关的，不看了。但是这老哥的输出频率不错，可以长期看看。

      - feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/3546620985608836
        url: https://space.bilibili.com/3546620985608836/upload/video
        des: 【StatQuest】

      - feed: https://daily-blog.chlinlearn.top/feed
        url: https://daily-blog.chlinlearn.top/
        des: 【chlinlearn的博客】





  - type: IndieHacker
    record:
      - 【2025-03-24】移除【独立开发变现周刊 ezindie.com】已经EOL了
      - 【2025-03-24】移除【独立开发者出海周刊 gapis.money】也已经EOL了
      - 【2025-04-03】移除【IndieHackers Organic】
      - 【2025-04-03】移除【IndieHackers Featured】
      - 【2025-04-03】移除【Dribbble】暂时注释，每天的feed太多了，dribbble更适用于搜索+浏览。
    urls:
      - feed: https://www.91wink.com/index.php/feed
        url: https://www.91wink.com/
        des: 【独立开发前线】

      - feed: https://decohack.com/category/decohack-weekly/feed/
        url: https://decohack.com/category/decohack-weekly/
        des: 【DecoHack周刊//独立产品灵感周刊】

      - feed: https://decohack.com/category/producthunt/feed/
        url: https://decohack.com/category/producthunt/
        des: 【DecoHack周刊//PH今日热榜】




  - type: News
    record:
      - 【2025-04-03】移除掉【Investing 财经】，因为基本上都是一些交易信息，减持套现增持增发定增之类的，没啥意思
      - 【2025-04-05】移除了youtube【BernieSanders】、【AOC】、【HasanAbi】、【Atrioc】。之前想练习英语听力，顺便
      - 【2025-04-03】移除【安森垚】、【欣哥的超级补习班】（视频更新很慢，内容也没啥意思，天天做动画，属于是“好钢用在刀背上”了。更正评价：纯sb。）、【杜茹慧】（女性搞键政、搞历史，很容易情绪化，很容易偏激，这位同样如此。看多了真心没啥意思，不输出观点，只输出情绪。当然，也输出一些人尽皆知的“历史”。）、【文不醜】（三国类up，喜欢研究一些很细节的东西，但是我对这种细节其实并没有那么关心。我看历史是需要记忆的，但是这些细节我真心懒得记，所以就不想看了。）、【安州牧】（历史爱好者水平，更适合小白，太浅了。之前看“五胡十六国系列”时关注的，稀里糊涂也跟着看，但是本身很简单的历史被他搞的很复杂，。都是按照“可信史料”照本宣科，安州牧就不如稚嫩的魔法师。当然，他的优势是建模和动画，但是我压根不看重这个。我很喜欢他的几个“访古vlog”，因为我也一直想做类似的事情，但是直到现在还没成行。）、【平兄Plus】可能是bz最好的三国，我是看他最早的几期“党锢之祸”的视频关注的，用一种代入。
      - 【倍儿兔】目前有 太平天国、秦汉、先秦 相关的
      - 【沧浪知舟】十六国和先秦史
      - 【草说木言】使用的史料相对可信，且用词精准。兼职做视频的，更细真的很慢。可以等更新完之后，一次性看完。反正他的视频我也是反复看的。
      - 【御史房】
      - 【麟台令】
      - 【术澄】
      - 【失踪棱镜】不如直接看他的公众号，每期视频都有文字稿，虽然一样更新极慢。
      - 【小牧phenix】
      - 【大佐/尖峰苏打cfz】
      - 【多明海】没干货。当然，标题选的总是不错。
      - 【古必的精读书柜】
      - 【包刚升】讲解政治学经典著作。但是“味太冲了”，典型的被洗脑洗傻了的那种。
      - 【吸奇侠】文案确实不错，但是
      - 【2025-04-04】移除【热带榕树】（作为一个"观点输出类"博主，输出效率有点低，观点也不够鲜明，很墨迹。）、【大门ZRR】（网文博主，内容还行，但是更新太慢了，所以清理掉。现在更是直接停更了。）、【橙飞】、【食贫道】（其实没啥干货，做“充电视频”之后比之前更火了，但是我反而更喜欢之前的探店之类的视频，更实用吧。他“充电视频”想输出的那些东西，对我来说没有产生知识增量。）、【一横zzz】（没啥意思，也停更了）、【荷包兔时间】（同样停更了）
      - 【2025-04-06】移除【kris】 # 曾经是我最喜欢的up主（没有之一），但是最近半年质量急剧下降。看了几次直播，也发觉并没有我预期/想象的那么"有意思"，不知道是什么问题。
      - 【2025-04-06】移除【胡风南渡】 # 胡阁老最近两期"心学"和"明朝君主离线"的视频不错。但是更新太慢了，年更博主了属于是，跟【无聊的无桥】一样。质量高但是年更。
      - 【2025-04-06】移除bz【罗兰朵】 #  真实有趣又有干货
      - 【2025-04-06】移除bz【杰克小兔】 # 感觉比【康哟喂】有意思。分享生活体验、旅游经历、社会现象观察等内容，丰富多样。【2025-04-06】移除了
      - 【2025-04-06】移除bz【差评君】 # 相较于其他测评类博主要geek很多，颇有一些确实比较新的行业动态分析等内容。但是最近这几个月广告太多了。
      - 【2025-04-19】移除bz【麻薯波比】 # 写个综评吧，算是bz在搞笑风格的时事闲聊类up中做的最好且比较火的了。之前因为他的"大美王朝"而关注的。那么为什么取关了呢？心境变了，键政就是精神撸管。宏大叙事去NM吧。“工人没有祖国”。之后我仍然会以周期//历史的角度来看当下，但是绝不会再去看什么“时事类博主”。
      - 【2025-04-20】移除bz【双尾彗星】 # 这人嘴里没半句实话。自诩有知识系统，其实压根没有，它实际上就是个“二道贩子”，看了大量的书，但是没有自己的思考。
      - 【2025-04-21】移除bz【瓜熟迪落拉】 # 长期追踪美国政治，能补充很多信息差，带来耳目一新的观点，很不错。【2025-04-21】x
      - 【2025-04-21】移除bz【小王Albert】 # 2022年初回归之后做了"美帝霸权三头犬"、"东盟十国"、"中亚五国"、"中东"、"马格里布"系列。吐槽一下，看了几个王骁在观网时期的视频，应该说，单飞之后的视频质量确实不如之前的那些。无非是三段论，介绍某个国家，就是古代史、近代史、近年历史，最后再说说跟中国的关系。太平淡了，其实应该从当前状况，再往前推，让读者带着问题往下看更好。
      - 【2025-04-21】移除bz【文化纵横】 # 搜了一下摘录的观网的网页，相当部分都是文化纵横的，所以就把观网从ws.yml删掉了。并且观网的新闻真不太行。就当zhihu专栏用呗。
      - 【2025-04-21】移除【经济观察报】 # 之前在虎嗅订阅的经济观察报feed。但是实话说也没啥意思，两个都没啥意思。
      - 【2025-02-03】【36kr】也是一些交易相关信息，没啥意思。注释掉。
      - 【2025-02-03】【人人都是产品经理】36kr平台上【人人都是产品经理】的投稿，都是一些摘录的精品文章，质量相当不错，很有启发。【2025-03-12】不更新了
      - 【2025-04-03】移除掉【slashdot】类似 hackernews 那种泛科技论坛。但是每天feed太多，并且绝大部分我都不关心。
      - 【2025-04-03】移除掉【scitechdaily】，都是生化环材相关，并且feed过多，不看了
      - 【2025-04-06】移除tg群组【aboutrss】 #  [AboutRSS/ALL-about-RSS: A list of RSS related stuff: tools, services, communities and tutorials, etc.](https://github.com/AboutRSS/ALL-about-RSS)
      - 【2025-04-08】移除【TechCrunch】 # feed一直返回429
      - 【2025-04-21】移除【Noema Magazine】、【The Verge】、【SuperTechFans（HackerNews）】
      - 【2025-04-21】移除【极客公园（内容很不错，都是深度报道）】、【少数派】、【雷峰网】、【蓝点网】
      - 【2025-05-29】移除bz【傅正】、【杜预全时空-真实财经（这哥们水平真心不错，但是更多是基本面、房地产等行业预判，而非经济学框架。好但是对我没用。）】
    urls:
      - url: https://space.bilibili.com/1596926736/video
        feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/1596926736
        des: 【河畔的伯爵】 # 就是zhihu的【莱茵行宫伯爵】，分享历史文化相关内容，视角独特。

      - url: https://space.bilibili.com/3959068/video
        feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/3959068
        des: 【战舰波将金】 # 分享历史相关内容，如新中国历史、苏联历史等，具有一定的深度和独特视角。

      - url: https://space.bilibili.com/97150452
        feed: https://rss2.lucc.dev/rss/bilibili/user/dynamic/97150452
        des: 【跑不快的吴二】 # 之前关注了可能至少30多个跑步博主，后来就只看看吴二了，偶尔看看宁哥。现在买鞋都买的是两三年前的、口碑本身不错的老款了，也就没必要看这些跑鞋博主了。

      - url: https://www.ruanyifeng.com/blog/
        feed: https://feeds.feedburner.com/ruanyifeng
        des: 【阮一峰】 # 阮老师这个周刊做的真心不错，很能拓宽眼界。看了不少周刊、月刊了，阮老师的主打一个量大管饱，质量还不差。

      - feed: https://wenfeixiang.com/feed/
        url: https://wenfeixiang.com/
        des: 【飞翔的黑板报】 # IT桔子老板的blog，关注创业、投资、孩子成长

      - feed: https://rsshub.shuaizheng.org/latepost
        url: https://www.latepost.com/
        des: 【晚点 LATEPOST】

      - feed: https://pt.plus/rss/
        url: https://pt.plus/
        des: 【Platform Thinking】 # 付费$100/year，免费用户每个月只能看一篇，看看标题得了

      - feed: https://meta.appinn.net/latest.rss
        url: https://meta.appinn.net/latest
        des: 【小众软件】 # 把macos版、ios版、chrome版的rss都移除了，没人气

      - feed: https://linux.do/c/develop/4.rss
        url: https://linux.do/c/develop/4
        des: 【开发调优 - LINUX DO】




#  - type: asmr
#    record:
#      - 【2025-04-03】移除了之前所有已注释的youtube asmr博主。基本上都是【RoseASMR】
#      - 【2025-04-04】移除部分已经长时间停更的youtube博主。比如【中文ASMR】、【青橙asmr放松小屋】、【顾业】、【SJ RELAX TV】、【Lyna Hair ASMR】、【esteASMR】、【BoneCrackers】、【川音ASMR】、【easy pillow 易枕枕】、【Irene】、【jinnytty 企鹅妹】
#      - 【2025-04-04】移除asmrgay的【雾心宝贝】、【小元】、【Rainnight雨】、【大宝】、【桥桥超温柔】、【Flora圆圆】、【婉儿】、【奶油米子】、【是喵宝呀】、【芝恩㱏】、【bitchery】、【凌晨三点钟】、【猫耳sugar】、【奈兔sama】、【杭白芷】（是真的顶）、【温柔的尔耳】、【痴痴的小瑶儿】、【奶乎乎的花妖】、【林晓蜜】、【桃夭】、【Rain】、【Miss哈尼】、【桑九学姐】、【闪亮银】、【蛇蛇助眠】、【池喵】、【无名音声社】、【林暮色】、【说人话的吊】、【凌晨三点钟】、【暴躁啊御】、【林暮色】、【喵西早点睡】、【离二烟烟】（是真的顶）
#      - 【2025-04-05】移除了 youtube【筑雨未晴】
#      - 【2025-04-22】移除了 youtube【nara】、【Einfach ASMR】、【DudeThatsWholesome】、【AnniePantsASMR】、【MamaYunyaa】、【alex talks asmr】、【EinAudios】、【JuriCharmAudio】、【DarkDreamsASMR】、【Lune VA】、【CharlottesAudios】、【MethodSwitch】（喜欢这个mz的声音）、【AudioEve】（纯骚鸡，twitter上有很多福利）、【Koi】（声音很娇，神似twitter上的【】）、【KittyKi】、【催眠红茶】。以及bz的【子不言吾不语s】、【柳柳haruyi】、【白泉鸠】
