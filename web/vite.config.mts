import { resolve } from 'node:path';

import { defineConfig } from '@vben/vite-config';

import Vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import Markdown from 'unplugin-vue-markdown/vite';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      build: {
        chunkSizeWarningLimit: 1000,
        minify: 'terser',
        rollupOptions: {
          output: {
            manualChunks: {
              markdown: ['markdown-it'],
              ui: ['naive-ui', '@vueuse/core'],
              vendor: ['vue', 'vue-router', 'pinia'],
            },
          },
        },
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
          },
        },
      },
      plugins: [
        Vue,
        Markdown({
          // 启用 markdown-it 的默认配置
          markdownItOptions: {
            html: true,
            linkify: true,
            typographer: true,
          },
          // 添加 wrapper class
          wrapperClasses: 'markdown-content',
        }),
        // 确保 Components 插件在 Markdown 插件之后
        Components({
          // 允许自动导入 markdown 组件
          extensions: ['vue', 'md'],
          // 包含 markdown 文件
          include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
        }),
      ],
      resolve: {
        alias: [
          {
            find: '#',
            replacement: resolve(__dirname, './src'),
          },
          {
            find: '@data',
            replacement: resolve(__dirname, './data'),
          },
        ],
      },
    },
  };
});
