<script lang="ts" setup>
import type { ConfigRepo, Repository } from '#/types/data';

import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getScoreClassName } from '#/utils/score';

import { Page } from '@vben/common-ui';

import { DocumentText as DocIcon } from '@vicons/ionicons5';
import { NButton, NCard, NIcon, NSpace, NTag, NText } from 'naive-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getGithubListApi } from '#/api/adapters/github';
import CardDrawer from '#/components/Drawer/CardDrawer.vue';
import QAList from '#/components/QA/QAList.vue';
import TopicsList from '#/components/QA/TopicsList.vue';
import GithubTableSimple from '#/components/Table/GithubTableSimple.vue';
import { useMobile } from '#/hooks/use-mobile';

// 表格数据
const repositories = ref<ConfigRepo[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(50);

// 抽屉相关
const drawerVisible = ref(false);
const currentType = ref<ConfigRepo | Repository | null>(null);
const nestedDrawerVisible = ref(false);
const nestedType = ref<ConfigRepo | Repository | null>(null);

// 获取路由实例和查询参数
const route = useRoute();

// 使用移动端检测 hook
const { isMobileWidth } = useMobile();

// 表格配置
const gridOptions = {
  border: true,
  columns: [
    {
      align: 'left',
      field: 'type',
      slots: { default: 'type' },
      title: '类型',
      visible: !isMobileWidth.value,
      width: 170,
    },
    {
      align: 'left',
      className: ({ row }) => {
        const { score } = row;
        return {
          [getScoreClassName(score)]: true,
        };
      },
      field: 'url',
      minWidth: 60,
      slots: { default: 'url' },
      title: '仓库',
    },
    {
      align: 'left',
      field: 'score',
      title: 'score',
    },
    {
      align: 'left',
      field: 'des',
      minWidth: 400,
      showOverflow: true,
      title: '描述',
      visible: !isMobileWidth.value,
    },
  ],
  pagerConfig: {
    currentPage: currentPage.value,
    enabled: true,
    pageSize: pageSize.value,
    pageSizes: [20, 50, 100],
    total: total.value,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        // 处理查询参数
        const searchParams = {
          ...formValues,
          page: page.currentPage,
          pageSize: page.pageSize,
        };

        // 从路由参数获取 tag 和 type 值
        const routeTag = route.query.tag as string;
        const routeType = route.query.type as string;

        if (routeTag) {
          searchParams.tag = routeTag;
        }

        if (routeType) {
          searchParams.type = routeType;
        }

        // 处理合并的tagType字段
        if (searchParams.tagType) {
          const [tag, type] = searchParams.tagType.split('#');
          searchParams.tag = tag;
          searchParams.type = type;
          delete searchParams.tagType;
        }

        const result = await getGithubListApi(searchParams);
        repositories.value = result.items;
        total.value = result.total;
        return result;
      },
    },
    props: {
      result: 'items',
      total: 'total',
    },
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
};

// 监听移动端状态变化，更新表格列显示
watch(isMobileWidth, (newVal) => {
  gridOptions.columns = gridOptions.columns.map((col) => {
    col.visible = col.field === 'url' ? true : !newVal;
    return col;
  });
});

const gridEvents = {
  cellClick: ({ row }) => {
    if (isMobileWidth.value) {
      showDrawer(row);
    }
  },
  cellDblclick: ({ row }) => {
    showDrawer(row);
  },
};

const [GithubGrid] = useVbenVxeGrid({
  gridEvents,
  gridOptions,
});

// 获取仓库列表
const fetchRepositories = async (
  page: number,
  size: number,
  tagType: null | string = null,
) => {
  const searchParams: Record<string, any> = {
    page,
    pageSize: size,
  };
  // 从路由参数获取 tag 和 type 值
  const routeTag = route.query.tag as string;
  const routeType = route.query.type as string;

  if (routeTag) {
    searchParams.tag = routeTag;
  }

  if (routeType) {
    searchParams.type = routeType;
  }

  // 处理合并的tagType字段
  if (tagType) {
    const [tag, type] = tagType.split('#');
    searchParams.tag = tag;
    searchParams.type = type;
  }

  const result = await getGithubListApi(searchParams);
  repositories.value = result.items;
  total.value = result.total;
};

// 处理分页变化
const handlePageChange = (page: number, size: number) => {
  currentPage.value = page;
  pageSize.value = size;
  fetchRepositories(page, size);
};

// 显示详情抽屉
const showDrawer = (type: ConfigRepo | Repository) => {
  if (drawerVisible.value) {
    // 如果主抽屉已经打开，则打开嵌套抽屉
    nestedType.value = type;
    nestedDrawerVisible.value = true;
  } else {
    // 否则打开主抽屉
    currentType.value = type;
    drawerVisible.value = true;
  }
};

// 关闭嵌套抽屉
const closeNestedDrawer = () => {
  nestedDrawerVisible.value = false;
  nestedType.value = null;
};

// 处理文档链接点击
const handleDocClick = (url?: string) => {
  if (url) {
    window.open(url, '_blank');
  }
};

function getBadges(row: ConfigRepo) {
  if (!row) return [];

  const badges: { type: 'error' | 'info' | 'success' | 'warning' }[] = [];

  if (row.repo?.length) {
    badges.push({ type: 'info' });
  }

  if (row.topics?.length) {
    badges.push({ type: 'error' });
  }

  if (row.using?.qs?.length) {
    badges.push({ type: 'error' });
  }

  return badges;
}

// 监听路由参数变化
watch(
  () => route.query,
  () => {
    fetchRepositories(currentPage.value, pageSize.value);
  },
  { immediate: true },
);

// 初始加载
onMounted(() => {
  fetchRepositories(currentPage.value, pageSize.value);
});
</script>

<template>
  <Page auto-content-height>
    <GithubGrid>
      <template #type="{ row }">
        <NTag size="tiny" type="info" class="leading-none">
          <NText italic>[{{ row.tag }}] #{{ row.type }}</NText>
        </NTag>
      </template>

      <template #url="{ row }">
        <div class="flex items-center gap-2">
          <NButton
            v-if="row.using"
            text
            tag="a"
            :href="row.using.url"
            target="_blank"
            size="small"
          >
            {{ row.using.url.replace('https://github.com/', '') }}
          </NButton>
          <NButton
            v-if="row.using?.doc"
            text
            type="primary"
            @click="handleDocClick(row.using.doc)"
          >
            <NIcon :size="16">
              <DocIcon />
            </NIcon>
          </NButton>
        </div>
      </template>
    </GithubGrid>

    <!-- 主抽屉 -->
    <CardDrawer
      v-model:show="drawerVisible"
      :title="currentType?.type"
      :width="1200"
    >
      <template v-if="currentType">
        <NSpace vertical size="large">
          <!-- 仓库列表 -->
          <NCard
            v-if="'repo' in currentType && currentType.repo?.length"
            title="仓库列表"
            :bordered="false"
          >
            <GithubTableSimple
              :repositories="currentType.repo"
              @view-details="showDrawer"
            />
          </NCard>

          <!-- Using 仓库的详细信息 -->
          <template v-if="'using' in currentType && currentType.using">
            <!-- 子仓库 -->
            <NCard
              v-if="currentType.using.sub?.length"
              title="子仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="currentType.using.sub"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- 相关仓库 -->
            <NCard
              v-if="currentType.using.rel?.length"
              title="相关仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="currentType.using.rel"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- 常用命令 -->
            <NCard
              v-if="currentType.using.cmd?.length"
              title="常用命令"
              :bordered="false"
            >
              <div class="rounded-lg bg-gray-50 p-4">
                <NSpace vertical size="small">
                  <NText
                    v-for="(cmd, index) in currentType.using.cmd"
                    :key="index"
                    class="font-mono text-sm"
                    style="white-space: pre-wrap; padding: 4px"
                  >
                    {{ cmd }}
                  </NText>
                </NSpace>
              </div>
            </NCard>
          </template>

          <!-- Repository 的直接属性 -->
          <template v-if="!('using' in currentType)">
            <!-- 子仓库 -->
            <NCard
              v-if="currentType.sub?.length"
              title="子仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="currentType.sub"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- 相关仓库 -->
            <NCard
              v-if="currentType.rel?.length"
              title="相关仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="currentType.rel"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- Qs -->
            <NCard
              v-if="currentType.qs?.length"
              title="Qs"
              :bordered="false"
            >
              <QAList title="Qs" :items="currentType.qs" />
            </NCard>

            <!-- Topics -->
            <TopicsList
              v-if="currentType.topics?.length"
              title="Topics"
              :items="currentType.topics"
            />
          </template>

          <!-- ConfigRepo 的 Topics -->
          <template v-if="'using' in currentType">
            <TopicsList
              v-if="currentType.topics?.length"
              title="Type Topics"
              :items="currentType.topics"
            />

            <!-- Using 仓库的 Qs 和 Topics -->
            <template v-if="currentType.using">
              <NCard title="Qs" v-if="currentType.using.qs?.length">
                <QAList title="Qs" :items="currentType.using.qs" />
              </NCard>

              <TopicsList
                v-if="currentType.using.topics?.length"
                title="Using Topics"
                :items="currentType.using.topics"
              />
            </template>
          </template>
        </NSpace>
      </template>
    </CardDrawer>

    <!-- 嵌套抽屉 -->
    <CardDrawer
      v-model:show="nestedDrawerVisible"
      :title="nestedType?.type"
      :width="1200"
      @close="closeNestedDrawer"
    >
      <template v-if="nestedType">
        <NSpace vertical size="large">
          <!-- 仓库列表 -->
          <NCard
            v-if="'repo' in nestedType && nestedType.repo?.length"
            title="仓库列表"
            :bordered="false"
          >
            <GithubTableSimple
              :repositories="nestedType.repo"
              @view-details="showDrawer"
            />
          </NCard>

          <!-- Using 仓库的详细信息 -->
          <template v-if="'using' in nestedType && nestedType.using">
            <!-- 子仓库 -->
            <NCard
              v-if="nestedType.using.sub?.length"
              title="子仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="nestedType.using.sub"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- 相关仓库 -->
            <NCard
              v-if="nestedType.using.rel?.length"
              title="相关仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="nestedType.using.rel"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- 常用命令 -->
            <NCard
              v-if="nestedType.using.cmd?.length"
              title="常用命令"
              :bordered="false"
            >
              <div class="rounded-lg bg-gray-50 p-4">
                <NSpace vertical size="small">
                  <NText
                    v-for="(cmd, index) in nestedType.using.cmd"
                    :key="index"
                    class="font-mono text-sm"
                    style="white-space: pre-wrap; padding: 4px"
                  >
                    {{ cmd }}
                  </NText>
                </NSpace>
              </div>
            </NCard>
          </template>

          <!-- Repository 的直接属性 -->
          <template v-if="!('using' in nestedType)">
            <!-- 子仓库 -->
            <NCard
              v-if="nestedType.sub?.length"
              title="子仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="nestedType.sub"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- 相关仓库 -->
            <NCard
              v-if="nestedType.rel?.length"
              title="相关仓库"
              :bordered="false"
            >
              <GithubTableSimple
                :repositories="nestedType.rel"
                @view-details="showDrawer"
              />
            </NCard>

            <!-- Qs -->
            <NCard
              v-if="nestedType.qs?.length"
              title="Qs"
              :bordered="false"
            >
              <QAList title="Qs" :items="nestedType.qs" />
            </NCard>

            <!-- Topics -->
            <TopicsList
              v-if="nestedType.topics?.length"
              title="Topics"
              :items="nestedType.topics"
            />
          </template>

          <!-- ConfigRepo 的 Topics -->
          <template v-if="'using' in nestedType">
            <TopicsList
              v-if="nestedType.topics?.length"
              title="Type Topics"
              :items="nestedType.topics"
            />

            <!-- Using 仓库的 Qs 和 Topics -->
            <template v-if="nestedType.using">
              <NCard title="Qs" v-if="nestedType.using.qs?.length">
                <QAList title="Qs" :items="nestedType.using.qs" />
              </NCard>

              <TopicsList
                v-if="nestedType.using.topics?.length"
                title="Using Topics"
                :items="nestedType.using.topics"
              />
            </template>
          </template>
        </NSpace>
      </template>
    </CardDrawer>
  </Page>
</template>

<style scoped>
.gh-link {
  @apply text-blue-500 hover:text-blue-700;
}

:deep(.drawer-tooltip-container) {
  width: 100%;
  max-width: 100%;
}

:deep(.n-tooltip) {
  max-width: 400px !important;
}

/* Score 样式已移至全局样式文件 */
</style>
