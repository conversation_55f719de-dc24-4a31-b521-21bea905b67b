<script lang="ts" setup>
import { watch, ref, onMounted, onUnmounted } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

const props = defineProps<{
  show: boolean;
  title?: string;
  width?: number;
}>();

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void;
}>();

// 窗口宽度状态
const windowWidth = ref(window.innerWidth);
const isFullScreen = ref(window.innerWidth >= 1600);

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  isFullScreen.value = window.innerWidth >= 1600;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

const [Drawer, drawerApi] = useVbenDrawer({
  onCancel() {
    drawerApi.close();
    handleUpdateShow(false);
  },
  onOpenChange(isOpen: boolean) {
    handleUpdateShow(isOpen);
  },
});

const handleUpdateShow = (value: boolean) => {
  emit('update:show', value);
  if (value) {
    drawerApi.open();
  } else {
    drawerApi.close();
  }
};

watch(
  () => props.show,
  (val) => {
    if (val) {
      drawerApi.open();
    } else {
      drawerApi.close();
    }
  },
);
</script>

<template>
  <Drawer :title="title" :class="isFullScreen ? 'w-[1600px]' : '!w-full'">
    <div class="drawer-wrapper">
      <div class="drawer-content">
        <slot></slot>
      </div>
    </div>
  </Drawer>
</template>

<style>
.drawer-wrapper {
  height: 100%;
  background-color: #f3f4f6;
  padding: 8px;
}

.drawer-content {
  height: 100%;
  overflow-y: auto;
}

.drawer-card {
  background: white;
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.drawer-card:last-child {
  margin-bottom: 0;
}

.drawer-card-title {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 600;
  color: rgb(31 41 55);
  background-color: rgb(249 250 251);
  border-bottom: 1px solid rgb(229 231 235);
}

.drawer-card-content {
  padding: 12px;
  color: rgb(75 85 99);
  background-color: white;
}

/* Utility classes for content layout */
.drawer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 8px;
}

.drawer-flex {
  display: flex;
  gap: 8px;
}

.drawer-flex-col {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
:deep(.markdown-content) {
  @apply text-sm text-gray-600;

  p {
    margin: 0.75em 0;
  }

  ul {
    list-style-type: disc;
    padding-left: 1.5em;
    margin: 0.75em 0;
  }

  ol {
    list-style-type: decimal;
    padding-left: 1.5em;
    margin: 0.75em 0;
  }

  li {
    margin: 0.5em 0;
  }

  a {
    @apply text-blue-500 hover:text-blue-700;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
  }

  pre code {
    display: block;
    padding: 1em;
    overflow-x: auto;
  }
}
a {
  @apply text-blue-500 hover:text-blue-700;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,
    monospace;
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  /*white-space: break-spaces;*/
  border-radius: 6px;
  border: 1px solid var(--borderColor-neutral-muted, var(--color-neutral-muted));
  /*white-space : pre-wrap !important;*/
  /*用来对code block实现auto-wrap*/
  white-space: pre-wrap;
  overflow-wrap: anywhere;
}
</style>
