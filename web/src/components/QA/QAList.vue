<script lang="ts" setup>
import { NCard, NLi, NUl } from "naive-ui";

import { renderMarkdown } from "#/utils/markdown";

interface Props {
  items: string[];
  title?: string;
}

defineProps<Props>();
</script>

<template>
    <NUl class="list-disc space-y-2 pl-4">
      <NLi
        v-for="(item, index) in items"
        :key="index"
        v-html="renderMarkdown(item)"
      />
    </NUl>
</template>

<style scoped></style>
