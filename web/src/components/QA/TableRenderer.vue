<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { DocumentText as DocIcon } from '@vicons/ionicons5';
import { NButton, NIcon } from 'naive-ui';

interface Props {
  data: Record<string, any>[];
}

const props = defineProps<Props>();

// 检测列的数据类型
const getColumnType = (
  data: any[],
  field: string,
): 'boolean' | 'number' | 'string' => {
  for (const row of data) {
    const value = row[field];
    if (value != null) {
      if (typeof value === 'boolean') return 'boolean';
      if (typeof value === 'number') return 'number';
      if (!isNaN(Number(value)) && value !== '') return 'number';
    }
  }
  return 'string';
};

// 计算字符串的显示宽度（考虑中英文混合）
const getStringDisplayWidth = (text: string): number => {
  if (!text || typeof text !== 'string') return 0;

  let width = 0;
  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    // 中文字符、全角字符等宽度为2，英文字符宽度为1
    if (/[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]/.test(char)) {
      width += 2;
    } else {
      width += 1;
    }
  }
  return width;
};

// 计算多行文本中最长行的显示宽度
const getMaxLineDisplayWidth = (text: string): number => {
  if (!text || typeof text !== 'string') return 0;
  const lines = text.split('\n');
  return Math.max(...lines.map(line => getStringDisplayWidth(line)));
};

// 计算列的最佳宽度（基于最长行的显示宽度）
const calculateColumnWidth = (data: any[], field: string, title: string): number => {
  let maxDisplayWidth = getStringDisplayWidth(title); // 从标题显示宽度开始

  // 遍历所有行数据，找到该列中最长的行
  for (const row of data) {
    const value = row[field];
    if (value != null) {
      const strValue = String(value);
      const lineMaxDisplayWidth = getMaxLineDisplayWidth(strValue);
      maxDisplayWidth = Math.max(maxDisplayWidth, lineMaxDisplayWidth);
    }
  }

  // 根据显示宽度估算像素宽度
  // 每个显示宽度单位大约6px，加上padding和边距
  const estimatedWidth = maxDisplayWidth * 6 + 32;

  // 设置最小和最大宽度限制
  return Math.min(Math.max(estimatedWidth, 80), 500);
};

// 处理文档链接点击
const handleDocClick = (url?: string) => {
  if (url) {
    window.open(url, '_blank');
  }
};

// 计算所有可能的列，排除color、url和doc列
const columns = computed(() => {
  if (!props.data?.length) return [];

  // 收集所有可能的列名，但排除特殊列
  const allKeys = new Set<string>();
  props.data.forEach((item) => {
    Object.keys(item)
      .filter((key) => !['color', 'url', 'doc'].includes(key))
      .forEach((key) => allKeys.add(key));
  });

  return [...allKeys].map((key, index) => {
    const columnType = getColumnType(props.data, key);
    const calculatedWidth = calculateColumnWidth(props.data, key, key);

    const column = {
      align: 'left',
      cellStyle: {
        'background-color': 'inherit',
        'text-align': 'left',
        'white-space': 'pre-wrap',
        'word-break': 'break-word',
      },
      field: key,
      width: calculatedWidth,  // 使用计算出的宽度
      minWidth: 80,           // 最小宽度限制
      showOverflow: false,
      sortable: columnType === 'number',
      title: key,
      // 固定第一列
      fixed: index === 0 ? 'left' : undefined,
    };

    // 为布尔类型添加自定义渲染
    if (columnType === 'boolean') {
      column.formatter = ({ cellValue }) => {
        return cellValue ? '✅' : '❌';
      };
      column.cellStyle = ({ cellValue }) => {
        return {
          'background-color': 'inherit',
          color: cellValue ? '#67C23A' : '#F56C6C',
          'text-align': 'center',
          'font-weight': 'bold'
        };
      };
    }

    // 为name列添加特殊处理，如果有url或doc
    if (key === 'name') {
      column.slots = { default: 'name' };
    }

    return column;
  });
});

// 处理数据，添加行样式信息
const processedData = computed(() => {
  let currentColorValue = null;
  return props.data.map((row) => {
    if (row.color) {
      currentColorValue = row.color;
    }
    return {
      ...row,
      _backgroundColor: currentColorValue,
    };
  });
});

const gridOptions: VxeGridProps = {
  border: true,
  columnConfig: {
    adaptiveColumn: false, // 禁用自适应列宽，使用我们自定义的宽度计算
    minWidth: 80,          // 最小宽度限制
    resizable: true,       // 允许用户手动调整列宽
    align: 'left'
  },
  columns: columns.value,
  data: processedData.value,
  pagerConfig: {
    enabled: false,
  },
  rowConfig: {
    height: 'auto',
    isHover: true,
  },
  showOverflow: false,
  size: 'small',
  sortConfig: {
    multiple: true,
    remote: false,
    trigger: 'cell',
  },
  stripe: false,
  cellStyle: ({ row }) => {
    const style: any = {
      'white-space': 'pre-wrap',
      'word-break': 'break-word',
      'text-align': 'left'
    };

    if (row._backgroundColor) {
      style.backgroundColor = row._backgroundColor;
      style.color = '#000000';
    }

    return style;
  }
};

const gridEvents: VxeGridListeners = {
  mounted: ({ $grid }) => {
    if ($grid) {
      $grid.loadData(processedData.value);
    }
  },
};

const [TableGrid] = useVbenVxeGrid({
  gridEvents,
  gridOptions,
});
</script>

<template>
  <div class="table-wrapper">
    <TableGrid>
      <template #name="{ row }">
        <div class="flex items-center gap-2">
          <NButton
            v-if="row.url"
            text
            tag="a"
            :href="row.url"
            target="_blank"
            size="small"
            type="info"
          >
            {{ row.name }}
          </NButton>
          <template v-else>
            {{ row.name }}
          </template>
          <NButton
            v-if="row.doc"
            text
            type="primary"
            @click="handleDocClick(row.doc)"
          >
            <NIcon :size="16">
              <DocIcon />
            </NIcon>
          </NButton>
        </div>
      </template>
    </TableGrid>
  </div>
</template>

<style lang="scss">
:deep(.vxe-table--main-wrapper) {
  .vxe-table--body-wrapper {
    .vxe-table--body {
      .vxe-body--row {
        td.vxe-body--column {
          background-color: inherit !important;
          white-space: pre-wrap !important;
          word-break: break-word !important;
          height: auto !important;
          padding: 8px !important;
        }
      }
    }
  }

  // Dark mode 适配
  .dark & {
    .vxe-table--header-wrapper {
      background-color: theme('colors.gray.800') !important;

      .vxe-table--header {
        .vxe-header--row {
          th.vxe-header--column {
            background-color: theme('colors.gray.800') !important;
            color: theme('colors.gray.100') !important;
            border-color: theme('colors.gray.600') !important;
          }
        }
      }
    }

    .vxe-table--body-wrapper {
      background-color: theme('colors.gray.900') !important;

      .vxe-table--body {
        .vxe-body--row {
          td.vxe-body--column {
            background-color: theme('colors.gray.900') !important;
            color: theme('colors.gray.100') !important;
            border-color: theme('colors.gray.600') !important;
          }

          &:hover {
            td.vxe-body--column {
              background-color: theme('colors.gray.800') !important;
            }
          }
        }
      }
    }

    .vxe-table--border-line {
      border-color: theme('colors.gray.600') !important;
    }
  }
}
</style>
