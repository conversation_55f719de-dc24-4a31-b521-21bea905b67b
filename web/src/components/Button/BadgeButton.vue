<script lang="ts" setup>
import { NBadge, NButton } from 'naive-ui';

interface BadgeItem {
  type: 'error' | 'info' | 'success' | 'warning';
}

defineProps<{
  badges?: BadgeItem[];
}>();

const emit = defineEmits<{
  (e: 'click'): void;
}>();
</script>

<template>
  <NButton
    size="tiny"
    type="primary"
    class="badge-button relative !h-8 !px-3"
    @click="emit('click')"
  >
    <span>详情</span>
    <div class="absolute -right-0.5 -top-0.5 flex -space-x-3.5">
      <NBadge
        v-for="badge in badges"
        :key="badge.type"
        :type="badge.type"
        dot
        class="badge-mini"
      />
    </div>
  </NButton>
</template>

<style scoped>
.badge-button {
  display: inline-flex;
  align-items: center;
}

:deep(.badge-mini) {
  --n-font-size: 11px;
}

:deep(.n-badge-sup) {
  box-shadow: 0 0 0 1px #fff;
  transform: scale(0.85);
}

:deep(.n-button) {
  background-color: var(--n-color) !important;
}

:deep(.n-button:hover) {
  background-color: var(--n-color-hover) !important;
}

:deep(.n-button:active) {
  background-color: var(--n-color-pressed) !important;
}
</style>
