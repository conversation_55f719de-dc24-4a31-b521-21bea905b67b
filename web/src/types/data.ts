// Table related interfaces
export interface Table {
  name?: string;
  url?: string;
  table?: Record<string, any>[];
}

// Topic related interfaces
export interface TopicItem {
  des?: string;
  isFold?: boolean;
  picDir?: string;
  qs?: string[];
  table?: Record<string, any>[];
  tables?: Table[];
  topic: string;
  url?: string;
  isX?: boolean;
}

// Task related interfaces
export interface Task {
  date?: string;
  des?: string;
  item?: string[];
  pid?: string;
  qs?: string[];
  review?: string;
  sub?: Task[];
  tag: string;
  task: string;
  topics?: TopicItem[];
  url?: string;
  x?: boolean;
}

// Github related interfaces
export interface Repository {
  cmd?: string[];
  des?: string;
  doc?: string;
  qs?: string[];
  rel?: Repository[];
  rep?: Repository[];
  score: number;
  sub?: Repository[];
  tag?: string;
  topics?: TopicItem[];
  type: string;
  url: string;
}

export interface ConfigRepo {
  repo: Repository[];
  score: number;
  tag: string;
  topics?: TopicItem[];
  type: string;
  using?: Repository;
}

// Goods related interfaces
export interface GoodsItem {
  date?: string;
  des?: string;
  name: string;
  param?: string;
  price?: string;
  record?: string[];
  score: number;
  url?: string;
  use?: boolean;
}

export interface GoodsCategory {
  des?: string;
  item: GoodsItem[];
  name?: string;
  score: number;
  topics?: TopicItem[];
  type: string;
  tag: string;
  using?: GoodsItem;
}

export interface FlattenedGoodsItem {
  category: GoodsCategory;
  date?: string;
  des?: string;
  name: string;
  param?: string;
  price?: string;
  score: number;
  tag: string;
  type: string;
  url?: string;
}

export interface GoodsData {
  [tag: string]: GoodsCategory[];
}
