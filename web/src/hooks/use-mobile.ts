import { onMounted, onUnmounted, ref } from 'vue';

export function useMobile(breakpoint = 800) {
  const isMobileWidth = ref(false);

  const checkMobile = () => {
    isMobileWidth.value = window.innerWidth < breakpoint;
  };

  onMounted(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
  });

  return {
    isMobileWidth,
  };
}
