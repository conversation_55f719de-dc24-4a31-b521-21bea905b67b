import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // {
  //   component: () => import('#/views/dashboard/analytics/index.vue'),
  //   meta: {
  //     affixTab: true,
  //     // icon: 'lucide:area-chart',
  //     title: $t('page.dashboard.analytics'),
  //   },
  //   name: 'Analytics',
  //   path: '/analytics',
  // },
  // {
  //   component: () => import('#/views/dashboard/workspace/index.vue'),
  //   meta: {
  //     // icon: 'carbon:workspace',
  //     title: $t('page.dashboard.workspace'),
  //   },
  //   name: 'Workspace',
  //   path: '/workspace',
  // },
];

export default routes;
