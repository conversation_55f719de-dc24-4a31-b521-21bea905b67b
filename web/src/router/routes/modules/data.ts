import type { RouteRecordRaw } from "vue-router";

const routes: RouteRecordRaw[] = [
  {
    component: () => import("#/views/data/gh/index.vue"),
    meta: {
      query: {
        tag: "",
        type: "",
      },
      title: "Github"
    },
    name: "GithubRepoManage",
    path: "/data/gh"
  },
  // {
  //   component: () => import("#/views/data/task/index.vue"),
  //   meta: {
  //     title: "Task"
  //   },
  //   name: "TaskManage",
  //   path: "/data/task"
  // },
  {
    component: () => import("#/views/data/goods/index.vue"),
    meta: {
      title: "Goods"
    },
    name: "GoodsManage",
    path: "/data/goods"
  }
];

export default routes;
