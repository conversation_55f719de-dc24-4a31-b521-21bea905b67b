import { describe, expect, it } from 'vitest';
import {
  getScoreClassName,
  getScoreBackgroundColor,
  getScoreDescription,
  isLowScore,
  isHighScore,
  SCORE_CONFIG
} from '../score';

describe('Score Utils', () => {
  describe('getScoreClassName', () => {
    it('should return correct class names for valid scores', () => {
      expect(getScoreClassName(0)).toBe('score-0');
      expect(getScoreClassName(1)).toBe('score-1');
      expect(getScoreClassName(2)).toBe('score-2');
      expect(getScoreClassName(3)).toBe('score-3');
      expect(getScoreClassName(4)).toBe('score-4');
      expect(getScoreClassName(5)).toBe('score-5');
    });

    it('should handle edge cases', () => {
      expect(getScoreClassName(-1)).toBe('score-0'); // 负数应该返回最小值
      expect(getScoreClassName(6)).toBe('score-5'); // 超出范围应该返回最大值
      expect(getScoreClassName(2.7)).toBe('score-2'); // 小数应该向下取整
      expect(getScoreClassName(undefined)).toBe('score-0'); // undefined应该返回默认值
      expect(getScoreClassName(null)).toBe('score-0'); // null应该返回默认值
    });
  });

  describe('getScoreBackgroundColor', () => {
    it('should return correct background colors', () => {
      expect(getScoreBackgroundColor(0)).toBe('indianred');
      expect(getScoreBackgroundColor(1)).toBe('lightcoral');
      expect(getScoreBackgroundColor(2)).toBe('lightsalmon');
      expect(getScoreBackgroundColor(3)).toBe('lightgoldenrodyellow');
      expect(getScoreBackgroundColor(4)).toBe('lightgreen');
      expect(getScoreBackgroundColor(5)).toBe('lightblue');
    });

    it('should handle null and undefined', () => {
      expect(getScoreBackgroundColor(undefined)).toBe('indianred');
      expect(getScoreBackgroundColor(null)).toBe('indianred');
    });
  });

  describe('getScoreDescription', () => {
    it('should return correct descriptions', () => {
      expect(getScoreDescription(0)).toBe('极差');
      expect(getScoreDescription(1)).toBe('很差');
      expect(getScoreDescription(2)).toBe('较差');
      expect(getScoreDescription(3)).toBe('一般');
      expect(getScoreDescription(4)).toBe('良好');
      expect(getScoreDescription(5)).toBe('优秀');
    });
  });

  describe('isLowScore', () => {
    it('should correctly identify low scores', () => {
      expect(isLowScore(0)).toBe(true);
      expect(isLowScore(1)).toBe(false);
      expect(isLowScore(2)).toBe(false);
      expect(isLowScore(3)).toBe(false);
      expect(isLowScore(4)).toBe(false);
      expect(isLowScore(5)).toBe(false);
    });
  });

  describe('isHighScore', () => {
    it('should correctly identify high scores', () => {
      expect(isHighScore(0)).toBe(false);
      expect(isHighScore(1)).toBe(false);
      expect(isHighScore(2)).toBe(false);
      expect(isHighScore(3)).toBe(false);
      expect(isHighScore(4)).toBe(false);
      expect(isHighScore(5)).toBe(true);
    });
  });

  describe('SCORE_CONFIG', () => {
    it('should have all required score configurations', () => {
      expect(SCORE_CONFIG).toHaveProperty('0');
      expect(SCORE_CONFIG).toHaveProperty('1');
      expect(SCORE_CONFIG).toHaveProperty('2');
      expect(SCORE_CONFIG).toHaveProperty('3');
      expect(SCORE_CONFIG).toHaveProperty('4');
      expect(SCORE_CONFIG).toHaveProperty('5');
    });

    it('should have correct structure for each score', () => {
      Object.values(SCORE_CONFIG).forEach(config => {
        expect(config).toHaveProperty('className');
        expect(config).toHaveProperty('backgroundColor');
        expect(config).toHaveProperty('description');
      });
    });
  });
});
