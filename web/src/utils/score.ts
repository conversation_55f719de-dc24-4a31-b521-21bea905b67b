/**
 * Score 相关工具函数和配置
 */

// Score 颜色配置
export const SCORE_CONFIG = {
  0: {
    className: 'score-0',
    backgroundColor: 'indianred',
    description: '无',
    isDisplay: true
  },
  1: {
    className: 'score-1',
    backgroundColor: 'lightcoral',
    description: '差',
    isDisplay: true
  },
  2: {
    className: 'score-2',
    backgroundColor: 'lightsalmon',
    description: '差',
    isDisplay: true
  },
  3: {
    className: 'score-3',
    backgroundColor: 'lightgoldenrodyellow',
    description: '中',
    isDisplay: false
  },
  4: {
    className: 'score-4',
    backgroundColor: 'lightgreen',
    description: '良',
    isDisplay: false
  },
  5: {
    className: 'score-5',
    backgroundColor: 'lightblue',
    description: '优',
    isDisplay: true
  }
} as const;

/**
 * 根据score获取对应的CSS类名
 * @param score 分数值
 * @returns CSS类名，如果不应该显示则返回空字符串
 */
export function getScoreClassName(score: number | undefined | null): string {
  // 如果没有score，直接返回空字符串
  if (score === undefined || score === null) {
    return '';
  }

  // 确保score在0-5范围内
  const normalizedScore = Math.max(0, Math.min(5, Math.floor(score)));
  const config = SCORE_CONFIG[normalizedScore as keyof typeof SCORE_CONFIG];

  // 检查是否应该显示颜色
  if (!config.isDisplay) {
    return '';
  }

  return config.className;
}

/**
 * 根据score获取对应的背景色
 * @param score 分数值
 * @returns 背景色值，如果不应该显示则返回空字符串
 */
export function getScoreBackgroundColor(score: number | undefined | null): string {
  // 如果没有score，直接返回空字符串
  if (score === undefined || score === null) {
    return '';
  }

  const normalizedScore = Math.max(0, Math.min(5, Math.floor(score)));
  const config = SCORE_CONFIG[normalizedScore as keyof typeof SCORE_CONFIG];

  // 检查是否应该显示颜色
  if (!config.isDisplay) {
    return '';
  }

  return config.backgroundColor;
}

/**
 * 根据score获取描述
 * @param score 分数值
 * @returns 描述文本
 */
export function getScoreDescription(score: number | undefined | null): string {
  const safeScore = score ?? 0;
  const normalizedScore = Math.max(0, Math.min(5, Math.floor(safeScore)));
  return SCORE_CONFIG[normalizedScore as keyof typeof SCORE_CONFIG].description;
}

/**
 * 获取所有score配置
 * @returns 完整的score配置对象
 */
export function getAllScoreConfig() {
  return SCORE_CONFIG;
}

/**
 * 检查是否为低分（根据业务需求，可以是0分）
 * @param score 分数值
 * @returns 是否为低分
 */
export function isLowScore(score: number | undefined | null): boolean {
  const safeScore = score ?? 0;
  return safeScore === 0;
}

/**
 * 检查是否为高分（根据业务需求，可以是5分）
 * @param score 分数值
 * @returns 是否为高分
 */
export function isHighScore(score: number | undefined | null): boolean {
  const safeScore = score ?? 0;
  return safeScore === 5;
}

/**
 * 检查是否应该显示score对应的背景色
 * @param score 分数值
 * @returns 是否应该显示背景色
 */
export function shouldDisplayScoreColor(score: number | undefined | null): boolean {
  // 如果没有score，不显示
  if (score === undefined || score === null) {
    return false;
  }

  const normalizedScore = Math.max(0, Math.min(5, Math.floor(score)));
  const config = SCORE_CONFIG[normalizedScore as keyof typeof SCORE_CONFIG];

  return config.isDisplay;
}

/**
 * 获取score的样式对象（包含背景色）
 * @param score 分数值
 * @returns 样式对象，如果不应该显示则返回空对象
 */
export function getScoreStyle(score: number | undefined | null): { backgroundColor?: string } {
  const backgroundColor = getScoreBackgroundColor(score);
  return backgroundColor ? { backgroundColor } : {};
}
