interface UserInfo {
  id: number;
  password: string;
  realName: string;
  roles: string[];
  username: string;
}

const MOCK_USERS: UserInfo = {
  id: 0,
  password: '123456',
  realName: 'Vben',
  roles: ['super'],
  username: 'vben',
};

/**
 * 获取用户信息
 */
export function getUserInfoApi() {
  // 返回超级管理员用户信息
  const defaultUser = MOCK_USERS as UserInfo;
  const { password: _pwd, ...userInfo } = defaultUser;
  return {
    code: 0,
    data: userInfo,
    message: 'ok',
  };
}
