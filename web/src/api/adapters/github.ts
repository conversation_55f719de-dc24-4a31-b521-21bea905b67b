import type { Page<PERSON>arams } from "#/types";
import type { ConfigRepo } from "#/types/data";

import githubData from "@data/gh.json";

function formatGithubData(data: ConfigRepo[]) {
  // Now we return the ConfigRepo array directly since we want to show types
  return data;
}

class GithubAdapter {
  private data: ConfigRepo[];

  constructor() {
    this.data = formatGithubData(githubData as unknown as ConfigRepo[]);
  }

  // getTypes() {
  //   const tagTypes: { tag: string; types: string[] }[] = [];
  //   const tagMap = new Map<string, Set<string>>();
  //
  //   this.data.forEach((item) => {
  //     if (!tagMap.has(item.tag!)) {
  //       tagMap.set(item.tag!, new Set());
  //     }
  //     tagMap.get(item.tag!)?.add(item.type);
  //   });
  //
  //   tagMap.forEach((types, tag) => {
  //     tagTypes.push({
  //       tag,
  //       types: [...types].sort(),
  //     });
  //   });
  //
  //   return tagTypes;
  // }

  query(
    params: PageParams & {
      repo?: string;
      tag?: string;
      type?: string;
    }
  ) {
    let filteredData = this.data;

    if (params.repo) {
      const repoLower = params.repo.toLowerCase();
      filteredData = filteredData.filter((item) =>
        item.repo.some(repo => repo.url.toLowerCase().includes(repoLower))
      );
    }

    if (params.tag) {
      filteredData = filteredData.filter((item) => item.tag === params.tag);
    }

    if (params.type) {
      filteredData = filteredData.filter((item) => item.type === params.type);
    }

    // Sort by score in descending order
    filteredData.sort((a, b) => (b.score || 0) - (a.score || 0));

    const start = (params.page - 1) * params.pageSize;
    const end = start + params.pageSize;
    const items = filteredData.slice(start, end);

    return {
      page: params.page,
      pageSize: params.pageSize,
      total: filteredData.length,
      items,
    };
  }
}

export const githubAdapter = new GithubAdapter();
export const getGithubListApi = githubAdapter.query.bind(githubAdapter);
// export const getGithubTagApi = githubAdapter.getTypes.bind(githubAdapter);
