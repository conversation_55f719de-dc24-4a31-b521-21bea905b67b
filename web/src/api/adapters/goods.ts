import type { PageParams } from '#/types';
import type { FlattenedGoodsItem, GoodsData, GoodsCategory } from '#/types/data';

import goodsData from '@data/goods.json';

function formatGoodsData(data: GoodsData) {
  const result: FlattenedGoodsItem[] = [];

  // 遍历每个分类数组
  Object.values(data).forEach((categoryArray: GoodsCategory[]) => {
    // 遍历每个分类
    categoryArray.forEach((category: GoodsCategory) => {
      // 添加当前使用的物品
      if (category.using) {
        result.push({
          category,
          ...category.using,
          score: category.score,
          tag: category.tag,
          type: category.type,
        });
      }
    });
  });

  return result;
}

class GoodsAdapter {
  private data: FlattenedGoodsItem[];

  constructor() {
    this.data = formatGoodsData(goodsData as unknown as GoodsData);
  }

  getTypes() {
    const tagTypes: { tag: string; types: string[] }[] = [];
    const tagMap = new Map<string, Set<string>>();

    this.data.forEach((item) => {
      if (!tagMap.has(item.tag)) {
        tagMap.set(item.tag, new Set());
      }
      tagMap.get(item.tag)?.add(item.type);
    });

    tagMap.forEach((types, tag) => {
      tagTypes.push({
        tag,
        types: [...types].sort(),
      });
    });

    return tagTypes.sort((a, b) => a.tag.localeCompare(b.tag));
  }

  query(params: PageParams & { tag?: string; type?: string }) {
    let filteredData = this.data;

    if (params.tag) {
      filteredData = filteredData.filter((item) => item.tag === params.tag);
    }

    if (params.type) {
      filteredData = filteredData.filter((item) => item.type === params.type);
    }

    const start = (params.page - 1) * params.pageSize;
    const end = start + params.pageSize;
    const items = filteredData.slice(start, end);

    return {
      page: params.page,
      pageSize: params.pageSize,
      total: filteredData.length,
      items,
    };
  }
}

export const goodsAdapter = new GoodsAdapter();
export const getGoodsListApi = goodsAdapter.query.bind(goodsAdapter);
export const getGoodsTagApi = goodsAdapter.getTypes.bind(goodsAdapter);
